# Event Unlock Guide for RPG Maker Game

This guide will help you unlock all events in the game by manipulating the game's save data through JavaScript console commands.

## Method 1: Simple Console Command (Recommended)

1. **Open the game** and load your save file
2. **Open Developer Console**:
   - Press `F12` on your keyboard
   - Click on the "Console" tab
3. **Copy and paste** the contents of `simple_unlock.js` into the console
4. **Press Enter** to execute the script
5. **Save your game** to preserve the changes

## Method 2: Full Script Execution

1. **Open the game** and load your save file
2. **Open Developer Console** (F12 → Console)
3. **Copy and paste** the contents of `unlock_all_events.js` into the console
4. **Press Enter** to execute the script
5. **Save your game** to preserve the changes

## What These Scripts Do

### Switches Unlocked:
- Game initialization and core systems
- Sister interaction events (diary, portrait interaction, tea party, bath)
- Cooking system activation
- All achievement switches
- Time system switches

### Variables Set:
- Sister's affection level to maximum (100)
- Sister's mood to maximum (100)
- Days lived set to 30 (unlocks time-gated content)
- Interaction counters (patting, ahoge pulling, etc.)
- Combat power and other progression variables

### Self-Switches:
- Unlocks all self-switches (A, B, C, D) for all events across all maps
- This ensures that repeatable events and conditional events are accessible

### Items Given:
- Camera item (for screenshot functionality)
- 10,000 gold
- Various useful materials and items

## Important Notes

1. **Backup Your Save**: Before running these scripts, make a backup of your save file
2. **Save After Execution**: Always save your game after running the script to preserve changes
3. **Map Refresh**: The script automatically refreshes the current map, but you may need to change maps to see all effects
4. **Compatibility**: These scripts are designed for this specific RPG Maker game and may not work with other games

## Troubleshooting

### If the script doesn't work:
1. Make sure you're in the game (not the title screen)
2. Ensure you have a save file loaded
3. Check the console for any error messages
4. Try reloading the game and running the script again

### If some events still don't appear:
1. Try changing maps or reloading the game
2. Some events may require specific time conditions - the script sets time to 2 PM
3. Some events may require story progression that can't be unlocked via switches alone

### Console Access Issues:
- **Desktop**: F12 should work in most cases
- **Mobile/Android**: Console access may not be available
- **Steam/Packaged versions**: Some versions may have console disabled

## Alternative Methods

If console access doesn't work, you can also:

1. **Save File Editing**: Locate your save files and edit them directly (advanced users only)
2. **Plugin Installation**: Add the script as a plugin file in the game's plugin folder
3. **Cheat Engine**: Use memory editing tools (very advanced, not recommended)

## File Locations

### Save Files (typical locations):
- **Windows**: `%LOCALAPPDATA%/[GameName]/`
- **Steam**: `Steam/steamapps/common/[GameName]/www/save/`
- **Portable**: `[GameFolder]/www/save/`

### Plugin Folder:
- `[GameFolder]/www/js/plugins/`

## Script Contents Summary

The scripts manipulate the following game systems:
- **$gameSwitches**: Controls event availability and game states
- **$gameVariables**: Stores progression values and counters
- **$gameSelfSwitches**: Controls individual event states per map
- **$gameParty**: Player inventory and resources
- **$gameMap**: Current map state and refresh

## Safety

These scripts only modify game state variables and don't:
- Damage your computer or game files
- Access personal information
- Modify files outside the game
- Cause permanent damage (can be undone by loading an earlier save)

## Support

If you encounter issues:
1. Check that you copied the entire script correctly
2. Ensure you're running it in the correct context (game loaded)
3. Try the simpler version first (`simple_unlock.js`)
4. Make sure your game version is compatible

Remember to save your game after running the script to keep all the unlocked events!
