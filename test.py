import os

paths = []

keywords = ["CDKEY", "cdkey", "Key", "key", "redeem", "领取", "NSFW", "itch", "激活码", "キー"]
for root, _, files in os.walk("www/js"):
    for f in files:
        if f.endswith(".js"):
            path = os.path.join(root, f)
            with open(path, "r", encoding="utf-8", errors="ignore") as fp:
                text = fp.read()
                for kw in keywords:
                    if kw in text:
                        print(f"[FOUND] {kw} in {path}")
                        paths.append(path)
                        
print(set(paths))

