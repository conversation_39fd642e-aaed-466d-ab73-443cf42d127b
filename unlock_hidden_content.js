// Enhanced Event Unlock Script - Attempts to unlock hidden/censored content
// This script tries to access content that may be hidden in the Safe Edition

console.log("Starting Enhanced Event Unlock (including hidden content)...");

// === BASIC UNLOCKS ===
// Core switches for events and achievements
[1,2,3,4,5,23,24,30,31,36,54,55,400,460,461,465,470,471,473,474,475,476,478,479,480,481].forEach(id => {
    $gameSwitches.setValue(id, true);
});

// === BATH/LEWD CONTENT SPECIFIC UNLOCKS ===
console.log("Attempting to unlock bath/intimate content...");

// Bath-related switches
$gameSwitches.setValue(31, true);  // 洗澡前后 (Before/After Bath)
$gameSwitches.setValue(36, true);  // 浴室门打开 (Bathroom Door Open)
$gameSwitches.setValue(465, true); // 成就：和妹妹一起共浴 (Achievement: Bath with Sister)
$gameSwitches.setValue(476, true); // 成就：浴缸里的新陪伴 (Achievement: New Companion in Bathtub)

// Sister interaction switches
$gameSwitches.setValue(24, true);  // 与妹妹立绘互动 (Interact with Sister Portrait)
$gameSwitches.setValue(25, true);  // 妹妹黑化 (Sister Dark Mode)
$gameSwitches.setValue(26, true);  // 摸头中 (Patting Head)
$gameSwitches.setValue(27, true);  // 摸头结束 (Head Patting End)
$gameSwitches.setValue(32, true);  // 摸头演出等待 (Head Pat Scene Wait)
$gameSwitches.setValue(33, true);  // 妹妹呆毛演出等待 (Sister Ahoge Scene Wait)

// === ADVANCED CONTENT VARIABLES ===
console.log("Setting intimate interaction variables...");

// Sister affection and mood (maximum values)
$gameVariables.setValue(12, 999);  // 妹妹累积好感度 (Sister's Accumulated Affection)
$gameVariables.setValue(15, 99);   // 妹妹好感度等级 (Sister's Affection Level)
$gameVariables.setValue(17, 999);  // 妹妹好感度 (Sister's Affection)
$gameVariables.setValue(20, 999);  // 妹妹心情值 (Sister's Mood Value)

// Interaction counters (high values to unlock content)
$gameVariables.setValue(211, 999); // 摸摸次数 (Pat Count)
$gameVariables.setValue(212, 50);  // 洗澡后计数（专用）(Post-Bath Count - Special)
$gameVariables.setValue(213, 999); // 薅呆毛次数 (Ahoge Pull Count)

// Heart colors (all unlocked)
for(let i = 201; i <= 207; i++) {
    $gameVariables.setValue(i, 2); // Set all hearts to special color
}

// === ATTEMPT TO UNLOCK DELUXE CONTENT ===
console.log("Attempting deluxe version unlock...");

// Try to simulate deluxe version
$gameSwitches.setValue(499, true); // Potential deluxe flag
$gameVariables.setValue(1, 1);     // Language version
$gameVariables.setValue(2, 999);   // Game version

// === SELF-SWITCHES FOR INTIMATE EVENTS ===
console.log("Unlocking self-switches for intimate events...");

// Focus on maps that likely contain bath/intimate content
const intimateMaps = [21, 24, 25]; // Home/bedroom/bathroom maps
intimateMaps.forEach(mapId => {
    for(let eventId = 1; eventId <= 50; eventId++) {
        ['A','B','C','D'].forEach(letter => {
            $gameSelfSwitches.setValue([mapId, eventId, letter], true);
        });
    }
});

// === SPECIAL CONTENT FLAGS ===
console.log("Setting special content flags...");

// Time-based unlocks (set to evening/night for intimate content)
if (typeof $gameSystem.set_hour === 'function') {
    $gameSystem.set_hour(20); // 8 PM - evening time
}

// Special state flags
$gameSwitches.setValue(471, true); // 成就:妹妹的特别奖励 (Achievement: Sister's Special Reward)
$gameSwitches.setValue(479, true); // 成就：收到了妹妹的… (Achievement: Received from Sister...)

// === ATTEMPT TO BYPASS SAFE EDITION RESTRICTIONS ===
console.log("Attempting to bypass Safe Edition restrictions...");

// Try to modify game title check
if (typeof $dataSystem !== 'undefined' && $dataSystem.gameTitle) {
    // Temporarily modify title to trigger deluxe content
    const originalTitle = $dataSystem.gameTitle;
    $dataSystem.gameTitle = $dataSystem.gameTitle.replace("Safe Edition", "Deluxe");
    
    // Set a flag to restore later if needed
    $gameVariables.setValue(999, 1); // Flag that we modified the title
}

// === UNLOCK ALL REMAINING CONTENT ===
console.log("Performing comprehensive unlock...");

// Unlock all maps and events
[1,2,4,5,9,10,11,13,14,15,16,17,18,20,21,24,25,26,27,28,29,30,31,32,33,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55].forEach(mapId => {
    for(let eventId = 1; eventId <= 50; eventId++) {
        ['A','B','C','D'].forEach(letter => {
            $gameSelfSwitches.setValue([mapId, eventId, letter], true);
        });
    }
});

// Give useful items
$gameParty.gainItem($dataItems[14], 1); // Camera
$gameParty.gainGold(99999); // Lots of money

// === FINAL SETUP ===
// Refresh map and systems
$gameMap.requestRefresh();

// Update localized names if function exists
if (typeof DataManager.updateLocalizedNames === 'function') {
    DataManager.updateLocalizedNames();
}

console.log("Enhanced unlock completed!");
console.log("Note: Some content may still be unavailable if completely removed from Safe Edition");
console.log("Try checking bathroom, bedroom, and evening/night events");
console.log("Save your game to preserve changes");

// Show completion message
if ($gameMessage && !$gameMessage.isBusy()) {
    $gameMessage.add("Enhanced unlock completed!");
    $gameMessage.add("Hidden content attempts made.");
    $gameMessage.add("Check bathroom and bedroom areas.");
    $gameMessage.add("Save your game!");
}
