# Hidden Content Guide - Safe Edition Analysis

## 🔍 **What's Hidden in the Safe Edition**

Based on code analysis, the following content appears to be hidden or censored:

### **Bath/Intimate Events:**
- **Bathroom interactions** with sister character
- **Bathing together scenes** (Achievement: 和妹妹一起共浴)
- **Post-bath interactions** (Variable 212: 洗澡后计数)
- **Intimate touching/patting** beyond basic interactions

### **Special Rewards:**
- **Sister's special reward** (Achievement 471)
- **Mysterious items from sister** (Achievement 479)
- **Adult-oriented dialogue** and scenes

### **Time-Gated Content:**
- **Evening/night intimate events** (19:00-22:59 time period)
- **Bedroom interactions** during specific times
- **Sleep-related events**

## 🛠️ **How to Attempt Unlocking Hidden Content**

### **Method 1: Enhanced Script**
Use the `unlock_hidden_content.js` script which:
- Sets maximum affection levels
- Unlocks bath-related switches
- Attempts to bypass Safe Edition restrictions
- Sets optimal time for intimate content

### **Method 2: Manual Console Commands**
```javascript
// Set maximum sister affection
$gameVariables.setValue(12, 999); // Max affection
$gameVariables.setValue(17, 999); // Max affection level
$gameVariables.setValue(20, 999); // Max mood

// Unlock bath switches
$gameSwitches.setValue(31, true);  // Bath before/after
$gameSwitches.setValue(36, true);  // Bathroom door open
$gameSwitches.setValue(465, true); // Bath achievement

// Set evening time
$gameSystem.set_hour(20); // 8 PM

// High interaction counts
$gameVariables.setValue(211, 999); // Pat count
$gameVariables.setValue(213, 999); // Ahoge pull count
```

## 📍 **Where to Look for Hidden Content**

### **Locations to Check:**
1. **Map 21** - Home/Living area
2. **Map 24** - Likely bathroom/private areas  
3. **Map 25** - Bedroom areas
4. **Evening hours** (19:00-22:59)
5. **After bath events** (check bathroom after setting bath flags)

### **Interaction Points:**
- **Sister character** during evening hours
- **Bathroom door** (switch 36 unlocks this)
- **Bedroom areas** with high affection
- **Portrait interactions** (switch 24)

### **Triggers to Try:**
- **Long press** on sister character
- **Multiple touches** in sequence
- **Specific time periods** (evening/night)
- **High affection + specific locations**

## ⚠️ **Important Limitations**

### **Safe Edition Restrictions:**
The Safe Edition may have:
- **Completely removed assets** (images, audio, scripts)
- **Hardcoded content blocks** that can't be bypassed
- **Missing dialogue files** for adult content
- **Placeholder events** instead of real content

### **What the Scripts Can/Cannot Do:**

**✅ Can Unlock:**
- Hidden switches and variables
- Time-gated content
- Affection-locked events
- Achievement flags

**❌ Cannot Restore:**
- Completely removed image files
- Deleted dialogue text
- Missing audio files
- Removed script functions

## 🎯 **Specific Things to Try After Running Script**

### **Immediate Actions:**
1. **Save your game** after running the script
2. **Go to Map 21** (home area)
3. **Set time to evening** (8 PM)
4. **Check bathroom area** (Map 24 if accessible)
5. **Interact with sister** during evening hours

### **Interaction Sequence:**
1. **Approach sister character**
2. **Long press/hold** interaction
3. **Try multiple quick taps**
4. **Check during different times of day**
5. **Look for new dialogue options**

### **Achievement Checking:**
After running the script, check if these achievements unlock:
- **Bath with Sister** (成就：和妹妹一起共浴)
- **New Companion in Bathtub** (成就：浴缸里的新陪伴)
- **Sister's Special Reward** (成就:妹妹的特别奖励)

## 🔧 **Advanced Techniques**

### **File Analysis:**
If you want to dig deeper:
1. **Check dialogue files** in `/data/CN/`, `/data/EN/`, `/data/JP/`
2. **Look for missing map files** (gaps in Map###.json sequence)
3. **Search for image references** that might point to removed content

### **Code Modification:**
For advanced users:
1. **Modify game title** check in code
2. **Search for "Safe Edition" restrictions** in plugins
3. **Look for version checks** that might hide content

## 📝 **Expected Results**

### **If Content Exists but Hidden:**
- New dialogue options appear
- Previously locked areas become accessible
- Achievement notifications trigger
- New interaction animations

### **If Content is Completely Removed:**
- Scripts run but no new content appears
- Placeholder text or empty events
- Missing image/audio errors
- Events trigger but show generic content

## 🎮 **Final Notes**

The Safe Edition appears to be a legitimate censored version, so some content may be genuinely unavailable. However, the scripts provided will unlock any content that's merely hidden behind flags rather than completely removed.

**Remember:** Always backup your save files before running any scripts!
