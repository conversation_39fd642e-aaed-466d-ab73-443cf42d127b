{"autoplayBgm": false, "autoplayBgs": false, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 23, "note": "<深渊>", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 20, "width": 19, "data": [5984, 5984, 5984, 5984, 5984, 5984, 5984, 6008, 2832, 2816, 2840, 6000, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5988, 6022, 2832, 2816, 2840, 6024, 5992, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5988, 6022, 6422, 2832, 2816, 2840, 6419, 6024, 6012, 5992, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 6008, 6422, 6428, 2832, 2816, 2840, 6425, 6419, 6418, 6000, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5988, 6022, 6428, 2850, 2817, 2816, 2818, 2852, 6425, 6424, 6000, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 6008, 6422, 2850, 2817, 2820, 2844, 2824, 2818, 2836, 2852, 6024, 5992, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 6008, 6428, 2832, 2816, 2840, 2906, 2832, 2816, 2816, 2840, 6419, 6000, 5984, 5984, 5984, 5984, 5984, 5984, 5988, 6022, 2850, 2817, 2820, 2854, 2896, 2832, 2816, 2816, 2840, 6425, 6000, 5984, 5984, 5984, 5984, 5984, 5984, 6008, 6422, 2832, 2816, 2840, 2898, 2890, 2832, 2816, 2816, 2818, 2852, 6000, 5984, 5984, 5984, 5984, 5984, 5984, 6008, 6428, 2832, 2820, 2854, 2882, 2902, 2832, 2816, 2816, 2816, 2840, 6000, 5984, 5984, 5984, 5984, 5984, 5984, 6008, 2850, 2817, 2840, 2898, 2890, 2850, 2817, 2816, 2816, 2816, 2840, 6000, 5984, 5984, 5984, 5984, 5984, 5984, 6008, 2832, 2816, 2840, 2880, 2888, 2856, 2824, 2816, 2816, 2816, 2840, 6000, 5984, 5984, 5984, 5984, 5984, 5984, 6008, 2856, 2824, 2840, 2880, 2866, 2900, 2832, 2816, 2816, 2816, 2840, 6000, 5984, 5984, 5984, 5984, 5984, 5984, 5986, 6020, 2832, 2840, 2904, 2872, 2888, 2832, 2816, 2816, 2816, 2840, 6000, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 6008, 2856, 2826, 2852, 2880, 2888, 2832, 2816, 2816, 2816, 2840, 6000, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5986, 6020, 2832, 2840, 2904, 2902, 2832, 2816, 2816, 2816, 2840, 6000, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 6008, 2856, 2826, 2836, 2836, 2817, 2816, 2816, 2820, 2854, 6000, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5986, 6020, 2832, 2816, 2816, 2816, 2816, 2820, 2854, 6018, 5985, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 6008, 2832, 2816, 2816, 2820, 2844, 2854, 6018, 5985, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 6008, 2832, 2816, 2820, 2854, 6018, 6004, 5985, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 6008, 2832, 2816, 2840, 6018, 5985, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 6008, 2832, 2816, 2840, 6000, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 6008, 2832, 2816, 2840, 6000, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3050, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3042, 3044, 0, 3025, 3044, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3042, 3009, 3032, 0, 3048, 3018, 3028, 3044, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3024, 3012, 3046, 0, 0, 3048, 3016, 3032, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3042, 3013, 3046, 0, 0, 0, 0, 3048, 3033, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3026, 3046, 0, 0, 0, 0, 3474, 3476, 3025, 3044, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3052, 0, 0, 0, 0, 3474, 3441, 3464, 3048, 3046, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3456, 3440, 3442, 3476, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3042, 3044, 0, 0, 0, 3456, 3440, 3440, 3464, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3048, 3033, 0, 0, 0, 3456, 3440, 3440, 3464, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3025, 3044, 0, 0, 3480, 3448, 3440, 3464, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3024, 3032, 0, 3042, 3044, 3456, 3440, 3464, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3048, 3046, 0, 3024, 3032, 3456, 3444, 3478, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3024, 3032, 3480, 3478, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3048, 3018, 3044, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3048, 3046, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 404, 0, 0, 0, 0, 0, 403, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 388, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 95, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 258, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 266, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 251, 0, 253, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 301, 0, 299, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 356, 367, 0, 299, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 295, 309, 1, 307, 294, 356, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 295, 327, 317, 0, 315, 326, 349, 294, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 356, 319, 317, 0, 0, 0, 315, 357, 318, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 295, 309, 0, 0, 0, 0, 0, 12, 366, 355, 0, 0, 0, 0, 0, 0, 0, 0, 0, 319, 317, 11, 0, 0, 0, 0, 0, 307, 294, 0, 0, 0, 0, 0, 0, 0, 0, 0, 367, 0, 0, 21, 0, 1, 0, 0, 315, 318, 0, 0, 0, 0, 0, 0, 0, 0, 295, 309, 0, 0, 0, 0, 0, 0, 0, 0, 291, 0, 0, 0, 0, 0, 0, 0, 0, 319, 317, 8, 0, 0, 0, 85, 38, 39, 0, 291, 0, 0, 0, 0, 0, 0, 0, 0, 367, 0, 0, 0, 0, 0, 0, 46, 47, 87, 366, 0, 0, 0, 0, 0, 0, 0, 0, 301, 0, 0, 37, 0, 0, 93, 0, 20, 95, 366, 0, 0, 0, 0, 0, 0, 0, 0, 311, 285, 0, 45, 0, 0, 93, 0, 11, 0, 299, 0, 0, 0, 0, 0, 0, 0, 0, 356, 293, 0, 0, 0, 0, 0, 0, 0, 0, 299, 0, 0, 0, 0, 0, 0, 0, 0, 0, 311, 285, 0, 0, 0, 0, 0, 32, 95, 366, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 293, 0, 0, 0, 0, 93, 40, 0, 291, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 311, 285, 0, 0, 0, 0, 103, 283, 286, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 355, 293, 0, 0, 0, 0, 283, 286, 355, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 301, 1, 0, 283, 333, 286, 356, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 367, 0, 283, 286, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 367, 0, 291, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 301, 0, 299, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 301, 252, 299, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 5, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 5, 5, 0, 5, 5, 5, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 5, 5, 5, 0, 5, 5, 5, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 5, 5, 5, 0, 0, 5, 5, 5, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 5, 5, 0, 0, 0, 0, 5, 5, 0, 255, 255, 255, 255, 255, 255, 255, 255, 0, 5, 5, 0, 0, 0, 0, 0, 0, 5, 5, 255, 255, 255, 255, 255, 255, 255, 255, 0, 5, 5, 0, 0, 0, 0, 0, 0, 5, 5, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 5, 5, 0, 5, 5, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 5, 5, 0, 5, 5, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 5, 5, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 5, 5, 5, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 5, 5, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255], "events": [null, {"id": 1, "name": "EV001", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 0, "characterIndex": 3}, "list": [{"code": 356, "indent": 0, "parameters": ["particle set stardust_w weather"]}, {"code": 356, "indent": 0, "parameters": ["PB_BGS_全停止"]}, {"code": 241, "indent": 0, "parameters": [{"name": "彷徨う小さな侵入者(Wandering Little Intruder)", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["AudioManager.fadeInBgm(3)"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 211, "indent": 0, "parameters": [1]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "$gameScreen.zoomScale() == 1"]}, {"code": 356, "indent": 1, "parameters": ["d<PERSON><PERSON><PERSON> 2 60 player"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["particle set dust_walk walk:player:0 def below"]}, {"code": 356, "indent": 0, "parameters": ["particle set grass_walk walk:player:5 def below"]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 3, "y": 19}, {"id": 2, "name": "EV002", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<width: 5>"]}, {"code": 408, "indent": 0, "parameters": ["<height: 1>"]}, {"code": 408, "indent": 0, "parameters": ["<offsetX: 0>"]}, {"code": 408, "indent": 0, "parameters": ["<offsetY: -0.25>"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Move", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 121, "indent": 0, "parameters": [40, 40, 0]}, {"code": 201, "indent": 0, "parameters": [0, 15, 13, 23, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 2, "walkAnime": true}], "x": 8, "y": 0}, {"id": 3, "name": "EV003", "note": "Light 100 #FFFFFF B50", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<width: 5>"]}, {"code": 408, "indent": 0, "parameters": ["<height: 1>"]}, {"code": 408, "indent": 0, "parameters": ["<offsetX: 0>"]}, {"code": 408, "indent": 0, "parameters": ["<offsetY: -0.25>"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Move", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 201, "indent": 0, "parameters": [0, 16, 10, 1, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 2, "walkAnime": true}], "x": 7, "y": 22}, null, null, null]}