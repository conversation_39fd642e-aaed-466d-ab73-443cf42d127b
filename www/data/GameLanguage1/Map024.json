{"autoplayBgm": false, "autoplayBgs": false, "battleback1Name": "genkan", "battleback2Name": "gray bar", "bgm": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 9, "note": "<宅>", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": true, "tilesetId": 1, "width": 9, "data": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "场景初始化", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 0, "characterIndex": 3}, "list": [{"code": 108, "indent": 0, "parameters": ["防报错措施"]}, {"code": 117, "indent": 0, "parameters": [8]}, {"code": 117, "indent": 0, "parameters": [20]}, {"code": 313, "indent": 0, "parameters": [0, 2, 1, 35]}, {"code": 313, "indent": 0, "parameters": [0, 2, 1, 36]}, {"code": 111, "indent": 0, "parameters": [1, 13, 0, 0, 3]}, {"code": 355, "indent": 1, "parameters": ["let ID = $gameVariables.value(13);"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.event(ID).start()"]}, {"code": 122, "indent": 1, "parameters": [13, 13, 0, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameMap.event(2).start()"]}, {"code": 122, "indent": 1, "parameters": [13, 13, 0, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}], "x": 0, "y": 8}, {"id": 2, "name": "清晨出门", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 4, "pattern": 1, "characterIndex": 1}, "list": [{"code": 111, "indent": 0, "parameters": [1, 60, 0, 2, 0]}, {"code": 231, "indent": 1, "parameters": [1, "entrance_R_D_DO", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 60, 0, 1, 0]}, {"code": 231, "indent": 2, "parameters": [1, "entrance_C_D_DO", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [0, 123, 0]}, {"code": 231, "indent": 3, "parameters": [1, "entrance_S_D_DO", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 231, "indent": 3, "parameters": [1, "entrance_S_M_DO", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 108, "indent": 0, "parameters": ["妹妹早起分歧"]}, {"code": 111, "indent": 0, "parameters": [0, 124, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["「そろそろ出発しないと……」"]}, {"code": 355, "indent": 1, "parameters": ["let id = this._eventId;"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.event(id).steupCEQJ(2)"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["「じゃ、いってきます。」"]}, {"code": 242, "indent": 1, "parameters": [2]}, {"code": 246, "indent": 1, "parameters": [2]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 122, "indent": 1, "parameters": [13, 13, 0, 0, 3]}, {"code": 201, "indent": 1, "parameters": [0, 36, 5, 3, 0, 2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 231, "indent": 0, "parameters": [9, "mio_tachie_smile", 1, 0, 572, 950, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": [">显现动作 : 图片[9] : 移动显现 : 时间[60] : 方向角度[180] : 移动距离[180]"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 111, "indent": 0, "parameters": [1, 20, 0, 70, 1]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE genkan_event_sis_40"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE genkan_event_sis_39"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >「いってらっしゃい、お兄ちゃん！」"]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.changePictureName(9, \"mio_tachie_smile2\");"]}, {"code": 111, "indent": 0, "parameters": [1, 60, 0, 2, 1]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE genkan_event_sis_42"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nl< >「今日は天気があまり良くないみたいだから、"]}, {"code": 401, "indent": 1, "parameters": ["気をつけてね…」"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE genkan_event_sis_41"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nl< >「無理しそうになったら、必ず戻ってきてね！」"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [2, "A", 1]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["朝の元気いっぱいな妹の姿を見て、"]}, {"code": 401, "indent": 1, "parameters": ["こんなに生き生きとした光景はとても新鮮で、"]}, {"code": 401, "indent": 1, "parameters": ["思わず強い感動に包まれた。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["妹をぎゅっと抱きしめたい衝動を必死にこらえながら、"]}, {"code": 401, "indent": 1, "parameters": ["笑顔で妹に応えた。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\{「おお…！行ってくる！」\\}"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["「ああ…お留守番よろしくね！」"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["妹に手を振りながら家を出て、今日も妹のために頑張ろう！"]}, {"code": 242, "indent": 0, "parameters": [2]}, {"code": 246, "indent": 0, "parameters": [2]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [13, 13, 0, 0, 3]}, {"code": 201, "indent": 0, "parameters": [0, 36, 5, 3, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 1, "y": 8}, {"id": 3, "name": "从深渊归来", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 111, "indent": 0, "parameters": [12, "$gameSystem.hour() <= 16"]}, {"code": 111, "indent": 1, "parameters": [1, 60, 0, 2, 0]}, {"code": 231, "indent": 2, "parameters": [1, "entrance_R_D_DO", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [1, 60, 0, 1, 0]}, {"code": 231, "indent": 3, "parameters": [1, "entrance_C_D_DO", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 231, "indent": 3, "parameters": [1, "entrance_S_D_DO", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 231, "indent": 1, "parameters": [1, "entrance_S_E_DO_RL", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 5, 0]}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 121, "indent": 1, "parameters": [5, 5, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 241, "indent": 0, "parameters": [{"name": "日常_放課後", "volume": 60, "pitch": 100, "pan": 0}]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 232, "indent": 0, "parameters": [92, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, true]}, {"code": 235, "indent": 0, "parameters": [92]}, {"code": 356, "indent": 0, "parameters": ["SaveNow"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["「ただいま。」"]}, {"code": 108, "indent": 0, "parameters": ["提前回家"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameSystem.hour() <= 16"]}, {"code": 355, "indent": 1, "parameters": ["$gameMap.event(6).start()"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["特殊分支剧情"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameParty.hasItem($dataItems[15]) && !$gameSwitches.value(500)"]}, {"code": 355, "indent": 1, "parameters": ["$gameMap.event(7).start()"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["$gameMap.event(9).start()"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 2, "y": 8}, {"id": 4, "name": "第一次出门", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 2, "characterIndex": 1}, "list": [{"code": 111, "indent": 0, "parameters": [1, 60, 0, 2, 0]}, {"code": 231, "indent": 1, "parameters": [1, "entrance_R_D_DO", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 60, 0, 1, 0]}, {"code": 231, "indent": 2, "parameters": [1, "entrance_C_D_DO", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 231, "indent": 2, "parameters": [1, "entrance_S_D_DO", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["「よし、準備完了だ！」"]}, {"code": 231, "indent": 0, "parameters": [9, "mio_tachie_smile2", 1, 0, 572, 950, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": [">显现动作 : 图片[9] : 移动显现 : 时间[60] : 方向角度[180] : 移动距离[180]"]}, {"code": 230, "indent": 0, "parameters": [35]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_18"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >「ごめんね、お兄ちゃん……\\w[60]"]}, {"code": 401, "indent": 0, "parameters": ["また危険な場所に行かせてしまって——」"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["「またそんなこと言って、心配しなくても大丈夫だよ。」"]}, {"code": 231, "indent": 0, "parameters": [9, "mio_tachie_naku", 1, 0, 572, 950, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_19"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >「だって——」"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["「もう何度も探索しているし、"]}, {"code": 401, "indent": 0, "parameters": ["この数年であの迷宮もかなり安定しているから」"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["「じゃあ、お家のことは任せたよ！」"]}, {"code": 231, "indent": 0, "parameters": [9, "mio_tachie_shinnpai", 1, 0, 572, 950, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_20"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >「うん…気をつけてね……」"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["「行ってきます」"]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.startFadeOut(180);"]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["そう、俺の妹には奇妙な病がある。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["外に出ることができず、日光を浴びることもできない。"]}, {"code": 401, "indent": 0, "parameters": ["そして定期的に特別な錬金薬を服用しなければ、"]}, {"code": 401, "indent": 0, "parameters": ["体の状態を安定させることができないのだ。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["その薬を作るための材料は、"]}, {"code": 401, "indent": 0, "parameters": ["市場では簡単に手に入るものではない。"]}, {"code": 401, "indent": 0, "parameters": ["今、素材を集めることができる唯一の場所、それは——"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["だから俺は兄として、何度でもあの場所に潜り、"]}, {"code": 401, "indent": 0, "parameters": ["妹を治すための新たな方法を探し続けなければならないのだ。"]}, {"code": 242, "indent": 0, "parameters": [2]}, {"code": 122, "indent": 0, "parameters": [13, 13, 0, 0, 1]}, {"code": 201, "indent": 0, "parameters": [0, 36, 6, 6, 0, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 0}, {"id": 5, "name": "第一次深渊死亡回归", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 2, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["报错防范"]}, {"code": 117, "indent": 0, "parameters": [21]}, {"code": 235, "indent": 0, "parameters": [99]}, {"code": 355, "indent": 0, "parameters": ["var actor = $gameParty.leader();"]}, {"code": 655, "indent": 0, "parameters": ["var states = actor.states();"]}, {"code": 655, "indent": 0, "parameters": ["for (var i = 0; i < states.length; i++) {"]}, {"code": 655, "indent": 0, "parameters": ["    actor.removeState(states[i].id);"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 655, "indent": 0, "parameters": ["actor.setHp(1);"]}, {"code": 241, "indent": 0, "parameters": [{"name": "日常_放課後", "volume": 60, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 0, "parameters": [1, "entrance_S_E_DO_RL", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 356, "indent": 0, "parameters": ["DS_方向設定 90"]}, {"code": 225, "indent": 0, "parameters": [6, 8, 30, false]}, {"code": 224, "indent": 0, "parameters": [[0, 0, 0, 185], 30, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "ドシャァ！地面に倒れる、落ちる", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 355, "indent": 0, "parameters": ["$gameSelfVariables.setValue([$gameMap.mapId(), 13, 'days'], 0);"]}, {"code": 655, "indent": 0, "parameters": ["$gameSwitches.setValue(490, false)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["「ダ、ダメだ、\\w[30]もう死んじゃう…」"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["最後の瞬間に逃げ出せてよかった…"]}, {"code": 401, "indent": 0, "parameters": ["やっぱり、今の力ではそこに挑戦するのは無理か…"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["もっと修練して、"]}, {"code": 401, "indent": 0, "parameters": ["準備をしっかりしてから考えるべきだな。"]}, {"code": 231, "indent": 0, "parameters": [9, "mio_tachie_smile", 0, 0, 200, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": [">显现动作 : 图片[9] : 移动显现 : 时间[60] : 方向角度[180] : 移动距离[180]"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_14"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >「お帰りなさい、\\w[50]お兄…\\.…\\^"]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.changePictureName(9, \"mio_tachie_ase\");"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_15"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 356, "indent": 0, "parameters": [">持续动作 : 图片[9] : 上下震动 : 持续时间[40] : 周期[20] : 震动幅度[3]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >「お兄ちゃん！？」"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_09"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >「お兄ちゃん大丈夫！？\\w[60]"]}, {"code": 401, "indent": 0, "parameters": ["全身に傷だらけみたい……！」"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["「ああ、ちょっと無理をした結果、"]}, {"code": 401, "indent": 0, "parameters": ["失敗してしまった…あはは…」"]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.changePictureName(9, \"mio_tachie_shy1\");"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_10"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >「もう、お兄ちゃんったら、"]}, {"code": 401, "indent": 0, "parameters": ["もっと自分のことを大切にしてよね!」"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_11"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >「怪我したら、早く包帯しなきゃ！」"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 235, "indent": 0, "parameters": [9]}, {"code": 250, "indent": 0, "parameters": [{"name": "Heal2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["let base = 15 + Math.randomInt(11);"]}, {"code": 655, "indent": 0, "parameters": ["let heal = $gameVariables.value(15) * base + 15;"]}, {"code": 655, "indent": 0, "parameters": ["$gameActors.actor(1).gainHp(heal);"]}, {"code": 231, "indent": 0, "parameters": [9, "mio_tachie_nocoat_nununu", 0, 0, 200, 1600, 100, 100, 255, 0]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 111, "indent": 0, "parameters": [1, 15, 0, 4, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["妹に熟練した手つきで丁寧に包帯を巻かれたら、"]}, {"code": 401, "indent": 1, "parameters": ["体力が大きく回復した感じがする。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 15, 0, 2, 1]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["妹に少し慣れた手つきで包帯を巻かれたら、"]}, {"code": 401, "indent": 2, "parameters": ["体力がかなり回復した感じがする。"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["妹に不器用に全身を包帯で巻かれたけど、"]}, {"code": 401, "indent": 2, "parameters": ["体力が少し回復した感じがする。"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["var picture = $gameScreen.picture(9);"]}, {"code": 655, "indent": 0, "parameters": ["var data = {\t\t\t\t\t\t\t\t\"type\":\"smoothMove\","]}, {"code": 655, "indent": 0, "parameters": ["\"valueX\":0,"]}, {"code": 655, "indent": 0, "parameters": ["\"valueY\":-1450,"]}, {"code": 655, "indent": 0, "parameters": ["\"time\":60,"]}, {"code": 655, "indent": 0, "parameters": ["\"cur_time\":0,\"cur_speedX\":0,\"cur_speedY\":0,}"]}, {"code": 655, "indent": 0, "parameters": ["if (picture) { picture._drill_PSh_commandChangeTank.push(data);"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_12"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >「次はお兄ちゃんゆっくり休んでね！"]}, {"code": 401, "indent": 0, "parameters": ["夕食はあたしが作るから！」"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["「あ、はい！」"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["それにしても、今俺は全身包帯でぐるぐる巻きにされていて、"]}, {"code": 401, "indent": 0, "parameters": ["全く動けないんだが……"]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.changePictureName(9, \"mio_tachie_nocoat_smile\");"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_13"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >「うん、お兄ちゃんはいい子ね。」"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["気のせいか、妹のやる気がすごく高い気がするな…"]}, {"code": 122, "indent": 0, "parameters": [14, 14, 0, 0, 15]}, {"code": 117, "indent": 0, "parameters": [3]}, {"code": 121, "indent": 0, "parameters": [5, 5, 1]}, {"code": 122, "indent": 0, "parameters": [13, 13, 0, 0, 22]}, {"code": 201, "indent": 0, "parameters": [0, 11, 5, 6, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 4, "y": 0}, {"id": 6, "name": "白天提前回家", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["早归触发游戏机事件"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameParty.hasItem($dataItems[15]) && !$gameSwitches.value(500)"]}, {"code": 355, "indent": 1, "parameters": ["$gameMap.event(7).start()"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 231, "indent": 0, "parameters": [9, "mio_tachie_OAO", 0, 0, 200, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": [">显现动作 : 图片[9] : 移动显现 : 时间[60] : 方向角度[180] : 移动距离[200]"]}, {"code": 230, "indent": 0, "parameters": [70]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_24"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >「お兄ちゃん？今日は早く帰ってきたね？」"]}, {"code": 355, "indent": 0, "parameters": ["this.count = Math.randomInt(4);"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["「うん、今日はちょっと疲れたから、"]}, {"code": 401, "indent": 0, "parameters": ["無理して探索には行かないでおこう…」"]}, {"code": 111, "indent": 0, "parameters": [12, "this.count === 3"]}, {"code": 231, "indent": 1, "parameters": [9, "mio_tachie_=ω=", 0, 0, 200, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE genkan_event_sis_28"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nl< >「これってあれでしょ……五月病ってもの？」"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["五月……？今は暑い夏なのに？"]}, {"code": 231, "indent": 1, "parameters": [9, "mio_tachie_IAI", 0, 0, 200, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE genkan_event_sis_29"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nl< >「あたしもよくそんな感じになるんだよね…"]}, {"code": 401, "indent": 1, "parameters": ["お昼まで寝てたつもりが、起きたらお兄ちゃんが"]}, {"code": 401, "indent": 1, "parameters": ["もう夕飯を作ってたりして…」"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["……ただ単に寝坊が好きなだけじゃないか？"]}, {"code": 119, "indent": 1, "parameters": ["3"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "this.count === 2"]}, {"code": 231, "indent": 2, "parameters": [9, "mio_tachie_happy", 0, 0, 200, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["SV_PLAY_VOICE genkan_event_sis_27"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["\\nl< >「たまにはこういうのもいいよね、"]}, {"code": 401, "indent": 2, "parameters": ["一緒に遊ぼう、お兄ちゃん！」"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [12, "this.count === 1"]}, {"code": 231, "indent": 3, "parameters": [9, "mio_tachie_shinnpai", 0, 0, 200, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 3, "parameters": ["SV_PLAY_VOICE genkan_event_sis_26"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nl< >「休息も大事だから、次はしっかり休んでね。」"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 231, "indent": 3, "parameters": [9, "mio_tachie_smile", 0, 0, 200, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 3, "parameters": ["SV_PLAY_VOICE genkan_event_sis_25"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nl< >「そうなんだ、"]}, {"code": 401, "indent": 3, "parameters": ["じゃああたしがリラックスさせてあげるね、"]}, {"code": 401, "indent": 3, "parameters": ["お兄ちゃん！」"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["そうだな、まだ時間があるし、今日はゆっくり休もう。"]}, {"code": 242, "indent": 0, "parameters": [2]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "明るくて可愛いチェレスタのジングル", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$gameSystem.hour() >= 17"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["$gameSystem.add_hour(1)"]}, {"code": 108, "indent": 2, "parameters": ["计算体力恢复"]}, {"code": 311, "indent": 2, "parameters": [0, 1, 0, 0, 12, false]}, {"code": 117, "indent": 2, "parameters": [8]}, {"code": 230, "indent": 2, "parameters": [30]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 111, "indent": 0, "parameters": [12, "this.count === 3"]}, {"code": 118, "indent": 1, "parameters": ["3"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["でもそうだな、まだ時間があるし、今日はゆっくり休もう。"]}, {"code": 231, "indent": 1, "parameters": [9, "mio_tachie_happy", 0, 0, 200, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE genkan_event_sis_30"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nl< >「じゃあ、お兄ちゃんも一緒に昼寝してみようよ…"]}, {"code": 401, "indent": 1, "parameters": ["すごく気持ちいいよ？」"]}, {"code": 242, "indent": 1, "parameters": [2]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "明るくて可愛いチェレスタのジングル", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 112, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [12, "$gameSystem.hour() >= 17"]}, {"code": 113, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 355, "indent": 3, "parameters": ["$gameSystem.add_hour(1)"]}, {"code": 108, "indent": 3, "parameters": ["计算体力恢复"]}, {"code": 311, "indent": 3, "parameters": [0, 1, 0, 0, 12, false]}, {"code": 117, "indent": 3, "parameters": [8]}, {"code": 230, "indent": 3, "parameters": [30]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 413, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["結局、妹と一緒に夕方近くまで寝てしまった。"]}, {"code": 119, "indent": 1, "parameters": ["结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "this.count === 2"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["妹と一緒に一日中ゲームをした。"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [12, "this.count === 1"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["妹と一緒にのんびりと昼間を過ごした。"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["妹が張り切って長い間マッサージしてくれて、"]}, {"code": 401, "indent": 3, "parameters": ["気持ち良すぎてそのまま寝ちゃった…"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [13, 13, 0, 0, 36]}, {"code": 201, "indent": 0, "parameters": [0, 4, 8, 6, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 3, "y": 8}, {"id": 7, "name": "游戏机事件", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 2, "characterIndex": 1}, "list": [{"code": 111, "indent": 0, "parameters": [12, "$gameSystem.hour() <= 16"]}, {"code": 231, "indent": 1, "parameters": [9, "mio_tachie_OAO", 0, 0, 200, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 1, "parameters": [">显现动作 : 图片[9] : 移动显现 : 时间[60] : 方向角度[180] : 移动距离[200]"]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE genkan_event_sis_24"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nl< >「お兄ちゃん？今日は早く帰ってきたね？」"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 231, "indent": 1, "parameters": [9, "mio_tachie_smile", 1, 0, 572, 950, 100, 100, 255, 0]}, {"code": 356, "indent": 1, "parameters": [">显现动作 : 图片[9] : 移动显现 : 时间[60] : 方向角度[180] : 移动距离[180]"]}, {"code": 230, "indent": 1, "parameters": [35]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE genkan_event_sis_01"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nl< >「お帰りなさい、お兄ちゃん。」"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["「ふんふん、妹よ、"]}, {"code": 401, "indent": 0, "parameters": ["今日兄ちゃんが何を持って帰ったと思う——！」"]}, {"code": 231, "indent": 0, "parameters": [9, "mio_tachie_OAO", 1, 0, 572, 950, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_16"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >「大きな包みだね——\\w[90]\\^"]}, {"code": 231, "indent": 0, "parameters": [9, "mio_tachie_=ω=", 1, 0, 572, 950, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_17"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >「もしかして美味しいもの……！？」"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["うーん……うちの妹は美味しいもの以外には"]}, {"code": 401, "indent": 0, "parameters": ["あんまり興味ないんだよな…"]}, {"code": 242, "indent": 0, "parameters": [2]}, {"code": 122, "indent": 0, "parameters": [13, 13, 0, 0, 34]}, {"code": 201, "indent": 0, "parameters": [0, 4, 6, 8, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 2, "y": 0}, null, {"id": 9, "name": "常规回家后事件", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 1, "characterIndex": 3}, "list": [{"code": 111, "indent": 0, "parameters": [8, 197]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["胸に抱いた温かい感触が、"]}, {"code": 401, "indent": 1, "parameters": ["夢にまで見た薬を手に入れたことを常に思い出させてくれる。"]}, {"code": 401, "indent": 1, "parameters": ["けれども、あまりにも現実離れした体験のせいで、"]}, {"code": 401, "indent": 1, "parameters": ["まだ気持ちの整理がつかない。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["家の入口とつながっていた星のタロットカード——"]}, {"code": 401, "indent": 1, "parameters": ["それは父が遺した物の一つだ。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["生前、父は妹を元に戻す方法を研究していたが、"]}, {"code": 401, "indent": 1, "parameters": ["どうしてこんなにも簡単に反応が起きたのだろうか。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["……"]}, {"code": 401, "indent": 1, "parameters": ["……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["何を考えても結論は出ない。"]}, {"code": 401, "indent": 1, "parameters": ["こんなふうにぐるぐると思い悩んでも意味はない。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["結果として、紅髄液を手に入れたのだから、"]}, {"code": 401, "indent": 1, "parameters": ["妹に試してみる機会を探そう。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["色はイチゴジュースみたいだし、もしかしたら、"]}, {"code": 401, "indent": 1, "parameters": ["妹の好きなイチゴミルクと同じ味かもしれないね……。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 231, "indent": 0, "parameters": [9, "mio_tachie_smile", 1, 0, 572, 950, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": [">显现动作 : 图片[9] : 移动显现 : 时间[60] : 方向角度[180] : 移动距离[180]"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_01"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >「お帰りなさい、お兄ちゃん。」"]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.changePictureName(9, \"mio_tachie_IAI\");"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.erasePicture(10);"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.erasePicture(11);"]}, {"code": 356, "indent": 0, "parameters": [">持续动作 : 图片[9] : 上下震动 : 持续时间[50] : 周期[25] : 震动幅度[3]"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_02"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >「お腹がもう空いた…」"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["「わかったわかった、すぐに準備するよ。」"]}, {"code": 119, "indent": 0, "parameters": ["1"]}, {"code": 108, "indent": 0, "parameters": ["修正选项组图标"]}, {"code": 355, "indent": 0, "parameters": ["$gameSystem._drill_DCB_curStyle = 25"]}, {"code": 355, "indent": 0, "parameters": ["let style = [];"]}, {"code": 655, "indent": 0, "parameters": ["style.push(\"eventButtons_sis_crafting\");"]}, {"code": 655, "indent": 0, "parameters": ["$gameSwitches.value(480) ? style.push(\"eventButtons_ofuro1\") : null;"]}, {"code": 655, "indent": 0, "parameters": ["style.push(\"eventButtons_toilet\");"]}, {"code": 655, "indent": 0, "parameters": ["//style.push(\"eventButtons_sex1\");"]}, {"code": 655, "indent": 0, "parameters": ["DrillUp.g_DCB_data[24].btn_src = style;"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["妹の頭を撫でて慰めた。\\w[30]"]}, {"code": 401, "indent": 0, "parameters": ["さて、次に何をすべきだろうか——"]}, {"code": 102, "indent": 0, "parameters": [["食事の準備", "<<!s[480]>>\\c[15]お風呂に入る", "\\c[15]トイレに行く"], -1, -1, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "食事の準備"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["そうだな、もう遅いし、夕食の準備をしないと。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["「今夜は何を食べたい？」"]}, {"code": 355, "indent": 1, "parameters": ["$gameScreen.changePictureName(9, \"mio_tachie_happy\");"]}, {"code": 355, "indent": 1, "parameters": ["$gameScreen.picture(9).drill_PCE_playSustainingShakeRotate( 120,60,1);"]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE genkan_event_sis_03"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nl< >「肉料理でいい！」"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["うん、予想通りの答えだ。"]}, {"code": 401, "indent": 1, "parameters": ["やっぱり、食事のバランスの重要性をもっと強調すべきかな。"]}, {"code": 122, "indent": 1, "parameters": [13, 13, 0, 0, 1]}, {"code": 201, "indent": 1, "parameters": [0, 11, 5, 5, 0, 2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "<<!s[480]>>\\c[15]お風呂に入る"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["汗かいたし、先にお風呂に入ってしまおうかな。"]}, {"code": 355, "indent": 1, "parameters": ["$gameScreen.changePictureName(9, \"mio_tachie_=ω=\");"]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE genkan_event_sis_04"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nl< >「お兄ちゃん先にお風呂に入るの？"]}, {"code": 401, "indent": 1, "parameters": ["それじゃいってらっしゃい～」"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "\\c[15]トイレに行く"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["ちょっとトイレに行ってくるか…"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["「ごめん、もう少し待っててね、"]}, {"code": 401, "indent": 1, "parameters": ["後でご飯を準備するから。」"]}, {"code": 355, "indent": 1, "parameters": ["$gameScreen.changePictureName(9, \"mio_tachie_OAO\");"]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE genkan_event_sis_05"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nl< >「わかった。」"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 122, "indent": 1, "parameters": [13, 13, 0, 0, 3]}, {"code": 201, "indent": 1, "parameters": [0, 20, 3, 6, 0, 2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 2, "y": 7}, {"id": 10, "name": "深渊死亡回归", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["报错防范"]}, {"code": 117, "indent": 0, "parameters": [21]}, {"code": 235, "indent": 0, "parameters": [99]}, {"code": 355, "indent": 0, "parameters": ["var actor = $gameParty.leader();"]}, {"code": 655, "indent": 0, "parameters": ["var states = actor.states();"]}, {"code": 655, "indent": 0, "parameters": ["for (var i = 0; i < states.length; i++) {"]}, {"code": 655, "indent": 0, "parameters": ["    actor.removeState(states[i].id);"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 655, "indent": 0, "parameters": ["actor.setHp(1);"]}, {"code": 241, "indent": 0, "parameters": [{"name": "日常_放課後", "volume": 60, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 0, "parameters": [1, "entrance_S_E_DO_RL", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 356, "indent": 0, "parameters": ["DS_方向設定 90"]}, {"code": 225, "indent": 0, "parameters": [6, 8, 30, false]}, {"code": 224, "indent": 0, "parameters": [[0, 0, 0, 185], 30, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "ドシャァ！地面に倒れる、落ちる", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 355, "indent": 0, "parameters": ["$gameSelfVariables.setValue([$gameMap.mapId(), 13, 'days'], 0);"]}, {"code": 655, "indent": 0, "parameters": ["$gameSwitches.setValue(490, false)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["「ダ、ダメだ、\\w[30]もう死んじゃう…」"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["最後の瞬間に逃げ出せてよかった…"]}, {"code": 401, "indent": 0, "parameters": ["やっぱり、今の力ではそこに挑戦するのは無理か…"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["もっと修練して、"]}, {"code": 401, "indent": 0, "parameters": ["準備をしっかりしてから考えるべきだな。"]}, {"code": 231, "indent": 0, "parameters": [9, "mio_tachie_smile", 0, 0, 200, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": [">显现动作 : 图片[9] : 移动显现 : 时间[60] : 方向角度[180] : 移动距离[180]"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_14"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >「お帰りなさい、\\w[50]お兄…\\.…\\^"]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.changePictureName(9, \"mio_tachie_ase\");"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_15"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 356, "indent": 0, "parameters": [">持续动作 : 图片[9] : 上下震动 : 持续时间[40] : 周期[20] : 震动幅度[3]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >「お兄ちゃん！？」"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_09"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >「お兄ちゃん大丈夫！？\\w[60]"]}, {"code": 401, "indent": 0, "parameters": ["全身に傷だらけみたい……！」"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["「ああ、ちょっと無理をした結果、"]}, {"code": 401, "indent": 0, "parameters": ["失敗してしまった…あはは…」"]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.changePictureName(9, \"mio_tachie_shy1\");"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_21"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >「お兄ちゃん、また無理してるんだね！」"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_22"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >「早く傷口を止血しなきゃ！」"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "Heal2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["let base = 15 + Math.randomInt(11);"]}, {"code": 655, "indent": 0, "parameters": ["let heal = $gameVariables.value(15) * base + 15;"]}, {"code": 655, "indent": 0, "parameters": ["$gameActors.actor(1).gainHp(heal);"]}, {"code": 231, "indent": 0, "parameters": [9, "mio_tachie_nocoat_bimyou2", 0, 0, 200, 1600, 100, 100, 255, 0]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 111, "indent": 0, "parameters": [1, 15, 0, 4, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["妹に熟練した手つきで丁寧に包帯を巻かれたら、"]}, {"code": 401, "indent": 1, "parameters": ["体力が大きく回復した感じがする。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 15, 0, 2, 1]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["妹に少し慣れた手つきで包帯を巻かれたら、"]}, {"code": 401, "indent": 2, "parameters": ["体力がかなり回復した感じがする。"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["妹に不器用に全身を包帯で巻かれたけど、"]}, {"code": 401, "indent": 2, "parameters": ["体力が少し回復した感じがする。"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["var picture = $gameScreen.picture(9);"]}, {"code": 655, "indent": 0, "parameters": ["var data = {\t\t\t\t\t\t\t\t\"type\":\"smoothMove\","]}, {"code": 655, "indent": 0, "parameters": ["\"valueX\":0,"]}, {"code": 655, "indent": 0, "parameters": ["\"valueY\":-1450,"]}, {"code": 655, "indent": 0, "parameters": ["\"time\":60,"]}, {"code": 655, "indent": 0, "parameters": ["\"cur_time\":0,\"cur_speedX\":0,\"cur_speedY\":0,}"]}, {"code": 655, "indent": 0, "parameters": ["if (picture) { picture._drill_PSh_commandChangeTank.push(data);"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 230, "indent": 0, "parameters": [45]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_23"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >「次はあたしがしっかりお兄ちゃん"]}, {"code": 401, "indent": 0, "parameters": ["をお世話するからね！」"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["気のせいかな？妹がすごく嬉しそうに見えるんだけど…"]}, {"code": 122, "indent": 0, "parameters": [14, 14, 0, 0, 10]}, {"code": 117, "indent": 0, "parameters": [3]}, {"code": 122, "indent": 0, "parameters": [13, 13, 0, 0, 18]}, {"code": 201, "indent": 0, "parameters": [0, 11, 5, 6, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 4, "y": 8}, {"id": 11, "name": "白天提前回家", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["this.count = 0"]}, {"code": 118, "indent": 0, "parameters": ["配音"]}, {"code": 241, "indent": 0, "parameters": [{"name": "日常_放課後", "volume": 60, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["$gameSystem.set_hour(10)"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameSystem.hour() <= 16"]}, {"code": 111, "indent": 1, "parameters": [1, 60, 0, 2, 0]}, {"code": 231, "indent": 2, "parameters": [1, "entrance_R_D_DO", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [1, 60, 0, 1, 0]}, {"code": 231, "indent": 3, "parameters": [1, "entrance_C_D_DO", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 231, "indent": 3, "parameters": [1, "entrance_S_D_DO", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 231, "indent": 1, "parameters": [1, "entrance_S_E_DO_RL", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 231, "indent": 0, "parameters": [9, "mio_tachie_OAO", 0, 0, 200, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": [">显现动作 : 图片[9] : 移动显现 : 时间[60] : 方向角度[180] : 移动距离[200]"]}, {"code": 230, "indent": 0, "parameters": [70]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >「哥哥？今天回来得很早诶？」"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["「嗯、今天感觉有点累，所以不勉强去探索了…」"]}, {"code": 111, "indent": 0, "parameters": [12, "this.count === 3"]}, {"code": 231, "indent": 1, "parameters": [9, "mio_tachie_=ω=", 0, 0, 200, 150, 100, 100, 255, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nl< >「这是那个什么吧……五月病来着？」"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["五月……？现在明明是炎热的夏天诶？"]}, {"code": 231, "indent": 1, "parameters": [9, "mio_tachie_IAI", 0, 0, 200, 150, 100, 100, 255, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nl< >「我也经常会有这种感觉呢…"]}, {"code": 401, "indent": 1, "parameters": ["明明只想睡到中午，结果醒来时哥哥已经连晚饭都做好了…」"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["……那只是单纯地喜欢睡懒觉吧？"]}, {"code": 119, "indent": 1, "parameters": ["3"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "this.count === 2"]}, {"code": 231, "indent": 2, "parameters": [9, "mio_tachie_happy", 0, 0, 200, 150, 100, 100, 255, 0]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["\\nl< >「偶尔这样也很不错呢，一起来玩吧哥哥！」"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [12, "this.count === 1"]}, {"code": 231, "indent": 3, "parameters": [9, "mio_tachie_shinnpai", 0, 0, 200, 150, 100, 100, 255, 0]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nl< >「劳逸结合很重要呢，哥哥接下来就好好休息下吧。」"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 231, "indent": 3, "parameters": [9, "mio_tachie_smile", 0, 0, 200, 150, 100, 100, 255, 0]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nl< >「是这样啊、那我来帮哥哥放松一下吧！」"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["是啊、时间还有余裕、今天就好好休息放松下吧。"]}, {"code": 242, "indent": 0, "parameters": [2]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "明るくて可愛いチェレスタのジングル", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$gameSystem.hour() >= 17"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["$gameSystem.add_hour(1)"]}, {"code": 108, "indent": 2, "parameters": ["计算体力恢复"]}, {"code": 311, "indent": 2, "parameters": [0, 1, 0, 0, 12, false]}, {"code": 117, "indent": 2, "parameters": [8]}, {"code": 230, "indent": 2, "parameters": [30]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 111, "indent": 0, "parameters": [12, "this.count === 3"]}, {"code": 118, "indent": 1, "parameters": ["3"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["不过也是呢、时间还有余裕、今天就好好休息吧。"]}, {"code": 231, "indent": 1, "parameters": [9, "mio_tachie_happy", 0, 0, 200, 150, 100, 100, 255, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nl< >「那哥哥也来一起试试午睡吧…很舒服哦？」"]}, {"code": 242, "indent": 1, "parameters": [2]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "明るくて可愛いチェレスタのジングル", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 112, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [12, "$gameSystem.hour() >= 17"]}, {"code": 113, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 355, "indent": 3, "parameters": ["$gameSystem.add_hour(1)"]}, {"code": 108, "indent": 3, "parameters": ["计算体力恢复"]}, {"code": 311, "indent": 3, "parameters": [0, 1, 0, 0, 12, false]}, {"code": 117, "indent": 3, "parameters": [8]}, {"code": 230, "indent": 3, "parameters": [30]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 413, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["结果和妹妹一起睡到了临近晚上才醒来。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "this.count === 2"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["和妹妹一起玩了一整天的游戏。"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [12, "this.count === 1"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["和妹妹一起悠闲地度过了白天。"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["妹妹干劲十足地替我按摩了很久、"]}, {"code": 401, "indent": 3, "parameters": ["因为太舒服就这么睡到了下午…"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["this.count++"]}, {"code": 119, "indent": 0, "parameters": ["配音"]}, {"code": 122, "indent": 0, "parameters": [13, 13, 0, 0, 1]}, {"code": 201, "indent": 0, "parameters": [0, 4, 8, 6, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 8, "y": 5}, {"id": 12, "name": "后续事件检测", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 0, "characterIndex": 1}, "list": [{"code": 230, "indent": 0, "parameters": [60]}, {"code": 111, "indent": 0, "parameters": [0, 493, 1]}, {"code": 355, "indent": 1, "parameters": ["for (var i = 23; i <= 27; i++) {"]}, {"code": 655, "indent": 1, "parameters": ["    var item = $dataItems[i];"]}, {"code": 655, "indent": 1, "parameters": ["    if ($gameParty.numItems(item) >= 1) {"]}, {"code": 655, "indent": 1, "parameters": ["        $gameSelfSwitches.setValue([4, 44, 'A'], true);"]}, {"code": 655, "indent": 1, "parameters": ["        break;"]}, {"code": 655, "indent": 1, "parameters": ["    }"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [4]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 115, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 8, "y": 1}, null, null, {"id": 15, "name": "EV015", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["$gameMap.event(3).start()"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 8, "y": 8}, null]}