{"autoplayBgm": false, "autoplayBgs": false, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 9, "note": "", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 1, "width": 9, "data": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "场景初始化", "note": "<resetSelfSwitch>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 0, "characterIndex": 3}, "list": [{"code": 108, "indent": 0, "parameters": ["加载多语言地图对话模板"]}, {"code": 355, "indent": 0, "parameters": ["const mapId = $gameMap.mapId();"]}, {"code": 655, "indent": 0, "parameters": ["const key = 'MapEventDialogue' + mapId; "]}, {"code": 655, "indent": 0, "parameters": ["if (!window[key]) { "]}, {"code": 655, "indent": 0, "parameters": ["chahuiUtil.loadMapEventDialogue();"]}, {"code": 655, "indent": 0, "parameters": ["this.wait(20);"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["补充监听器"]}, {"code": 355, "indent": 0, "parameters": ["QJ.MPMZ.tl._imoutoUtilCheckInitialization()"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameStrings.value(20).trim() !== \"\""]}, {"code": 355, "indent": 1, "parameters": ["let code = $gameStrings.value(20);"]}, {"code": 655, "indent": 1, "parameters": ["eval(code);"]}, {"code": 655, "indent": 1, "parameters": ["$gameStrings.setValue(20, \"\");"]}, {"code": 119, "indent": 1, "parameters": ["end"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 13, 0, 0, 3]}, {"code": 355, "indent": 1, "parameters": ["let ID = $gameVariables.value(13);"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.event(ID).start()"]}, {"code": 122, "indent": 1, "parameters": [13, 13, 0, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameMap.event(22).start()"]}, {"code": 122, "indent": 1, "parameters": [13, 13, 0, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["end"]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 8}, null, {"id": 3, "name": "哥哥独自在厕所", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["$gameSelfVariables.setValue([$gameMap.mapId(), 12, 'paper'], 3);"]}, {"code": 355, "indent": 0, "parameters": ["var level = $gameSelfVariables.value([$gameMap.mapId(), 14, 'level']);"]}, {"code": 655, "indent": 0, "parameters": ["var base = Math.floor(level / 3) * 3;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSelfVariables.setValue([$gameMap.mapId(), 14, 'level'], base);"]}, {"code": 355, "indent": 0, "parameters": ["var level = $gameSelfVariables.value([$gameMap.mapId(), 15, 'level']);"]}, {"code": 655, "indent": 0, "parameters": ["var base = Math.floor(level / 3) * 3;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSelfVariables.setValue([$gameMap.mapId(), 15, 'level'], base);"]}, {"code": 122, "indent": 0, "parameters": [45, 45, 0, 0, 4]}, {"code": 122, "indent": 0, "parameters": [209, 209, 0, 0, 3]}, {"code": 355, "indent": 0, "parameters": ["var IMG = 'toilet_S_N_L(have paper)';"]}, {"code": 655, "indent": 0, "parameters": ["var path = \"toilet_nozoku\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(1, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 250, "indent": 0, "parameters": [{"name": "トイレを流す音", "volume": 70, "pitch": 100, "pan": 0}]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 122, "indent": 0, "parameters": [14, 14, 0, 0, 10]}, {"code": 117, "indent": 0, "parameters": [3]}, {"code": 108, "indent": 0, "parameters": ["激活监听器"]}, {"code": 355, "indent": 0, "parameters": ["let id = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.event(id).steupCEQJ(2)"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(0);"]}, {"code": 355, "indent": 0, "parameters": ["let id = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.event(id).steupCEQJ(3)"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 0, "characterIndex": 1}, "list": [{"code": 355, "indent": 0, "parameters": ["var currentHour = $gameSystem.hour();"]}, {"code": 655, "indent": 0, "parameters": ["var currentMinute = $gameSystem.minute();"]}, {"code": 655, "indent": 0, "parameters": ["var newMinute = currentMinute + 19;"]}, {"code": 655, "indent": 0, "parameters": ["var newHour = currentHour;"]}, {"code": 655, "indent": 0, "parameters": ["if (newMinute >= 60) {"]}, {"code": 655, "indent": 0, "parameters": ["    newMinute -= 60;"]}, {"code": 655, "indent": 0, "parameters": ["    newHour += 1;"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 655, "indent": 0, "parameters": ["this.Hour = newHour;"]}, {"code": 655, "indent": 0, "parameters": ["this.Minute = newMinute;"]}, {"code": 108, "indent": 0, "parameters": ["监听有没有超时"]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["var fixedHour = this.Hour; "]}, {"code": 655, "indent": 1, "parameters": ["var fixedMinute = this.Minute;"]}, {"code": 655, "indent": 1, "parameters": ["var currentHour = $gameSystem.hour();  "]}, {"code": 655, "indent": 1, "parameters": ["var currentMinute = $gameSystem.minute();"]}, {"code": 655, "indent": 1, "parameters": ["var currentTimeInMinutes = currentHour * 60 + currentMinute;  "]}, {"code": 655, "indent": 1, "parameters": ["var fixedTimeInMinutes = fixedHour * 60 + fixedMinute;"]}, {"code": 655, "indent": 1, "parameters": ["if (currentTimeInMinutes > fixedTimeInMinutes) {"]}, {"code": 655, "indent": 1, "parameters": ["   $gameMap.event(9).steupCEQJ(1);"]}, {"code": 655, "indent": 1, "parameters": ["   this._index = this._list.length;"]}, {"code": 655, "indent": 1, "parameters": ["} "]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 1, "characterIndex": 3}, "list": [{"code": 118, "indent": 0, "parameters": ["选项"]}, {"code": 108, "indent": 0, "parameters": ["修正选项组图标"]}, {"code": 355, "indent": 0, "parameters": ["$gameSystem._drill_DCB_curStyle = 25"]}, {"code": 355, "indent": 0, "parameters": ["let style = [\"eventButtons_touch2\",\"eventButtons_unknown\",\"eventButtons_toilet\",\"eventButtons_returns\"];"]}, {"code": 655, "indent": 0, "parameters": ["DrillUp.g_DCB_data[24].btn_src = style;"]}, {"code": 108, "indent": 0, "parameters": ["选项赋予名称"]}, {"code": 355, "indent": 0, "parameters": ["const idx   = 2;"]}, {"code": 655, "indent": 0, "parameters": ["const eid   = String(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["const mapId = $gameMap.mapId();"]}, {"code": 655, "indent": 0, "parameters": ["const key   = `MapEventDialogue${mapId}`;"]}, {"code": 655, "indent": 0, "parameters": ["const dialogueTable = window[key] || {};"]}, {"code": 655, "indent": 0, "parameters": ["const textArray     = dialogueTable[eid]?.[String(idx)];"]}, {"code": 655, "indent": 0, "parameters": ["const option1 = textArray[0]; const option2 = textArray[1];"]}, {"code": 655, "indent": 0, "parameters": ["const option3 = textArray[2]; const option4 = textArray[3];"]}, {"code": 655, "indent": 0, "parameters": [" $gameStrings.setValue(6,option1);"]}, {"code": 655, "indent": 0, "parameters": [" $gameStrings.setValue(7,option2);"]}, {"code": 655, "indent": 0, "parameters": [" $gameStrings.setValue(8,option3);"]}, {"code": 655, "indent": 0, "parameters": [" $gameStrings.setValue(9,option4);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(1);"]}, {"code": 102, "indent": 0, "parameters": [["\\str[6]", "\\c[15]\\str[7]", "\\c[15]\\str[8]", "\\c[15]\\str[9]"], -1, -1, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\str[6]"]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(3);"]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 111, "indent": 1, "parameters": [12, "Math.random() > 0.5"]}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(4);"]}, {"code": 111, "indent": 2, "parameters": [12, "Math.random() > 0.7 && $gameSelfVariables.value([$gameMap.mapId(), 12, 'paper']) > 0"]}, {"code": 355, "indent": 3, "parameters": ["$gameSelfVariables.setValue([$gameMap.mapId(), 12, 'paper'], 0);"]}, {"code": 355, "indent": 3, "parameters": ["var IMG = 'toilet_S_N_L(no paper)';"]}, {"code": 655, "indent": 3, "parameters": ["var path = \"toilet_nozoku\";"]}, {"code": 655, "indent": 3, "parameters": ["$gameScreen.showPictureFromPath(3, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 128, "indent": 3, "parameters": [27, 0, 0, 1, false]}, {"code": 355, "indent": 3, "parameters": ["this.showMapEventDialogue(5);"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 118, "indent": 3, "parameters": ["junk"]}, {"code": 126, "indent": 3, "parameters": [3, 0, 0, 1]}, {"code": 355, "indent": 3, "parameters": ["this.showMapEventDialogue(6);"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(7);"]}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(8);"]}, {"code": 122, "indent": 2, "parameters": [14, 14, 0, 0, 5]}, {"code": 117, "indent": 2, "parameters": [3]}, {"code": 119, "indent": 2, "parameters": ["选项"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [0, 494, 0]}, {"code": 119, "indent": 2, "parameters": ["junk"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "Math.random() > 0.5"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [0, 22, 1]}, {"code": 111, "indent": 3, "parameters": [1, 208, 0, 1, 1]}, {"code": 250, "indent": 4, "parameters": [{"name": "カサカサ(虫、素早く移動、動作)", "volume": 65, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 4, "parameters": ["this.showMapEventDialogue(9);"]}, {"code": 355, "indent": 4, "parameters": ["var IMG = 'G';"]}, {"code": 655, "indent": 4, "parameters": ["var path = \"toilet_nozoku\";"]}, {"code": 655, "indent": 4, "parameters": ["$gameScreen.showPictureFromPath(40, path, IMG, 0, 1000, 1280, 100, 100, 255, 0)"]}, {"code": 356, "indent": 4, "parameters": [">图片快捷操作 : 图片[40] : 修改单属性 : 旋转[20] : 时间[45]"]}, {"code": 230, "indent": 4, "parameters": [1]}, {"code": 250, "indent": 4, "parameters": [{"name": "カサカサ(虫、素早く移動、動作)", "volume": 65, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 4, "parameters": [">图片快捷操作 : 图片[40] : 增减速移动到 : 位置[1075,990] : 时间[45]"]}, {"code": 230, "indent": 4, "parameters": [60]}, {"code": 101, "indent": 4, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 4, "parameters": ["………………"]}, {"code": 401, "indent": 4, "parameters": ["………………"]}, {"code": 355, "indent": 4, "parameters": ["this.showMapEventDialogue(10);"]}, {"code": 355, "indent": 4, "parameters": ["this.showMapEventDialogue(11);"]}, {"code": 355, "indent": 4, "parameters": ["this.showMapEventDialogue(12);"]}, {"code": 122, "indent": 4, "parameters": [208, 208, 1, 0, 1]}, {"code": 250, "indent": 4, "parameters": [{"name": "剣・棒状の風切り音1 ヒュン！", "volume": 65, "pitch": 50, "pan": 0}]}, {"code": 230, "indent": 4, "parameters": [30]}, {"code": 108, "indent": 4, "parameters": ["判定是否打死蟑螂"]}, {"code": 111, "indent": 4, "parameters": [12, "Math.random() > 0.9"]}, {"code": 250, "indent": 5, "parameters": [{"name": "Collapse1", "volume": 65, "pitch": 100, "pan": 0}]}, {"code": 224, "indent": 5, "parameters": [[255, 255, 255, 136], 30, false]}, {"code": 230, "indent": 5, "parameters": [30]}, {"code": 250, "indent": 5, "parameters": [{"name": "<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 5, "parameters": [">图片滤镜 : 图片[40] : 着色滤镜 : 红色通道 : 255 : 30"]}, {"code": 356, "indent": 5, "parameters": [">图片快捷操作 : 图片[40] : 修改单属性 : 透明度[0] : 时间[60]"]}, {"code": 230, "indent": 5, "parameters": [60]}, {"code": 355, "indent": 5, "parameters": ["this.showMapEventDialogue(13);"]}, {"code": 355, "indent": 5, "parameters": ["this.showMapEventDialogue(14);"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 250, "indent": 5, "parameters": [{"name": "Evasion1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 5, "parameters": [20]}, {"code": 250, "indent": 5, "parameters": [{"name": "カサカサ(虫、素早く移動、動作)", "volume": 65, "pitch": 120, "pan": 0}]}, {"code": 356, "indent": 5, "parameters": [">图片快捷操作 : 图片[40] : 修改单属性 : 旋转[-15] : 时间[40]"]}, {"code": 356, "indent": 5, "parameters": [">图片快捷操作 : 图片[40] : 增减速移动到 : 位置[1045,810] : 时间[40]"]}, {"code": 230, "indent": 5, "parameters": [40]}, {"code": 250, "indent": 5, "parameters": [{"name": "カサカサ(虫、素早く移動、動作)", "volume": 65, "pitch": 120, "pan": 0}]}, {"code": 356, "indent": 5, "parameters": [">图片快捷操作 : 图片[40] : 修改单属性 : 旋转[45] : 时间[25]"]}, {"code": 356, "indent": 5, "parameters": [">图片快捷操作 : 图片[40] : 增减速移动到 : 位置[1200,770] : 时间[40]"]}, {"code": 356, "indent": 5, "parameters": [">图片快捷操作 : 图片[40] : 修改单属性 : 透明度[0] : 时间[30]"]}, {"code": 230, "indent": 5, "parameters": [30]}, {"code": 355, "indent": 5, "parameters": ["this.showMapEventDialogue(15);"]}, {"code": 355, "indent": 5, "parameters": ["this.showMapEventDialogue(16);"]}, {"code": 121, "indent": 5, "parameters": [22, 22, 0]}, {"code": 355, "indent": 5, "parameters": ["$gameNumberArray.value(43).push(2)"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 111, "indent": 4, "parameters": [12, "$gameVariables.value(208) > 2 && $gameSwitches.value(494)"]}, {"code": 355, "indent": 5, "parameters": ["this.showMapEventDialogue(17);"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 250, "indent": 4, "parameters": [{"name": "カサカサ(虫、素早く移動、動作)", "volume": 65, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 4, "parameters": ["this.showMapEventDialogue(18);"]}, {"code": 101, "indent": 4, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 4, "parameters": ["…………"]}, {"code": 401, "indent": 4, "parameters": ["…………"]}, {"code": 355, "indent": 4, "parameters": ["this.showMapEventDialogue(19);"]}, {"code": 122, "indent": 4, "parameters": [208, 208, 1, 0, 1]}, {"code": 355, "indent": 4, "parameters": ["$gameNumberArray.value(43).push(2)"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 242, "indent": 1, "parameters": [2]}, {"code": 122, "indent": 1, "parameters": [14, 14, 0, 0, 5]}, {"code": 117, "indent": 1, "parameters": [3]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 119, "indent": 1, "parameters": ["选项"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\c[15]\\str[7]"]}, {"code": 241, "indent": 1, "parameters": [{"name": "m-art_AmazedResult", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 1, "parameters": ["AudioManager.fadeInBgm(2)"]}, {"code": 355, "indent": 1, "parameters": ["$gameMap.event(14).start()"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [4, 1, 6, 57]}, {"code": 355, "indent": 2, "parameters": ["$gameMap.event(14).start()"]}, {"code": 115, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameMap.event(15).start()"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "\\c[15]\\str[8]"]}, {"code": 355, "indent": 1, "parameters": ["$gameMap.event(12).start()"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [3, "\\c[15]\\str[9]"]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(20);"]}, {"code": 122, "indent": 1, "parameters": [14, 14, 0, 0, 5]}, {"code": 117, "indent": 1, "parameters": [3]}, {"code": 122, "indent": 1, "parameters": [13, 13, 0, 0, 1]}, {"code": 201, "indent": 1, "parameters": [0, 11, 5, 5, 0, 2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}], "x": 2, "y": 8}, null, null, null, null, null, {"id": 9, "name": "强制离开厕所", "note": "<重置独立开关>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 0, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["消除其他正在执行的事件"]}, {"code": 355, "indent": 0, "parameters": ["chahuiUtil.abortEventById.call(this, -1)"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE toilet_event_sis_38"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(0);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(1);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(2);"]}, {"code": 122, "indent": 0, "parameters": [13, 13, 0, 0, 2]}, {"code": 201, "indent": 0, "parameters": [0, 25, 3, 7, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 8, "y": 8}, null, null, {"id": 12, "name": "撕纸玩", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 4, "pattern": 1, "characterIndex": 1}, "list": [{"code": 111, "indent": 0, "parameters": [12, "$gameSelfVariables.get(this, 'paper') <= 0"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Miss", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["「あ、ペーパーが全部なくなった——」"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["これでやることがなくなったな。"]}, {"code": 355, "indent": 1, "parameters": ["$gameMap.event(10).start()"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameSelfVariables.add(this, 'paper', -1)"]}, {"code": 111, "indent": 1, "parameters": [12, "$gameSelfVariables.get(this, 'paper') === 0"]}, {"code": 355, "indent": 2, "parameters": ["var IMG = 'toilet_S_N_L(no paper)';"]}, {"code": 655, "indent": 2, "parameters": ["var path = \"toilet_nozoku\";"]}, {"code": 655, "indent": 2, "parameters": ["$gameScreen.showPictureFromPath(3, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["var soundName = \"リップ-トイレットペーパー\" + Math.randomInt(3); "]}, {"code": 655, "indent": 1, "parameters": ["var volume = 100; "]}, {"code": 655, "indent": 1, "parameters": ["AudioManager.playSe({name: soundName, volume: volume, pitch: 100, pan: 0}); "]}, {"code": 241, "indent": 1, "parameters": [{"name": "m-art_AmazedResult", "volume": 60, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 1, "parameters": ["AudioManager.fadeInBgm(4)"]}, {"code": 230, "indent": 1, "parameters": [120]}, {"code": 111, "indent": 1, "parameters": [12, "Math.random() > 0.66"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["トイレットペーパーを引き剝がして、"]}, {"code": 401, "indent": 2, "parameters": ["何かの形を編もうと試みた、最後には失敗した。"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["「おかしいな……妹の髪を結ぶときは"]}, {"code": 401, "indent": 2, "parameters": ["こんなに難しくなかったのに……」"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["気分が無駄になった感じがする。"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [12, "Math.random() > 0.33"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["やっぱり紙飛行機でも折ろうかな。"]}, {"code": 401, "indent": 3, "parameters": ["トイレットペーパーを少しちぎった。"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["柔らかすぎて、何度か失敗したけど、"]}, {"code": 401, "indent": 3, "parameters": ["なんとか紙飛行機を作れた。"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["\\{「行け！」\\}"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["でも紙飛行機はすぐにまっすぐに落ちてしまった..."]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["やっぱり折り鶴でも折ろうかな…"]}, {"code": 401, "indent": 3, "parameters": ["トイレットペーパーを少しちぎった。"]}, {"code": 401, "indent": 3, "parameters": ["でもどうしても形が気に入らない…"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 122, "indent": 1, "parameters": [14, 14, 0, 0, 5]}, {"code": 117, "indent": 1, "parameters": [3]}, {"code": 242, "indent": 1, "parameters": [2]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 355, "indent": 1, "parameters": ["let id = 3;"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.event(id).steupCEQJ(3)"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 5}, null, {"id": 14, "name": "贤者模式的思考", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 4, "pattern": 1, "characterIndex": 1}, "list": [{"code": 111, "indent": 0, "parameters": [12, "$gameSelfVariables.get(this, 'level') >= 3"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["……ダメだ、妄想が続かない。"]}, {"code": 401, "indent": 1, "parameters": ["妄想に何か素材が足りない気がするな..."]}, {"code": 122, "indent": 1, "parameters": [14, 14, 0, 0, 5]}, {"code": 117, "indent": 1, "parameters": [3]}, {"code": 242, "indent": 1, "parameters": [2]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 355, "indent": 1, "parameters": ["$gameMap.event(10).start()"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["var labelName = $gameSelfVariables.get(this, 'level').toString(); "]}, {"code": 655, "indent": 0, "parameters": ["for (var i = 0; i < this._list.length; i++) {"]}, {"code": 655, "indent": 0, "parameters": ["    var command = this._list[i];"]}, {"code": 655, "indent": 0, "parameters": ["    if (command.code === 118 && command.parameters[0] === labelName) {"]}, {"code": 655, "indent": 0, "parameters": ["        this.jumpTo(i);"]}, {"code": 655, "indent": 0, "parameters": ["        break;"]}, {"code": 655, "indent": 0, "parameters": ["    }"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 118, "indent": 0, "parameters": ["0"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["宇宙も最初は虚無から生まれたけど、"]}, {"code": 401, "indent": 0, "parameters": ["宇宙が存在する目的は何だろう？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["人間の存在にはどんな意味があるんだろう？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["生命の意味って何だろう？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["…………"]}, {"code": 401, "indent": 0, "parameters": ["…………"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["なんだか思考が昇華された気がする。"]}, {"code": 119, "indent": 0, "parameters": ["事件结束"]}, {"code": 118, "indent": 0, "parameters": ["1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["宇宙よ、どうか私に指示をください。"]}, {"code": 401, "indent": 0, "parameters": ["生命、宇宙、そして全てのことの究極の答えは何でしょう？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["（目を閉じて、宇宙からの啓示を受け取る）"]}, {"code": 355, "indent": 0, "parameters": ["var textArray = [\"42\", \"E=mc²\", \"Don't trust the truth.\"];"]}, {"code": 655, "indent": 0, "parameters": ["var text = textArray[Math.floor(Math.random() * textArray.length)];"]}, {"code": 655, "indent": 0, "parameters": ["$gameStrings.setValue(6,text)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\>\\dac\\{\\c[2]\\str[6]\\w[60]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["頭の中にふと浮かんだ。"]}, {"code": 401, "indent": 0, "parameters": ["これって一体どういう意味なんだろう？"]}, {"code": 119, "indent": 0, "parameters": ["事件结束"]}, {"code": 118, "indent": 0, "parameters": ["2"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["人間の存在意義とは何だろう？"]}, {"code": 401, "indent": 0, "parameters": ["神は「妹あれ」と言われた。すると妹があった。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["妹いる、ゆえに我あり…"]}, {"code": 401, "indent": 0, "parameters": ["生命の究極の意味を悟り、感動で涙が止まらない。"]}, {"code": 119, "indent": 0, "parameters": ["事件结束"]}, {"code": 118, "indent": 0, "parameters": ["事件结束"]}, {"code": 355, "indent": 0, "parameters": ["$gameSelfVariables.add(this, 'level', 1)"]}, {"code": 122, "indent": 0, "parameters": [14, 14, 0, 0, 5]}, {"code": 117, "indent": 0, "parameters": [3]}, {"code": 108, "indent": 0, "parameters": ["修正选项组图标"]}, {"code": 355, "indent": 0, "parameters": ["$gameSystem._drill_DCB_curStyle = 25"]}, {"code": 355, "indent": 0, "parameters": ["let style = [\"eventButtons_talk\",\"eventButtons_returns\"];"]}, {"code": 655, "indent": 0, "parameters": ["DrillUp.g_DCB_data[24].btn_src = style;"]}, {"code": 102, "indent": 0, "parameters": [["妄想を続ける", "\\c[15]妄想をやめる"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "妄想を続ける"]}, {"code": 111, "indent": 1, "parameters": [12, "$gameSelfVariables.get(this, 'level') < 3"]}, {"code": 355, "indent": 2, "parameters": ["var labelName = $gameSelfVariables.get(this, 'level').toString(); "]}, {"code": 655, "indent": 2, "parameters": ["for (var i = 0; i < this._list.length; i++) {"]}, {"code": 655, "indent": 2, "parameters": ["    var command = this._list[i];"]}, {"code": 655, "indent": 2, "parameters": ["    if (command.code === 118 && command.parameters[0] === labelName) {"]}, {"code": 655, "indent": 2, "parameters": ["        this.jumpTo(i);"]}, {"code": 655, "indent": 2, "parameters": ["        break;"]}, {"code": 655, "indent": 2, "parameters": ["    }"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["……ダメだ、妄想が続かない。"]}, {"code": 401, "indent": 2, "parameters": ["妄想に何か素材が足りない気がするな..."]}, {"code": 122, "indent": 2, "parameters": [14, 14, 0, 0, 5]}, {"code": 117, "indent": 2, "parameters": [3]}, {"code": 242, "indent": 2, "parameters": [2]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 355, "indent": 2, "parameters": ["$gameMap.event(10).start()"]}, {"code": 115, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\c[15]妄想をやめる"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["そろそろ終わりにしよう。"]}, {"code": 401, "indent": 1, "parameters": ["今日もたくさんのことを学んだ……"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 242, "indent": 0, "parameters": [2]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 355, "indent": 0, "parameters": ["let id = 3;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.event(id).steupCEQJ(3)"]}, {"code": 115, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 1, "y": 4}, {"id": 15, "name": "普通的胡思乱想", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 4, "pattern": 1, "characterIndex": 1}, "list": [{"code": 111, "indent": 0, "parameters": [12, "$gameSelfVariables.get(this, 'level') >= 3"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["……ダメだ、妄想が続かない。"]}, {"code": 401, "indent": 1, "parameters": ["妄想に何か素材が足りない気がするな..."]}, {"code": 122, "indent": 1, "parameters": [14, 14, 0, 0, 5]}, {"code": 117, "indent": 1, "parameters": [3]}, {"code": 242, "indent": 1, "parameters": [2]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 355, "indent": 1, "parameters": ["$gameMap.event(10).start()"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["var level = $gameSelfVariables.get(this, 'level');"]}, {"code": 655, "indent": 0, "parameters": ["var base = Math.floor(level / 3) * 3;"]}, {"code": 655, "indent": 0, "parameters": ["var labelName = base.toString(); "]}, {"code": 655, "indent": 0, "parameters": ["for (var i = 0; i < this._list.length; i++) {"]}, {"code": 655, "indent": 0, "parameters": ["    var command = this._list[i];"]}, {"code": 655, "indent": 0, "parameters": ["    if (command.code === 118 && command.parameters[0] === labelName) {"]}, {"code": 655, "indent": 0, "parameters": ["        this.jumpTo(i);"]}, {"code": 655, "indent": 0, "parameters": ["        break;"]}, {"code": 655, "indent": 0, "parameters": ["    }"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 118, "indent": 0, "parameters": ["0"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["もしかしたら、賢者さまもトイレにいる時は、"]}, {"code": 401, "indent": 0, "parameters": ["今と同じように、かつてない集中力で賢者の知恵を"]}, {"code": 401, "indent": 0, "parameters": ["発揮しているかもしれない。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\{つまり、多くの真理は実際には"]}, {"code": 401, "indent": 0, "parameters": ["トイレで考え出されているんだ！\\}"]}, {"code": 119, "indent": 0, "parameters": ["事件结束"]}, {"code": 118, "indent": 0, "parameters": ["1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["宇宙よ、どうか私に指示をください。"]}, {"code": 401, "indent": 0, "parameters": ["生命、宇宙、そして全てのことの究極の答えは何でしょう？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["（目を閉じて、宇宙からの啓示を受け取る）"]}, {"code": 355, "indent": 0, "parameters": ["var textArray = [\"42\", \"E=mc²\", \"Don't trust the truth.\"];"]}, {"code": 655, "indent": 0, "parameters": ["var text = textArray[Math.floor(Math.random() * textArray.length)];"]}, {"code": 655, "indent": 0, "parameters": ["$gameStrings.setValue(6,text)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\>\\dac\\{\\c[2]\\str[6]\\w[60]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["頭の中にふと浮かんだ。"]}, {"code": 401, "indent": 0, "parameters": ["これって一体どういう意味なんだろう？"]}, {"code": 119, "indent": 0, "parameters": ["事件结束"]}, {"code": 118, "indent": 0, "parameters": ["2"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["ふん、そんなの妄想さ。"]}, {"code": 401, "indent": 0, "parameters": ["賢者様がそんな粗野な存在なわけないじゃん…"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["考え事で暇を潰した。"]}, {"code": 119, "indent": 0, "parameters": ["事件结束"]}, {"code": 118, "indent": 0, "parameters": ["事件结束"]}, {"code": 355, "indent": 0, "parameters": ["$gameSelfVariables.add(this, 'level', 1)"]}, {"code": 122, "indent": 0, "parameters": [14, 14, 0, 0, 5]}, {"code": 117, "indent": 0, "parameters": [3]}, {"code": 108, "indent": 0, "parameters": ["修正选项组图标"]}, {"code": 355, "indent": 0, "parameters": ["$gameSystem._drill_DCB_curStyle = 25"]}, {"code": 355, "indent": 0, "parameters": ["let style = [\"eventButtons_talk\",\"eventButtons_returns\"];"]}, {"code": 655, "indent": 0, "parameters": ["DrillUp.g_DCB_data[24].btn_src = style;"]}, {"code": 102, "indent": 0, "parameters": [["妄想を続ける", "\\c[15]妄想をやめる"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "妄想を続ける"]}, {"code": 111, "indent": 1, "parameters": [12, "$gameSelfVariables.get(this, 'level') < 3"]}, {"code": 355, "indent": 2, "parameters": ["var labelName = $gameSelfVariables.get(this, 'level').toString(); "]}, {"code": 655, "indent": 2, "parameters": ["for (var i = 0; i < this._list.length; i++) {"]}, {"code": 655, "indent": 2, "parameters": ["    var command = this._list[i];"]}, {"code": 655, "indent": 2, "parameters": ["    if (command.code === 118 && command.parameters[0] === labelName) {"]}, {"code": 655, "indent": 2, "parameters": ["        this.jumpTo(i);"]}, {"code": 655, "indent": 2, "parameters": ["        break;"]}, {"code": 655, "indent": 2, "parameters": ["    }"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["……ダメだ、妄想が続かない。"]}, {"code": 401, "indent": 2, "parameters": ["妄想に何か素材が足りない気がするな..."]}, {"code": 122, "indent": 2, "parameters": [14, 14, 0, 0, 5]}, {"code": 117, "indent": 2, "parameters": [3]}, {"code": 242, "indent": 2, "parameters": [2]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 355, "indent": 2, "parameters": ["$gameMap.event(10).start()"]}, {"code": 115, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\c[15]妄想をやめる"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["そろそろ終わりにしよう。"]}, {"code": 401, "indent": 1, "parameters": ["今日もたくさんのことを学んだ……"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 242, "indent": 0, "parameters": [2]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 355, "indent": 0, "parameters": ["let id = 3;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.event(id).steupCEQJ(3)"]}, {"code": 115, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 1, "y": 5}, null, null, null, null, null, null, null, null, null, null]}