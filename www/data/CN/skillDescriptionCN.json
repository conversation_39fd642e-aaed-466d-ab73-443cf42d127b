{"skillToggle": ["已激活", "未激活"], "1": {"name": ["魔法收纳袋"], "subtitle": [""], "description": ["随身携带的魔法道具，允许装入数量远超常规量的东西", "但就算是魔法道具，收纳量也是有限度的……"], "ability": ["武器收纳量: ${QJ.MPMZ.tl.ex_playerCheckInventory('weapon')}", "装备收纳量: ${QJ.MPMZ.tl.ex_playerCheckInventory('gear')}"]}, "2": {"name": ["闪步"], "subtitle": [""], "description": ["踏击地面瞬间缩短一定的距离，可以顺势闪避掉一些攻击", "只是类似于预备起跑动作的技巧，但本人坚持要取一个帅气的技能名"], "ability": ["按空格键可释放"]}, "3": {"name": ["闪步太刀"], "subtitle": [""], "description": ["得到年轻的剑圣指教而掌握的拔刀术。借助闪步爆发出来的", "速度强化斩击的威力，有所准备的话甚至足以击飞敌人"], "ability": ["闪步期间点击鼠标左键发起斩击，根据移速强化伤害", "闪步期间长按鼠标右键进行蓄力，根据气势强化效果"], "mobileAbility": ["闪步期间点击技能按钮发起斩击，根据移速强化伤害", "闪步期间长按技能按钮进行蓄力，根据气势强化效果"]}, "4": {"name": ["旋风斩"], "subtitle": [""], "description": ["挥舞武器并不断旋转发起斩击，攻击速度会随持续时间而加快", "对不擅长剑术的人而言，这是非常容易掌握又最快发挥出效果的招式"], "ability": ["长按鼠标右键发动技能", "旋转中取消技能时可以投掷出武器"], "mobileAbility": ["长按技能按钮发动技能", "旋转中取消技能时可以投掷出武器"]}, "7": {"name": ["危险感知"], "subtitle": [""], "description": ["通称「心眼」的技能,但这是因多次出入生死才觉醒的直感", "换言之这更像是一种心理阴影的体现……"], "ability": ["鼠标放置于想要检查的对象身上可以查看详细的情报"], "mobileAbility": ["点击想要检查的对象可以显示详细的情报"]}, "10": {"name": ["气息消除"], "subtitle": ["true"], "description": ["擅长潜伏于阴影中的忍者的技艺。尽管只是临阵磨枪中习得的", "但因为自己似乎在潜伏和隐匿方面有着才能，很快就掌握到了要领"], "ability": ["激活技能时可以通过Shift键切换为潜行状态", "潜行状态下减少\\c[17]${1+$gameParty.leader().skillMasteryLevel(10)}\\c[108]格被怪物发现的警戒范围", "减少\\c[17]${15+15*$gameParty.leader().skillMasteryLevel(10)}%\\c[108]偷窥时妹妹警戒值的累积速率"]}, "11": {"name": ["背后暗杀"], "subtitle": ["true"], "description": ["擅长潜伏于阴影中的忍者的技艺、潜行状态下可以发动", "从背后接近无防备的敌人并发起攻击时，将造成极大伤害！"], "ability": ["技能熟练度: \\c[6]${$gameActors.actor(1).skillMasteryUses(11)}\\c[0]", "成功发动时伤害提升率: \\c[17]${Math.round(200 + (2333 - 200) * Math.pow($gameActors.actor(1).skillMasteryLevel(11) / 9, 1.8))}%\\c[0]"]}, "15": {"name": ["女装"], "subtitle": [""], "description": ["穿上了本不属于自己的服饰……然后似乎觉醒了新的可能性。", "没有任何战斗力加成，甚至可能有点心理负担……", "同伴的视线变得微妙，敌人的攻击也许会迟疑……？", "毕竟，在这个世界——可爱就是正义！"], "ability": ["根据装备中的女装数量提升罪恶值！"]}, "18": {"name": ["武器损毁"], "subtitle": [""], "description": ["所有武器都拥有耐久度的限制，也因为自己并不擅长施展精准的攻击动作", "所以会有最终令武器遭到破坏的风险。 但在武器被破坏的瞬间", "一定可以打出强力的会心一击!"], "ability": ["最后一击伤害提升率: \\c[6]${200+(100*$gameActors.actor(1).skillMasteryLevel(48))}%\\c[0]"]}, "26": {"name": ["剑术修行"], "subtitle": ["true"], "description": ["为了力量而向剑圣少女请教了怎么才能不断变强的方法", "但只得到「每天空挥1000次练习就对了！」的回复", "——这样真的能变强吗！？总而言之先照做吧……"], "ability": ["空挥练习次数: \\{\\c[6]${$gameActors.actor(1).skillMasteryUses(26)}\\c[0]", "近战武器的基础攻击提升\\c[6]${Math.ceil(1.8**$gameActors.actor(1).skillMasteryLevel(26))}%\\c[0]伤害"]}}