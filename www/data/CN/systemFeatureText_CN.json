{"gameTitle": ["和存在感薄弱少女一起的简单生活(Deluxe Edition)"], "quantityHeld": ["\\fs[18]•持有数量:\\c[17] ${} \\c[0]\\py[-8]"], "autoUpdate": ["检测到可同步的${}版本游戏数据，是否立即下载并更新？"], "fullScreen": ["全屏显示"], "masterVolume": ["主音量"], "voiceVolume": ["妹妹 音量"], "BgmVolume": ["背景音乐 音量"], "BgsVolume": ["背景音效 音量"], "SeVolume": ["效果音 音量"], "MaxPlayerMsgs": ["玩家讯息最大显示数量"], "GameFAQ": ["游戏常见问题解答"], "ShowWeaponDurability": ["武器耐久度常时显示"], "DisableAutoUpdate": ["禁止自动更新"], "LockedFPS": ["锁定60帧"], "Language": ["游戏语言"], "SwitchLanguage": ["检测到游戏语言设置发生了改变！", "将立即重启游戏以适配新的语言环境！"], "ErrorReport": ["游戏发生错误，是否前往逆流茶会的Discord服务器进行Bug反馈？", "(点击“确定”将打开Discord邀请链接)", "(请附带游戏报错画面的截屏)"], "UpdateComplete1": ["游戏自动更新完成！", "关闭该弹窗后将自动重启游戏以适配更新内容！", "重启后可以读取自动存档回到当前进度！"], "UpdateComplete2": ["游戏自动更新完成！", "游戏即将自动重启以适配更新内容！"], "RemindToUpdate": ["检测到游戏出现错误并已冻结！", "目前游戏已经有更高版本的补丁发布，", "请通过自动更新应用补丁修复问题！"], "bagFull": ["超出背包上限！！"], "noStatusEffects": ["什么状态也没有！"], "unimplementedDoor": ["这扇门无法从这一侧打开"], "zipUpdateComplete": ["补丁安装成功，", "即将重启游戏以应用更新！"], "zipUpdateFail": ["补丁解压安装失败！", "请重新尝试自动更新或", "手动解压补丁文件进行安装！"], "fileDownloadedSuccessfully": ["${}文件下载成功！"], "fileDownloadFailed": ["${}文件下载失败！正在重新尝试下载！"], "installPatch": ["检测到需要解压缩安装的补丁，正在解压缩安装——这个过程可能游戏画面会卡顿，请耐心等候！"], "totalFilesToUpdate": ["检测到${}个文件需要更新——开始下载流程！"]}