{"autoplayBgm": false, "autoplayBgs": false, "battleback1Name": "Abyss", "battleback2Name": "gray bar", "bgm": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 49, "note": "<深渊>\n<All Allow Region: 249,250>", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": true, "tilesetId": 18, "width": 37, "data": [2864, 2864, 2868, 2892, 2902, 2860, 2880, 2864, 2864, 2864, 2888, 2834, 2844, 2824, 2840, 1554, 2832, 2816, 2816, 2816, 2840, 3312, 3296, 3320, 2832, 2816, 2816, 2816, 2840, 1552, 2832, 2840, 2904, 2872, 2864, 2864, 2864, 2864, 2864, 2888, 2851, 2861, 2898, 2865, 2868, 2892, 2892, 2902, 2860, 2910, 2856, 2854, 1554, 2856, 2844, 2824, 2816, 2840, 3312, 3296, 3320, 2832, 2816, 2816, 2816, 2840, 1552, 2856, 2846, 2853, 2904, 2872, 2864, 2864, 2864, 2868, 2902, 2848, 2898, 2869, 2892, 2902, 2859, 2849, 2849, 1564, 1579, 1584, 1584, 1562, 2898, 2900, 2856, 2824, 2840, 3336, 3324, 3334, 2832, 2816, 2816, 2816, 2840, 1560, 1561, 1563, 2857, 2861, 2904, 2872, 2864, 2868, 2902, 2850, 2842, 2904, 2902, 2859, 1564, 1585, 1561, 1584, 1562, 1574, 1574, 1554, 1570, 2880, 2870, 2909, 2834, 2854, 4002, 1646, 1655, 2834, 2844, 2844, 2844, 2854, 1573, 1574, 1560, 1584, 1563, 2858, 2880, 2864, 2902, 2850, 2817, 2822, 1564, 1579, 1543, 1562, 1574, 1574, 1574, 1578, 2851, 2861, 1554, 1578, 2880, 2888, 2851, 2855, 1654, 3969, 3968, 3992, 2860, 2898, 2884, 2884, 2900, 2859, 2853, 1573, 1574, 1552, 2848, 2904, 2872, 2836, 2817, 2816, 2840, 1554, 4004, 1595, 4000, 2850, 2836, 2836, 2836, 2842, 1564, 1580, 2898, 2869, 2902, 2860, 1654, 3969, 3968, 3968, 3970, 1655, 2904, 2892, 2892, 2894, 2909, 2833, 2836, 2852, 1552, 2833, 2852, 2904, 2816, 2816, 2816, 2840, 1572, 3972, 3972, 1663, 2832, 2816, 2816, 2816, 2840, 1572, 1575, 2880, 2888, 2862, 1654, 3969, 3968, 3968, 3968, 3968, 1645, 2851, 2861, 3378, 3364, 3380, 2856, 2828, 2854, 1555, 2834, 2846, 2849, 2844, 2844, 2824, 2840, 1572, 3972, 1663, 2850, 2817, 2816, 2816, 2820, 2854, 1572, 2898, 2869, 2902, 1654, 3969, 3968, 3968, 3968, 3968, 3972, 1663, 2848, 3378, 3345, 3344, 3346, 3380, 2848, 1552, 2850, 2842, 2898, 2884, 2884, 2900, 2832, 2840, 1554, 1645, 2850, 2817, 2816, 2816, 2816, 2840, 1564, 1580, 2880, 2888, 2858, 1644, 3968, 3968, 3968, 3972, 3996, 1663, 2850, 2842, 3360, 3344, 3344, 3344, 3368, 2848, 1552, 2832, 2840, 2880, 2864, 2864, 2888, 2856, 2826, 2852, 4012, 2832, 2816, 2816, 2816, 2816, 2840, 1554, 1575, 2904, 2902, 2860, 3984, 3968, 3968, 3968, 1645, 2858, 2906, 2834, 2854, 3360, 3344, 3348, 3372, 3382, 2860, 1555, 2832, 2840, 2882, 2892, 2864, 2866, 2900, 2832, 2840, 1556, 2834, 2844, 2824, 2816, 2820, 2854, 1554, 2850, 2836, 2852, 1654, 3969, 3968, 3968, 3972, 1663, 2860, 2908, 2848, 3387, 3353, 3344, 3368, 2851, 2861, 1552, 2850, 2821, 2854, 2896, 2850, 2892, 2872, 2888, 2856, 2826, 2836, 2842, 1554, 2832, 2816, 2840, 1564, 1580, 2832, 2816, 2840, 3984, 3968, 3968, 3968, 1645, 2858, 2906, 2850, 2819, 2852, 3384, 3356, 3382, 2860, 2958, 1571, 2856, 2854, 2907, 2903, 2856, 2852, 2880, 2866, 2900, 2856, 2824, 2840, 1572, 2856, 2828, 2854, 1554, 1575, 2832, 2816, 2840, 1644, 3968, 3968, 3972, 1663, 2848, 2896, 2832, 2816, 2818, 2852, 3388, 2858, 3006, 2862, 1555, 6018, 6004, 6004, 6004, 6004, 2840, 2904, 2872, 2866, 2900, 2832, 2840, 1572, 1655, 2848, 1564, 1580, 2850, 2817, 2820, 2854, 1644, 3968, 3968, 3992, 2850, 2842, 2908, 2832, 2816, 2816, 2818, 2836, 2842, 1552, 6018, 6004, 5985, 5984, 5984, 5984, 5984, 2818, 2852, 2880, 2868, 2902, 2834, 1564, 1580, 1645, 2848, 1572, 1575, 2856, 2824, 2840, 1654, 3969, 3968, 3968, 1645, 2832, 2818, 2836, 2817, 2816, 2816, 2816, 2816, 2840, 1552, 6000, 5984, 5984, 5984, 5984, 5984, 5984, 2816, 2840, 2904, 2902, 2850, 2842, 1554, 3968, 3992, 2860, 1554, 2898, 2900, 2834, 2854, 1644, 3968, 3968, 3972, 1663, 2832, 2816, 2816, 2816, 2816, 2816, 2820, 2844, 2854, 1555, 6000, 5984, 5984, 5984, 5984, 5984, 5984, 2844, 2826, 2836, 2836, 2821, 1564, 1580, 3972, 1663, 1564, 1580, 2880, 2888, 2848, 1654, 3969, 3968, 3968, 1645, 2850, 2817, 2816, 2816, 2820, 2844, 2844, 2854, 1555, 6018, 6004, 5985, 5984, 5984, 5984, 5984, 5984, 5984, 2900, 2856, 2824, 2820, 2840, 1572, 3968, 1645, 2862, 1554, 1575, 2882, 2902, 2860, 3984, 3968, 3968, 3972, 1663, 2832, 2816, 2816, 2820, 2854, 1555, 6018, 6004, 6004, 5985, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 2866, 2900, 2856, 2828, 1564, 1580, 3972, 4006, 1564, 1580, 2898, 2890, 2858, 1654, 3969, 3968, 3972, 1663, 2850, 2817, 2820, 2844, 2854, 1555, 6018, 5985, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 2864, 2866, 2900, 2848, 1554, 3968, 3992, 1564, 1580, 1575, 2882, 2902, 2860, 1644, 3968, 3968, 1645, 2850, 2817, 2820, 2854, 1555, 6018, 6004, 5985, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 2864, 2864, 2888, 1564, 1580, 1647, 1663, 1554, 1575, 2898, 2890, 2858, 1654, 3969, 3968, 3972, 1663, 2832, 2820, 2854, 1555, 6018, 5985, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 2864, 2864, 2888, 1572, 1575, 2858, 1564, 1580, 2898, 2869, 2902, 2860, 3984, 3968, 3968, 3992, 2850, 2817, 2840, 1552, 2862, 6000, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 2864, 2864, 2888, 1572, 2850, 2842, 1554, 1575, 2880, 2888, 2858, 1654, 3969, 3968, 3968, 1645, 2832, 2820, 2854, 1560, 1563, 6024, 5992, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 2864, 2868, 2902, 1554, 2832, 2840, 1572, 2898, 2869, 2902, 2848, 1644, 3968, 3968, 3972, 1663, 2834, 2854, 2946, 2948, 1552, 2862, 6000, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 2864, 2888, 2858, 1572, 2832, 2840, 1554, 2880, 2888, 2850, 2842, 3984, 3968, 3968, 1645, 2850, 2842, 2946, 2913, 2936, 1560, 1563, 6024, 5992, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 2864, 2888, 2848, 1554, 2832, 2840, 1572, 2904, 2902, 2832, 2840, 1644, 3968, 3968, 3992, 2832, 2840, 2928, 2912, 2914, 2948, 1552, 2816, 6024, 5992, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 2864, 2888, 2848, 1556, 2856, 2841, 1572, 2850, 2836, 2817, 2840, 3984, 3968, 3968, 1645, 2832, 2840, 2952, 2920, 2912, 2936, 1571, 2816, 2853, 6024, 6012, 6012, 5992, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 2864, 2888, 2833, 2852, 1554, 2848, 1554, 2832, 2816, 2820, 2854, 1644, 3968, 3968, 1645, 2832, 2818, 2852, 2928, 2912, 2936, 1560, 1563, 2857, 2849, 2838, 2816, 6024, 6012, 6012, 6012, 5992, 5984, 5984, 5984, 5984, 5984, 2864, 2888, 2832, 2840, 1572, 2860, 1572, 2832, 2816, 2840, 1654, 3969, 3968, 3968, 3992, 2856, 2844, 2841, 2952, 2940, 2922, 2948, 1560, 1584, 1563, 2816, 2816, 2849, 2838, 2836, 2852, 6024, 6012, 5992, 5984, 5984, 5984, 2868, 2902, 2832, 2840, 1572, 1584, 1562, 2832, 2816, 2840, 3984, 3968, 3968, 3968, 3970, 1646, 1655, 2833, 2836, 2852, 2952, 2950, 1573, 1574, 1560, 1584, 1584, 1563, 2816, 2844, 2846, 2849, 2853, 6024, 6012, 6012, 6012, 2902, 2850, 2817, 2840, 1554, 1574, 1575, 2832, 2816, 2840, 1644, 3968, 3968, 3968, 3968, 3968, 3992, 2856, 2844, 2846, 2838, 2852, 2898, 2900, 1573, 1574, 1574, 1560, 1561, 1584, 1584, 1563, 2816, 2849, 2838, 2816, 2836, 2836, 2817, 2820, 2854, 1572, 2850, 2836, 2817, 2816, 2840, 1644, 3968, 3968, 3968, 3968, 3968, 3970, 3988, 1646, 1655, 2832, 2840, 2904, 2874, 2884, 2884, 2900, 1573, 1574, 1574, 3976, 1560, 1561, 1563, 2816, 2816, 2844, 2816, 2816, 2840, 1564, 1580, 2832, 2816, 2816, 2820, 2854, 3984, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3992, 2856, 2846, 2861, 2904, 2892, 2892, 2902, 2850, 2836, 2852, 1644, 3968, 3968, 1560, 1584, 1561, 1584, 2816, 2816, 2840, 1572, 1570, 2832, 2816, 2816, 2840, 4002, 3969, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3970, 1646, 3988, 1646, 1646, 3988, 1655, 2850, 2817, 2820, 2854, 1644, 3968, 3968, 3968, 3972, 1574, 1574, 2816, 2816, 2840, 1572, 1578, 2832, 2816, 2816, 2840, 1644, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 1645, 2832, 2816, 2840, 1654, 3969, 3968, 3968, 3968, 3992, 2859, 2849, 2844, 2824, 2840, 1572, 2859, 2845, 2824, 2820, 2854, 1644, 3968, 3968, 3968, 3968, 3972, 3996, 1647, 1647, 3976, 3968, 3968, 3968, 3968, 3972, 1647, 1663, 2832, 2820, 2854, 3984, 3968, 3968, 3968, 3972, 1663, 3243, 3233, 2900, 2832, 2840, 1554, 2898, 2900, 2856, 2841, 1654, 3969, 3968, 3968, 3968, 3972, 1663, 2850, 2836, 2852, 4008, 3996, 3996, 1647, 3996, 1663, 2850, 2836, 2817, 2840, 1654, 3969, 3968, 3968, 3968, 1645, 2859, 2849, 2849, 2888, 2832, 2840, 1554, 2880, 2866, 2900, 2848, 1644, 3968, 3968, 3968, 3972, 1663, 2850, 2817, 2816, 2818, 2836, 2836, 2836, 2836, 2836, 2837, 2845, 2844, 2844, 2854, 1644, 3968, 6018, 6004, 6004, 6004, 6004, 6004, 6004, 2888, 2832, 2840, 1572, 2880, 2864, 2888, 2860, 3984, 3968, 3968, 3972, 1663, 2851, 2845, 2844, 2844, 2824, 2816, 2820, 2844, 2844, 2844, 2841, 6018, 6004, 6004, 6004, 6004, 6004, 5985, 5984, 5984, 5984, 5984, 5984, 5984, 2902, 2832, 2840, 1572, 2904, 2872, 2888, 1654, 3969, 3968, 3968, 1645, 2851, 2855, 2946, 2932, 2948, 2834, 2844, 2841, 1553, 1553, 1558, 2860, 6000, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 2836, 2817, 2840, 1556, 2862, 2904, 2902, 3984, 3968, 3968, 3968, 3992, 2848, 2946, 2913, 2916, 2950, 2860, 1555, 2835, 2849, 2839, 2861, 6018, 5985, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 2816, 2816, 2818, 2852, 1554, 2850, 2852, 1644, 3968, 3968, 3972, 1663, 2848, 2952, 2940, 2950, 2862, 1555, 2850, 2842, 2910, 2860, 6018, 5985, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 2844, 2844, 2844, 2841, 1572, 2832, 2840, 3984, 3968, 3968, 1645, 2850, 2819, 2837, 2849, 2853, 1555, 2850, 2817, 2818, 2852, 6018, 5985, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 2884, 2884, 2900, 2848, 1572, 2832, 2840, 3984, 3968, 3968, 1645, 2834, 2844, 2841, 1555, 2860, 2906, 2856, 2824, 2820, 2854, 6000, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 2864, 2864, 2888, 2848, 1556, 2834, 2854, 3984, 3968, 3972, 1663, 2860, 1555, 2857, 2861, 2898, 2867, 2900, 2856, 2854, 6018, 5985, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 2864, 2864, 2888, 2835, 2849, 2855, 1554, 3984, 3968, 3992, 2862, 1555, 2862, 2898, 2884, 2865, 2864, 2870, 2909, 6018, 5985, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 2864, 2868, 2902, 2860, 2898, 2900, 1572, 3984, 3968, 3992, 1552, 2862, 2898, 2865, 2864, 2864, 2864, 2888, 2862, 6000, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 2892, 2902, 2862, 2898, 2865, 2888, 1572, 3984, 3968, 3992, 1552, 2898, 2865, 2864, 2864, 2864, 2868, 2902, 6018, 5985, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 2836, 2852, 2898, 2865, 2864, 2888, 1572, 3984, 3968, 3992, 1552, 2880, 2864, 2864, 2864, 2864, 2888, 2858, 6000, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 3156, 3190, 0, 0, 0, 0, 3168, 3156, 3180, 3190, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3192, 3160, 3152, 3152, 3152, 3152, 3176, 0, 0, 0, 0, 3186, 3157, 3190, 0, 0, 0, 0, 0, 0, 0, 0, 3963, 3953, 3953, 3953, 3965, 0, 0, 0, 3963, 3953, 3953, 3953, 3965, 0, 0, 0, 3192, 3160, 3152, 3152, 3152, 3190, 0, 0, 0, 3195, 3181, 3190, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3192, 3160, 3152, 3152, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3168, 3152, 3152, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4290, 4292, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3192, 3160, 3152, 0, 0, 3042, 3044, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4290, 4257, 4258, 4292, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3168, 3152, 0, 3042, 3009, 3032, 0, 0, 0, 0, 0, 3474, 3476, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4272, 4256, 4260, 4294, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3168, 3152, 0, 3024, 3008, 3032, 0, 0, 0, 3474, 3460, 3441, 3442, 3476, 0, 0, 0, 0, 4346, 0, 0, 4290, 4261, 4284, 4294, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3168, 3152, 3188, 3048, 3016, 3032, 0, 0, 0, 3480, 3468, 3468, 3448, 3442, 3476, 0, 0, 0, 4336, 0, 4290, 4257, 4280, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3168, 3152, 3176, 0, 3048, 3046, 0, 0, 0, 3042, 3028, 3044, 3456, 3440, 3464, 0, 0, 0, 4348, 0, 4272, 4260, 4294, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3192, 3160, 3154, 3188, 0, 0, 0, 0, 0, 3048, 3016, 3032, 3480, 3468, 3478, 0, 0, 0, 0, 0, 4272, 4280, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3192, 3152, 3154, 3188, 0, 0, 0, 0, 0, 3048, 3018, 3044, 0, 0, 0, 0, 4346, 0, 4290, 4261, 4294, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3180, 3160, 3176, 0, 0, 0, 0, 0, 0, 3048, 3046, 0, 0, 0, 0, 4336, 0, 4272, 4280, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3192, 3162, 3188, 0, 0, 0, 0, 0, 0, 0, 0, 3138, 3140, 0, 4348, 0, 4272, 4280, 0, 4346, 0, 0, 0, 3146, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3192, 3190, 0, 0, 0, 0, 0, 0, 0, 0, 3120, 3128, 0, 0, 0, 4272, 4280, 0, 4336, 0, 0, 0, 3121, 3124, 3125, 3149, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3138, 3109, 3142, 0, 0, 4290, 4261, 4294, 0, 4348, 0, 0, 0, 3144, 3132, 3142, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3188, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3120, 3128, 0, 0, 4290, 4261, 4294, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3176, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3120, 3128, 0, 0, 4272, 4280, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3154, 3188, 0, 0, 0, 0, 0, 0, 0, 0, 3138, 3109, 3142, 0, 4290, 4261, 4294, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3152, 3154, 3188, 0, 0, 0, 0, 0, 0, 0, 3144, 3142, 0, 0, 4272, 4280, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3152, 3152, 3176, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4290, 4261, 4294, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3152, 3152, 3176, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4272, 4280, 0, 4346, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3152, 3156, 3190, 0, 0, 0, 0, 0, 0, 0, 4346, 0, 4290, 4261, 4294, 0, 4336, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3152, 3176, 0, 0, 0, 0, 0, 0, 0, 0, 4336, 0, 4272, 4280, 0, 0, 4348, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3156, 3190, 0, 0, 0, 0, 0, 0, 0, 0, 4336, 0, 4272, 4280, 0, 4346, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3190, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4336, 0, 4272, 4280, 0, 4336, 0, 0, 3906, 3908, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4348, 0, 4272, 4280, 0, 4336, 0, 0, 3888, 3874, 3908, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4272, 4280, 0, 4348, 0, 0, 3912, 3884, 3910, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4272, 4280, 0, 0, 0, 0, 0, 3916, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3188, 0, 0, 0, 0, 0, 0, 0, 0, 4346, 0, 0, 4272, 4280, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3154, 3188, 0, 0, 0, 0, 0, 0, 0, 4336, 0, 4290, 4257, 4258, 4292, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3152, 3154, 3188, 0, 0, 0, 0, 0, 0, 4336, 0, 4272, 4256, 4260, 4286, 4293, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3152, 3152, 3176, 0, 0, 0, 0, 0, 0, 4348, 0, 4272, 4256, 4280, 0, 4273, 4292, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3152, 3152, 3176, 0, 0, 0, 0, 0, 0, 0, 0, 4272, 4256, 4258, 4276, 4257, 4258, 4276, 4292, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3152, 3152, 3176, 0, 0, 0, 0, 0, 0, 0, 4290, 4257, 4256, 4260, 4284, 4284, 4284, 4284, 4286, 4278, 4277, 4301, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3152, 3152, 3176, 0, 0, 0, 0, 4346, 0, 0, 4272, 4260, 4284, 4294, 0, 0, 0, 0, 0, 4296, 4294, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3180, 3160, 3176, 0, 0, 0, 0, 4336, 0, 0, 4272, 4280, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3168, 3176, 0, 0, 0, 0, 4336, 0, 4290, 4261, 4294, 0, 0, 4347, 4337, 4337, 4337, 4349, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3192, 3190, 0, 0, 0, 0, 4348, 0, 4272, 4280, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4272, 4280, 0, 4346, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3172, 3188, 0, 0, 0, 0, 0, 0, 4290, 4261, 4294, 0, 4336, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3152, 3154, 3188, 0, 0, 0, 0, 0, 4272, 4280, 0, 0, 4348, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3180, 3180, 3162, 3188, 0, 0, 0, 4290, 4257, 4280, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3412, 3428, 3192, 3190, 0, 0, 0, 4272, 4256, 4280, 0, 0, 0, 0, 0, 0, 3194, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3392, 3394, 3428, 0, 0, 0, 0, 4272, 4256, 4280, 0, 0, 0, 0, 0, 3186, 3155, 3188, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3392, 3396, 3430, 0, 0, 0, 0, 4272, 4256, 4280, 0, 0, 0, 3186, 3172, 3153, 3152, 3158, 3197, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3396, 3430, 0, 0, 0, 0, 0, 4272, 4256, 4280, 0, 0, 3186, 3153, 3152, 3156, 3180, 3190, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3430, 0, 0, 0, 0, 0, 0, 4272, 4256, 4280, 0, 0, 3192, 3160, 3156, 3190, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4272, 4256, 4280, 0, 0, 0, 3168, 3176, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 176, 0, 0, 0, 0, 0, 0, 0, 0, 0, 78, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 282, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 48, 49, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 48, 49, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 48, 49, 49, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 48, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 48, 49, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 48, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 256, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 272, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 257, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 273, 256, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 272, 256, 256, 257, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 274, 274, 273, 256, 256, 256, 256, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 274, 274, 272, 272, 256, 256, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 272, 274, 256, 256, 257, 256, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 272, 274, 273, 272, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 162, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 49, 49, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 280, 0, 0, 0, 0, 262, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 291, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 37, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 18, 0, 312, 313, 314, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 45, 0, 0, 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 18, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 38, 39, 0, 1, 0, 0, 3, 0, 0, 0, 0, 0, 0, 44, 44, 44, 24, 0, 0, 0, 0, 44, 44, 44, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 46, 47, 2, 0, 0, 0, 0, 0, 0, 0, 57, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 168, 0, 0, 0, 280, 0, 0, 0, 9, 0, 0, 0, 0, 0, 155, 0, 0, 67, 0, 0, 68, 67, 38, 39, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 796, 797, 0, 184, 0, 0, 0, 288, 0, 0, 0, 0, 0, 59, 0, 79, 67, 163, 1, 0, 0, 0, 0, 0, 0, 46, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 804, 805, 0, 0, 0, 0, 0, 296, 0, 0, 0, 0, 0, 0, 0, 38, 39, 38, 39, 0, 2, 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 48, 0, 3, 0, 312, 0, 0, 0, 0, 0, 0, 0, 46, 47, 46, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 35, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 59, 3, 0, 0, 37, 0, 38, 39, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 43, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 0, 0, 0, 45, 0, 46, 47, 0, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 48, 0, 0, 0, 0, 0, 12, 0, 0, 0, 0, 0, 49, 50, 0, 0, 0, 37, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 33, 34, 0, 0, 0, 0, 0, 0, 0, 8, 0, 12, 0, 0, 0, 0, 0, 0, 0, 51, 0, 0, 45, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 283, 320, 284, 284, 284, 284, 0, 0, 0, 0, 0, 0, 21, 0, 0, 0, 0, 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 283, 284, 286, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 59, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 291, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 35, 0, 0, 0, 37, 0, 0, 19, 0, 0, 0, 0, 0, 0, 0, 0, 0, 38, 39, 0, 0, 0, 0, 0, 321, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 43, 0, 66, 67, 45, 0, 59, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 46, 47, 0, 0, 0, 283, 320, 286, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 59, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 283, 284, 284, 286, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 66, 67, 0, 0, 0, 19, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 283, 286, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 59, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 283, 320, 286, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 66, 67, 3, 0, 0, 19, 0, 0, 0, 0, 0, 0, 3, 0, 0, 283, 286, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 0, 1, 0, 0, 0, 0, 0, 0, 0, 48, 291, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 299, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 321, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 38, 39, 0, 0, 38, 39, 0, 0, 0, 0, 0, 0, 0, 0, 0, 77, 307, 294, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12, 38, 39, 0, 46, 47, 0, 0, 46, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 315, 318, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 46, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 77, 307, 294, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 37, 0, 0, 0, 315, 326, 294, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 51, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 45, 0, 0, 0, 0, 315, 326, 337, 339, 294, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 77, 0, 315, 345, 347, 326, 337, 338, 339, 294, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 169, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 315, 345, 346, 347, 326, 336, 294, 0, 0, 0, 0, 0, 0, 0, 0, 0, 185, 0, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 27, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 315, 344, 326, 308, 336, 308, 336, 0, 0, 0, 8, 0, 0, 0, 38, 39, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 78, 0, 315, 316, 344, 316, 344, 0, 0, 0, 0, 0, 157, 0, 46, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 77, 78, 0, 0, 0, 0, 0, 0, 0, 0, 0, 165, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 77, 78, 0, 0, 0, 0, 0, 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 37, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 154, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 45, 0, 1, 0, 0, 283, 284, 284, 284, 320, 284, 284, 320, 0, 12, 0, 0, 154, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 283, 320, 284, 284, 320, 284, 286, 0, 0, 0, 0, 0, 0, 0, 9, 0, 0, 0, 162, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 291, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 48, 49, 49, 49, 283, 286, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 0, 37, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 48, 0, 0, 3, 283, 310, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 51, 45, 0, 0, 0, 0, 0, 0, 0, 0, 0, 48, 0, 0, 0, 283, 286, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 48, 49, 0, 0, 0, 0, 321, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 48, 49, 0, 0, 0, 0, 0, 283, 286, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 33, 34, 0, 0, 0, 49, 50, 0, 0, 0, 0, 48, 0, 0, 0, 0, 0, 0, 0, 262, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 0, 8, 0, 51, 0, 0, 48, 0, 0, 0, 0, 0, 0, 0, 321, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 281, 282, 0, 0, 0, 0, 0, 0, 0, 0, 0, 280, 281, 282, 0, 0, 0, 283, 286, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 289, 290, 3, 0, 0, 0, 0, 0, 0, 0, 0, 288, 289, 290, 280, 281, 282, 291, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 297, 298, 0, 0, 0, 0, 0, 0, 0, 0, 0, 296, 297, 298, 288, 289, 290, 291, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 0, 0, 0, 0, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 5, 5, 5, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 0, 1, 0, 5, 5, 5, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 1, 0, 0, 0, 0, 0, 0, 249, 249, 249, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 0, 0, 0, 5, 0, 0, 250, 250, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 0, 0, 0, 0, 0, 0, 250, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 35, 35, 35, 35, 35, 35, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 5, 0, 0, 0, 0, 0, 15, 249, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 35, 35, 35, 35, 35, 35, 35, 35, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 5, 5, 5, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 0, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 240, 240, 255, 255, 255, 255, 255, 0, 0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 0, 0, 240, 240, 240, 240, 240, 240, 240, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 0, 0, 0, 240, 240, 240, 240, 240, 240, 240, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 240, 240, 240, 240, 240, 240, 240, 5, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 240, 240, 240, 240, 240, 240, 240, 240, 5, 5, 0, 0, 0, 0, 15, 0, 0, 0, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 240, 240, 240, 240, 240, 240, 240, 240, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 240, 240, 240, 240, 240, 240, 240, 240, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 5, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 118, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 15, 15, 0, 0, 0, 0, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 0, 5, 5, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 0, 0, 0, 0, 1, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 5, 5, 5, 5, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255], "events": [null, {"id": 1, "name": "EV001", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 283, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 22, "y": 39}, {"id": 2, "name": "EV002", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 0, "characterIndex": 3}, "list": [{"code": 355, "indent": 0, "parameters": ["$gameMap.event(21).locate(28.8,8.3)"]}, {"code": 121, "indent": 0, "parameters": [5, 5, 1]}, {"code": 121, "indent": 0, "parameters": [3, 3, 0]}, {"code": 121, "indent": 0, "parameters": [14, 14, 1]}, {"code": 117, "indent": 0, "parameters": [79]}, {"code": 108, "indent": 0, "parameters": ["生成篝火"]}, {"code": 355, "indent": 0, "parameters": ["QJ.SE.spawnXy(1,7,26,8,false);"]}, {"code": 108, "indent": 0, "parameters": ["骑士的消失"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameParty.hasItem($dataItems[16])) {"]}, {"code": 655, "indent": 0, "parameters": ["let knight = $gameSelfVariables.value([$gameMap.mapId(), 23, 'E']);"]}, {"code": 655, "indent": 0, "parameters": ["knight += 1;"]}, {"code": 655, "indent": 0, "parameters": ["if ( $gameSystem.day() > knight ) {"]}, {"code": 655, "indent": 0, "parameters": ["  $gameSelfSwitches.setValue([$gameMap.mapId(), 23, 'F'], true);"]}, {"code": 655, "indent": 0, "parameters": ["  }"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 111, "indent": 0, "parameters": [12, "!$gameParty.hasItem($dataItems[15])"]}, {"code": 355, "indent": 1, "parameters": ["let event = $gameMap.event(23);"]}, {"code": 655, "indent": 1, "parameters": ["if (event && event._drill_EASe_controller !== undefined) {"]}, {"code": 655, "indent": 1, "parameters": ["event.drill_EASe_setSimpleStateNode( [\"记笔记\"] );"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "$gameScreen.zoomScale() == 1"]}, {"code": 356, "indent": 1, "parameters": ["dpZoom 2 1 player"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["particle set dust_walk walk:player:0 def below"]}, {"code": 356, "indent": 0, "parameters": ["particle set grass_walk walk:player:5 def below"]}, {"code": 356, "indent": 0, "parameters": [">独立开关 : 事件[21] : B : 关闭"]}, {"code": 230, "indent": 0, "parameters": [4]}, {"code": 356, "indent": 0, "parameters": [">允许操作玩家移动 : 开启"]}, {"code": 117, "indent": 0, "parameters": [6]}, {"code": 117, "indent": 0, "parameters": [100]}, {"code": 117, "indent": 0, "parameters": [99]}, {"code": 108, "indent": 0, "parameters": ["对象名标记"]}, {"code": 355, "indent": 0, "parameters": ["let id = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.event(id).steupCEQJ(3)"]}, {"code": 108, "indent": 0, "parameters": ["创造物生成"]}, {"code": 355, "indent": 0, "parameters": ["let id = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.event(id).steupCEQJ(2)"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["let id = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.event(id).steupCEQJ(4)"]}, {"code": 111, "indent": 0, "parameters": [12, "!Utils.isOptionValid(\"test\")"]}, {"code": 111, "indent": 1, "parameters": [0, 392, 1]}, {"code": 356, "indent": 2, "parameters": [">允许操作玩家移动 : 关闭"]}, {"code": 230, "indent": 2, "parameters": [120]}, {"code": 121, "indent": 2, "parameters": [392, 392, 0]}, {"code": 117, "indent": 2, "parameters": [83]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 123, 0]}, {"code": 111, "indent": 1, "parameters": [2, "C", 1]}, {"code": 123, "indent": 2, "parameters": ["C", 0]}, {"code": 356, "indent": 2, "parameters": [">对话框变形器 : 切换形状 : 变形样式[8]"]}, {"code": 356, "indent": 2, "parameters": [">对话框皮肤 : 只对话框 : 修改样式 : 样式[9]"]}, {"code": 355, "indent": 2, "parameters": ["let event = $gamePlayer;"]}, {"code": 655, "indent": 2, "parameters": ["let x = (event.screenX() - 72) * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 2, "parameters": ["let y = (event.screenY() - 32) * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 2, "parameters": ["$gameSystem._drill_DOp_curStyle['x_value'] = x ;"]}, {"code": 655, "indent": 2, "parameters": ["$gameSystem._drill_DOp_curStyle['y_value'] = y ;"]}, {"code": 655, "indent": 2, "parameters": ["$gameSystem._drill_DOp_curStyle['height_rowCount'] = 2;"]}, {"code": 655, "indent": 2, "parameters": ["$gameSystem._drill_DOp_curStyle['height_value'] = 52 * 2;"]}, {"code": 213, "indent": 2, "parameters": [-1, 7, true]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["（……怎么回事？阳光好强啊！？"]}, {"code": 401, "indent": 2, "parameters": ["话说晒到身上好痛！！）"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 214, "indent": 0, "parameters": []}, {"code": 115, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["雨天重置的创造物"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameSelfVariables.value([$gameMap.mapId(), 1, 'rainyCreation']) > 0"]}, {"code": 108, "indent": 1, "parameters": ["生成蔬菜"]}, {"code": 355, "indent": 1, "parameters": ["let type = \"vege\";"]}, {"code": 655, "indent": 1, "parameters": ["let times = 4;"]}, {"code": 655, "indent": 1, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.steupCEQJ(75,eid,{creationType:type,creationQuantity:times})"]}, {"code": 108, "indent": 1, "parameters": ["生成树"]}, {"code": 355, "indent": 1, "parameters": ["let type = \"tree\";"]}, {"code": 655, "indent": 1, "parameters": ["let times = 11;"]}, {"code": 655, "indent": 1, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.steupCEQJ(75,eid,{creationType:type,creationQuantity:times})"]}, {"code": 108, "indent": 1, "parameters": ["清除标记"]}, {"code": 355, "indent": 1, "parameters": ["$gameSelfVariables.setValue([$gameMap.mapId(), 1, 'rainyCreation'], 0);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["日常重置的创造物"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameSelfVariables.value([$gameMap.mapId(), 1, 'day']) < $gameSystem.day()"]}, {"code": 108, "indent": 1, "parameters": ["更新标记天数"]}, {"code": 355, "indent": 1, "parameters": ["let day = $gameSystem.day();"]}, {"code": 655, "indent": 1, "parameters": ["$gameSelfVariables.setValue([$gameMap.mapId(), 1, 'day'], day)"]}, {"code": 108, "indent": 1, "parameters": ["生成采集物"]}, {"code": 355, "indent": 1, "parameters": ["$gameSelfSwitches.setValue([$gameMap.mapId(), 27, 'A'], false);"]}, {"code": 108, "indent": 1, "parameters": ["垃圾桶"]}, {"code": 355, "indent": 1, "parameters": ["$gameMap.spawnEventQJ(1,102,9,33,true)"]}, {"code": 108, "indent": 1, "parameters": ["生成假人"]}, {"code": 355, "indent": 1, "parameters": ["QJ.SE.spawnXy(1,110,14,17,true);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [4]}, {"code": 111, "indent": 0, "parameters": [12, "$gameMap.drill_COET_getEventsByTag_direct(\"训练假人\").length > 0"]}, {"code": 355, "indent": 1, "parameters": ["   var event = $gameMap.drill_COET_getEventsByTag_direct(\"训练假人\")[0];"]}, {"code": 655, "indent": 1, "parameters": ["   switch ( $gameVariables.value(60) ) {"]}, {"code": 655, "indent": 1, "parameters": ["        case 0:  "]}, {"code": 655, "indent": 1, "parameters": ["            event.setDirection(2); "]}, {"code": 655, "indent": 1, "parameters": ["            break;"]}, {"code": 655, "indent": 1, "parameters": ["        case 1:  "]}, {"code": 655, "indent": 1, "parameters": ["            event.setDirection(6); "]}, {"code": 655, "indent": 1, "parameters": ["            break;"]}, {"code": 655, "indent": 1, "parameters": ["        case 2:  "]}, {"code": 655, "indent": 1, "parameters": ["            event.setDirection(4); "]}, {"code": 655, "indent": 1, "parameters": ["            break;"]}, {"code": 655, "indent": 1, "parameters": ["    }"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["骑士"]}, {"code": 115, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "!$gameSelfSwitches.value([$gameMap.mapId(), 23, 'F'])"]}, {"code": 355, "indent": 1, "parameters": ["let lang = ConfigManager.language; if (lang > 2) lang = 2;"]}, {"code": 655, "indent": 1, "parameters": ["let event = $gameMap.event(23); let imgName = \"object_name/Knight\" + lang;"]}, {"code": 655, "indent": 1, "parameters": ["let posX = event.screenShootXQJ() - 4; let posY = event.screenShootYQJ() - 54;"]}, {"code": 655, "indent": 1, "parameters": ["    QJ.MPMZ.Shoot({"]}, {"code": 655, "indent": 1, "parameters": ["        img:img<PERSON><PERSON>,groupName:['Knight'],"]}, {"code": 655, "indent": 1, "parameters": ["        position:[['S',posX],['S',posY]],opacity:1,"]}, {"code": 655, "indent": 1, "parameters": ["        initialRotation:['S',0],scale:0.5,"]}, {"code": 655, "indent": 1, "parameters": ["        imgRotation:['F'],z:\"W\",collisionBox:['C',420],"]}, {"code": 655, "indent": 1, "parameters": ["        moveType:['S',0],existData:[   ],"]}, {"code": 655, "indent": 1, "parameters": ["        moveF:[20,20,QJ.MPMZ.tl.ex_AnuszEffectActivation<PERSON><PERSON><PERSON>,[\"<PERSON>\"]],"]}, {"code": 655, "indent": 1, "parameters": ["    });"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [12, "$gameScreen.picture(1)"]}, {"code": 356, "indent": 1, "parameters": [">图片快捷操作 : 图片[1] : 修改单属性 : 透明度[0] : 时间[90]"]}, {"code": 356, "indent": 1, "parameters": [">图片快捷操作 : 图片[2] : 修改单属性 : 透明度[0] : 时间[90]"]}, {"code": 230, "indent": 1, "parameters": [90]}, {"code": 235, "indent": 1, "parameters": [1]}, {"code": 235, "indent": 1, "parameters": [2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 12, "y": 1}, {"id": 3, "name": "EV003", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<width: 5>"]}, {"code": 408, "indent": 0, "parameters": ["<height: 1>"]}, {"code": 408, "indent": 0, "parameters": ["<offsetX: 0>"]}, {"code": 408, "indent": 0, "parameters": ["<offsetY: 0>"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameSelfSwitches.value([$gameMap.mapId(), 22, 'B'])"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "Move", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 121, "indent": 0, "parameters": [40, 40, 0]}, {"code": 201, "indent": 0, "parameters": [0, 13, 10, 33, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 20, "y": 0}, {"id": 4, "name": "EV004", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<width: 5>"]}, {"code": 408, "indent": 0, "parameters": ["<height: 1>"]}, {"code": 408, "indent": 0, "parameters": ["<offsetX: 0>"]}, {"code": 408, "indent": 0, "parameters": ["<offsetY: 0>"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameSelfSwitches.value([$gameMap.mapId(), 22, 'B'])"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[8]"]}, {"code": 356, "indent": 0, "parameters": [">对话框皮肤 : 只对话框 : 修改样式 : 样式[9]"]}, {"code": 355, "indent": 0, "parameters": ["let event = $gamePlayer;"]}, {"code": 655, "indent": 0, "parameters": ["let x = event.screenX() * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 0, "parameters": ["let y = (event.screenY() - 100) * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['x_value'] = x ;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['y_value'] = y ;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['height_rowCount'] = 2;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['height_value'] = 64 * 2;"]}, {"code": 108, "indent": 0, "parameters": ["选项框适配"]}, {"code": 355, "indent": 0, "parameters": ["$gameSystem._drill_DCB_enable = true;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DCB_curStyle = 30;"]}, {"code": 655, "indent": 0, "parameters": ["DrillUp.g_DCB_data[29].x = $gamePlayer.screenX() + 64;"]}, {"code": 655, "indent": 0, "parameters": ["DrillUp.g_DCB_data[29].x *= $gameScreen.zoomScale();"]}, {"code": 655, "indent": 0, "parameters": ["DrillUp.g_DCB_data[29].y = $gamePlayer.screenY() - 24;"]}, {"code": 655, "indent": 0, "parameters": ["DrillUp.g_DCB_data[29].y *= $gameScreen.zoomScale();"]}, {"code": 108, "indent": 0, "parameters": ["高温天气分歧"]}, {"code": 111, "indent": 0, "parameters": [0, 123, 0]}, {"code": 355, "indent": 1, "parameters": ["let id = this._eventId;"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.event(id).steupCEQJ(2)"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "$gameSystem.hour() <= 12"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["现在还很早，"]}, {"code": 401, "indent": 1, "parameters": ["放弃探索就这么回家吗？"]}, {"code": 102, "indent": 1, "parameters": [["回家", "算了"], 1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "回家"]}, {"code": 121, "indent": 2, "parameters": [3, 3, 1]}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 117, "indent": 2, "parameters": [109]}, {"code": 117, "indent": 2, "parameters": [57]}, {"code": 122, "indent": 2, "parameters": [13, 13, 0, 0, 3]}, {"code": 201, "indent": 2, "parameters": [0, 24, 4, 6, 0, 2]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "算了"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["离太阳下山还有点时间，"]}, {"code": 401, "indent": 1, "parameters": ["放弃探索就这么回家吗？"]}, {"code": 102, "indent": 1, "parameters": [["回家", "算了"], 1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "回家"]}, {"code": 121, "indent": 2, "parameters": [3, 3, 1]}, {"code": 121, "indent": 2, "parameters": [16, 16, 1]}, {"code": 117, "indent": 2, "parameters": [109]}, {"code": 117, "indent": 2, "parameters": [57]}, {"code": 122, "indent": 2, "parameters": [13, 13, 0, 0, 3]}, {"code": 201, "indent": 2, "parameters": [0, 24, 4, 6, 0, 2]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "算了"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["今天天气非常地炎热，"]}, {"code": 401, "indent": 0, "parameters": ["还是早点回去吧……？"]}, {"code": 102, "indent": 0, "parameters": [["回家", "算了"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "回家"]}, {"code": 121, "indent": 1, "parameters": [3, 3, 1]}, {"code": 121, "indent": 1, "parameters": [16, 16, 1]}, {"code": 117, "indent": 1, "parameters": [109]}, {"code": 117, "indent": 1, "parameters": [57]}, {"code": 122, "indent": 1, "parameters": [13, 13, 0, 0, 15]}, {"code": 201, "indent": 1, "parameters": [0, 24, 4, 6, 0, 2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "算了"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 7, "y": 48}, {"id": 5, "name": "街灯", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!fsm_Flame04", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": false}], "x": 16, "y": 10}, {"id": 6, "name": "EV006", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 67, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 12, "y": 14}, {"id": 7, "name": "EV007", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 67, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 11, "y": 17}, {"id": 8, "name": "EV008", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 67, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 10, "y": 19}, {"id": 9, "name": "街灯", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!fsm_Flame04", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": false}], "x": 22, "y": 11}, {"id": 10, "name": "街灯", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!fsm_Flame04", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": false}], "x": 15, "y": 28}, {"id": 11, "name": "街灯", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!fsm_Flame04", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": false}], "x": 10, "y": 27}, {"id": 12, "name": "街灯", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!fsm_Flame04", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": false}], "x": 22, "y": 32}, {"id": 13, "name": "街灯", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!fsm_Flame04", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": false}], "x": 22, "y": 37}, {"id": 14, "name": "街灯", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!fsm_Flame04", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": false}], "x": 7, "y": 39}, {"id": 15, "name": "街灯", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!fsm_Flame04", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": false}], "x": 11, "y": 42}, {"id": 16, "name": "EV016", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 8, "pattern": 1, "characterIndex": 5}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": false}], "x": 36, "y": 35}, {"id": 17, "name": "EV017", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 1, "characterIndex": 5}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": false}], "x": 22, "y": 0}, {"id": 18, "name": "EV018", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 4, "pattern": 1, "characterIndex": 5}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": false}], "x": 8, "y": 48}, null, {"id": 20, "name": "废弃事件", "note": "<resetSelfSwitch>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!fsm_Flame04", "direction": 4, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>标签核心 : 添加标签 : 篝火"]}, {"code": 108, "indent": 0, "parameters": ["<width: 1.2>"]}, {"code": 408, "indent": 0, "parameters": ["<height: 1.2>"]}, {"code": 408, "indent": 0, "parameters": ["<offsetX: +0.1>"]}, {"code": 408, "indent": 0, "parameters": ["<offsetY: -0.1>"]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 111, "indent": 0, "parameters": [12, "hasRainParticle()"]}, {"code": 123, "indent": 1, "parameters": ["B", 0]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["var tag = \"tag:event\" + this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["var args = [\"group\",\"set\",\"smithFire_c-EID\",\"this\",\"smithFire_c\",tag];"]}, {"code": 655, "indent": 0, "parameters": ["this.pluginCommand(\"particle\", args)"]}, {"code": 355, "indent": 0, "parameters": ["var tag = \"tag:event\" + this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["var args = [\"update\",tag,\"alpha\",\"0.3\"];"]}, {"code": 655, "indent": 0, "parameters": ["this.pluginCommand(\"particle\", args)"]}, {"code": 355, "indent": 0, "parameters": ["var tag = \"tag:event\" + this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["var args = [\"update\",tag,\"pos\",\"0\",\"-24\"];"]}, {"code": 655, "indent": 0, "parameters": ["this.pluginCommand(\"particle\", args)"]}, {"code": 108, "indent": 0, "parameters": ["篝火音效音量随距离变化"]}, {"code": 355, "indent": 0, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["      QJ.MPMZ.Shoot({"]}, {"code": 655, "indent": 0, "parameters": ["        img:\"null1\","]}, {"code": 655, "indent": 0, "parameters": ["        groupName:['bonfire'],"]}, {"code": 655, "indent": 0, "parameters": ["        position:[['E',eid],['E',eid]],"]}, {"code": 655, "indent": 0, "parameters": ["        moveType:['D',true],"]}, {"code": 655, "indent": 0, "parameters": ["        existData:[],"]}, {"code": 655, "indent": 0, "parameters": ["        moveF:["]}, {"code": 655, "indent": 0, "parameters": ["           [0,1680,QJ.MPMZ.tl.bonfireSoundVolumeChange,[eid]]  "]}, {"code": 655, "indent": 0, "parameters": ["        ],"]}, {"code": 655, "indent": 0, "parameters": ["     });"]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!fsm_Flame04", "direction": 4, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字自动显现 : 触发范围 : 固定自动显现范围 : 横向图块[3] : 纵向图块[3]"]}, {"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字自动显现 : 开启"]}, {"code": 108, "indent": 0, "parameters": ["=>事件漂浮背景 : 设置背景 : 2"]}, {"code": 108, "indent": 0, "parameters": ["=>事件漂浮背景 : 背景偏移 : 0 : -64"]}, {"code": 108, "indent": 0, "parameters": ["适配对话框样式"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[8]"]}, {"code": 356, "indent": 0, "parameters": [">对话框皮肤 : 只对话框 : 修改样式 : 样式[9]"]}, {"code": 355, "indent": 0, "parameters": ["let event = $gamePlayer;"]}, {"code": 655, "indent": 0, "parameters": ["let x = event.screenX() * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 0, "parameters": ["let y = (event.screenY() - 86) * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['x_value'] = x ;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['y_value'] = y ;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['height_rowCount'] = 2;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['height_value'] = 55 * 2;"]}, {"code": 108, "indent": 0, "parameters": ["选项赋予名称"]}, {"code": 355, "indent": 0, "parameters": ["let event = $gameMap.event(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["let eid = String(event._sourceeventId);"]}, {"code": 655, "indent": 0, "parameters": ["let index = 2;"]}, {"code": 655, "indent": 0, "parameters": ["let option1 = window.prototypeEventTemplate[eid][String(index)][0];"]}, {"code": 655, "indent": 0, "parameters": ["let option2 = window.prototypeEventTemplate[eid][String(index)][1];"]}, {"code": 655, "indent": 0, "parameters": [" $gameStrings.setValue(6,option1);"]}, {"code": 655, "indent": 0, "parameters": [" $gameStrings.setValue(7,option2);"]}, {"code": 108, "indent": 0, "parameters": ["选项框修正"]}, {"code": 355, "indent": 0, "parameters": ["$gameSystem._drill_DCB_enable = true;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DCB_curStyle = 30;"]}, {"code": 655, "indent": 0, "parameters": ["DrillUp.g_DCB_data[29].x = $gamePlayer.screenX() + 72;"]}, {"code": 655, "indent": 0, "parameters": ["DrillUp.g_DCB_data[29].x *= $gameScreen.zoomScale();"]}, {"code": 655, "indent": 0, "parameters": ["DrillUp.g_DCB_data[29].y = $gamePlayer.screenY() - 42;"]}, {"code": 655, "indent": 0, "parameters": ["DrillUp.g_DCB_data[29].y *= $gameScreen.zoomScale();"]}, {"code": 355, "indent": 0, "parameters": ["let type = String(this._sourceeventId);"]}, {"code": 655, "indent": 0, "parameters": ["let index = 1;"]}, {"code": 655, "indent": 0, "parameters": ["this.showPrototypeEventDialogue(type,String(index))"]}, {"code": 102, "indent": 0, "parameters": [["\\str[6]", "\\str[7]"], 1, -1, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\str[6]"]}, {"code": 117, "indent": 1, "parameters": [85]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\str[7]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!fsm_Flame04", "direction": 6, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["particle play smoke_c-EID this smoke_c"]}, {"code": 108, "indent": 0, "parameters": ["particle update smoke_c-EID pos 0 -36"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 36, "y": 2}, {"id": 21, "name": "年轻的剑圣Sh", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$kurohanyan", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[0,0]"]}, {"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字自动显现 : 触发范围 : 固定自动显现范围 : 横向图块[3] : 纵向图块[3]"]}, {"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字自动显现 : 开启"]}, {"code": 108, "indent": 0, "parameters": ["=>事件漂浮背景 : 设置背景 : 7"]}, {"code": 108, "indent": 0, "parameters": ["=>事件漂浮背景 : 背景偏移 : 0 : -16"]}, {"code": 108, "indent": 0, "parameters": ["<Group:\"NPC\">"]}, {"code": 108, "indent": 0, "parameters": ["<width: 1.4>"]}, {"code": 408, "indent": 0, "parameters": ["<height: 1.4>"]}, {"code": 408, "indent": 0, "parameters": ["<offsetX: 0.2>"]}, {"code": 408, "indent": 0, "parameters": ["<offsetY: 0>"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[8]"]}, {"code": 356, "indent": 0, "parameters": [">对话框皮肤 : 只对话框 : 修改样式 : 样式[9]"]}, {"code": 355, "indent": 0, "parameters": ["let event = $gameMap.event(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["let x = (event.screenX() - 72) * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 0, "parameters": ["let y = (event.screenY() - 86) * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['x_value'] = x ;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['y_value'] = y ;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['height_rowCount'] = 2;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['height_value'] = 52 * 2;"]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 25, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 25, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["我只是来挑战自我的、"]}, {"code": 401, "indent": 0, "parameters": ["大迷宫的真相什么的，并不感兴趣。"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameParty.hasItem($dataWeapons[1]) || $gameParty.leader().equips()[0].baseItemId == 1"]}, {"code": 213, "indent": 1, "parameters": [0, 1, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["你拿着的那个是……！你也是勇者大人的粉丝吗！？"]}, {"code": 401, "indent": 1, "parameters": ["这么好质量的木剑可不容易入手啊……"]}, {"code": 108, "indent": 1, "parameters": ["选项框适配"]}, {"code": 355, "indent": 1, "parameters": ["$gameSystem._drill_DCB_enable = true;"]}, {"code": 655, "indent": 1, "parameters": ["$gameSystem._drill_DCB_curStyle = 30;"]}, {"code": 655, "indent": 1, "parameters": ["DrillUp.g_DCB_data[29].x = $gamePlayer.screenX() + 64;"]}, {"code": 655, "indent": 1, "parameters": ["DrillUp.g_DCB_data[29].x *= $gameScreen.zoomScale();"]}, {"code": 655, "indent": 1, "parameters": ["DrillUp.g_DCB_data[29].y = $gamePlayer.screenY() - 12;"]}, {"code": 655, "indent": 1, "parameters": ["DrillUp.g_DCB_data[29].y *= $gameScreen.zoomScale();"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["那个——！如果不介意的话、可以和我较量一番吗？"]}, {"code": 401, "indent": 1, "parameters": ["作为竞争对手实在想比个高低呢。"]}, {"code": 102, "indent": 1, "parameters": [["接受", "拒绝"], -1, -1, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "接受"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["那真是太好了、"]}, {"code": 401, "indent": 2, "parameters": ["现在就开始吧！"]}, {"code": 242, "indent": 2, "parameters": [2]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 356, "indent": 2, "parameters": [">独立开关 : 事件[22] : A : 开启"]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 123, "indent": 2, "parameters": ["B", 0]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "拒绝"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["这样啊……那真是遗憾。但是如果什么时候有兴趣了，"]}, {"code": 401, "indent": 2, "parameters": ["我随时都可以奉陪的。"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 313, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "$kurohanyan", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[0,-5]"]}, {"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字自动显现 : 触发范围 : 固定自动显现范围 : 横向图块[3] : 纵向图块[3]"]}, {"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字自动显现 : 开启"]}, {"code": 108, "indent": 0, "parameters": ["=>事件漂浮背景 : 设置背景 : 7"]}, {"code": 108, "indent": 0, "parameters": ["=>事件漂浮背景 : 背景偏移 : 0 : -16"]}, {"code": 108, "indent": 0, "parameters": ["<Group:\"NPC\">"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameSelfSwitches.value([$gameMap.mapId(), 22, 'B'])"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[8]"]}, {"code": 356, "indent": 0, "parameters": [">对话框皮肤 : 只对话框 : 修改样式 : 样式[9]"]}, {"code": 355, "indent": 0, "parameters": ["let event = $gameMap.event(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["let x = (event.screenX() - 72) * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 0, "parameters": ["let y = (event.screenY() - 86) * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['x_value'] = x ;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['y_value'] = y ;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['height_rowCount'] = 2;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['height_value'] = 52 * 2;"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["啊啊、看起来没什么事、真是太好了！"]}, {"code": 401, "indent": 0, "parameters": ["之前提议要较量真是非常抱歉！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["看来我似乎稍微用力过猛了。不过，既然是决斗，"]}, {"code": 401, "indent": 0, "parameters": ["我就会全力以赴不会放水的。"]}, {"code": 108, "indent": 0, "parameters": ["选项框适配"]}, {"code": 355, "indent": 0, "parameters": ["$gameSystem._drill_DCB_enable = true;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DCB_curStyle = 30;"]}, {"code": 655, "indent": 0, "parameters": ["DrillUp.g_DCB_data[29].x = $gamePlayer.screenX() + 64;"]}, {"code": 655, "indent": 0, "parameters": ["DrillUp.g_DCB_data[29].x *= $gameScreen.zoomScale();"]}, {"code": 655, "indent": 0, "parameters": ["DrillUp.g_DCB_data[29].y = $gamePlayer.screenY() - 24;"]}, {"code": 655, "indent": 0, "parameters": ["DrillUp.g_DCB_data[29].y *= $gameScreen.zoomScale();"]}, {"code": 102, "indent": 0, "parameters": [["发起挑战", "算了吧"], -1, -1, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "发起挑战"]}, {"code": 213, "indent": 1, "parameters": [0, 1, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["你也是一名战士啊、不用说、"]}, {"code": 401, "indent": 1, "parameters": ["我是不会拒绝挑战的！"]}, {"code": 242, "indent": 1, "parameters": [2]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 356, "indent": 1, "parameters": [">独立开关 : 事件[22] : A : 开启"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "算了吧"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 314, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "$kurohanyan", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[0,-5]"]}, {"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字自动显现 : 触发范围 : 固定自动显现范围 : 横向图块[3] : 纵向图块[3]"]}, {"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字自动显现 : 开启"]}, {"code": 108, "indent": 0, "parameters": ["=>事件漂浮背景 : 设置背景 : 7"]}, {"code": 108, "indent": 0, "parameters": ["=>事件漂浮背景 : 背景偏移 : 0 : -16"]}, {"code": 108, "indent": 0, "parameters": ["<Group:\"NPC\">"]}, {"code": 108, "indent": 0, "parameters": ["=>标签核心 : 添加标签 : NPC"]}, {"code": 314, "indent": 0, "parameters": [0, 1]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 313, "indent": 0, "parameters": [0, 1, 1, 65]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[8]"]}, {"code": 356, "indent": 0, "parameters": [">对话框皮肤 : 只对话框 : 修改样式 : 样式[9]"]}, {"code": 355, "indent": 0, "parameters": ["let event = $gameMap.event(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["let x = (event.screenX() - 72) * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 0, "parameters": ["let y = (event.screenY() - 86) * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['x_value'] = x ;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['y_value'] = y ;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['height_rowCount'] = 2;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['height_value'] = 52 * 2;"]}, {"code": 356, "indent": 0, "parameters": [">允许操作玩家移动 : 开启"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["哈啊…哈啊…是我输了……你很厉害啊、"]}, {"code": 401, "indent": 0, "parameters": ["不愧是我的对手。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["要求你和我进行这么不讲理的较量、"]}, {"code": 401, "indent": 0, "parameters": ["什么回报也不提供也让人过意不去啊。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["收下这个吧、这是我自己钻研出来的技能。"]}, {"code": 401, "indent": 0, "parameters": ["一定对你有所帮助的。"]}, {"code": 318, "indent": 0, "parameters": [0, 1, 0, 3]}, {"code": 318, "indent": 0, "parameters": [0, 1, 0, 26]}, {"code": 231, "indent": 0, "parameters": [9, "black", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["let lang = ConfigManager.language;"]}, {"code": 655, "indent": 0, "parameters": ["if (lang > 2) lang = 2;"]}, {"code": 655, "indent": 0, "parameters": ["let IMG = \"senPoTaChi\" + lang;"]}, {"code": 655, "indent": 0, "parameters": ["let path = \"Item_Acquisition_Performance\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(10, path, IMG, 1, 960, 540, 150, 150, 0, 0)"]}, {"code": 232, "indent": 0, "parameters": [9, 0, 0, 0, 0, 0, 100, 100, 160, 0, 120, false]}, {"code": 232, "indent": 0, "parameters": [10, 0, 1, 0, 960, 540, 100, 100, 255, 0, 120, false]}, {"code": 355, "indent": 0, "parameters": ["let pic = $gameScreen.picture(10);"]}, {"code": 655, "indent": 0, "parameters": ["if (pic) {"]}, {"code": 655, "indent": 0, "parameters": ["var id = \"bloom10\";"]}, {"code": 655, "indent": 0, "parameters": ["var filterTarget = 5010;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.createFilter(id, \"bloom\", filterTarget);"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.setFilter( id ,[16,2,0.5,3]);"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.moveFilter(id, [2,0,0.1,1], 240);"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.eraseFilterAfterMove(id);"]}, {"code": 655, "indent": 0, "parameters": ["   }"]}, {"code": 355, "indent": 0, "parameters": ["let pic = $gameScreen.picture(10);"]}, {"code": 655, "indent": 0, "parameters": ["if (pic) {"]}, {"code": 655, "indent": 0, "parameters": ["var id = \"blur10\";"]}, {"code": 655, "indent": 0, "parameters": ["var filterTarget = 5010;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.createFilter(id, \"blur\", filterTarget);"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.setFilter( id ,[6]);"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.moveFilter(id, [0], 180);"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.eraseFilterAfterMove(id);"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "財宝や貴重なものを発見したときの音", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [240]}, {"code": 355, "indent": 0, "parameters": ["var id = \"bloom10\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.moveFilter(id, [8,0,0.5,1], 90);"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.eraseFilterAfterMove(id);"]}, {"code": 232, "indent": 0, "parameters": [9, 0, 0, 0, 0, 0, 100, 100, 0, 0, 120, false]}, {"code": 232, "indent": 0, "parameters": [10, 0, 1, 0, 960, 540, 100, 100, 0, 0, 120, false]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 235, "indent": 0, "parameters": [9]}, {"code": 235, "indent": 0, "parameters": [10]}, {"code": 355, "indent": 0, "parameters": ["ctb.useTurnPlayer = true"]}, {"code": 117, "indent": 0, "parameters": [93]}, {"code": 123, "indent": 0, "parameters": ["C", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "C", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 313, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "$kurohanyan", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[0,-5]"]}, {"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字自动显现 : 触发范围 : 固定自动显现范围 : 横向图块[3] : 纵向图块[3]"]}, {"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字自动显现 : 开启"]}, {"code": 108, "indent": 0, "parameters": ["=>事件漂浮背景 : 设置背景 : 7"]}, {"code": 108, "indent": 0, "parameters": ["=>事件漂浮背景 : 背景偏移 : 0 : -16"]}, {"code": 108, "indent": 0, "parameters": ["<Group:\"NPC\">"]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 25, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 25, "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[8]"]}, {"code": 356, "indent": 0, "parameters": [">对话框皮肤 : 只对话框 : 修改样式 : 样式[9]"]}, {"code": 355, "indent": 0, "parameters": ["let event = $gameMap.event(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["let x = (event.screenX() - 72) * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 0, "parameters": ["let y = (event.screenY() - 86) * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['x_value'] = x ;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['y_value'] = y ;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['height_rowCount'] = 2;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['height_value'] = 52 * 2;"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["虽然有点自满，"]}, {"code": 401, "indent": 0, "parameters": ["但我已经是村里最强的了。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["但多亏了你，"]}, {"code": 401, "indent": 0, "parameters": ["让我见识到了人外有人。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["哎呀、必须要更加拼命修炼才行了呀！"]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 123, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 29, "y": 9}, {"id": 22, "name": "年轻的剑圣（一阶段）Sh", "note": "<重置独立开关>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$kurohanyan", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 230, "indent": 0, "parameters": [30]}, {"code": 355, "indent": 0, "parameters": ["$gamePlayer.locate(13, 32);"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 241, "indent": 0, "parameters": [{"name": "m-art_<PERSON><PERSON><PERSON><PERSON><PERSON>", "volume": 70, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["AudioManager.fadeInBgm(5)"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[8]"]}, {"code": 356, "indent": 0, "parameters": [">对话框皮肤 : 只对话框 : 修改样式 : 样式[9]"]}, {"code": 355, "indent": 0, "parameters": ["let event = $gameMap.event(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["let x = (event.screenX() - 72) * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 0, "parameters": ["let y = (event.screenY() + 12) * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['x_value'] = x ;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['y_value'] = y ;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['height_rowCount'] = 2;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['height_value'] = 52 * 2;"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["十里村出身、志向是成为剑圣！"]}, {"code": 401, "indent": 0, "parameters": ["在此参上！"]}, {"code": 122, "indent": 0, "parameters": [313, 313, 1, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["let Hp = 200;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSelfVariables.set(this, 'HP', Hp);"]}, {"code": 355, "indent": 0, "parameters": ["let Hp = 200;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSelfVariables.set(this, 'MHP', Hp);"]}, {"code": 355, "indent": 0, "parameters": ["$gameSelfVariables.set(this, 'DEF', 5);"]}, {"code": 355, "indent": 0, "parameters": ["$gameSelfVariables.set(this, 'enemyId', 25);"]}, {"code": 123, "indent": 0, "parameters": ["B", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 45, "parameters": ["this._moveSpeed = 24"], "indent": null}, {"code": 45, "parameters": ["this._sightLock = 360000"], "indent": null}, {"code": 45, "parameters": ["this._fleeRange = 22"], "indent": null}, {"code": 45, "parameters": ["this._chaseRange = 18"], "indent": null}, {"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$kurohanyan", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Group:\"enemy\">"]}, {"code": 108, "indent": 0, "parameters": ["水平斩击"]}, {"code": 111, "indent": 0, "parameters": [12, "$gamePlayer.calcDistance(this._eventId) < 64 "]}, {"code": 355, "indent": 1, "parameters": ["var seNames = [\"風切り音（誇張のない音。素早く）\", \"剣・棒状の風切り音1 ヒュン！\"];"]}, {"code": 655, "indent": 1, "parameters": ["var randomSeName = seNames[Math.floor(Math.random() * seNames.length)];"]}, {"code": 655, "indent": 1, "parameters": ["var randomPitch = Math.randomInt(40) + 81;"]}, {"code": 655, "indent": 1, "parameters": ["var se = { name: randomSeName, volume: 60, pitch: randomPitch, pan: 0 };"]}, {"code": 655, "indent": 1, "parameters": ["AudioManager.playSe(se);"]}, {"code": 355, "indent": 1, "parameters": ["var EID = $gameSelfVariables.get(this, 'enemyId');"]}, {"code": 655, "indent": 1, "parameters": ["QJ.MPMZ.tl.enemy_meleeAttack(EID);"]}, {"code": 655, "indent": 1, "parameters": ["QJ.MPMZ.tl.enemy_meleeAttackTrail(EID)"]}, {"code": 230, "indent": 1, "parameters": [25]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["冲刺斩"]}, {"code": 111, "indent": 0, "parameters": [12, "$gamePlayer.calcDistance(this._eventId) > 72 && Math.random() > 0.5"]}, {"code": 123, "indent": 1, "parameters": ["C", 0]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 45, "parameters": ["Math.random() > 0.1 ? this.moveTowardPlayer() : this.moveRandom()"], "indent": null}, {"code": 45, "parameters": ["this._moveSpeed = 16"], "indent": null}, {"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 6, "moveType": 3, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "C", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$kurohanyan", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Group:\"enemy\">"]}, {"code": 108, "indent": 0, "parameters": ["冲刺斩"]}, {"code": 118, "indent": 0, "parameters": ["loop"]}, {"code": 356, "indent": 0, "parameters": ["particle play fuss_startdash this def 0.9"]}, {"code": 355, "indent": 0, "parameters": ["this.count = 0"]}, {"code": 355, "indent": 0, "parameters": ["let deg = $gameMap.event(this._eventId).calcDeg($gamePlayer);"]}, {"code": 655, "indent": 0, "parameters": ["$gameSelfVariables.set(this, 'DEG', Math.round(deg));"]}, {"code": 108, "indent": 0, "parameters": ["运动模糊"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Wind1", "volume": 55, "pitch": 140, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["var id = \"モーションブラー\" + this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["this.pluginCommand('setFilter', [id, '30', '0']);"]}, {"code": 355, "indent": 0, "parameters": ["var id = \"モーションブラー\" + this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["this.pluginCommand('moveFilter', [id, '0', '0', '20']);"]}, {"code": 108, "indent": 0, "parameters": ["冲刺残像"]}, {"code": 355, "indent": 0, "parameters": ["var eid = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["var name = 'enemyDash' + eid;"]}, {"code": 655, "indent": 0, "parameters": ["QJ.MPMZ.Shoot({"]}, {"code": 655, "indent": 0, "parameters": ["        img: \"null1\","]}, {"code": 655, "indent": 0, "parameters": ["        groupName: ['enemyDash'],"]}, {"code": 655, "indent": 0, "parameters": ["        imgRotation: ['F'],"]}, {"code": 655, "indent": 0, "parameters": ["        existData: [ { t: ['Time', 20] } ],"]}, {"code": 655, "indent": 0, "parameters": ["        moveF: [  "]}, {"code": 655, "indent": 0, "parameters": ["        [0,2,QJ.MPMZ.tl.ex_senpoResidualEffect,[eid,[255, 255, 255, 255],true]] "]}, {"code": 655, "indent": 0, "parameters": ["        ],"]}, {"code": 655, "indent": 0, "parameters": ["    });"]}, {"code": 108, "indent": 0, "parameters": ["锁定玩家位置"]}, {"code": 355, "indent": 0, "parameters": ["let event = $gameMap.event(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["let xx = $gamePlayer.centerRealX();"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.setValue(85, xx);"]}, {"code": 655, "indent": 0, "parameters": ["let yy = $gamePlayer.centerRealY();"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.setValue(86, yy);"]}, {"code": 655, "indent": 0, "parameters": ["event._moveSpeed = 0;"]}, {"code": 655, "indent": 0, "parameters": ["event._through = true"]}, {"code": 108, "indent": 0, "parameters": ["发动冲刺拔刀"]}, {"code": 355, "indent": 0, "parameters": ["QJ.MPMZ.tl.APC_dashIai(this._eventId,15)"]}, {"code": 355, "indent": 0, "parameters": ["let event = $gameMap.event(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["event._moveSpeed = 72;"]}, {"code": 655, "indent": 0, "parameters": ["let xx = $gameVariables.value(85);"]}, {"code": 655, "indent": 0, "parameters": ["let yy = $gameVariables.value(86);"]}, {"code": 655, "indent": 0, "parameters": ["event.moveToTarget(xx,yy)"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Sword4", "volume": 65, "pitch": 100, "pan": 0}]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 111, "indent": 1, "parameters": [12, "!$gameMap.event(this._eventId).isMoved()"]}, {"code": 355, "indent": 2, "parameters": ["let event = $gameMap.event(this._eventId);"]}, {"code": 655, "indent": 2, "parameters": ["event._moveSpeed = 16;"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "Math.random() > 0.9"]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 119, "indent": 1, "parameters": ["loop"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 123, "indent": 0, "parameters": ["C", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 45, "parameters": ["Math.random() > 0.1 ? this.moveTowardPlayer() : this.moveRandom()"], "indent": null}, {"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 6, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "C", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$kurohanyan", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Group:\"enemy\">"]}, {"code": 108, "indent": 0, "parameters": ["冲刺斩"]}, {"code": 118, "indent": 0, "parameters": ["loop"]}, {"code": 356, "indent": 0, "parameters": ["particle play fuss_startdash this def 0.9"]}, {"code": 355, "indent": 0, "parameters": ["this.count = 0"]}, {"code": 355, "indent": 0, "parameters": ["let deg = $gameMap.event(this._eventId).calcDeg($gamePlayer);"]}, {"code": 655, "indent": 0, "parameters": ["$gameSelfVariables.set(this, 'DEG', Math.round(deg));"]}, {"code": 108, "indent": 0, "parameters": ["运动模糊"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Wind1", "volume": 55, "pitch": 140, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["var id = \"モーションブラー\" + this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["this.pluginCommand('setFilter', [id, '30', '0']);"]}, {"code": 355, "indent": 0, "parameters": ["var id = \"モーションブラー\" + this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["this.pluginCommand('moveFilter', [id, '0', '0', '20']);"]}, {"code": 108, "indent": 0, "parameters": ["残影"]}, {"code": 108, "indent": 0, "parameters": ["发动冲刺拔刀"]}, {"code": 355, "indent": 0, "parameters": ["QJ.MPMZ.tl.APC_dashIai(this._eventId,15)"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Sword4", "volume": 65, "pitch": 100, "pan": 0}]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["let deg = $gameSelfVariables.get(this, 'DEG');"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.event(this._eventId).dotMoveByDeg(deg);"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.event(this._eventId)._moveSpeed = 48;"]}, {"code": 355, "indent": 1, "parameters": ["this.count++"]}, {"code": 111, "indent": 1, "parameters": [12, "this.count > 30"]}, {"code": 355, "indent": 2, "parameters": ["$gameMap.event(this._eventId)._moveSpeed = 18;"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "Math.random() > 0.9"]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 119, "indent": 1, "parameters": ["loop"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 123, "indent": 0, "parameters": ["C", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 45, "parameters": ["Math.random() > 0.1 ? this.moveTowardPlayer() : this.moveRandom()"], "indent": null}, {"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 6, "moveType": 3, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$kurohanyan", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 221, "indent": 0, "parameters": []}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 123, "indent": 0, "parameters": ["B", 1]}, {"code": 355, "indent": 0, "parameters": ["$gameSystem._drill_COI_map_mouse = false;"]}, {"code": 655, "indent": 0, "parameters": ["TouchInput._mousePressed = false"]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 355, "indent": 0, "parameters": ["$gameSystem._drill_COI_map_mouse = true"]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 355, "indent": 0, "parameters": ["AudioManager.fadeOutBgsByLine(1,9);"]}, {"code": 655, "indent": 0, "parameters": ["$gameSwitches.setValue(182, false);"]}, {"code": 122, "indent": 0, "parameters": [314, 314, 1, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["$gameSelfSwitches.setValue([18, 21, 'B'],false)"]}, {"code": 242, "indent": 0, "parameters": [2]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": [">行走图动画序列 : 玩家 : 立即停止动作元"]}, {"code": 356, "indent": 0, "parameters": [">行走图动画序列 : 玩家 : 全标签播放 : 开启"]}, {"code": 314, "indent": 0, "parameters": [0, 1]}, {"code": 355, "indent": 0, "parameters": ["$gamePlayer.locate(29.3, 10);"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 60, 0, 2, 0]}, {"code": 241, "indent": 1, "parameters": [{"name": "Adventurer's promenade_Rainy", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 60, 0, 1, 0]}, {"code": 241, "indent": 2, "parameters": [{"name": "Adventurer's promenade_Cloudy", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 241, "indent": 2, "parameters": [{"name": "Adventurer's promenade_Hare", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["AudioManager.fadeInBgm(3)"]}, {"code": 355, "indent": 0, "parameters": ["ctb.useTurnPlayer = false"]}, {"code": 123, "indent": 0, "parameters": ["D", 1]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 45, "parameters": ["this._moveSpeed = 24"], "indent": null}, {"code": 45, "parameters": ["this._sightLock = 360000"], "indent": null}, {"code": 45, "parameters": ["this._fleeRange = 22"], "indent": null}, {"code": 45, "parameters": ["this._chaseRange = 18"], "indent": null}, {"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 3, "walkAnime": true}], "x": 12, "y": 27}, {"id": 23, "name": "骑士Sh", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$knight", "direction": 6, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>持续动作 : 缩放状态 : 缩放比例[1.25]"]}, {"code": 108, "indent": 0, "parameters": ["<Group:\"NPC\">"]}, {"code": 108, "indent": 0, "parameters": ["<width: 1.4>"]}, {"code": 408, "indent": 0, "parameters": ["<height: 1>"]}, {"code": 408, "indent": 0, "parameters": ["<offsetX: +0.2>"]}, {"code": 408, "indent": 0, "parameters": ["<offsetY: 0>"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameSelfSwitches.value([$gameMap.mapId(), 22, 'B'])"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 25, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 25, "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[8]"]}, {"code": 356, "indent": 0, "parameters": [">对话框皮肤 : 只对话框 : 修改样式 : 样式[9]"]}, {"code": 355, "indent": 0, "parameters": ["let event = $gameMap.event(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["let x = (event.screenX() - 72) * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 0, "parameters": ["let y = (event.screenY() - 124) * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['x_value'] = x ;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['y_value'] = y ;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['height_rowCount'] = 2;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['height_value'] = 52 * 2;"]}, {"code": 355, "indent": 0, "parameters": ["let event = $gameMap.event(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["if (event && event._drill_EASe_controller !== undefined) {"]}, {"code": 655, "indent": 0, "parameters": ["event.drill_EASe_setSimpleStateNode( [\"注意\"] );"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 213, "indent": 0, "parameters": [0, 1, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["……噢噢！"]}, {"code": 401, "indent": 0, "parameters": ["阁下也是追求大迷宫而来的冒险者吗？"]}, {"code": 355, "indent": 0, "parameters": ["let event = $gameMap.event(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["if (event && event._drill_EASe_controller !== undefined) {"]}, {"code": 655, "indent": 0, "parameters": ["event.drill_EASe_setSimpleStateNode( [\"站立\"] );"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["阁下可能有所耳闻，"]}, {"code": 401, "indent": 0, "parameters": ["在下乃是还不成气候的游戏开发者。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["在下被这大迷宫所吸引，在下希望能向世人传达这秘境"]}, {"code": 401, "indent": 0, "parameters": ["的神秘魅力。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["但说来惭愧，在下并不擅武艺，"]}, {"code": 401, "indent": 0, "parameters": ["只能通过创作的形式来表达在下的热情。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["什么？很在意这副铠甲吗？"]}, {"code": 401, "indent": 0, "parameters": ["哈哈哈…这只是在下的一点兴趣爱好而已！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["只要穿上了这么一身，在下也能多少体验到"]}, {"code": 401, "indent": 0, "parameters": ["身为冒险者的感觉了。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["当然、是非常地沉重。"]}, {"code": 401, "indent": 0, "parameters": ["在下其实已经被压到无法动弹了……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["但是……！"]}, {"code": 401, "indent": 0, "parameters": ["在下是不会认输的！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["这里聚集了如此多的豪杰人物和传奇故事，"]}, {"code": 401, "indent": 0, "parameters": ["在下还得不断地收集灵感和素材才行！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["如此说来，阁下不介意的话，"]}, {"code": 401, "indent": 0, "parameters": ["要尝试看看在下的愚作吗？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["肯定对阁下的探索有所帮助的！\\w[45]"]}, {"code": 401, "indent": 0, "parameters": ["……什么！没有运行游戏的机器吗？"]}, {"code": 213, "indent": 0, "parameters": [-1, 6, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["无需担心！在下为了应对这种情况、"]}, {"code": 401, "indent": 0, "parameters": ["一直随身携带着传教用的道具。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["和阁下的相遇也是一种缘分、"]}, {"code": 401, "indent": 0, "parameters": ["在下愿以成本价将机器交付给阁下！"]}, {"code": 355, "indent": 0, "parameters": ["let event = $gamePlayer;"]}, {"code": 655, "indent": 0, "parameters": ["let x = event.screenX() * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 0, "parameters": ["let y = (event.screenY() - 100) * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['x_value'] = x ;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['y_value'] = y ;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['height_rowCount'] = 2;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['height_value'] = 64 * 2;"]}, {"code": 108, "indent": 0, "parameters": ["选项框适配"]}, {"code": 355, "indent": 0, "parameters": ["$gameSystem._drill_DCB_enable = true;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DCB_curStyle = 30;"]}, {"code": 655, "indent": 0, "parameters": ["DrillUp.g_DCB_data[29].x = $gamePlayer.screenX() + 64;"]}, {"code": 655, "indent": 0, "parameters": ["DrillUp.g_DCB_data[29].x *= $gameScreen.zoomScale();"]}, {"code": 655, "indent": 0, "parameters": ["DrillUp.g_DCB_data[29].y = $gamePlayer.screenY() - 24;"]}, {"code": 655, "indent": 0, "parameters": ["DrillUp.g_DCB_data[29].y *= $gameScreen.zoomScale();"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["要买\\ii[15]吗？"]}, {"code": 401, "indent": 0, "parameters": ["价格是\\c[6]5000\\c[0]元。"]}, {"code": 102, "indent": 0, "parameters": [["买了！", "算了"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "买了！"]}, {"code": 111, "indent": 1, "parameters": [12, "$gameParty.gold() >= 5000"]}, {"code": 125, "indent": 2, "parameters": [1, 0, 5000]}, {"code": 355, "indent": 2, "parameters": ["let event = $gameMap.event(this._eventId);"]}, {"code": 655, "indent": 2, "parameters": ["let x = (event.screenX() - 80) * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 2, "parameters": ["let y = (event.screenY() - 124 ) * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 2, "parameters": ["$gameSystem._drill_DOp_curStyle['x_value'] = x ;"]}, {"code": 655, "indent": 2, "parameters": ["$gameSystem._drill_DOp_curStyle['y_value'] = y ;"]}, {"code": 655, "indent": 2, "parameters": ["$gameSystem._drill_DOp_curStyle['height_rowCount'] = 2;"]}, {"code": 655, "indent": 2, "parameters": ["$gameSystem._drill_DOp_curStyle['height_value'] = 55 * 2;"]}, {"code": 250, "indent": 2, "parameters": [{"name": "キラーン（ひらめき・アイテム発見・獲得）", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 126, "indent": 2, "parameters": [15, 0, 0, 1]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["哈哈哈、这样契约变成立了。"]}, {"code": 401, "indent": 2, "parameters": ["在下期待着阁下游玩后的评价。"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["如果阁下还能将冒险的见闻分享给在下，"]}, {"code": 401, "indent": 2, "parameters": ["在下也能从中汲取灵感继续创作。"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["let event = $gameMap.event(this._eventId);"]}, {"code": 655, "indent": 2, "parameters": ["let x = (event.screenX() - 80) * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 2, "parameters": ["let y = (event.screenY() - 124 ) * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 2, "parameters": ["$gameSystem._drill_DOp_curStyle['x_value'] = x ;"]}, {"code": 655, "indent": 2, "parameters": ["$gameSystem._drill_DOp_curStyle['y_value'] = y ;"]}, {"code": 655, "indent": 2, "parameters": ["$gameSystem._drill_DOp_curStyle['height_rowCount'] = 2;"]}, {"code": 655, "indent": 2, "parameters": ["$gameSystem._drill_DOp_curStyle['height_value'] = 55 * 2;"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["哦？没带够钱吗？"]}, {"code": 401, "indent": 2, "parameters": ["哈哈哈……无需介意。"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["在下每天都在这里逗留、"]}, {"code": 401, "indent": 2, "parameters": ["准备好资金前在下都会等待阁下的到来。"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["在下也期待着可以听到阁下的故事。"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "算了"]}, {"code": 355, "indent": 1, "parameters": ["let event = $gameMap.event(this._eventId);"]}, {"code": 655, "indent": 1, "parameters": ["let x = (event.screenX() - 80) * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 1, "parameters": ["let y = (event.screenY() - 124 ) * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 1, "parameters": ["$gameSystem._drill_DOp_curStyle['x_value'] = x ;"]}, {"code": 655, "indent": 1, "parameters": ["$gameSystem._drill_DOp_curStyle['y_value'] = y ;"]}, {"code": 655, "indent": 1, "parameters": ["$gameSystem._drill_DOp_curStyle['height_rowCount'] = 2;"]}, {"code": 655, "indent": 1, "parameters": ["$gameSystem._drill_DOp_curStyle['height_value'] = 55 * 2;"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["是这样吗？无需在意在下。"]}, {"code": 401, "indent": 1, "parameters": ["在下每日都会在此逗留，"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["什么时候阁下有兴趣了，"]}, {"code": 401, "indent": 1, "parameters": ["还请随时来找在下。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["在下也期待着可以听到阁下的故事。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "!$gameParty.hasItem($dataItems[15])"]}, {"code": 355, "indent": 1, "parameters": ["let event = $gameMap.event(this._eventId);"]}, {"code": 655, "indent": 1, "parameters": ["if (event && event._drill_EASe_controller !== undefined) {"]}, {"code": 655, "indent": 1, "parameters": ["event.drill_EASe_setSimpleStateNode( [\"记笔记\"] );"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 123, "indent": 0, "parameters": ["B", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$knight", "direction": 6, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>持续动作 : 缩放状态 : 缩放比例[1.25]"]}, {"code": 108, "indent": 0, "parameters": ["<Group:\"NPC\">"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameSelfSwitches.value([$gameMap.mapId(), 22, 'B'])"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["ctb.useTurnPlayer = false"]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 25, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 25, "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[8]"]}, {"code": 356, "indent": 0, "parameters": [">对话框皮肤 : 只对话框 : 修改样式 : 样式[9]"]}, {"code": 355, "indent": 0, "parameters": ["let event = $gameMap.event(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["let x = (event.screenX() - 80) * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 0, "parameters": ["let y = (event.screenY() - 124 ) * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['x_value'] = x ;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['y_value'] = y ;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['height_rowCount'] = 2;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['height_value'] = 55 * 2;"]}, {"code": 355, "indent": 0, "parameters": ["let event = $gameMap.event(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["if (event && event._drill_EASe_controller !== undefined) {"]}, {"code": 655, "indent": 0, "parameters": ["event.drill_EASe_setSimpleStateNode( [\"注意\"] );"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 213, "indent": 0, "parameters": [0, 1, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["……噢噢！"]}, {"code": 401, "indent": 0, "parameters": ["阁下也是追求大迷宫而来的冒险者吗？"]}, {"code": 355, "indent": 0, "parameters": ["$gamePlayer.moveToTarget(17,6.5)"]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "!$gamePlayer.isMoved()"]}, {"code": 213, "indent": 2, "parameters": [-1, 2, true]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [4]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["ctb.useTurnPlayer = true"]}, {"code": 355, "indent": 0, "parameters": ["let event = $gameMap.event(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["let x = (event.screenX() - 80) * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 0, "parameters": ["let y = (event.screenY() - 124 ) * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['x_value'] = x ;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['y_value'] = y ;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['height_rowCount'] = 2;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['height_value'] = 55 * 2;"]}, {"code": 355, "indent": 0, "parameters": ["let event = $gameMap.event(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["if (event && event._drill_EASe_controller !== undefined) {"]}, {"code": 655, "indent": 0, "parameters": ["event.drill_EASe_setSimpleStateNode( [\"站立\"] );"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["阁下可能有所耳闻，"]}, {"code": 401, "indent": 0, "parameters": ["在下乃是还不成气候的游戏开发者。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["在下被这大迷宫所吸引，在下希望能向世人传达这秘境"]}, {"code": 401, "indent": 0, "parameters": ["的神秘魅力。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["但说来惭愧，在下并不擅武艺，"]}, {"code": 401, "indent": 0, "parameters": ["只能通过创作的形式来表达在下的热情。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["什么？很在意这副铠甲吗？"]}, {"code": 401, "indent": 0, "parameters": ["哈哈哈…这只是在下的一点兴趣爱好而已！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["只要穿上了这么一身，在下也能多少体验到"]}, {"code": 401, "indent": 0, "parameters": ["身为冒险者的感觉了。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["当然、是非常地沉重。"]}, {"code": 401, "indent": 0, "parameters": ["在下其实已经被压到无法动弹了……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["但是……！"]}, {"code": 401, "indent": 0, "parameters": ["在下是不会认输的！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["这里聚集了如此多的豪杰人物和传奇故事，"]}, {"code": 401, "indent": 0, "parameters": ["在下还得不断地收集灵感和素材才行！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["如此说来，阁下不介意的话，"]}, {"code": 401, "indent": 0, "parameters": ["要尝试看看在下的愚作吗？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["肯定对阁下的探索有所帮助的！\\w[45]"]}, {"code": 401, "indent": 0, "parameters": ["……什么！没有运行游戏的机器吗？"]}, {"code": 213, "indent": 0, "parameters": [-1, 6, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["无需担心！在下为了应对这种情况、"]}, {"code": 401, "indent": 0, "parameters": ["一直随身携带着传教用的道具。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["和阁下的相遇也是一种缘分、"]}, {"code": 401, "indent": 0, "parameters": ["在下愿以成本价将机器交付给阁下！"]}, {"code": 355, "indent": 0, "parameters": ["let event = $gamePlayer;"]}, {"code": 655, "indent": 0, "parameters": ["let x = event.screenX() * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 0, "parameters": ["let y = (event.screenY() - 100) * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['x_value'] = x ;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['y_value'] = y ;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['height_rowCount'] = 2;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['height_value'] = 64 * 2;"]}, {"code": 108, "indent": 0, "parameters": ["选项框适配"]}, {"code": 355, "indent": 0, "parameters": ["$gameSystem._drill_DCB_enable = true;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DCB_curStyle = 30;"]}, {"code": 655, "indent": 0, "parameters": ["DrillUp.g_DCB_data[29].x = $gamePlayer.screenX() + 64;"]}, {"code": 655, "indent": 0, "parameters": ["DrillUp.g_DCB_data[29].x *= $gameScreen.zoomScale();"]}, {"code": 655, "indent": 0, "parameters": ["DrillUp.g_DCB_data[29].y = $gamePlayer.screenY() - 24;"]}, {"code": 655, "indent": 0, "parameters": ["DrillUp.g_DCB_data[29].y *= $gameScreen.zoomScale();"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["要买\\ii[15]吗？"]}, {"code": 401, "indent": 0, "parameters": ["价格是\\c[6]5000\\c[0]元。"]}, {"code": 102, "indent": 0, "parameters": [["买了！", "算了"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "买了！"]}, {"code": 111, "indent": 1, "parameters": [12, "$gameParty.gold() >= 5000"]}, {"code": 125, "indent": 2, "parameters": [1, 0, 5000]}, {"code": 355, "indent": 2, "parameters": ["let event = $gameMap.event(this._eventId);"]}, {"code": 655, "indent": 2, "parameters": ["let x = (event.screenX() - 80) * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 2, "parameters": ["let y = (event.screenY() - 124 ) * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 2, "parameters": ["$gameSystem._drill_DOp_curStyle['x_value'] = x ;"]}, {"code": 655, "indent": 2, "parameters": ["$gameSystem._drill_DOp_curStyle['y_value'] = y ;"]}, {"code": 655, "indent": 2, "parameters": ["$gameSystem._drill_DOp_curStyle['height_rowCount'] = 2;"]}, {"code": 655, "indent": 2, "parameters": ["$gameSystem._drill_DOp_curStyle['height_value'] = 55 * 2;"]}, {"code": 126, "indent": 2, "parameters": [15, 0, 0, 1]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["哈哈哈、这样契约变成立了。"]}, {"code": 401, "indent": 2, "parameters": ["在下期待着阁下游玩后的评价。"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["如果阁下还能将冒险的见闻分享给在下，"]}, {"code": 401, "indent": 2, "parameters": ["在下也能从中汲取灵感继续创作。"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["let event = $gameMap.event(this._eventId);"]}, {"code": 655, "indent": 2, "parameters": ["let x = (event.screenX() - 80) * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 2, "parameters": ["let y = (event.screenY() - 124 ) * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 2, "parameters": ["$gameSystem._drill_DOp_curStyle['x_value'] = x ;"]}, {"code": 655, "indent": 2, "parameters": ["$gameSystem._drill_DOp_curStyle['y_value'] = y ;"]}, {"code": 655, "indent": 2, "parameters": ["$gameSystem._drill_DOp_curStyle['height_rowCount'] = 2;"]}, {"code": 655, "indent": 2, "parameters": ["$gameSystem._drill_DOp_curStyle['height_value'] = 55 * 2;"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["哦？没带够钱吗？"]}, {"code": 401, "indent": 2, "parameters": ["哈哈哈……无需介意。"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["在下每天都在这里逗留、"]}, {"code": 401, "indent": 2, "parameters": ["准备好资金前在下都会等待阁下的到来。"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["在下也期待着可以听到阁下的故事。"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "算了"]}, {"code": 355, "indent": 1, "parameters": ["let event = $gameMap.event(this._eventId);"]}, {"code": 655, "indent": 1, "parameters": ["let x = (event.screenX() - 80) * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 1, "parameters": ["let y = (event.screenY() - 124 ) * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 1, "parameters": ["$gameSystem._drill_DOp_curStyle['x_value'] = x ;"]}, {"code": 655, "indent": 1, "parameters": ["$gameSystem._drill_DOp_curStyle['y_value'] = y ;"]}, {"code": 655, "indent": 1, "parameters": ["$gameSystem._drill_DOp_curStyle['height_rowCount'] = 2;"]}, {"code": 655, "indent": 1, "parameters": ["$gameSystem._drill_DOp_curStyle['height_value'] = 55 * 2;"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["是这样吗？无需在意在下。"]}, {"code": 401, "indent": 1, "parameters": ["在下每日都会在此逗留，"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["什么时候阁下有兴趣了，"]}, {"code": 401, "indent": 1, "parameters": ["还请随时来找在下。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["在下也期待着可以听到阁下的故事。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "!$gameParty.hasItem($dataItems[15])"]}, {"code": 355, "indent": 1, "parameters": ["let event = $gameMap.event(this._eventId);"]}, {"code": 655, "indent": 1, "parameters": ["if (event && event._drill_EASe_controller !== undefined) {"]}, {"code": 655, "indent": 1, "parameters": ["event.drill_EASe_setSimpleStateNode( [\"记笔记\"] );"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 123, "indent": 0, "parameters": ["B", 0]}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$knight", "direction": 6, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>持续动作 : 缩放状态 : 缩放比例[1.25]"]}, {"code": 108, "indent": 0, "parameters": ["<Group:\"NPC\">"]}, {"code": 108, "indent": 0, "parameters": ["=>事件说明窗口 : 显示下列文本"]}, {"code": 408, "indent": 0, "parameters": ["=:\\c[114]奇怪的骑士\\c[0]"]}, {"code": 408, "indent": 0, "parameters": ["=:\\{\\i[16]\\}???"]}, {"code": 408, "indent": 0, "parameters": ["=:\\fs[14]✦\\fi\\c[110]穿着散发不祥气息的盔甲的古怪骑士，"]}, {"code": 408, "indent": 0, "parameters": ["=:\\fs[14]\\fi\\c[110]  自称是游戏开发者。"]}, {"code": 408, "indent": 0, "parameters": ["=:\\fs[14]\\fi\\c[110]  正在积极收集着有趣的见闻。"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameSelfSwitches.value([$gameMap.mapId(), 22, 'B'])"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 25, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 25, "indent": null}]}, {"code": 111, "indent": 0, "parameters": [12, "!$gameParty.hasItem($dataItems[15])"]}, {"code": 355, "indent": 1, "parameters": ["let event = $gameMap.event(this._eventId);"]}, {"code": 655, "indent": 1, "parameters": ["if (event && event._drill_EASe_controller !== undefined) {"]}, {"code": 655, "indent": 1, "parameters": ["event.drill_EASe_setSimpleStateNode( [\"注意\"] );"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[8]"]}, {"code": 356, "indent": 0, "parameters": [">对话框皮肤 : 只对话框 : 修改样式 : 样式[9]"]}, {"code": 355, "indent": 0, "parameters": ["let event = $gameMap.event(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["let x = (event.screenX() - 80) * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 0, "parameters": ["let y = (event.screenY() - 124 ) * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['x_value'] = x ;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['y_value'] = y ;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['height_rowCount'] = 2;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['height_value'] = 55 * 2;"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameNumberArray.value(37).length > 4 && !$gameSwitches.value(492)"]}, {"code": 356, "indent": 1, "parameters": [">独立开关 : 本事件 : E : 开启"]}, {"code": 355, "indent": 1, "parameters": ["let day = $gameSystem.day();"]}, {"code": 655, "indent": 1, "parameters": ["$gameSelfVariables.set(this, 'E', day)"]}, {"code": 121, "indent": 1, "parameters": [492, 492, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["噢、同胞哟！"]}, {"code": 401, "indent": 1, "parameters": ["快来收下这个！"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Item3", "volume": 50, "pitch": 90, "pan": 0}]}, {"code": 128, "indent": 1, "parameters": [150, 0, 0, 1, false]}, {"code": 213, "indent": 1, "parameters": [-1, 2, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["这些天阁下为我提供了不少有趣的灵感，"]}, {"code": 401, "indent": 1, "parameters": ["没有谢礼也实在说不过去…"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["这是在下为游戏宣传而设计的服装，"]}, {"code": 401, "indent": 1, "parameters": ["正是代表心意的礼物。"]}, {"code": 355, "indent": 1, "parameters": ["let event = $gamePlayer;"]}, {"code": 655, "indent": 1, "parameters": ["let x = event.screenX() * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 1, "parameters": ["let y = (event.screenY() - 100) * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 1, "parameters": ["$gameSystem._drill_DOp_curStyle['x_value'] = x ;"]}, {"code": 655, "indent": 1, "parameters": ["$gameSystem._drill_DOp_curStyle['y_value'] = y ;"]}, {"code": 655, "indent": 1, "parameters": ["$gameSystem._drill_DOp_curStyle['height_rowCount'] = 2;"]}, {"code": 655, "indent": 1, "parameters": ["$gameSystem._drill_DOp_curStyle['height_value'] = 64 * 2;"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["（诶…特典T恤…怎么办…"]}, {"code": 401, "indent": 1, "parameters": ["我可不想穿这种东西出门啊…）"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["（不知道妹妹会喜欢这种衣服吗……）"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 500, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["——噢噢！又见面了、"]}, {"code": 401, "indent": 1, "parameters": ["同胞哟！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["如何、有从在下的愚作中得到什么感想吗？"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["——噢噢！又见面了、"]}, {"code": 401, "indent": 1, "parameters": ["同胞哟！"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["卡扭蛋适配"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameMap.drill_COET_getEventsByTag_direct(\"扭蛋\").length > 0"]}, {"code": 355, "indent": 1, "parameters": ["var gachaArray = $gameMap.drill_COET_getEventsByTag_direct(\"扭蛋\");"]}, {"code": 655, "indent": 1, "parameters": ["var throwArray = [];"]}, {"code": 655, "indent": 1, "parameters": ["var EID = this._eventId;"]}, {"code": 655, "indent": 1, "parameters": ["gachaArray.forEach(function(i) {"]}, {"code": 655, "indent": 1, "parameters": ["if ( $gameMap.event(EID).calcDistance(i._eventId) < 48 ) {"]}, {"code": 655, "indent": 1, "parameters": ["throwArray.push(i._eventId);"]}, {"code": 655, "indent": 1, "parameters": ["     }"]}, {"code": 655, "indent": 1, "parameters": ["});"]}, {"code": 655, "indent": 1, "parameters": ["$gameNumberArray.setValue(20,throwArray);"]}, {"code": 111, "indent": 1, "parameters": [12, "$gameNumberArray.value(20).length > 0"]}, {"code": 213, "indent": 2, "parameters": [0, 1, true]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["这可真是在下粗心了，是在找此物吗？"]}, {"code": 401, "indent": 2, "parameters": ["流落到了在下的脚边真是缘分呢！"]}, {"code": 355, "indent": 2, "parameters": ["this.count = 0"]}, {"code": 355, "indent": 2, "parameters": ["let EID = $gameNumberArray.value(20)[0];"]}, {"code": 655, "indent": 2, "parameters": ["if($gameMap.event(EID)){"]}, {"code": 655, "indent": 2, "parameters": ["let xx = $gamePlayer.screenShootXQJ() / 48;"]}, {"code": 655, "indent": 2, "parameters": ["let yy = $gamePlayer.screenShootYQJ() / 48;"]}, {"code": 655, "indent": 2, "parameters": ["$gameMap.event(EID).smartJumpAbs(xx, yy);"]}, {"code": 655, "indent": 2, "parameters": ["let jumpCount = $gameMap.event(EID)._jumpCount;"]}, {"code": 655, "indent": 2, "parameters": ["this.wait(jumpCount)"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 355, "indent": 2, "parameters": ["let EID = $gameNumberArray.value(20)[0];"]}, {"code": 655, "indent": 2, "parameters": ["$gameNumberArray.value(20).shift();"]}, {"code": 655, "indent": 2, "parameters": ["if($gameMap.event(EID)){"]}, {"code": 655, "indent": 2, "parameters": ["$gameMap.event(EID).steupCEQJ(2);"]}, {"code": 655, "indent": 2, "parameters": ["this.count = 1;"]}, {"code": 655, "indent": 2, "parameters": ["} "]}, {"code": 230, "indent": 2, "parameters": [120]}, {"code": 111, "indent": 2, "parameters": [12, "this.count === 1"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["如何、有为阁下带来什么幸运之物吗？"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 115, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["选项框适配"]}, {"code": 355, "indent": 0, "parameters": ["$gameSystem._drill_DCB_enable = true;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DCB_curStyle = 30;"]}, {"code": 655, "indent": 0, "parameters": ["DrillUp.g_DCB_data[29].x = $gamePlayer.screenX() + 64;"]}, {"code": 655, "indent": 0, "parameters": ["DrillUp.g_DCB_data[29].x *= $gameScreen.zoomScale();"]}, {"code": 655, "indent": 0, "parameters": ["DrillUp.g_DCB_data[29].y = $gamePlayer.screenY() - 24;"]}, {"code": 655, "indent": 0, "parameters": ["DrillUp.g_DCB_data[29].y *= $gameScreen.zoomScale();"]}, {"code": 102, "indent": 0, "parameters": [["<<$gameParty.hasItem($dataItems[15])>>买东西", "「为什么是同胞？」", "<<!$gameParty.hasItem($dataItems[15])>>分享见闻"], -2, -1, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "<<$gameParty.hasItem($dataItems[15])>>买东西"]}, {"code": 111, "indent": 1, "parameters": [12, "$gameParty.gold() >= 5000"]}, {"code": 125, "indent": 2, "parameters": [1, 0, 5000]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["阁下真是神速啊、"]}, {"code": 401, "indent": 2, "parameters": ["这么快便能调度出资金来！"]}, {"code": 250, "indent": 2, "parameters": [{"name": "キラーン（ひらめき・アイテム発見・獲得）", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 126, "indent": 2, "parameters": [15, 0, 0, 1]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["这样契约变成立了。"]}, {"code": 401, "indent": 2, "parameters": ["在下期待着阁下游玩后的评价。"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["如果阁下还能将冒险的见闻分享给在下，"]}, {"code": 401, "indent": 2, "parameters": ["在下也能从中汲取灵感继续创作。"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["阁下有些操之过急啊、"]}, {"code": 401, "indent": 2, "parameters": ["金额这不是还没达到要求吗。"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["无需担心、在下一直会都在此地等待着阁下的到来。"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["let event = $gameMap.event(this._eventId);"]}, {"code": 655, "indent": 1, "parameters": ["if (event && event._drill_EASe_controller !== undefined) {"]}, {"code": 655, "indent": 1, "parameters": ["event.drill_EASe_setSimpleStateNode( [\"记笔记\"] );"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "「为什么是同胞？」"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["哈哈哈……！"]}, {"code": 401, "indent": 1, "parameters": ["同为追寻大迷宫深处真相之人、"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["彼此便是同胞，不是这样吗？"]}, {"code": 401, "indent": 1, "parameters": ["哇哈哈哈……！"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "<<!$gameParty.hasItem($dataItems[15])>>分享见闻"]}, {"code": 123, "indent": 1, "parameters": ["C", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 403, "indent": 0, "parameters": [6, null]}, {"code": 111, "indent": 1, "parameters": [12, "!$gameParty.hasItem($dataItems[15])"]}, {"code": 355, "indent": 2, "parameters": ["let event = $gameMap.event(this._eventId);"]}, {"code": 655, "indent": 2, "parameters": ["if (event && event._drill_EASe_controller !== undefined) {"]}, {"code": 655, "indent": 2, "parameters": ["event.drill_EASe_setSimpleStateNode( [\"注意\"] );"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 16, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$knight", "direction": 6, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>持续动作 : 缩放状态 : 缩放比例[1.25]"]}, {"code": 108, "indent": 0, "parameters": ["<<出现条件>> : 独立开关 : E : 为ON"]}, {"code": 108, "indent": 0, "parameters": ["<Group:\"NPC\">"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameSelfVariables.get(this, 'E') === 0"]}, {"code": 355, "indent": 1, "parameters": ["let day = $gameSystem.day() + 1;"]}, {"code": 655, "indent": 1, "parameters": ["$gameSelfVariables.set(this, 'E', day)"]}, {"code": 230, "indent": 1, "parameters": [4]}, {"code": 355, "indent": 1, "parameters": ["$gameMap.event(23).steupCEQJ(3)"]}, {"code": 230, "indent": 1, "parameters": [4]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [4]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$gameSelfVariables.get(this, 'E') < $gameSystem.day()"]}, {"code": 205, "indent": 2, "parameters": [0, {"list": [{"code": 25, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 25, "indent": null}]}, {"code": 356, "indent": 2, "parameters": [">对话框变形器 : 切换形状 : 变形样式[8]"]}, {"code": 356, "indent": 2, "parameters": [">对话框皮肤 : 只对话框 : 修改样式 : 样式[9]"]}, {"code": 355, "indent": 2, "parameters": ["let event = $gameMap.event(this._eventId);"]}, {"code": 655, "indent": 2, "parameters": ["let x = (event.screenX() - 80) * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 2, "parameters": ["let y = (event.screenY() - 124 ) * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 2, "parameters": ["$gameSystem._drill_DOp_curStyle['x_value'] = x ;"]}, {"code": 655, "indent": 2, "parameters": ["$gameSystem._drill_DOp_curStyle['y_value'] = y ;"]}, {"code": 655, "indent": 2, "parameters": ["$gameSystem._drill_DOp_curStyle['height_rowCount'] = 2;"]}, {"code": 655, "indent": 2, "parameters": ["$gameSystem._drill_DOp_curStyle['height_value'] = 55 * 2;"]}, {"code": 213, "indent": 2, "parameters": [0, 3, true]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["噢噢——！同胞哟！来得正好、"]}, {"code": 401, "indent": 2, "parameters": ["在下已经很久没有痛快地创作了！"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["灵感如泉涌！"]}, {"code": 401, "indent": 2, "parameters": ["如此一来，在下的作品也要进入尾声了。"]}, {"code": 250, "indent": 2, "parameters": [{"name": "Item3", "volume": 50, "pitch": 90, "pan": 0}]}, {"code": 126, "indent": 2, "parameters": [16, 0, 0, 1]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["快收下！想必阁下已经"]}, {"code": 401, "indent": 2, "parameters": ["迫不及待地想要体验一下了吧！"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 213, "indent": 2, "parameters": [-1, 6, true]}, {"code": 355, "indent": 2, "parameters": ["let event = $gamePlayer;"]}, {"code": 655, "indent": 2, "parameters": ["let x = event.screenX() * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 2, "parameters": ["let y = (event.screenY() - 100) * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 2, "parameters": ["$gameSystem._drill_DOp_curStyle['x_value'] = x ;"]}, {"code": 655, "indent": 2, "parameters": ["$gameSystem._drill_DOp_curStyle['y_value'] = y ;"]}, {"code": 655, "indent": 2, "parameters": ["$gameSystem._drill_DOp_curStyle['height_rowCount'] = 2;"]}, {"code": 655, "indent": 2, "parameters": ["$gameSystem._drill_DOp_curStyle['height_value'] = 64 * 2;"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["（并没有什么兴趣什么的，说不出来口啊…\\w[40]"]}, {"code": 401, "indent": 2, "parameters": ["嘛，妹妹肯定是会开心的吧…）"]}, {"code": 356, "indent": 2, "parameters": [">对话框变形器 : 切换形状 : 变形样式[8]"]}, {"code": 356, "indent": 2, "parameters": [">对话框皮肤 : 只对话框 : 修改样式 : 样式[9]"]}, {"code": 355, "indent": 2, "parameters": ["let event = $gameMap.event(this._eventId);"]}, {"code": 655, "indent": 2, "parameters": ["let x = (event.screenX() - 80) * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 2, "parameters": ["let y = (event.screenY() - 124 ) * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 2, "parameters": ["$gameSystem._drill_DOp_curStyle['x_value'] = x ;"]}, {"code": 655, "indent": 2, "parameters": ["$gameSystem._drill_DOp_curStyle['y_value'] = y ;"]}, {"code": 655, "indent": 2, "parameters": ["$gameSystem._drill_DOp_curStyle['height_rowCount'] = 2;"]}, {"code": 655, "indent": 2, "parameters": ["$gameSystem._drill_DOp_curStyle['height_value'] = 55 * 2;"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["另外，在下也已经下定决心要进入深渊了。"]}, {"code": 401, "indent": 2, "parameters": ["果然只是一昧地听闻他人的冒险故事并不能让在下满足…！"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["而且阁下还肩负着为了妹妹的使命吧，这样在下"]}, {"code": 401, "indent": 2, "parameters": ["更不能继续麻烦阁下了。"]}, {"code": 230, "indent": 2, "parameters": [4]}, {"code": 356, "indent": 2, "parameters": [">独立开关 : 本事件 : E : 关闭"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 230, "indent": 2, "parameters": [4]}, {"code": 355, "indent": 2, "parameters": ["$gameMap.event(23).steupCEQJ(3)"]}, {"code": 230, "indent": 2, "parameters": [4]}, {"code": 115, "indent": 2, "parameters": []}, {"code": 230, "indent": 2, "parameters": [4]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "C", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$knight", "direction": 6, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>持续动作 : 缩放状态 : 缩放比例[1.25]"]}, {"code": 108, "indent": 0, "parameters": ["见闻实绩计算"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameNumberArray.value(36).length === 0"]}, {"code": 213, "indent": 1, "parameters": [-1, 6, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["(好像也没什么发现值得分享的…）"]}, {"code": 123, "indent": 1, "parameters": ["C", 1]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["let event = $gameMap.event(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["if (event && event._drill_EASe_controller !== undefined) {"]}, {"code": 655, "indent": 0, "parameters": ["event.drill_EASe_setSimpleStateNode( [\"记笔记\"] );"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["随机选取见闻进行报告"]}, {"code": 355, "indent": 0, "parameters": ["let array1 = $gameNumberArray.value(36);"]}, {"code": 655, "indent": 0, "parameters": ["let array2 = $gameNumberArray.value(37);"]}, {"code": 655, "indent": 0, "parameters": ["array1 = array1.filter(value => !array2.includes(value));"]}, {"code": 655, "indent": 0, "parameters": ["$gameNumberArray.setValue(36,array1);"]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 355, "indent": 0, "parameters": ["var type = $gameNumberArray.value(36);"]}, {"code": 655, "indent": 0, "parameters": ["var randomIndex = Math.floor(Math.random() * type.length);"]}, {"code": 655, "indent": 0, "parameters": ["var randomType = type[randomIndex];"]}, {"code": 655, "indent": 0, "parameters": ["this.count = randomType"]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 111, "indent": 0, "parameters": [12, "this.count === 0"]}, {"code": 108, "indent": 1, "parameters": ["玩游戏的感想"]}, {"code": 355, "indent": 1, "parameters": ["$gameNumberArray.value(37).push(0)"]}, {"code": 213, "indent": 1, "parameters": [-1, 8, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["分享了妹妹玩游戏的一些感想。"]}, {"code": 355, "indent": 1, "parameters": ["let event = $gameMap.event(this._eventId);"]}, {"code": 655, "indent": 1, "parameters": ["if (event && event._drill_EASe_controller !== undefined) {"]}, {"code": 655, "indent": 1, "parameters": ["event.drill_EASe_setAct( \"点头\" );"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 213, "indent": 1, "parameters": [0, 3, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["噢噢！能得到阁下妹妹的赞美，真是无比荣幸！"]}, {"code": 401, "indent": 1, "parameters": ["这样在下就有更多动力来进行更新了！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["说来还没听到阁下本人的评价呢……\\w[45]"]}, {"code": 401, "indent": 1, "parameters": ["嗯？又大…又白…很柔软？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["……在下的游戏中有能让阁下产生"]}, {"code": 401, "indent": 1, "parameters": ["这种感想的内容吗？"]}, {"code": 355, "indent": 1, "parameters": ["let event = $gamePlayer;"]}, {"code": 655, "indent": 1, "parameters": ["let x = event.screenX() * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 1, "parameters": ["let y = (event.screenY() - 100) * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 1, "parameters": ["$gameSystem._drill_DOp_curStyle['x_value'] = x ;"]}, {"code": 655, "indent": 1, "parameters": ["$gameSystem._drill_DOp_curStyle['y_value'] = y ;"]}, {"code": 655, "indent": 1, "parameters": ["$gameSystem._drill_DOp_curStyle['height_rowCount'] = 2;"]}, {"code": 655, "indent": 1, "parameters": ["$gameSystem._drill_DOp_curStyle['height_value'] = 64 * 2;"]}, {"code": 213, "indent": 1, "parameters": [-1, 6, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["不好、一时沉浸在妹妹的胸部手感里说错话了…"]}, {"code": 123, "indent": 1, "parameters": ["C", 1]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "this.count === 1"]}, {"code": 108, "indent": 1, "parameters": ["行走菇很好吃"]}, {"code": 355, "indent": 1, "parameters": ["$gameNumberArray.value(37).push(1)"]}, {"code": 213, "indent": 1, "parameters": [-1, 8, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["分享了行走菇身上的蘑菇"]}, {"code": 401, "indent": 1, "parameters": ["其实很好吃的情报。"]}, {"code": 355, "indent": 1, "parameters": ["let event = $gameMap.event(this._eventId);"]}, {"code": 655, "indent": 1, "parameters": ["if (event && event._drill_EASe_controller !== undefined) {"]}, {"code": 655, "indent": 1, "parameters": ["event.drill_EASe_setAct( \"点头\" );"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 213, "indent": 1, "parameters": [0, 1, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["魔物料理吗！这可真是崭新的思路！"]}, {"code": 401, "indent": 1, "parameters": ["真不愧是阁下。"]}, {"code": 123, "indent": 1, "parameters": ["C", 1]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "this.count === 2"]}, {"code": 108, "indent": 1, "parameters": ["怪鸟的咆哮很吵"]}, {"code": 355, "indent": 1, "parameters": ["$gameNumberArray.value(37).push(2)"]}, {"code": 213, "indent": 1, "parameters": [-1, 8, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["分享了迷宫里的怪鸟叫声非常吵的情报。"]}, {"code": 355, "indent": 1, "parameters": ["let event = $gameMap.event(this._eventId);"]}, {"code": 655, "indent": 1, "parameters": ["if (event && event._drill_EASe_controller !== undefined) {"]}, {"code": 655, "indent": 1, "parameters": ["event.drill_EASe_setAct( \"点头\" );"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 213, "indent": 1, "parameters": [0, 1, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["迦楼罗的幼体吗…不知道和传说中的龙吼"]}, {"code": 401, "indent": 1, "parameters": ["相比，是谁更胜一筹啊。"]}, {"code": 123, "indent": 1, "parameters": ["C", 1]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "this.count === 3"]}, {"code": 108, "indent": 1, "parameters": ["兽人的肉质鲜美"]}, {"code": 355, "indent": 1, "parameters": ["$gameNumberArray.value(37).push(3)"]}, {"code": 213, "indent": 1, "parameters": [-1, 8, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["分享了兽人的肉非常鲜美的情报。"]}, {"code": 355, "indent": 1, "parameters": ["let event = $gameMap.event(this._eventId);"]}, {"code": 655, "indent": 1, "parameters": ["if (event && event._drill_EASe_controller !== undefined) {"]}, {"code": 655, "indent": 1, "parameters": ["event.drill_EASe_setAct( \"点头\" );"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 213, "indent": 1, "parameters": [0, 1, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["魔物料理吗！这可真是崭新的思路！"]}, {"code": 401, "indent": 1, "parameters": ["真不愧是阁下。"]}, {"code": 123, "indent": 1, "parameters": ["C", 1]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "this.count === 4"]}, {"code": 108, "indent": 1, "parameters": ["迷宫的隐藏通道"]}, {"code": 355, "indent": 1, "parameters": ["$gameNumberArray.value(37).push(4)"]}, {"code": 213, "indent": 1, "parameters": [-1, 8, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["分享了浅层遗迹迷宫有一处隐藏通道"]}, {"code": 401, "indent": 1, "parameters": ["连接着外界的情报。"]}, {"code": 355, "indent": 1, "parameters": ["let event = $gameMap.event(this._eventId);"]}, {"code": 655, "indent": 1, "parameters": ["if (event && event._drill_EASe_controller !== undefined) {"]}, {"code": 655, "indent": 1, "parameters": ["event.drill_EASe_setAct( \"点头\" );"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 213, "indent": 1, "parameters": [0, 1, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["是过去的开发队遗留下来的工程吧？"]}, {"code": 401, "indent": 1, "parameters": ["呀啊…听说他们试图将迷宫挖开完全暴露出来，"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["但工程进展到一半就停下了，"]}, {"code": 401, "indent": 1, "parameters": ["不知道是遇到什么阻碍呢。"]}, {"code": 123, "indent": 1, "parameters": ["C", 1]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "this.count === 5"]}, {"code": 108, "indent": 1, "parameters": ["发现了奇怪的蘑菇屋"]}, {"code": 355, "indent": 1, "parameters": ["$gameNumberArray.value(37).push(5)"]}, {"code": 213, "indent": 1, "parameters": [-1, 8, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["分享了会迷路的森林里尽头里"]}, {"code": 401, "indent": 1, "parameters": ["是奇怪的蘑菇屋的情报。"]}, {"code": 355, "indent": 1, "parameters": ["let event = $gameMap.event(this._eventId);"]}, {"code": 655, "indent": 1, "parameters": ["if (event && event._drill_EASe_controller !== undefined) {"]}, {"code": 655, "indent": 1, "parameters": ["event.drill_EASe_setAct( \"点头\" );"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 213, "indent": 1, "parameters": [0, 1, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["听说很多冒险者都会迷失在里面…"]}, {"code": 401, "indent": 1, "parameters": ["但不愧是阁下，能够顺利突破迷宫。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["不过巨大的蘑菇屋吗…"]}, {"code": 401, "indent": 1, "parameters": ["确实很让在下好奇是什么生物栖息在那呢…"]}, {"code": 123, "indent": 1, "parameters": ["C", 1]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "this.count === 6"]}, {"code": 108, "indent": 1, "parameters": ["摔进了迷宫的洞里"]}, {"code": 355, "indent": 1, "parameters": ["$gameNumberArray.value(37).push(6)"]}, {"code": 213, "indent": 1, "parameters": [-1, 6, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["分享了在迷宫里不幸掉落洞中的体验。"]}, {"code": 355, "indent": 1, "parameters": ["let event = $gameMap.event(this._eventId);"]}, {"code": 655, "indent": 1, "parameters": ["if (event && event._drill_EASe_controller !== undefined) {"]}, {"code": 655, "indent": 1, "parameters": ["event.drill_EASe_setAct( \"点头\" );"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 213, "indent": 1, "parameters": [0, 3, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["哈哈哈…！这还真是令人感到羞愧的体验！"]}, {"code": 401, "indent": 1, "parameters": ["身为冒险者应当时刻警惕周围才行。"]}, {"code": 123, "indent": 1, "parameters": ["C", 1]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "this.count === 7"]}, {"code": 108, "indent": 1, "parameters": ["迷失森林的机甲"]}, {"code": 355, "indent": 1, "parameters": ["$gameNumberArray.value(37).push(7)"]}, {"code": 213, "indent": 1, "parameters": [-1, 8, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["分享了在不知名的区域发现了一台机甲的情报。"]}, {"code": 355, "indent": 1, "parameters": ["let event = $gameMap.event(this._eventId);"]}, {"code": 655, "indent": 1, "parameters": ["if (event && event._drill_EASe_controller !== undefined) {"]}, {"code": 655, "indent": 1, "parameters": ["event.drill_EASe_setAct( \"点头\" );"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 213, "indent": 1, "parameters": [0, 1, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["竟然还有这种奇遇……！"]}, {"code": 401, "indent": 1, "parameters": ["大迷宫境内居然还有古代文明的遗物吗？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["……已经不见了吗？在下不会怀疑阁下的说辞，"]}, {"code": 401, "indent": 1, "parameters": ["这一定某种命运的因缘，日后一定还会再次出现的。"]}, {"code": 123, "indent": 1, "parameters": ["C", 1]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "this.count === 8"]}, {"code": 108, "indent": 1, "parameters": ["史莱姆果冻"]}, {"code": 355, "indent": 1, "parameters": ["$gameNumberArray.value(37).push(8)"]}, {"code": 213, "indent": 1, "parameters": [-1, 8, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["分享了用史莱姆做的果冻"]}, {"code": 401, "indent": 1, "parameters": ["意外地口感还不错的情报。"]}, {"code": 355, "indent": 1, "parameters": ["let event = $gameMap.event(this._eventId);"]}, {"code": 655, "indent": 1, "parameters": ["if (event && event._drill_EASe_controller !== undefined) {"]}, {"code": 655, "indent": 1, "parameters": ["event.drill_EASe_setAct( \"点头\" );"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 213, "indent": 1, "parameters": [0, 1, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["魔物料理吗！这可真是崭新的思路！"]}, {"code": 401, "indent": 1, "parameters": ["真不愧是阁下。"]}, {"code": 123, "indent": 1, "parameters": ["C", 1]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 16, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$letter", "direction": 4, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<<出现条件>> : 独立开关 : F : 为ON"]}, {"code": 108, "indent": 0, "parameters": ["=>事件说明窗口 : 显示下列文本"]}, {"code": 408, "indent": 0, "parameters": ["=:\\px[50]\\py[9]\\fs[18]\\c[110]✦\\fi「深渊里相见吧……！同胞哟！」"]}, {"code": 108, "indent": 0, "parameters": ["=>事件说明窗口 : 激活方式 : 鼠标接近"]}, {"code": 108, "indent": 0, "parameters": ["=>事件说明窗口 : 设置皮肤样式 : 样式[2]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}], "x": 16, "y": 6}, {"id": 24, "name": "EV024", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 0, "characterIndex": 1}, "list": [{"code": 230, "indent": 0, "parameters": [15]}, {"code": 111, "indent": 0, "parameters": [12, "Utils.isOptionValid(\"test\")"]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "$gameSelfSwitches.value([$gameMap.mapId(), 22, 'B'])"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "$gameMap.regionId($gamePlayer._realX + $gamePlayer.offsetX(), $gamePlayer._realY -0.3) === 35"]}, {"code": 356, "indent": 1, "parameters": [">独立开关 : 事件[23] : A : 开启"]}, {"code": 230, "indent": 1, "parameters": [15]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 21, "y": 6}, {"id": 25, "name": "自动贩售机Sh", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "$VendingMachineA", "direction": 6, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<width: 1.5>"]}, {"code": 408, "indent": 0, "parameters": ["<height: 1>"]}, {"code": 408, "indent": 0, "parameters": ["<offsetX: +0.25>"]}, {"code": 408, "indent": 0, "parameters": ["<offsetY: 0>"]}, {"code": 355, "indent": 0, "parameters": ["$gameSelfVariables.set(this, 'enemyId', 5);"]}, {"code": 655, "indent": 0, "parameters": ["$gameSelfVariables.add(this, 'Revival count', 1)"]}, {"code": 355, "indent": 0, "parameters": ["let extra = $gameSelfVariables.get(this, 'Revival count');"]}, {"code": 655, "indent": 0, "parameters": ["extra = 10 + extra * 10;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSelfVariables.set(this, 'enemyLevel', extra);"]}, {"code": 355, "indent": 0, "parameters": ["let eid = $gameSelfVariables.get(this, 'enemyId');"]}, {"code": 655, "indent": 0, "parameters": ["let enemyLevel = $gameSelfVariables.get(this, 'enemyLevel');"]}, {"code": 655, "indent": 0, "parameters": ["let enemy = $dataEnemies[eid];"]}, {"code": 655, "indent": 0, "parameters": ["if (!enemy) enemy = $dataEnemies[3];"]}, {"code": 655, "indent": 0, "parameters": ["let Hp = enemy.params[0];"]}, {"code": 655, "indent": 0, "parameters": ["Hp = Math.floor(Hp * ((100 + enemyLevel) / 100));"]}, {"code": 655, "indent": 0, "parameters": ["$gameSelfVariables.set(this, 'HP', Hp);"]}, {"code": 655, "indent": 0, "parameters": ["$gameSelfVariables.set(this, 'MHP', Hp);"]}, {"code": 355, "indent": 0, "parameters": ["$gameSelfVariables.set(this, 'fudou', 90)"]}, {"code": 356, "indent": 0, "parameters": [">多帧行走图 : 本事件 : 帧数 : 3"]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 3, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "$VendingMachineA", "direction": 6, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字自动显现 : 触发范围 : 固定自动显现范围 : 横向图块[3] : 纵向图块[3]"]}, {"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字自动显现 : 开启"]}, {"code": 108, "indent": 0, "parameters": ["=>事件漂浮背景 : 设置背景 : 4"]}, {"code": 108, "indent": 0, "parameters": ["=>事件漂浮背景 : 背景偏移 : 0 : -64"]}, {"code": 108, "indent": 0, "parameters": ["=>标签核心 : 添加标签 : 免疫异常"]}, {"code": 108, "indent": 0, "parameters": ["<BoxType:['R',42,72]>"]}, {"code": 108, "indent": 0, "parameters": ["<BoxOffset:12,-24>"]}, {"code": 108, "indent": 0, "parameters": ["<Group:\"object\">"]}, {"code": 111, "indent": 0, "parameters": [6, -1, 8]}, {"code": 356, "indent": 1, "parameters": [">对话框变形器 : 切换形状 : 变形样式[8]"]}, {"code": 356, "indent": 1, "parameters": [">对话框皮肤 : 只对话框 : 修改样式 : 样式[9]"]}, {"code": 355, "indent": 1, "parameters": ["let event = $gamePlayer;"]}, {"code": 655, "indent": 1, "parameters": ["let x = (event.screenX() - 72) * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 1, "parameters": ["let y = (event.screenY() - 86) * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 1, "parameters": ["$gameSystem._drill_DOp_curStyle['x_value'] = x ;"]}, {"code": 655, "indent": 1, "parameters": ["$gameSystem._drill_DOp_curStyle['y_value'] = y ;"]}, {"code": 655, "indent": 1, "parameters": ["$gameSystem._drill_DOp_curStyle['height_rowCount'] = 2;"]}, {"code": 655, "indent": 1, "parameters": ["$gameSystem._drill_DOp_curStyle['height_value'] = 52 * 2;"]}, {"code": 108, "indent": 1, "parameters": ["选项框适配"]}, {"code": 355, "indent": 1, "parameters": ["$gameSystem._drill_DCB_enable = true;"]}, {"code": 655, "indent": 1, "parameters": ["$gameSystem._drill_DCB_curStyle = 30;"]}, {"code": 655, "indent": 1, "parameters": ["DrillUp.g_DCB_data[29].x = $gamePlayer.screenX() + 64;"]}, {"code": 655, "indent": 1, "parameters": ["DrillUp.g_DCB_data[29].x *= $gameScreen.zoomScale();"]}, {"code": 655, "indent": 1, "parameters": ["DrillUp.g_DCB_data[29].y = $gamePlayer.screenY() - 12;"]}, {"code": 655, "indent": 1, "parameters": ["DrillUp.g_DCB_data[29].y *= $gameScreen.zoomScale();"]}, {"code": 355, "indent": 1, "parameters": ["var ratio = $gameSelfVariables.get(this, 'Revival count');"]}, {"code": 655, "indent": 1, "parameters": ["var price = 50 * 2 ** ratio;"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.setValue(86, price);"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["这里有一台奇怪的自动贩售机……"]}, {"code": 401, "indent": 1, "parameters": ["要投\\c[6]\\v[86]\\c[0]元进去试试看吗？"]}, {"code": 102, "indent": 1, "parameters": [["试试", "算了"], 1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "试试"]}, {"code": 111, "indent": 2, "parameters": [12, "$gameParty.gold() >= $gameVariables.value(86)"]}, {"code": 125, "indent": 3, "parameters": [1, 1, 86]}, {"code": 123, "indent": 3, "parameters": ["B", 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["但身上没有足够的钱……"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "算了"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 1, "moveRoute": {"list": [{"code": 16, "indent": null}, {"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "$VendingMachineA", "direction": 6, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字自动显现 : 触发范围 : 固定自动显现范围 : 横向图块[3] : 纵向图块[3]"]}, {"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字自动显现 : 开启"]}, {"code": 108, "indent": 0, "parameters": ["=>事件漂浮背景 : 设置背景 : 4"]}, {"code": 108, "indent": 0, "parameters": ["=>事件漂浮背景 : 背景偏移 : 0 : -64"]}, {"code": 355, "indent": 0, "parameters": ["$gameSelfVariables.add(this, 'HP', -5);"]}, {"code": 655, "indent": 0, "parameters": ["$gameSelfSwitches.setValue([4, 44, 'C'], true);"]}, {"code": 250, "indent": 0, "parameters": [{"name": "自動販売機にお金を入れる", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 356, "indent": 0, "parameters": [">持续动作 : 本事件 : 左右震动 : 持续时间[60] : 周期[6] : 震动幅度[2]"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 111, "indent": 0, "parameters": [12, "$gameSelfVariables.get(this, 'HP') <= 0"]}, {"code": 212, "indent": 1, "parameters": [0, 157, false]}, {"code": 230, "indent": 1, "parameters": [10]}, {"code": 123, "indent": 1, "parameters": ["D", 0]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [86, 86, 0, 2, 23, 27]}, {"code": 356, "indent": 0, "parameters": [">持续动作 : 本事件 : 原地小跳 : 持续时间[40] : 周期[40] : 跳跃高度[40]"]}, {"code": 250, "indent": 0, "parameters": [{"name": "自動販売機、手で打つ", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [35]}, {"code": 250, "indent": 0, "parameters": [{"name": "Heal1", "volume": 70, "pitch": 130, "pan": 0}]}, {"code": 111, "indent": 0, "parameters": [4, 1, 3, 40]}, {"code": 355, "indent": 1, "parameters": ["var item = $gameVariables.value(86);"]}, {"code": 655, "indent": 1, "parameters": ["var event = this._eventId;"]}, {"code": 655, "indent": 1, "parameters": ["QJ.MPMZ.itemGiverCharacter(0,item,event,1)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["let event = $gameMap.event(this._eventId);"]}, {"code": 655, "indent": 1, "parameters": ["dingk.Loot.generateVendingMachineDrops(event)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 123, "indent": 0, "parameters": ["B", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 16, "indent": null}, {"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 4, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "$VendingMachineA_failure", "direction": 6, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>多帧行走图 : 帧数 : 6"]}, {"code": 108, "indent": 0, "parameters": ["爆炸的碎片"]}, {"code": 355, "indent": 0, "parameters": ["let day = $gameSystem.day();"]}, {"code": 655, "indent": 0, "parameters": ["$gameSelfVariables.set(this, 'deadDay', day);"]}, {"code": 655, "indent": 0, "parameters": ["$gameSelfSwitches.setValue([4, 44, 'D'], true);"]}, {"code": 212, "indent": 0, "parameters": [0, 157, false]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 355, "indent": 0, "parameters": ["   QJ.MPMZ.Shoot({"]}, {"code": 655, "indent": 0, "parameters": ["        img:\"VendingMachineA_failure1\","]}, {"code": 655, "indent": 0, "parameters": ["        position:[['E',25],['E',25]],"]}, {"code": 655, "indent": 0, "parameters": ["        initialRotation:['S',290],"]}, {"code": 655, "indent": 0, "parameters": ["        imgRotation:['R',12,true],"]}, {"code": 655, "indent": 0, "parameters": ["        moveType:['QC',0,4,144,5],"]}, {"code": 655, "indent": 0, "parameters": ["        existData:[\t"]}, {"code": 655, "indent": 0, "parameters": ["           {t:['Time',35],d:[0,30]}"]}, {"code": 655, "indent": 0, "parameters": ["        ],"]}, {"code": 655, "indent": 0, "parameters": ["    });"]}, {"code": 355, "indent": 0, "parameters": ["this.count = 15"]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "Heal1", "volume": 70, "pitch": 130, "pan": 0}]}, {"code": 355, "indent": 1, "parameters": ["let event = $gameMap.event(this._eventId);"]}, {"code": 655, "indent": 1, "parameters": ["dingk.Loot.generateVendingMachineDrops(event)"]}, {"code": 111, "indent": 1, "parameters": [12, "this.count <= 0"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [6]}, {"code": 355, "indent": 1, "parameters": ["this.count -= 1"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": [">独立开关 : 本事件 : E : 开启"]}, {"code": 115, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 4, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "C", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "$VendingMachineA_failure", "direction": 6, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<<出现条件>> : 独立开关 : E : 为ON"]}, {"code": 108, "indent": 0, "parameters": ["=>多帧行走图 : 帧数 : 6"]}, {"code": 355, "indent": 0, "parameters": ["this.count = 0"]}, {"code": 355, "indent": 0, "parameters": ["let day = $gameSystem.day();"]}, {"code": 655, "indent": 0, "parameters": ["let deadDay = $gameSelfVariables.get(this, 'deadDay');"]}, {"code": 655, "indent": 0, "parameters": ["if (day !== deadDay && $gameVariables.value(60) === 2) {"]}, {"code": 655, "indent": 0, "parameters": ["this.count = 1;"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 111, "indent": 0, "parameters": [12, "this.count === 0"]}, {"code": 355, "indent": 1, "parameters": ["let eventId = this._eventId;"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.event(eventId)._pushableEvent = true"]}, {"code": 230, "indent": 1, "parameters": [999]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 123, "indent": 1, "parameters": ["A", 1]}, {"code": 123, "indent": 1, "parameters": ["B", 1]}, {"code": 123, "indent": 1, "parameters": ["C", 1]}, {"code": 123, "indent": 1, "parameters": ["D", 1]}, {"code": 356, "indent": 1, "parameters": [">独立开关 : 本事件 : E : 关闭"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 4, "walkAnime": false}], "x": 26, "y": 4}, null, {"id": 27, "name": "采集点", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["particle set mysterious_torch_c-EID this mysterious_torch_c def below"]}, {"code": 250, "indent": 0, "parameters": [{"name": "キラーン（ひらめき・アイテム発見・獲得）", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["dingk.Loot.directlyAcquireDrops(21,false)"]}, {"code": 356, "indent": 0, "parameters": ["particle clear mysterious_torch_c-EID"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 30, "y": 4}, {"id": 28, "name": "EV028", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<width: 1>"]}, {"code": 408, "indent": 0, "parameters": ["<height: 5>"]}, {"code": 408, "indent": 0, "parameters": ["<offsetX: 0>"]}, {"code": 408, "indent": 0, "parameters": ["<offsetY: 0>"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameSelfSwitches.value([$gameMap.mapId(), 22, 'B'])"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "Move", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 121, "indent": 0, "parameters": [40, 40, 0]}, {"code": 201, "indent": 0, "parameters": [0, 49, 2, 23, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 2, "walkAnime": true}], "x": 36, "y": 33}, {"id": 29, "name": "扭蛋机Sh", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "$Gacha", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["$gameSelfVariables.set(this, 'fudou', 110);"]}, {"code": 355, "indent": 0, "parameters": ["$gameSelfVariables.set(this, 'HP', 500)"]}, {"code": 355, "indent": 0, "parameters": ["$gameSelfVariables.set(this, 'MHP', 500)"]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 355, "indent": 0, "parameters": ["$gameMap.event(this._eventId).startOpacity(60, 255)"]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "$Gacha", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字自动显现 : 触发范围 : 固定自动显现范围 : 横向图块[3] : 纵向图块[3]"]}, {"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字自动显现 : 开启"]}, {"code": 108, "indent": 0, "parameters": ["=>事件漂浮背景 : 设置背景 : 5"]}, {"code": 108, "indent": 0, "parameters": ["=>事件漂浮背景 : 背景偏移 : 0 : -48"]}, {"code": 108, "indent": 0, "parameters": ["=>标签核心 : 添加标签 : 免疫异常"]}, {"code": 108, "indent": 0, "parameters": ["<Group:\"object\">"]}, {"code": 108, "indent": 0, "parameters": ["=>事件说明窗口 : 显示下列文本"]}, {"code": 408, "indent": 0, "parameters": ["=:\\c[116]神秘抽奖机"]}, {"code": 408, "indent": 0, "parameters": ["=:\\{\\i[16]\\}9"]}, {"code": 408, "indent": 0, "parameters": ["=:\\fs[14]✦\\fi\\c[110]不知为何出现在此，圆滚滚的扭蛋机。"]}, {"code": 408, "indent": 0, "parameters": ["=:\\fs[14]\\fi\\c[110]  会贪婪地吃下冒险者投喂的物品，但意外得很挑食。"]}, {"code": 408, "indent": 0, "parameters": ["=:\\fs[14]\\fi\\c[110]  满意时就会开心地转动起来，最后吐出装有奖励的扭蛋。"]}, {"code": 108, "indent": 0, "parameters": ["=:\\fs[14]\\fi\\c[110]  但要小心不要喂太多东西，虽然是机器，"]}, {"code": 408, "indent": 0, "parameters": ["=:\\fs[14]\\fi\\c[110]  但似乎也会食物中毒…"]}, {"code": 408, "indent": 0, "parameters": ["=:\\fs[14]✦可能掉落:"]}, {"code": 408, "indent": 0, "parameters": ["=:\\ii[1]"]}, {"code": 111, "indent": 0, "parameters": [6, -1, 2]}, {"code": 111, "indent": 1, "parameters": [12, "$gameSelfVariables.get(this, 'initiate') == 1"]}, {"code": 356, "indent": 2, "parameters": [">行走图动画序列 : 本事件 : 播放简单状态元集合 : 集合[扭蛋机未激活]"]}, {"code": 356, "indent": 2, "parameters": [">行走图动画序列 : 本事件 : 播放动作元 : 动作元[扭蛋机转头]"]}, {"code": 230, "indent": 2, "parameters": [20]}, {"code": 250, "indent": 2, "parameters": [{"name": "キラキラした移動、ワープ音_1", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 230, "indent": 2, "parameters": [70]}, {"code": 355, "indent": 2, "parameters": ["$gameSelfVariables.set(this, 'initiate', 0)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 356, "indent": 1, "parameters": [">持续动作 : 本事件 : 原地小跳 : 持续时间[60] : 周期[30] : 跳跃高度[50]"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Jump2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 250, "indent": 1, "parameters": [{"name": "Jump2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["重置状态"]}, {"code": 355, "indent": 1, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 1, "parameters": ["$gameSelfSwitches.setValue([$gameMap.mapId(), eid, 'G'], false);"]}, {"code": 111, "indent": 1, "parameters": [6, -1, 8]}, {"code": 111, "indent": 2, "parameters": [12, "$gameSelfVariables.get(this, 'initiate') == 0"]}, {"code": 250, "indent": 3, "parameters": [{"name": "キラキラした移動、ワープ音_1", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 356, "indent": 3, "parameters": [">行走图动画序列 : 本事件 : 播放简单状态元集合 : 集合[扭蛋机常态]"]}, {"code": 356, "indent": 3, "parameters": [">行走图动画序列 : 本事件 : 播放动作元 : 动作元[激活扭蛋机]"]}, {"code": 230, "indent": 3, "parameters": [60]}, {"code": 355, "indent": 3, "parameters": ["$gameSelfVariables.set(this, 'initiate', 1)"]}, {"code": 115, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [14, 14, 0]}, {"code": 356, "indent": 2, "parameters": [">对话框变形器 : 切换形状 : 变形样式[8]"]}, {"code": 356, "indent": 2, "parameters": [">对话框皮肤 : 只对话框 : 修改样式 : 样式[9]"]}, {"code": 108, "indent": 2, "parameters": ["对话框位置调整"]}, {"code": 355, "indent": 2, "parameters": ["let x = $gamePlayer.screenX() * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 2, "parameters": ["let y = ($gamePlayer.screenY() - 124) * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 2, "parameters": ["$gameSystem._drill_DOp_curStyle['x_value'] = x ;"]}, {"code": 655, "indent": 2, "parameters": ["$gameSystem._drill_DOp_curStyle['y_value'] = y ;"]}, {"code": 655, "indent": 2, "parameters": ["$gameSystem._drill_DOp_curStyle['height_rowCount'] = 2;"]}, {"code": 655, "indent": 2, "parameters": ["$gameSystem._drill_DOp_curStyle['height_value'] = 52 * 2;"]}, {"code": 108, "indent": 2, "parameters": ["选项位置调整"]}, {"code": 355, "indent": 2, "parameters": ["$gameSystem._drill_DCB_enable = true;"]}, {"code": 655, "indent": 2, "parameters": ["$gameSystem._drill_DCB_curStyle = 30;"]}, {"code": 655, "indent": 2, "parameters": ["DrillUp.g_DCB_data[29].x = $gamePlayer.screenX() + 64;"]}, {"code": 655, "indent": 2, "parameters": ["DrillUp.g_DCB_data[29].x *= $gameScreen.zoomScale();"]}, {"code": 655, "indent": 2, "parameters": ["DrillUp.g_DCB_data[29].y = $gamePlayer.screenY() - 72;"]}, {"code": 655, "indent": 2, "parameters": ["DrillUp.g_DCB_data[29].y *= $gameScreen.zoomScale();"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["发现了一台奇怪的扭蛋机…"]}, {"code": 401, "indent": 2, "parameters": ["话说表情和妹妹好像啊…"]}, {"code": 102, "indent": 2, "parameters": [["试试投币", "试试投喂"], -2, -1, 2, 0]}, {"code": 402, "indent": 2, "parameters": [0, "试试投币"]}, {"code": 102, "indent": 3, "parameters": [["\\ii[6]", "<<$gameParty.gold() < 500>>\\ii[7]", "<<$gameParty.gold() < 1000>>\\ii[8]", "<<$gameParty.gold() < 5000>>\\ii[9]", "<<$gameParty.gold() < 10000>>\\ii[10]"], -2, 0, 2, 0]}, {"code": 402, "indent": 3, "parameters": [0, "\\ii[6]"]}, {"code": 111, "indent": 4, "parameters": [7, 100, 2]}, {"code": 101, "indent": 5, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 5, "parameters": ["没有足够的钱…"]}, {"code": 121, "indent": 5, "parameters": [14, 14, 1]}, {"code": 115, "indent": 5, "parameters": []}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 122, "indent": 4, "parameters": [90, 90, 0, 0, 6]}, {"code": 125, "indent": 4, "parameters": [1, 0, 100]}, {"code": 119, "indent": 4, "parameters": ["开始投喂"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 402, "indent": 3, "parameters": [1, "<<$gameParty.gold() < 500>>\\ii[7]"]}, {"code": 122, "indent": 4, "parameters": [90, 90, 0, 0, 7]}, {"code": 125, "indent": 4, "parameters": [1, 0, 500]}, {"code": 119, "indent": 4, "parameters": ["开始投喂"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 402, "indent": 3, "parameters": [2, "<<$gameParty.gold() < 1000>>\\ii[8]"]}, {"code": 122, "indent": 4, "parameters": [90, 90, 0, 0, 8]}, {"code": 125, "indent": 4, "parameters": [1, 0, 1000]}, {"code": 119, "indent": 4, "parameters": ["开始投喂"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 402, "indent": 3, "parameters": [3, "<<$gameParty.gold() < 5000>>\\ii[9]"]}, {"code": 122, "indent": 4, "parameters": [90, 90, 0, 0, 9]}, {"code": 125, "indent": 4, "parameters": [1, 0, 5000]}, {"code": 119, "indent": 4, "parameters": ["开始投喂"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 402, "indent": 3, "parameters": [4, "<<$gameParty.gold() < 10000>>\\ii[10]"]}, {"code": 122, "indent": 4, "parameters": [90, 90, 0, 0, 10]}, {"code": 125, "indent": 4, "parameters": [1, 0, 10000]}, {"code": 119, "indent": 4, "parameters": ["开始投喂"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 403, "indent": 3, "parameters": [6, null]}, {"code": 121, "indent": 4, "parameters": [14, 14, 1]}, {"code": 115, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 404, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 402, "indent": 2, "parameters": [1, "试试投喂"]}, {"code": 119, "indent": 3, "parameters": ["开始选择物品"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 403, "indent": 2, "parameters": [6, null]}, {"code": 121, "indent": 3, "parameters": [14, 14, 1]}, {"code": 115, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 404, "indent": 2, "parameters": []}, {"code": 118, "indent": 2, "parameters": ["开始选择物品"]}, {"code": 121, "indent": 2, "parameters": [14, 14, 0]}, {"code": 104, "indent": 2, "parameters": [90, 2]}, {"code": 111, "indent": 2, "parameters": [1, 90, 0, 0, 5]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 3, "parameters": ["要投喂\\ii[\\v[90]]吗？"]}, {"code": 102, "indent": 3, "parameters": [["是的", "算了"], -1, -1, 1, 1]}, {"code": 402, "indent": 3, "parameters": [0, "是的"]}, {"code": 355, "indent": 4, "parameters": ["var itemId = $gameVariables.value(90); "]}, {"code": 655, "indent": 4, "parameters": ["var item = $dataItems[itemId]; "]}, {"code": 655, "indent": 4, "parameters": ["if (item) {"]}, {"code": 655, "indent": 4, "parameters": ["    $gameParty.gainItem(item, -1); "]}, {"code": 655, "indent": 4, "parameters": ["}"]}, {"code": 655, "indent": 4, "parameters": ["if (itemId === 1) {"]}, {"code": 655, "indent": 4, "parameters": ["$gameSelfSwitches.setValue([1, 32, 'pity'], true);"]}, {"code": 655, "indent": 4, "parameters": ["$gameSelfSwitches.setValue([$gameMap.mapId(), this._eventId, 'G'], true);"]}, {"code": 655, "indent": 4, "parameters": ["}"]}, {"code": 118, "indent": 4, "parameters": ["开始投喂"]}, {"code": 355, "indent": 4, "parameters": ["let Id = $gameVariables.value(90);"]}, {"code": 655, "indent": 4, "parameters": ["let Index = $dataItems[Id].iconIndex;"]}, {"code": 655, "indent": 4, "parameters": ["$gameVariables.setValue(90, Index);"]}, {"code": 655, "indent": 4, "parameters": ["let price = $dataItems[Id].price; "]}, {"code": 655, "indent": 4, "parameters": ["var type = price === 0 ? 1 : (price < 500 ? 2 : 0);"]}, {"code": 655, "indent": 4, "parameters": ["$gameSelfVariables.set(this, 'Type', type);"]}, {"code": 655, "indent": 4, "parameters": ["let count = chahuiUtil.gachaNumberRandom(price);"]}, {"code": 655, "indent": 4, "parameters": ["$gameSelfVariables.set(this, 'eggs', count);"]}, {"code": 355, "indent": 4, "parameters": ["var xx = $gamePlayer.centerRealX() - 0.5;"]}, {"code": 655, "indent": 4, "parameters": ["var yy = $gamePlayer.centerRealY() - 1.2;"]}, {"code": 655, "indent": 4, "parameters": ["var eid = $gameMap.spawnEventQJ(1,36,xx,yy);"]}, {"code": 655, "indent": 4, "parameters": ["$gameSystem._drill_EDu_last_id = eid;"]}, {"code": 356, "indent": 4, "parameters": [">行走图动画序列 : 玩家 : 播放简单状态元集合 : 集合[举物中]"]}, {"code": 230, "indent": 4, "parameters": [30]}, {"code": 250, "indent": 4, "parameters": [{"name": "037myuu_YumeSE_FukidashiHatena01", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 4, "parameters": [">行走图动画序列 : 本事件 : 播放动作元 : 动作元[扭蛋机投喂]"]}, {"code": 230, "indent": 4, "parameters": [290]}, {"code": 356, "indent": 4, "parameters": [">持续动作 : 本事件 : 上下震动 : 持续时间[30] : 周期[18] : 震动幅度[1]"]}, {"code": 121, "indent": 4, "parameters": [14, 14, 1]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 402, "indent": 3, "parameters": [1, "算了"]}, {"code": 121, "indent": 4, "parameters": [14, 14, 1]}, {"code": 115, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 404, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 121, "indent": 3, "parameters": [14, 14, 1]}, {"code": 115, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["var type = $gameSelfVariables.get(this, 'Type');"]}, {"code": 655, "indent": 2, "parameters": ["if (type === 1) {"]}, {"code": 655, "indent": 2, "parameters": ["var count = Math.randomInt(3) + 3;"]}, {"code": 655, "indent": 2, "parameters": ["$gameSelfVariables.set(this, 'eggs', count)"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 123, "indent": 2, "parameters": ["B", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "$Gacha", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字自动显现 : 触发范围 : 固定自动显现范围 : 横向图块[3] : 纵向图块[3]"]}, {"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字自动显现 : 开启"]}, {"code": 108, "indent": 0, "parameters": ["=>事件漂浮背景 : 设置背景 : 5"]}, {"code": 108, "indent": 0, "parameters": ["=>事件漂浮背景 : 背景偏移 : 0 : -64"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Open5", "volume": 100, "pitch": 80, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": [">行走图动画序列 : 本事件 : 播放简单状态元集合 : 集合[扭蛋机运作中]"]}, {"code": 356, "indent": 0, "parameters": [">行走图动画序列 : 本事件 : 播放动作元 : 动作元[启动扭蛋机]"]}, {"code": 230, "indent": 0, "parameters": [92]}, {"code": 356, "indent": 0, "parameters": [">持续动作 : 本事件 : 左右震动 : 持续时间[无限] : 周期[6] : 震动幅度[1]"]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 111, "indent": 0, "parameters": [12, "$gameSelfVariables.get(this, 'HP') <= 0"]}, {"code": 123, "indent": 1, "parameters": ["D", 0]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": [">持续动作 : 本事件 : 原地小跳 : 持续时间[30] : 周期[30] : 跳跃高度[50]"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Jump2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 111, "indent": 0, "parameters": [12, "$gameSelfVariables.get(this, 'Type') === 0"]}, {"code": 356, "indent": 1, "parameters": [">行走图动画序列 : 本事件 : 播放简单状态元集合 : 集合[扭蛋机常态]"]}, {"code": 356, "indent": 1, "parameters": [">行走图动画序列 : 本事件 : 播放动作元 : 动作元[扭蛋结束-好结果]"]}, {"code": 112, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [12, "$gameSelfVariables.get(this, 'eggs') > 0"]}, {"code": 250, "indent": 3, "parameters": [{"name": "ぴょん！(ジャンプする音) 3", "volume": 75, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 3, "parameters": ["var eventID = this._eventId;"]}, {"code": 655, "indent": 3, "parameters": ["var xx = $gameMap.event(eventID)._x + 0.15;"]}, {"code": 655, "indent": 3, "parameters": ["var yy = $gameMap.event(eventID)._y + 0.3;"]}, {"code": 655, "indent": 3, "parameters": ["var eid = $gameMap.spawnEventQJ(1,31,xx,yy,true);"]}, {"code": 655, "indent": 3, "parameters": ["$gameMap.event(eid)._opacity = 0;"]}, {"code": 655, "indent": 3, "parameters": ["let value = -1;"]}, {"code": 655, "indent": 3, "parameters": ["$gameSelfVariables.setValue([$gameMap._mapId, eid, \"Type\"], value)"]}, {"code": 230, "indent": 3, "parameters": [12]}, {"code": 355, "indent": 3, "parameters": ["$gameSelfVariables.add(this, 'eggs', -1)"]}, {"code": 355, "indent": 3, "parameters": ["if (!$gameSelfSwitches.value([$gameMap.mapId(), this._eventId, 'G'])) {"]}, {"code": 655, "indent": 3, "parameters": ["let damage = Math.randomInt(11) + 5;"]}, {"code": 655, "indent": 3, "parameters": ["$gameSelfVariables.add(this, 'HP', -damage);"]}, {"code": 655, "indent": 3, "parameters": ["}"]}, {"code": 111, "indent": 3, "parameters": [12, "$gameSelfVariables.get(this, 'HP') <= 0"]}, {"code": 123, "indent": 4, "parameters": ["D", 0]}, {"code": 115, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 113, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 413, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [120]}, {"code": 123, "indent": 1, "parameters": ["B", 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$gameSelfVariables.get(this, 'Type') === 1"]}, {"code": 356, "indent": 2, "parameters": [">行走图动画序列 : 本事件 : 播放简单状态元集合 : 集合[扭蛋机常态]"]}, {"code": 356, "indent": 2, "parameters": [">行走图动画序列 : 本事件 : 播放动作元 : 动作元[扭蛋结束-坏结果]"]}, {"code": 112, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [12, "$gameSelfVariables.get(this, 'eggs') > 0"]}, {"code": 250, "indent": 4, "parameters": [{"name": "ぴょん！(ジャンプする音) 3", "volume": 75, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 4, "parameters": ["var eventID = this._eventId;"]}, {"code": 655, "indent": 4, "parameters": ["var xx = $gameMap.event(eventID)._x + 0.15;"]}, {"code": 655, "indent": 4, "parameters": ["var yy = $gameMap.event(eventID)._y + 0.3;"]}, {"code": 655, "indent": 4, "parameters": ["var eid = $gameMap.spawnEventQJ(1,37,xx,yy);"]}, {"code": 655, "indent": 4, "parameters": ["$gameMap.event(eid)._opacity = 0;"]}, {"code": 230, "indent": 4, "parameters": [12]}, {"code": 355, "indent": 4, "parameters": ["$gameSelfVariables.add(this, 'eggs', -1)"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 113, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 413, "indent": 2, "parameters": []}, {"code": 230, "indent": 2, "parameters": [120]}, {"code": 123, "indent": 2, "parameters": ["B", 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [12, "$gameSelfVariables.get(this, 'Type') === 2"]}, {"code": 356, "indent": 3, "parameters": [">行走图动画序列 : 本事件 : 播放简单状态元集合 : 集合[扭蛋机常态]"]}, {"code": 356, "indent": 3, "parameters": [">行走图动画序列 : 本事件 : 播放动作元 : 动作元[扭蛋结束-一般结果]"]}, {"code": 355, "indent": 3, "parameters": ["$gameSelfVariables.set(this, 'eggs', 0)"]}, {"code": 230, "indent": 3, "parameters": [120]}, {"code": 123, "indent": 3, "parameters": ["B", 1]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [12, "$gameSelfVariables.get(this, 'Type') == 3"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 111, "indent": 4, "parameters": [12, "$gameSelfVariables.get(this, 'Type') == 4"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "$Gacha", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 111, "indent": 0, "parameters": [2, "C", 0]}, {"code": 214, "indent": 1, "parameters": []}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": [">行走图动画序列 : 本事件 : 播放动作元 : 动作元[扭蛋机-爆炸]"]}, {"code": 230, "indent": 0, "parameters": [85]}, {"code": 108, "indent": 0, "parameters": ["概率获得茶点心券"]}, {"code": 355, "indent": 0, "parameters": ["let chance = 0.01;"]}, {"code": 655, "indent": 0, "parameters": ["let eggs = $gameSelfVariables.get(this, 'eggs');"]}, {"code": 655, "indent": 0, "parameters": ["chance += eggs * 0.01;"]}, {"code": 655, "indent": 0, "parameters": ["if (Math.random() < chance) {"]}, {"code": 655, "indent": 0, "parameters": ["let event = $gameMap.event(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["let item = $dataItems[1];"]}, {"code": 655, "indent": 0, "parameters": ["dingk.Loot.getMapDrops(event,item);"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 356, "indent": 0, "parameters": [">持续动作 : 本事件 : 左右震动 : 持续时间[1] : 周期[6] : 震动幅度[1]"]}, {"code": 212, "indent": 0, "parameters": [0, 157, false]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 356, "indent": 0, "parameters": ["particle set smoke_c-EID this smoke_c"]}, {"code": 356, "indent": 0, "parameters": [">行走图动画序列 : 本事件 : 播放简单状态元集合 : 集合[扭蛋机战损]"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 355, "indent": 0, "parameters": ["this.count = 0;"]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["var eventID = this._eventId;"]}, {"code": 655, "indent": 1, "parameters": ["var xx = $gameMap.event(eventID)._x + 0.15;"]}, {"code": 655, "indent": 1, "parameters": ["var yy = $gameMap.event(eventID)._y - 0.2;"]}, {"code": 655, "indent": 1, "parameters": ["var eid = $gameMap.spawnEventQJ(1,33,xx,yy);"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.event(eid)._opacity = 0;"]}, {"code": 355, "indent": 1, "parameters": ["this.count++"]}, {"code": 111, "indent": 1, "parameters": [12, "this.count >= 15"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 230, "indent": 2, "parameters": [4]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 123, "indent": 1, "parameters": ["C", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 355, "indent": 0, "parameters": ["    QJ.MPMZ.Shoot({"]}, {"code": 655, "indent": 0, "parameters": ["        img:\"GACHAmachine_dead[5,15]\","]}, {"code": 655, "indent": 0, "parameters": ["        position:[['E',0],['E',0]],"]}, {"code": 655, "indent": 0, "parameters": ["        initialRotation:['S',0],"]}, {"code": 655, "indent": 0, "parameters": ["        imgRotation:['F'],"]}, {"code": 655, "indent": 0, "parameters": ["        moveType:['S',0],"]}, {"code": 655, "indent": 0, "parameters": ["        anchor:[0.1,1.6],"]}, {"code": 655, "indent": 0, "parameters": ["        existData:[\t"]}, {"code": 655, "indent": 0, "parameters": ["           {t:['Time',74]}"]}, {"code": 655, "indent": 0, "parameters": ["        ],"]}, {"code": 655, "indent": 0, "parameters": ["    });"]}, {"code": 250, "indent": 0, "parameters": [{"name": "<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [74]}, {"code": 355, "indent": 0, "parameters": ["let eventId = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.event(eventId)._pushableEvent = true"]}, {"code": 355, "indent": 0, "parameters": ["    QJ.MPMZ.Shoot({"]}, {"code": 655, "indent": 0, "parameters": ["        img:\"GACHAmachine_dead[2,15]\","]}, {"code": 655, "indent": 0, "parameters": ["        position:[['E',0],['E',0]],"]}, {"code": 655, "indent": 0, "parameters": ["        initialRotation:['S',0],"]}, {"code": 655, "indent": 0, "parameters": ["        imgRotation:['F'],"]}, {"code": 655, "indent": 0, "parameters": ["        moveType:['D',true],"]}, {"code": 655, "indent": 0, "parameters": ["        anchor:[0.1,1.6],"]}, {"code": 655, "indent": 0, "parameters": ["        existData:["]}, {"code": 655, "indent": 0, "parameters": ["        ],"]}, {"code": 655, "indent": 0, "parameters": ["    });"]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 115, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 19, "y": 4}, null, {"id": 31, "name": "EV031", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<width: 1>"]}, {"code": 408, "indent": 0, "parameters": ["<height: 8>"]}, {"code": 408, "indent": 0, "parameters": ["<offsetX: 0>"]}, {"code": 408, "indent": 0, "parameters": ["<offsetY: 0>"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameSelfSwitches.value([$gameMap.mapId(), 22, 'B'])"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "Move", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 201, "indent": 0, "parameters": [0, 9, 4, 16, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 36, "y": 13}, {"id": 32, "name": "旧存档适配", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [60]}, {"code": 355, "indent": 0, "parameters": ["var weapons = $gameParty.weapons();  "]}, {"code": 655, "indent": 0, "parameters": ["for (var j = 0; j < weapons.length; j++) {"]}, {"code": 655, "indent": 0, "parameters": ["    var weapon = weapons[j];"]}, {"code": 655, "indent": 0, "parameters": ["        if (weapon.baseItemId === 61) {"]}, {"code": 655, "indent": 0, "parameters": ["            $gameParty.loseItem(weapon, 1, false);"]}, {"code": 655, "indent": 0, "parameters": ["             $gameParty.gainItem($dataWeapons[61], 1);"]}, {"code": 655, "indent": 0, "parameters": ["                break;"]}, {"code": 655, "indent": 0, "parameters": ["        }"]}, {"code": 655, "indent": 0, "parameters": ["    }"]}, {"code": 355, "indent": 0, "parameters": ["var weapons = $gameParty.weapons();  "]}, {"code": 655, "indent": 0, "parameters": ["for (var j = 0; j < weapons.length; j++) {"]}, {"code": 655, "indent": 0, "parameters": ["    var weapon = weapons[j];"]}, {"code": 655, "indent": 0, "parameters": ["        if (weapon.baseItemId === 15) {"]}, {"code": 655, "indent": 0, "parameters": ["            $gameParty.loseItem(weapon, 1, false);"]}, {"code": 655, "indent": 0, "parameters": ["             $gameParty.gainItem($dataWeapons[15], 1);"]}, {"code": 655, "indent": 0, "parameters": ["                break;"]}, {"code": 655, "indent": 0, "parameters": ["        }"]}, {"code": 655, "indent": 0, "parameters": ["    }"]}, {"code": 230, "indent": 0, "parameters": [4]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 22, "y": 21}, null, {"id": 34, "name": "EV034", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!fsm_chest01", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 36, "indent": null}, {"code": 16, "indent": null}, {"code": 15, "parameters": [4], "indent": null}, {"code": 17, "indent": null}, {"code": 15, "parameters": [4], "indent": null}, {"code": 18, "indent": null}, {"code": 15, "parameters": [4], "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [4]}, {"code": 250, "indent": 0, "parameters": [{"name": "UI系　かっこいい装備音　", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 127, "indent": 0, "parameters": [105, 0, 0, 1, false]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[8]"]}, {"code": 356, "indent": 0, "parameters": [">对话框皮肤 : 只对话框 : 修改样式 : 样式[9]"]}, {"code": 355, "indent": 0, "parameters": ["let event = $gamePlayer;"]}, {"code": 655, "indent": 0, "parameters": ["let x = (event.screenX() - 48 ) * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 0, "parameters": ["let y = (event.screenY() - 100) * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['x_value'] = x ;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['y_value'] = y ;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['height_rowCount'] = 2;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['height_value'] = 64 * 2;"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["里面还有一张纸条……\\w[30]"]}, {"code": 401, "indent": 0, "parameters": ["上面有一段潦草到差点看不清的字："]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\py[24]「里面埋葬着我的青春……」"]}, {"code": 213, "indent": 0, "parameters": [-1, 6, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["（…………"]}, {"code": 401, "indent": 0, "parameters": ["好、好沉重……）"]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!fsm_chest01", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 3, "y": 1}, {"id": 35, "name": "EV035", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 797, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[-48,0]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 31, "y": 4}, {"id": 36, "name": "EV036", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 796, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 29, "y": 4}, {"id": 37, "name": "EV037", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 805, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 30, "y": 5}, {"id": 38, "name": "EV038", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 804, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 29, "y": 5}, null]}