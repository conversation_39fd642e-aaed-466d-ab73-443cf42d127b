{"autoplayBgm": false, "autoplayBgs": false, "battleback1Name": "genkan", "battleback2Name": "gray bar", "bgm": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 9, "note": "<宅>", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": true, "tilesetId": 1, "width": 9, "data": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "场景初始化", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 0, "characterIndex": 3}, "list": [{"code": 108, "indent": 0, "parameters": ["防报错措施"]}, {"code": 117, "indent": 0, "parameters": [8]}, {"code": 117, "indent": 0, "parameters": [20]}, {"code": 313, "indent": 0, "parameters": [0, 2, 1, 35]}, {"code": 313, "indent": 0, "parameters": [0, 2, 1, 36]}, {"code": 111, "indent": 0, "parameters": [1, 13, 0, 0, 3]}, {"code": 355, "indent": 1, "parameters": ["let ID = $gameVariables.value(13);"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.event(ID).start()"]}, {"code": 122, "indent": 1, "parameters": [13, 13, 0, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameMap.event(3).start()"]}, {"code": 122, "indent": 1, "parameters": [13, 13, 0, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}], "x": 0, "y": 8}, {"id": 2, "name": "清晨出门", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 4, "pattern": 1, "characterIndex": 1}, "list": [{"code": 111, "indent": 0, "parameters": [1, 60, 0, 2, 0]}, {"code": 231, "indent": 1, "parameters": [1, "entrance_R_D_DO", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 60, 0, 1, 0]}, {"code": 231, "indent": 2, "parameters": [1, "entrance_C_D_DO", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [0, 123, 0]}, {"code": 231, "indent": 3, "parameters": [1, "entrance_S_D_DO", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 231, "indent": 3, "parameters": [1, "entrance_S_M_DO", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 108, "indent": 0, "parameters": ["妹妹早起分歧"]}, {"code": 111, "indent": 0, "parameters": [0, 124, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["「差不多该出发了……」"]}, {"code": 355, "indent": 1, "parameters": ["let id = this._eventId;"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.event(id).steupCEQJ(2)"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["「那么、我出门了。」"]}, {"code": 242, "indent": 1, "parameters": [2]}, {"code": 246, "indent": 1, "parameters": [2]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 122, "indent": 1, "parameters": [13, 13, 0, 0, 3]}, {"code": 201, "indent": 1, "parameters": [0, 36, 5, 3, 0, 2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 231, "indent": 0, "parameters": [9, "mio_tachie_smile", 1, 0, 572, 950, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": [">显现动作 : 图片[9] : 移动显现 : 时间[60] : 方向角度[180] : 移动距离[180]"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 111, "indent": 0, "parameters": [1, 20, 0, 70, 1]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE genkan_event_sis_40"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE genkan_event_sis_39"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >「路上小心、哥哥！」"]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.changePictureName(9, \"mio_tachie_smile2\");"]}, {"code": 111, "indent": 0, "parameters": [1, 60, 0, 2, 1]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE genkan_event_sis_42"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nl< >「今天天气看起来不是很好、"]}, {"code": 401, "indent": 1, "parameters": ["要小心点噢…」"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE genkan_event_sis_41"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nl< >「感觉坚持不住了一定要及时回来噢！」"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [2, "A", 1]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["在早晨看到妹妹元气十足的模样，"]}, {"code": 401, "indent": 1, "parameters": ["如此陌生的光景让我一瞬间沉浸在强烈的感动中。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["强忍着紧紧抱住妹妹的冲动，我也微笑着回应着妹妹。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\{「噢…！我去去就回！」\\}"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["「知道了…就拜托你乖乖看家了！」"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["向妹妹一边招手一边离开家里，"]}, {"code": 401, "indent": 0, "parameters": ["今天也要为了妹妹继续努力了！"]}, {"code": 242, "indent": 0, "parameters": [2]}, {"code": 246, "indent": 0, "parameters": [2]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [13, 13, 0, 0, 3]}, {"code": 201, "indent": 0, "parameters": [0, 36, 5, 3, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 1, "y": 8}, {"id": 3, "name": "从深渊归来", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 111, "indent": 0, "parameters": [12, "$gameSystem.hour() <= 16"]}, {"code": 111, "indent": 1, "parameters": [1, 60, 0, 2, 0]}, {"code": 231, "indent": 2, "parameters": [1, "entrance_R_D_DO", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [1, 60, 0, 1, 0]}, {"code": 231, "indent": 3, "parameters": [1, "entrance_C_D_DO", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 231, "indent": 3, "parameters": [1, "entrance_S_D_DO", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 231, "indent": 1, "parameters": [1, "entrance_S_E_DO_RL", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 5, 0]}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 121, "indent": 1, "parameters": [5, 5, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 241, "indent": 0, "parameters": [{"name": "日常_放課後", "volume": 60, "pitch": 100, "pan": 0}]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 232, "indent": 0, "parameters": [92, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, true]}, {"code": 235, "indent": 0, "parameters": [92]}, {"code": 356, "indent": 0, "parameters": ["SaveNow"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["「我回来了。」"]}, {"code": 108, "indent": 0, "parameters": ["提前回家"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameSystem.hour() <= 16"]}, {"code": 355, "indent": 1, "parameters": ["$gameMap.event(6).start()"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["特殊分支剧情"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameParty.hasItem($dataItems[15]) && !$gameSwitches.value(500)"]}, {"code": 355, "indent": 1, "parameters": ["$gameMap.event(7).start()"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["$gameMap.event(9).start()"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 2, "y": 8}, {"id": 4, "name": "第一次出门", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 2, "characterIndex": 1}, "list": [{"code": 111, "indent": 0, "parameters": [1, 60, 0, 2, 0]}, {"code": 231, "indent": 1, "parameters": [1, "entrance_R_D_DO", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 60, 0, 1, 0]}, {"code": 231, "indent": 2, "parameters": [1, "entrance_C_D_DO", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 231, "indent": 2, "parameters": [1, "entrance_S_D_DO", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["「好、准备完毕！」"]}, {"code": 231, "indent": 0, "parameters": [9, "mio_tachie_smile2", 1, 0, 572, 950, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": [">显现动作 : 图片[9] : 移动显现 : 时间[60] : 方向角度[180] : 移动距离[180]"]}, {"code": 230, "indent": 0, "parameters": [35]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_18"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >「对不起、哥哥……\\w[60]"]}, {"code": 401, "indent": 0, "parameters": ["又要让你去危险的地方——」"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["「又在说这种话了、别担心没事的哦。」"]}, {"code": 231, "indent": 0, "parameters": [9, "mio_tachie_naku", 1, 0, 572, 950, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_19"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >「可是——」"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["「已经探索过好几次了、"]}, {"code": 401, "indent": 0, "parameters": ["这几年那个迷宫也一直都非常地安定所以没问题的。」"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["「那么就拜托你看家了哦！」"]}, {"code": 231, "indent": 0, "parameters": [9, "mio_tachie_shinnpai", 1, 0, 572, 950, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_20"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >「嗯…要小心呀……」"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["「我出门了。」"]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.startFadeOut(180);"]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["没错、我的妹妹患有奇怪的病症。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["无法来到户外、甚至不能沐浴阳光。"]}, {"code": 401, "indent": 0, "parameters": ["而且不定期地服用特别炼制的炼金药剂，"]}, {"code": 401, "indent": 0, "parameters": ["身体状况也会进一步变得不安定。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["用于制作药剂所需要的素材，也是市场上没法简单入手的。"]}, {"code": 401, "indent": 0, "parameters": ["现在唯一可以收集到所有素材的地方，只有那里了———"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["所以我作为兄长、不管多少次也必须要潜入那个地方，"]}, {"code": 401, "indent": 0, "parameters": ["去寻找能够彻底治好妹妹的新方法。"]}, {"code": 242, "indent": 0, "parameters": [2]}, {"code": 122, "indent": 0, "parameters": [13, 13, 0, 0, 1]}, {"code": 201, "indent": 0, "parameters": [0, 36, 6, 6, 0, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 0}, {"id": 5, "name": "第一次深渊死亡回归", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 2, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["报错防范"]}, {"code": 117, "indent": 0, "parameters": [21]}, {"code": 235, "indent": 0, "parameters": [99]}, {"code": 355, "indent": 0, "parameters": ["var actor = $gameParty.leader();"]}, {"code": 655, "indent": 0, "parameters": ["var states = actor.states();"]}, {"code": 655, "indent": 0, "parameters": ["for (var i = 0; i < states.length; i++) {"]}, {"code": 655, "indent": 0, "parameters": ["    actor.removeState(states[i].id);"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 655, "indent": 0, "parameters": ["actor.setHp(1);"]}, {"code": 241, "indent": 0, "parameters": [{"name": "日常_放課後", "volume": 60, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 0, "parameters": [1, "entrance_S_E_DO_RL", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 356, "indent": 0, "parameters": ["DS_方向設定 90"]}, {"code": 225, "indent": 0, "parameters": [6, 8, 30, false]}, {"code": 224, "indent": 0, "parameters": [[0, 0, 0, 185], 30, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "ドシャァ！地面に倒れる、落ちる", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 355, "indent": 0, "parameters": ["$gameSelfVariables.setValue([$gameMap.mapId(), 13, 'days'], 0);"]}, {"code": 655, "indent": 0, "parameters": ["$gameSwitches.setValue(490, false)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["「不、不行了、\\w[30]差点死掉了…」"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["在最后的瞬间逃出来真是太好了…"]}, {"code": 401, "indent": 0, "parameters": ["果然、以现在的力量挑战那里还是太勉强了吗…"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["应该要多加修炼、"]}, {"code": 401, "indent": 0, "parameters": ["做好充分准备后再考虑去攻略那里啊……"]}, {"code": 231, "indent": 0, "parameters": [9, "mio_tachie_smile", 0, 0, 200, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": [">显现动作 : 图片[9] : 移动显现 : 时间[60] : 方向角度[180] : 移动距离[180]"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_14"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >「欢迎回家、\\w[50]哥…\\.…\\^"]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.changePictureName(9, \"mio_tachie_ase\");"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_15"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 356, "indent": 0, "parameters": [">持续动作 : 图片[9] : 上下震动 : 持续时间[40] : 周期[20] : 震动幅度[3]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >「哥哥！？」"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_09"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >「哥哥没事吧！？\\w[60]"]}, {"code": 401, "indent": 0, "parameters": ["看起来浑身都是伤……！」"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["「啊啊、稍微勉强了下自己、"]}, {"code": 401, "indent": 0, "parameters": ["结果失败了…啊哈哈…」"]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.changePictureName(9, \"mio_tachie_shy1\");"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_10"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >「哥哥也真是的、"]}, {"code": 401, "indent": 0, "parameters": ["要多珍惜一下自己呀！」"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_11"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >「受伤了的话、得赶紧包扎才行！」"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 235, "indent": 0, "parameters": [9]}, {"code": 250, "indent": 0, "parameters": [{"name": "Heal2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["let base = 15 + Math.randomInt(11);"]}, {"code": 655, "indent": 0, "parameters": ["let heal = $gameVariables.value(15) * base + 15;"]}, {"code": 655, "indent": 0, "parameters": ["$gameActors.actor(1).gainHp(heal);"]}, {"code": 231, "indent": 0, "parameters": [9, "mio_tachie_nocoat_nununu", 0, 0, 200, 1600, 100, 100, 255, 0]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 111, "indent": 0, "parameters": [1, 15, 0, 4, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["妹妹用熟练的手法仔细地为我包扎后，"]}, {"code": 401, "indent": 1, "parameters": ["感觉体力大大恢复了。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 15, 0, 2, 1]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["妹妹用不太熟练的手法小心地为我包扎后，"]}, {"code": 401, "indent": 2, "parameters": ["感觉体力恢复了不少。"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["妹妹用非常笨拙的手法为我全身上下捆上了绷带，"]}, {"code": 401, "indent": 2, "parameters": ["感觉体力还是稍微回复了一些。"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["var picture = $gameScreen.picture(9);"]}, {"code": 655, "indent": 0, "parameters": ["var data = {\t\t\t\t\t\t\t\t\"type\":\"smoothMove\","]}, {"code": 655, "indent": 0, "parameters": ["\"valueX\":0,"]}, {"code": 655, "indent": 0, "parameters": ["\"valueY\":-1450,"]}, {"code": 655, "indent": 0, "parameters": ["\"time\":60,"]}, {"code": 655, "indent": 0, "parameters": ["\"cur_time\":0,\"cur_speedX\":0,\"cur_speedY\":0,}"]}, {"code": 655, "indent": 0, "parameters": ["if (picture) { picture._drill_PSh_commandChangeTank.push(data);"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_12"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >「接着哥哥就好好修养吧！"]}, {"code": 401, "indent": 0, "parameters": ["晚饭的事情就交给我了！」"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["「啊、好！」"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["话说回来，现在我全身被包扎得像个木乃伊，"]}, {"code": 401, "indent": 0, "parameters": ["也完全动不了啊…"]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.changePictureName(9, \"mio_tachie_nocoat_smile\");"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_13"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >「嗯、哥哥是个好孩子呢。」"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["是错觉吗、感觉妹妹比平时更有干劲的样子…"]}, {"code": 122, "indent": 0, "parameters": [14, 14, 0, 0, 15]}, {"code": 117, "indent": 0, "parameters": [3]}, {"code": 121, "indent": 0, "parameters": [5, 5, 1]}, {"code": 122, "indent": 0, "parameters": [13, 13, 0, 0, 22]}, {"code": 201, "indent": 0, "parameters": [0, 11, 5, 6, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 4, "y": 0}, {"id": 6, "name": "白天提前回家", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["获得风扇事件"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameParty.hasItem($dataItems[19]) && !$gameSwitches.value(500)"]}, {"code": 355, "indent": 1, "parameters": ["$gameMap.event(7).start()"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["早归触发游戏机事件"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameParty.hasItem($dataItems[15]) && !$gameSwitches.value(500)"]}, {"code": 355, "indent": 1, "parameters": ["$gameMap.event(7).start()"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 231, "indent": 0, "parameters": [9, "mio_tachie_OAO", 0, 0, 200, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": [">显现动作 : 图片[9] : 移动显现 : 时间[60] : 方向角度[180] : 移动距离[200]"]}, {"code": 230, "indent": 0, "parameters": [70]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_24"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >「哥哥？今天回来得很早呢？」"]}, {"code": 355, "indent": 0, "parameters": ["this.count = Math.randomInt(4);"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["「嗯，今天感觉有点累，"]}, {"code": 401, "indent": 0, "parameters": ["就不勉强去探索了…」"]}, {"code": 111, "indent": 0, "parameters": [12, "this.count === 3"]}, {"code": 231, "indent": 1, "parameters": [9, "mio_tachie_=ω=", 0, 0, 200, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE genkan_event_sis_28"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nl< >「这是那个吧……叫五月病来着？」"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["五月……？现在可是炎热的夏天哦？"]}, {"code": 231, "indent": 1, "parameters": [9, "mio_tachie_IAI", 0, 0, 200, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE genkan_event_sis_29"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nl< >「我也经常有这种感觉呢…"]}, {"code": 401, "indent": 1, "parameters": ["本来以为是中午，结果醒来时哥哥已经把晚饭做好了…」"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["……那只是单纯的喜欢睡懒觉不是吗？"]}, {"code": 119, "indent": 1, "parameters": ["3"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "this.count === 2"]}, {"code": 231, "indent": 2, "parameters": [9, "mio_tachie_happy", 0, 0, 200, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["SV_PLAY_VOICE genkan_event_sis_27"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["\\nl< >「偶尔这样也不错呢、哥哥，来一起玩吧！」"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [12, "this.count === 1"]}, {"code": 231, "indent": 3, "parameters": [9, "mio_tachie_shinnpai", 0, 0, 200, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 3, "parameters": ["SV_PLAY_VOICE genkan_event_sis_26"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nl< >「适当休息也很重要、下次要多注意呀。」"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 231, "indent": 3, "parameters": [9, "mio_tachie_smile", 0, 0, 200, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 3, "parameters": ["SV_PLAY_VOICE genkan_event_sis_25"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nl< >「这样啊、"]}, {"code": 401, "indent": 3, "parameters": ["那就让我来给哥哥按摩一下吧！」"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["是啊、时间还有很多、今天就好好放松休息吧。"]}, {"code": 242, "indent": 0, "parameters": [2]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "明るくて可愛いチェレスタのジングル", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$gameSystem.hour() >= 17"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["$gameSystem.add_hour(1)"]}, {"code": 108, "indent": 2, "parameters": ["计算体力恢复"]}, {"code": 311, "indent": 2, "parameters": [0, 1, 0, 0, 12, false]}, {"code": 117, "indent": 2, "parameters": [8]}, {"code": 230, "indent": 2, "parameters": [30]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 111, "indent": 0, "parameters": [12, "this.count === 3"]}, {"code": 118, "indent": 1, "parameters": ["3"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["不过说得也是啊、时间还有很多、今天就好好放松休息吧。"]}, {"code": 231, "indent": 1, "parameters": [9, "mio_tachie_happy", 0, 0, 200, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE genkan_event_sis_30"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nl< >「那哥哥也一起来试试午睡吧…"]}, {"code": 401, "indent": 1, "parameters": ["很舒服的哦？」"]}, {"code": 242, "indent": 1, "parameters": [2]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "明るくて可愛いチェレスタのジングル", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 112, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [12, "$gameSystem.hour() >= 17"]}, {"code": 113, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 355, "indent": 3, "parameters": ["$gameSystem.add_hour(1)"]}, {"code": 108, "indent": 3, "parameters": ["计算体力恢复"]}, {"code": 311, "indent": 3, "parameters": [0, 1, 0, 0, 12, false]}, {"code": 117, "indent": 3, "parameters": [8]}, {"code": 230, "indent": 3, "parameters": [30]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 413, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["结果最后和妹妹一起睡到了太阳下山的时候。"]}, {"code": 119, "indent": 1, "parameters": ["结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "this.count === 2"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["和妹妹一起玩了一天的游戏。"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [12, "this.count === 1"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["和妹妹一起悠闲地度过了白天。"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["妹妹干劲十足地为我按摩了很长时间，"]}, {"code": 401, "indent": 3, "parameters": ["因为很舒服就这么睡过去了…"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [13, 13, 0, 0, 7]}, {"code": 201, "indent": 0, "parameters": [0, 54, 6, 5, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 3, "y": 8}, {"id": 7, "name": "游戏机事件", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 2, "characterIndex": 1}, "list": [{"code": 111, "indent": 0, "parameters": [12, "$gameSystem.hour() <= 16"]}, {"code": 231, "indent": 1, "parameters": [9, "mio_tachie_OAO", 0, 0, 200, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 1, "parameters": [">显现动作 : 图片[9] : 移动显现 : 时间[60] : 方向角度[180] : 移动距离[200]"]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE genkan_event_sis_24"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nl< >「哥哥？今天回来得很早呢？」"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 231, "indent": 1, "parameters": [9, "mio_tachie_smile", 1, 0, 572, 950, 100, 100, 255, 0]}, {"code": 356, "indent": 1, "parameters": [">显现动作 : 图片[9] : 移动显现 : 时间[60] : 方向角度[180] : 移动距离[180]"]}, {"code": 230, "indent": 1, "parameters": [35]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE genkan_event_sis_01"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nl< >「欢迎回家，哥哥。」"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["「哼哼哼、吾妹哟、"]}, {"code": 401, "indent": 0, "parameters": ["猜猜看今天哥哥带什么东西回来了——！」"]}, {"code": 231, "indent": 0, "parameters": [9, "mio_tachie_OAO", 1, 0, 572, 950, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_16"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >「很大的包裹呢——\\w[90]\\^"]}, {"code": 231, "indent": 0, "parameters": [9, "mio_tachie_=ω=", 1, 0, 572, 950, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_17"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >「难道是什么好吃的东西……！？」"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["嗯……我家妹妹还真对美食以外的事物没什么兴趣呢…"]}, {"code": 242, "indent": 0, "parameters": [2]}, {"code": 122, "indent": 0, "parameters": [13, 13, 0, 0, 34]}, {"code": 201, "indent": 0, "parameters": [0, 4, 6, 8, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 2, "y": 0}, null, {"id": 9, "name": "常规回家后事件", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 1, "characterIndex": 3}, "list": [{"code": 111, "indent": 0, "parameters": [8, 197]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["「…………」"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["怀里温热的手感时刻提醒着我已经拿到了梦寐以求的药。"]}, {"code": 401, "indent": 1, "parameters": ["但过于不真实的经历还让我无法释然。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["连通了家门口的星之塔罗牌，这是父亲遗留下来的物品之一，"]}, {"code": 401, "indent": 1, "parameters": ["虽然父亲生前一直在研究可以让妹妹恢复正常的方法，"]}, {"code": 401, "indent": 1, "parameters": ["可为什么…这么轻易地就产生了反应。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["……"]}, {"code": 401, "indent": 1, "parameters": ["……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["什么结论也得不到，这么胡思乱想也没有意义，"]}, {"code": 401, "indent": 1, "parameters": ["结果而言我已经拿到了红髓液，找个机会让妹妹试一试吧。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["颜色看起来像草莓汁，"]}, {"code": 401, "indent": 1, "parameters": ["说不定味道也和她喜欢的草莓牛奶一样呢…"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": [">外发光效果 : 固定对话框外发光 : 颜色[11] : 厚度[2] : 偏移[3,3]"]}, {"code": 231, "indent": 0, "parameters": [9, "mio_tachie_smile", 1, 0, 572, 950, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": [">显现动作 : 图片[9] : 移动显现 : 时间[60] : 方向角度[180] : 移动距离[180]"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_01"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >「欢迎回家、哥哥。」"]}, {"code": 108, "indent": 0, "parameters": ["特典T恤"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameParty.hasItem($dataArmors[150]) && !$gameSelfSwitches.value([$gameMap.mapId(), 14, 'A'])"]}, {"code": 355, "indent": 1, "parameters": ["$gameMap.event(14).steupCEQJ(1)"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [4, 1, 6, 45]}, {"code": 355, "indent": 1, "parameters": ["ImageManager.reservePicture(\"mio_tachie_noKao\"); "]}, {"code": 230, "indent": 1, "parameters": [4]}, {"code": 355, "indent": 1, "parameters": ["$gameMap.event(13).steupCEQJ(1)"]}, {"code": 112, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [2, "D", 0]}, {"code": 123, "indent": 3, "parameters": ["D", 1]}, {"code": 113, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 230, "indent": 3, "parameters": [30]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 413, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.changePictureName(9, \"mio_tachie_IAI\");"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.erasePicture(10);"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.erasePicture(11);"]}, {"code": 356, "indent": 0, "parameters": [">持续动作 : 图片[9] : 上下震动 : 持续时间[50] : 周期[25] : 震动幅度[3]"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_02"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >「肚子已经饿了…」"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["「知道了知道了、马上就准备晚饭呢。」"]}, {"code": 119, "indent": 0, "parameters": ["1"]}, {"code": 108, "indent": 0, "parameters": ["修正选项组图标"]}, {"code": 355, "indent": 0, "parameters": ["$gameSystem._drill_DCB_curStyle = 25"]}, {"code": 355, "indent": 0, "parameters": ["let style = [];"]}, {"code": 655, "indent": 0, "parameters": ["style.push(\"eventButtons_sis_crafting\");"]}, {"code": 655, "indent": 0, "parameters": ["$gameSwitches.value(480) ? style.push(\"eventButtons_ofuro1\") : null;"]}, {"code": 655, "indent": 0, "parameters": ["style.push(\"eventButtons_toilet\");"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(15) >= 9999 ? style.push(\"eventButtons_sex1\") : null;"]}, {"code": 655, "indent": 0, "parameters": ["DrillUp.g_DCB_data[24].btn_src = style;"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["摸了摸妹妹的头安慰了下。\\w[30]"]}, {"code": 401, "indent": 0, "parameters": ["好了、接着做什么呢——"]}, {"code": 102, "indent": 0, "parameters": [["准备晚饭", "<<!s[480]>>\\c[15]お風呂に入る", "\\c[15]去厕所"], -1, -1, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "准备晚饭"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["是啊、已经不早了、得去准备晚饭了。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["「今天晚上想吃什么？」"]}, {"code": 355, "indent": 1, "parameters": ["$gameScreen.changePictureName(9, \"mio_tachie_happy\");"]}, {"code": 355, "indent": 1, "parameters": ["$gameScreen.picture(9).drill_PCE_playSustainingShakeRotate( 120,60,1);"]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE genkan_event_sis_03"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nl< >「肉料理就好了！」"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["嗯、预料之内的回答。"]}, {"code": 401, "indent": 1, "parameters": ["果然还是应该更加强调饮食平衡的重要性呀。"]}, {"code": 122, "indent": 1, "parameters": [13, 13, 0, 0, 1]}, {"code": 201, "indent": 1, "parameters": [0, 11, 5, 5, 0, 2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "<<!s[480]>>\\c[15]お風呂に入る"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["汗かいたし、先にお風呂に入ってしまおうかな。"]}, {"code": 355, "indent": 1, "parameters": ["$gameScreen.changePictureName(9, \"mio_tachie_=ω=\");"]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE genkan_event_sis_04"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nl< >「お兄ちゃん先にお風呂に入るの？"]}, {"code": 401, "indent": 1, "parameters": ["それじゃいってらっしゃい～」"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "\\c[15]去厕所"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["有点想去厕所了……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["「抱歉、再等一下吧、之后再准备晚饭。」"]}, {"code": 355, "indent": 1, "parameters": ["$gameScreen.changePictureName(9, \"mio_tachie_OAO\");"]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE genkan_event_sis_05"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nl< >「我知道了。」"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 122, "indent": 1, "parameters": [13, 13, 0, 0, 3]}, {"code": 201, "indent": 1, "parameters": [0, 20, 3, 6, 0, 2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 2, "y": 7}, {"id": 10, "name": "深渊死亡回归", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["报错防范"]}, {"code": 117, "indent": 0, "parameters": [21]}, {"code": 235, "indent": 0, "parameters": [99]}, {"code": 355, "indent": 0, "parameters": ["var actor = $gameParty.leader();"]}, {"code": 655, "indent": 0, "parameters": ["var states = actor.states();"]}, {"code": 655, "indent": 0, "parameters": ["for (var i = 0; i < states.length; i++) {"]}, {"code": 655, "indent": 0, "parameters": ["    actor.removeState(states[i].id);"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 655, "indent": 0, "parameters": ["actor.setHp(1);"]}, {"code": 241, "indent": 0, "parameters": [{"name": "日常_放課後", "volume": 60, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 0, "parameters": [1, "entrance_S_E_DO_RL", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 356, "indent": 0, "parameters": ["DS_方向設定 90"]}, {"code": 225, "indent": 0, "parameters": [6, 8, 30, false]}, {"code": 224, "indent": 0, "parameters": [[0, 0, 0, 185], 30, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "ドシャァ！地面に倒れる、落ちる", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 355, "indent": 0, "parameters": ["$gameSelfVariables.setValue([$gameMap.mapId(), 13, 'days'], 0);"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["「不、不行了、\\w[30]差点死掉了…」"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["在最后的瞬间逃出来真是太好了…"]}, {"code": 401, "indent": 0, "parameters": ["果然、以现在的力量挑战那里还是太勉强了吗…"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["应该要多加修炼、"]}, {"code": 401, "indent": 0, "parameters": ["做好充分准备后再考虑去攻略那里啊……"]}, {"code": 231, "indent": 0, "parameters": [9, "mio_tachie_smile", 0, 0, 200, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": [">显现动作 : 图片[9] : 移动显现 : 时间[60] : 方向角度[180] : 移动距离[180]"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_14"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >「欢迎回家、\\w[50]哥…\\.…\\^"]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.changePictureName(9, \"mio_tachie_ase\");"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_15"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 356, "indent": 0, "parameters": [">持续动作 : 图片[9] : 上下震动 : 持续时间[40] : 周期[20] : 震动幅度[3]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >「哥哥！？」"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_09"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >「哥哥没事吧！？\\w[60]"]}, {"code": 401, "indent": 0, "parameters": ["看起来浑身都是伤……！」"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["「啊啊、稍微勉强了下自己、"]}, {"code": 401, "indent": 0, "parameters": ["结果失败了…啊哈哈…」"]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.changePictureName(9, \"mio_tachie_shy1\");"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_21"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >「哥哥又在勉强自己了呢！」"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_22"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >「得快点给伤口止血才行！」"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "Heal2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["let base = 15 + Math.randomInt(11);"]}, {"code": 655, "indent": 0, "parameters": ["let heal = $gameVariables.value(15) * base + 15;"]}, {"code": 655, "indent": 0, "parameters": ["$gameActors.actor(1).gainHp(heal);"]}, {"code": 231, "indent": 0, "parameters": [9, "mio_tachie_nocoat_bimyou2", 0, 0, 200, 1600, 100, 100, 255, 0]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 111, "indent": 0, "parameters": [1, 15, 0, 4, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["妹妹用熟练的手法仔细地为我包扎后，"]}, {"code": 401, "indent": 1, "parameters": ["感觉体力大大恢复了。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 15, 0, 2, 1]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["妹妹用不太熟练的手法小心地为我包扎后，"]}, {"code": 401, "indent": 2, "parameters": ["感觉体力恢复了不少。"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["妹妹用非常笨拙的手法为我全身上下捆上了绷带，"]}, {"code": 401, "indent": 2, "parameters": ["感觉体力还是稍微回复了一些。"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["var picture = $gameScreen.picture(9);"]}, {"code": 655, "indent": 0, "parameters": ["var data = {\t\t\t\t\t\t\t\t\"type\":\"smoothMove\","]}, {"code": 655, "indent": 0, "parameters": ["\"valueX\":0,"]}, {"code": 655, "indent": 0, "parameters": ["\"valueY\":-1450,"]}, {"code": 655, "indent": 0, "parameters": ["\"time\":60,"]}, {"code": 655, "indent": 0, "parameters": ["\"cur_time\":0,\"cur_speedX\":0,\"cur_speedY\":0,}"]}, {"code": 655, "indent": 0, "parameters": ["if (picture) { picture._drill_PSh_commandChangeTank.push(data);"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 230, "indent": 0, "parameters": [45]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_23"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >「接下来就让我来照顾哥哥呢！」"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["是错觉吗？妹妹看起来比平时更兴奋的样子…"]}, {"code": 122, "indent": 0, "parameters": [14, 14, 0, 0, 10]}, {"code": 117, "indent": 0, "parameters": [3]}, {"code": 122, "indent": 0, "parameters": [13, 13, 0, 0, 18]}, {"code": 201, "indent": 0, "parameters": [0, 11, 5, 6, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 4, "y": 8}, {"id": 11, "name": "白天提前回家", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["this.count = 0"]}, {"code": 118, "indent": 0, "parameters": ["配音"]}, {"code": 241, "indent": 0, "parameters": [{"name": "日常_放課後", "volume": 60, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["$gameSystem.set_hour(10)"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameSystem.hour() <= 16"]}, {"code": 111, "indent": 1, "parameters": [1, 60, 0, 2, 0]}, {"code": 231, "indent": 2, "parameters": [1, "entrance_R_D_DO", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [1, 60, 0, 1, 0]}, {"code": 231, "indent": 3, "parameters": [1, "entrance_C_D_DO", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 231, "indent": 3, "parameters": [1, "entrance_S_D_DO", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 231, "indent": 1, "parameters": [1, "entrance_S_E_DO_RL", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 231, "indent": 0, "parameters": [9, "mio_tachie_OAO", 0, 0, 200, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": [">显现动作 : 图片[9] : 移动显现 : 时间[60] : 方向角度[180] : 移动距离[200]"]}, {"code": 230, "indent": 0, "parameters": [70]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >「哥哥？今天回来得很早诶？」"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["「嗯、今天感觉有点累，所以不勉强去探索了…」"]}, {"code": 111, "indent": 0, "parameters": [12, "this.count === 3"]}, {"code": 231, "indent": 1, "parameters": [9, "mio_tachie_=ω=", 0, 0, 200, 150, 100, 100, 255, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nl< >「这是那个什么吧……五月病来着？」"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["五月……？现在明明是炎热的夏天诶？"]}, {"code": 231, "indent": 1, "parameters": [9, "mio_tachie_IAI", 0, 0, 200, 150, 100, 100, 255, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nl< >「我也经常会有这种感觉呢…"]}, {"code": 401, "indent": 1, "parameters": ["明明只想睡到中午，结果醒来时哥哥已经连晚饭都做好了…」"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["……那只是单纯地喜欢睡懒觉吧？"]}, {"code": 119, "indent": 1, "parameters": ["3"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "this.count === 2"]}, {"code": 231, "indent": 2, "parameters": [9, "mio_tachie_happy", 0, 0, 200, 150, 100, 100, 255, 0]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["\\nl< >「偶尔这样也很不错呢，一起来玩吧哥哥！」"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [12, "this.count === 1"]}, {"code": 231, "indent": 3, "parameters": [9, "mio_tachie_shinnpai", 0, 0, 200, 150, 100, 100, 255, 0]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nl< >「劳逸结合很重要呢，哥哥接下来就好好休息下吧。」"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 231, "indent": 3, "parameters": [9, "mio_tachie_smile", 0, 0, 200, 150, 100, 100, 255, 0]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nl< >「是这样啊、那我来帮哥哥放松一下吧！」"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["是啊、时间还有余裕、今天就好好休息放松下吧。"]}, {"code": 242, "indent": 0, "parameters": [2]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "明るくて可愛いチェレスタのジングル", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$gameSystem.hour() >= 17"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["$gameSystem.add_hour(1)"]}, {"code": 108, "indent": 2, "parameters": ["计算体力恢复"]}, {"code": 311, "indent": 2, "parameters": [0, 1, 0, 0, 12, false]}, {"code": 117, "indent": 2, "parameters": [8]}, {"code": 230, "indent": 2, "parameters": [30]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 111, "indent": 0, "parameters": [12, "this.count === 3"]}, {"code": 118, "indent": 1, "parameters": ["3"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["不过也是呢、时间还有余裕、今天就好好休息吧。"]}, {"code": 231, "indent": 1, "parameters": [9, "mio_tachie_happy", 0, 0, 200, 150, 100, 100, 255, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nl< >「那哥哥也来一起试试午睡吧…很舒服哦？」"]}, {"code": 242, "indent": 1, "parameters": [2]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "明るくて可愛いチェレスタのジングル", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 112, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [12, "$gameSystem.hour() >= 17"]}, {"code": 113, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 355, "indent": 3, "parameters": ["$gameSystem.add_hour(1)"]}, {"code": 108, "indent": 3, "parameters": ["计算体力恢复"]}, {"code": 311, "indent": 3, "parameters": [0, 1, 0, 0, 12, false]}, {"code": 117, "indent": 3, "parameters": [8]}, {"code": 230, "indent": 3, "parameters": [30]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 413, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["结果和妹妹一起睡到了临近晚上才醒来。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "this.count === 2"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["和妹妹一起玩了一整天的游戏。"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [12, "this.count === 1"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["和妹妹一起悠闲地度过了白天。"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["妹妹干劲十足地替我按摩了很久、"]}, {"code": 401, "indent": 3, "parameters": ["因为太舒服就这么睡到了下午…"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["this.count++"]}, {"code": 119, "indent": 0, "parameters": ["配音"]}, {"code": 122, "indent": 0, "parameters": [13, 13, 0, 0, 1]}, {"code": 201, "indent": 0, "parameters": [0, 4, 8, 6, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 8, "y": 5}, {"id": 12, "name": "后续事件检测", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 0, "characterIndex": 1}, "list": [{"code": 230, "indent": 0, "parameters": [60]}, {"code": 111, "indent": 0, "parameters": [0, 493, 1]}, {"code": 355, "indent": 1, "parameters": ["for (var i = 23; i <= 27; i++) {"]}, {"code": 655, "indent": 1, "parameters": ["    var item = $dataItems[i];"]}, {"code": 655, "indent": 1, "parameters": ["    if ($gameParty.numItems(item) >= 1) {"]}, {"code": 655, "indent": 1, "parameters": ["        $gameSelfSwitches.setValue([4, 44, 'A'], true);"]}, {"code": 655, "indent": 1, "parameters": ["        break;"]}, {"code": 655, "indent": 1, "parameters": ["    }"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [4]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 115, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 8, "y": 1}, null, null, {"id": 15, "name": "EV015", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["$gameMap.event(3).start()"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 8, "y": 8}, null]}