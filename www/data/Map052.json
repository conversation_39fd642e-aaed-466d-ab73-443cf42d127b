{"autoplayBgm": false, "autoplayBgs": false, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 13, "note": "<深渊>", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 13, "width": 17, "data": [5888, 5888, 5912, 6275, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 6278, 5904, 5888, 5888, 5888, 5888, 5912, 6273, 6272, 6272, 6272, 6272, 6272, 6272, 6272, 6272, 6272, 6276, 5904, 5888, 5888, 5888, 5888, 5912, 6281, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6284, 5904, 5888, 5888, 5888, 5888, 5912, 2898, 2884, 2884, 2884, 2884, 2884, 2884, 2884, 2884, 2884, 2900, 5904, 5888, 5888, 5888, 5888, 5912, 2880, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2888, 5904, 5888, 5888, 5888, 5888, 5912, 2880, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2888, 5904, 5888, 5888, 5888, 5888, 5912, 2880, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2888, 5904, 5888, 5888, 5888, 5888, 5912, 2880, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2888, 5904, 5888, 5888, 5888, 5888, 5912, 2880, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2888, 5904, 5888, 5888, 5888, 5888, 5912, 2880, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2888, 5904, 5888, 5888, 5888, 5888, 5912, 2904, 2892, 2892, 2892, 2892, 2892, 2892, 2892, 2892, 2892, 2902, 5904, 5888, 5888, 5888, 5888, 5890, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5889, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 177, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 176, 177, 176, 177, 176, 16, 176, 177, 176, 177, 176, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 35, 0, 0, 24, 0, 0, 0, 0, 25, 0, 0, 0, 0, 0, 0, 36, 37, 38, 28, 0, 48, 0, 0, 28, 0, 33, 0, 0, 0, 0, 0, 0, 44, 45, 46, 0, 0, 0, 0, 0, 0, 0, 41, 0, 0, 0, 0, 0, 0, 28, 28, 28, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 28, 0, 0, 0, 113, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 89, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, null, {"id": 2, "name": "EV002", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 0, "characterIndex": 3}, "list": [{"code": 121, "indent": 0, "parameters": [118, 118, 1]}, {"code": 241, "indent": 0, "parameters": [{"name": "真実はどこ(Where is the truth)", "volume": 75, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": ["particle set dust_walk walk:player:0 def below"]}, {"code": 356, "indent": 0, "parameters": ["particle set ripple_walk walk:player:2 def below"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameScreen.zoomScale() == 1"]}, {"code": 356, "indent": 1, "parameters": ["dpZoom 2 1 player"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["生成篝火"]}, {"code": 355, "indent": 0, "parameters": ["QJ.SE.spawnXy(1,7,8,6,false);"]}, {"code": 108, "indent": 0, "parameters": ["如果没触发过忍者剧情，会一直播放肚子叫"]}, {"code": 111, "indent": 0, "parameters": [12, "!$gameSelfSwitches.value([52, 5, 'F'])"]}, {"code": 355, "indent": 1, "parameters": ["$gameSelfSwitches.setValue([$gameMap.mapId(), 5, 'A'], false);"]}, {"code": 355, "indent": 1, "parameters": ["let eid = 5;"]}, {"code": 655, "indent": 1, "parameters": ["      QJ.MPMZ.Shoot({"]}, {"code": 655, "indent": 1, "parameters": ["        img:\"null1\","]}, {"code": 655, "indent": 1, "parameters": ["        groupName:['guuuu'],"]}, {"code": 655, "indent": 1, "parameters": ["        position:[['E',eid],['E',eid]],"]}, {"code": 655, "indent": 1, "parameters": ["        moveType:['D',true],"]}, {"code": 655, "indent": 1, "parameters": ["        existData:[],"]}, {"code": 655, "indent": 1, "parameters": ["        moveF:["]}, {"code": 655, "indent": 1, "parameters": ["           [60,240,QJ.MPMZ.tl.weirdBellyBuzzingSound,[eid]]  "]}, {"code": 655, "indent": 1, "parameters": ["        ],"]}, {"code": 655, "indent": 1, "parameters": ["     });"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [100]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 121, "indent": 0, "parameters": [3, 3, 0]}, {"code": 121, "indent": 0, "parameters": [14, 14, 1]}, {"code": 111, "indent": 0, "parameters": [2, "D", 1]}, {"code": 123, "indent": 1, "parameters": ["D", 0]}, {"code": 108, "indent": 1, "parameters": ["初始化对话框"]}, {"code": 355, "indent": 1, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 1, "parameters": ["let target = -1;"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.steupCEQJ(110,eid,{target:target});"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.steupCEQJ(110,eid,{choice:true});"]}, {"code": 655, "indent": 1, "parameters": ["$gamePlayer.setDirection(8)"]}, {"code": 655, "indent": 1, "parameters": ["ctb.useTurnPlayer = false;"]}, {"code": 655, "indent": 1, "parameters": ["this.wait(4)"]}, {"code": 355, "indent": 1, "parameters": ["$gamePlayer.moveToTarget(8,4)"]}, {"code": 112, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [12, "!$gamePlayer.isMoved()"]}, {"code": 113, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 230, "indent": 2, "parameters": [4]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 413, "indent": 1, "parameters": []}, {"code": 213, "indent": 1, "parameters": [-1, 1, true]}, {"code": 355, "indent": 1, "parameters": ["let type = \"ninja\";"]}, {"code": 655, "indent": 1, "parameters": ["let index = 0;"]}, {"code": 655, "indent": 1, "parameters": ["this.showCommonEventDialogue(type,String(index))"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 37, "indent": null}, {"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 15, "y": 0}, {"id": 3, "name": "EV003", "note": "Fire 0 #AAFF00", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Move1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 246, "indent": 0, "parameters": [1]}, {"code": 201, "indent": 0, "parameters": [0, 28, 3, 20, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 2, "walkAnime": true}], "x": 8, "y": 0}, {"id": 4, "name": "采集点", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["=>标签核心 : 添加标签 : 宝箱"]}, {"code": 108, "indent": 0, "parameters": ["particle set mysterious_torch_c-EID this mysterious_torch_c"]}, {"code": 250, "indent": 0, "parameters": [{"name": "キラーン（ひらめき・アイテム発見・獲得）", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 128, "indent": 0, "parameters": [11, 0, 0, 1, false]}, {"code": 356, "indent": 0, "parameters": ["particle clear mysterious_torch_c-EID"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 13, "y": 10}, {"id": 5, "name": "忍者Sh", "note": "<resetSelfSwitch>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$Ninja", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<width: 1>"]}, {"code": 408, "indent": 0, "parameters": ["<height: 1.5>"]}, {"code": 408, "indent": 0, "parameters": ["<offsetX: 0>"]}, {"code": 408, "indent": 0, "parameters": ["<offsetY: 0>"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameSelfSwitches.value([$gameMap.mapId(), this._eventId, 'F'])"]}, {"code": 123, "indent": 1, "parameters": ["C", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 230, "indent": 1, "parameters": [4]}, {"code": 356, "indent": 1, "parameters": [">行走图动画序列 : 本事件 : 播放简单状态元集合 : 集合[饿晕1]"]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$Ninja", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 121, "indent": 0, "parameters": [118, 118, 0]}, {"code": 108, "indent": 0, "parameters": ["初始化对话框"]}, {"code": 355, "indent": 0, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["let target = -1;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.steupCEQJ(110,eid,{target:target});"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.steupCEQJ(110,eid,{choice:true});"]}, {"code": 655, "indent": 0, "parameters": ["$gamePlayer.setDirection(8)"]}, {"code": 655, "indent": 0, "parameters": ["ctb.useTurnPlayer = false;"]}, {"code": 655, "indent": 0, "parameters": ["this.wait(4)"]}, {"code": 355, "indent": 0, "parameters": ["let type = \"ninja\";"]}, {"code": 655, "indent": 0, "parameters": ["let index = 1;"]}, {"code": 655, "indent": 0, "parameters": ["this.showCommonEventDialogue(type,String(index))"]}, {"code": 355, "indent": 0, "parameters": ["let type = \"ninja\";"]}, {"code": 655, "indent": 0, "parameters": ["let index = 2;"]}, {"code": 655, "indent": 0, "parameters": ["this.showCommonEventDialogue(type,String(index))"]}, {"code": 250, "indent": 0, "parameters": [{"name": "042myuu_YumeSE_FukidashiAngry02", "volume": 40, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [70]}, {"code": 250, "indent": 0, "parameters": [{"name": "014myuu_YumeSE_SystemBuzzer03", "volume": 40, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": [">持续动作 : 本事件 : 左右震动 : 持续时间[14] : 周期[7] : 震动幅度[1]"]}, {"code": 108, "indent": 0, "parameters": ["初始化对话框"]}, {"code": 355, "indent": 0, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["let target = eid;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.steupCEQJ(110,eid,{target:target});"]}, {"code": 655, "indent": 0, "parameters": ["this.wait(4)"]}, {"code": 355, "indent": 0, "parameters": ["let type = \"ninja\";"]}, {"code": 655, "indent": 0, "parameters": ["let index = 3;"]}, {"code": 655, "indent": 0, "parameters": ["this.showCommonEventDialogue(type,String(index))"]}, {"code": 108, "indent": 0, "parameters": ["初始化对话框"]}, {"code": 355, "indent": 0, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["let target = -1;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.steupCEQJ(110,eid,{target:target});"]}, {"code": 655, "indent": 0, "parameters": ["this.wait(4)"]}, {"code": 355, "indent": 0, "parameters": ["let type = \"ninja\";"]}, {"code": 655, "indent": 0, "parameters": ["let index = 4;"]}, {"code": 655, "indent": 0, "parameters": ["this.showCommonEventDialogue(type,String(index))"]}, {"code": 355, "indent": 0, "parameters": ["$gamePlayer.setDirection(2)"]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 213, "indent": 0, "parameters": [-1, 9, true]}, {"code": 355, "indent": 0, "parameters": ["let type = \"ninja\";"]}, {"code": 655, "indent": 0, "parameters": ["let index = 5;"]}, {"code": 655, "indent": 0, "parameters": ["this.showCommonEventDialogue(type,String(index))"]}, {"code": 355, "indent": 0, "parameters": ["$gamePlayer.moveToTarget(9.5,6.2)"]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "!$gamePlayer.isMoved()"]}, {"code": 355, "indent": 2, "parameters": ["$gamePlayer.setDirection(4)"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [4]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 121, "indent": 0, "parameters": [30, 30, 1]}, {"code": 121, "indent": 0, "parameters": [55, 55, 1]}, {"code": 355, "indent": 0, "parameters": ["$gameNumberArray.setValue(18,[])"]}, {"code": 356, "indent": 0, "parameters": ["SetSelectItemType 0"]}, {"code": 118, "indent": 0, "parameters": ["start"]}, {"code": 104, "indent": 0, "parameters": [90, 2]}, {"code": 108, "indent": 0, "parameters": ["初始化对话框"]}, {"code": 355, "indent": 0, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["let target = -1;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.steupCEQJ(110,eid,{target:target});"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.steupCEQJ(110,eid,{choice:true});"]}, {"code": 655, "indent": 0, "parameters": ["this.wait(4)"]}, {"code": 111, "indent": 0, "parameters": [1, 90, 0, 0, 0]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["选项赋予名称"]}, {"code": 355, "indent": 0, "parameters": ["let eid = \"ninja\";"]}, {"code": 655, "indent": 0, "parameters": ["let index = 7;"]}, {"code": 655, "indent": 0, "parameters": ["let option1 = window.mapCommonEventDialogue[eid][String(index)][0];"]}, {"code": 655, "indent": 0, "parameters": ["let option2 = window.mapCommonEventDialogue[eid][String(index)][1];"]}, {"code": 655, "indent": 0, "parameters": [" $gameStrings.setValue(6,option1);"]}, {"code": 655, "indent": 0, "parameters": [" $gameStrings.setValue(7,option2);"]}, {"code": 355, "indent": 0, "parameters": ["let type = \"ninja\";"]}, {"code": 655, "indent": 0, "parameters": ["let index = 6;"]}, {"code": 655, "indent": 0, "parameters": ["this.showCommonEventDialogue(type,String(index))"]}, {"code": 102, "indent": 0, "parameters": [["\\str[6]", "\\str[7]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\str[6]"]}, {"code": 355, "indent": 1, "parameters": ["let itemId = $gameVariables.value(90);"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.setValue(75, itemId);"]}, {"code": 655, "indent": 1, "parameters": ["$gameParty.gainItem($dataItems[itemId], -1);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\str[7]"]}, {"code": 355, "indent": 1, "parameters": ["let type = \"ninja\";"]}, {"code": 655, "indent": 1, "parameters": ["let index = 8;"]}, {"code": 655, "indent": 1, "parameters": ["this.showCommonEventDialogue(type,String(index))"]}, {"code": 119, "indent": 1, "parameters": ["start"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 231, "indent": 0, "parameters": [29, "black", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 356, "indent": 0, "parameters": [">图片快捷操作 : 图片[29] : 切换图片层级 : 最顶层"]}, {"code": 232, "indent": 0, "parameters": [29, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, true]}, {"code": 250, "indent": 0, "parameters": [{"name": "高速DIY 料理系 ジャカガツガツ", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 0, "parameters": [78, 78, 0, 4, "$gameMap.checkCraftingFormula()"]}, {"code": 230, "indent": 0, "parameters": [180]}, {"code": 122, "indent": 0, "parameters": [14, 14, 0, 2, 10, 20]}, {"code": 117, "indent": 0, "parameters": [3]}, {"code": 111, "indent": 0, "parameters": [12, "!$dataItems[$gameVariables.value(78)].note.includes(\"<黑暗料理>\")"]}, {"code": 355, "indent": 1, "parameters": ["let IMG = 'Crafting_Finish';"]}, {"code": 655, "indent": 1, "parameters": ["let path = \"crafting_scene\";"]}, {"code": 655, "indent": 1, "parameters": ["$gameScreen.showPictureFromPath(30, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 250, "indent": 1, "parameters": [{"name": "032myuu_YumeSE_MassagePositive01", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["let IMG = 'Crafting_unsuccess';"]}, {"code": 655, "indent": 1, "parameters": ["let path = \"crafting_scene\";"]}, {"code": 655, "indent": 1, "parameters": ["$gameScreen.showPictureFromPath(30, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 250, "indent": 1, "parameters": [{"name": "034myuu_YumeSE_MassageGag01", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 78, 0, 3, 3]}, {"code": 355, "indent": 1, "parameters": ["var IMG = 'crafting' + $gameVariables.value(78);"]}, {"code": 655, "indent": 1, "parameters": ["var path = \"crafting_scene\";"]}, {"code": 655, "indent": 1, "parameters": ["var X = 893;"]}, {"code": 655, "indent": 1, "parameters": ["var Y = 474;"]}, {"code": 655, "indent": 1, "parameters": ["$gameScreen.showPictureFromPath(31, path, IMG, 0, X, Y, 100, 100, 255, 0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["var IMG = 'crafting' + Math.randomInt(4);"]}, {"code": 655, "indent": 1, "parameters": ["var path = \"crafting_scene\";"]}, {"code": 655, "indent": 1, "parameters": ["var X = 893;"]}, {"code": 655, "indent": 1, "parameters": ["var Y = 474;"]}, {"code": 655, "indent": 1, "parameters": ["$gameScreen.showPictureFromPath(31, path, IMG, 0, X, Y, 100, 100, 255, 0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": [">图片快捷操作 : 批量图片[30,31] : 切换图片层级 : 最顶层"]}, {"code": 232, "indent": 0, "parameters": [29, 0, 0, 0, 0, 0, 100, 100, 125, 0, 20, false]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["const maxPictures = 40; "]}, {"code": 655, "indent": 0, "parameters": ["for (let pictureId = 1; pictureId <= maxPictures; pictureId++) {"]}, {"code": 655, "indent": 0, "parameters": ["    let picture = $gameScreen.picture(pictureId);"]}, {"code": 655, "indent": 0, "parameters": ["    if (picture) {"]}, {"code": 655, "indent": 0, "parameters": ["        $gameScreen.erasePicture(pictureId);"]}, {"code": 655, "indent": 0, "parameters": ["    }"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 355, "indent": 0, "parameters": ["$gamePlayer.setDirection(8);"]}, {"code": 655, "indent": 0, "parameters": ["$gamePlayer.locate(12.2,4.5);"]}, {"code": 108, "indent": 0, "parameters": ["设置料理和蒸汽效果"]}, {"code": 355, "indent": 0, "parameters": ["let index = $dataItems[$gameVariables.value(78)].iconIndex;"]}, {"code": 655, "indent": 0, "parameters": ["if ( index == 0 ) {index = 1542;}"]}, {"code": 655, "indent": 0, "parameters": ["let iconScale = 0.4; if (Utils.isMobileDevice()) iconScale = 0.8;"]}, {"code": 655, "indent": 0, "parameters": ["let food = QJ.MPMZ.Shoot({"]}, {"code": 655, "indent": 0, "parameters": ["    groupName:['food'], img:[\"I\",index],"]}, {"code": 655, "indent": 0, "parameters": ["    position:[['Map',12],['Map',3.5]],"]}, {"code": 655, "indent": 0, "parameters": ["    initialRotation:['S',0], imgRotation:['F'],"]}, {"code": 655, "indent": 0, "parameters": ["    scale:iconScale, existData:[ ], moveType:['S',0],z:\"E\","]}, {"code": 655, "indent": 0, "parameters": ["}); "]}, {"code": 655, "indent": 0, "parameters": ["  let posX = (food.x-24) / 48; let posY = (food.y-12) / 48;"]}, {"code": 655, "indent": 0, "parameters": ["  $gameScreen._particle.particleSet(0, \"dish_steam\", 'tilemap', 'dish_steam_c', 'above', posX, posY); "]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 108, "indent": 0, "parameters": ["初始化对话框"]}, {"code": 355, "indent": 0, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["let target = -1;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.steupCEQJ(110,eid,{target:target});"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.steupCEQJ(110,eid,{choice:true});"]}, {"code": 655, "indent": 0, "parameters": ["this.wait(4)"]}, {"code": 355, "indent": 0, "parameters": ["let type = \"ninja\";"]}, {"code": 655, "indent": 0, "parameters": ["let index = 9;"]}, {"code": 655, "indent": 0, "parameters": ["this.showCommonEventDialogue(type,String(index))"]}, {"code": 213, "indent": 0, "parameters": [0, 8, true]}, {"code": 356, "indent": 0, "parameters": [">行走图动画序列 : 本事件 : 播放简单状态元集合 : 集合[饿晕2]"]}, {"code": 108, "indent": 0, "parameters": ["初始化对话框"]}, {"code": 355, "indent": 0, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["let target = eid;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.steupCEQJ(110,eid,{target:target});"]}, {"code": 655, "indent": 0, "parameters": ["this.wait(4)"]}, {"code": 355, "indent": 0, "parameters": ["QJ.MPMZ.deleteProjectile('guuuu');"]}, {"code": 355, "indent": 0, "parameters": ["let type = \"ninja\";"]}, {"code": 655, "indent": 0, "parameters": ["let index = 10;"]}, {"code": 655, "indent": 0, "parameters": ["this.showCommonEventDialogue(type,String(index))"]}, {"code": 356, "indent": 0, "parameters": [">持续动作 : 本事件 : 左右震动 : 持续时间[99999] : 周期[5] : 震动幅度[1]"]}, {"code": 355, "indent": 0, "parameters": ["let eid = \"ninja\";"]}, {"code": 655, "indent": 0, "parameters": ["let index = 11;"]}, {"code": 655, "indent": 0, "parameters": ["let textArray = window.mapCommonEventDialogue[eid][String(index)];"]}, {"code": 655, "indent": 0, "parameters": ["var template = \"\\\\{\\\\dDCCE[文本[%TEXT%]:预设[11]]\\\\}\";"]}, {"code": 655, "indent": 0, "parameters": ["textArray = textArray.map(t => template.replace(\"%TEXT%\", t));"]}, {"code": 655, "indent": 0, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 356, "indent": 0, "parameters": [">持续动作 : 本事件 : 立即终止动作"]}, {"code": 356, "indent": 0, "parameters": [">行走图动画序列 : 本事件 : 播放简单状态元集合 : 集合[静止]"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameMap.getGroupBulletListQJ('food').length > 0) {"]}, {"code": 655, "indent": 0, "parameters": ["let bulletList = $gameMap.getGroupBulletListQJ('food');"]}, {"code": 655, "indent": 0, "parameters": ["bulletList.forEach(bid => {"]}, {"code": 655, "indent": 0, "parameters": ["    let bullet = $gameMap._mapBulletsQJ[bid];"]}, {"code": 655, "indent": 0, "parameters": ["    if (bullet) {"]}, {"code": 655, "indent": 0, "parameters": ["        bullet.changeAttribute('z','W');"]}, {"code": 655, "indent": 0, "parameters": ["         }"]}, {"code": 655, "indent": 0, "parameters": ["    });"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Jump2", "volume": 65, "pitch": 100, "pan": 0}]}, {"code": 212, "indent": 0, "parameters": [-1, 140, false]}, {"code": 355, "indent": 0, "parameters": ["    QJ.MPMZ.Shoot({"]}, {"code": 655, "indent": 0, "parameters": ["        img:\"animehit[5,4]\","]}, {"code": 655, "indent": 0, "parameters": ["        position:[['P'],['P']],"]}, {"code": 655, "indent": 0, "parameters": ["        initialRotation:['S',0],"]}, {"code": 655, "indent": 0, "parameters": ["        imgRotation:['F'],"]}, {"code": 655, "indent": 0, "parameters": ["        collisionBox:['C',1],"]}, {"code": 655, "indent": 0, "parameters": ["        moveType:['S',0],"]}, {"code": 655, "indent": 0, "parameters": ["        existData:[\t"]}, {"code": 655, "indent": 0, "parameters": ["           {t:['Time',19]},"]}, {"code": 655, "indent": 0, "parameters": ["        ],"]}, {"code": 655, "indent": 0, "parameters": ["    });"]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen._particle.particleClear('dish_steam');"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.event(this._eventId).jump(0,0);"]}, {"code": 355, "indent": 0, "parameters": ["let id = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.event(id).steupCEQJ(3)"]}, {"code": 230, "indent": 0, "parameters": [25]}, {"code": 108, "indent": 0, "parameters": ["初始化对话框"]}, {"code": 355, "indent": 0, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["let target = eid;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.steupCEQJ(110,eid,{target:target});"]}, {"code": 655, "indent": 0, "parameters": ["this.wait(4)"]}, {"code": 355, "indent": 0, "parameters": ["let type = \"ninja\";"]}, {"code": 655, "indent": 0, "parameters": ["let index = 12;"]}, {"code": 655, "indent": 0, "parameters": ["this.showCommonEventDialogue(type,String(index))"]}, {"code": 356, "indent": 0, "parameters": [">行走图动画序列 : 本事件 : 播放简单状态元集合 : 集合[吃东西1]"]}, {"code": 108, "indent": 0, "parameters": ["初始化对话框"]}, {"code": 355, "indent": 0, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["let target = -1;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.steupCEQJ(110,eid,{target:target});"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.steupCEQJ(110,eid,{choice:true});"]}, {"code": 655, "indent": 0, "parameters": ["this.wait(4)"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 213, "indent": 0, "parameters": [-1, 7, true]}, {"code": 355, "indent": 0, "parameters": ["let type = \"ninja\";"]}, {"code": 655, "indent": 0, "parameters": ["let index = 13;"]}, {"code": 655, "indent": 0, "parameters": ["this.showCommonEventDialogue(type,String(index))"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 123, "indent": 0, "parameters": ["B", 0]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 122, "indent": 0, "parameters": [14, 14, 0, 0, 5]}, {"code": 117, "indent": 0, "parameters": [3]}, {"code": 356, "indent": 0, "parameters": [">行走图动画序列 : 本事件 : 播放简单状态元集合 : 集合[吃东西2]"]}, {"code": 355, "indent": 0, "parameters": ["$gamePlayer.setDirection(8);"]}, {"code": 655, "indent": 0, "parameters": ["$gamePlayer.locate(12.2,4.5);"]}, {"code": 655, "indent": 0, "parameters": ["$gamePlayer.drill_EASA_setEnabled(true);"]}, {"code": 655, "indent": 0, "parameters": ["QJ.MPMZ.deleteProjectile('food');"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["初始化对话框"]}, {"code": 355, "indent": 0, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["let target = -1;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.steupCEQJ(110,eid,{target:target});"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.steupCEQJ(110,eid,{choice:true});"]}, {"code": 655, "indent": 0, "parameters": ["$gamePlayer.setDirection(8)"]}, {"code": 655, "indent": 0, "parameters": ["ctb.useTurnPlayer = false;"]}, {"code": 655, "indent": 0, "parameters": ["this.wait(4)"]}, {"code": 213, "indent": 0, "parameters": [-1, 6, true]}, {"code": 355, "indent": 0, "parameters": ["let type = \"ninja\";"]}, {"code": 655, "indent": 0, "parameters": ["let index = 14;"]}, {"code": 655, "indent": 0, "parameters": ["this.showCommonEventDialogue(type,String(index))"]}, {"code": 355, "indent": 0, "parameters": ["let id = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.event(id).steupCEQJ(4)"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<<出现条件>> : 独立开关 : E : 为ON"]}, {"code": 355, "indent": 0, "parameters": ["let target = $gamePlayer;"]}, {"code": 655, "indent": 0, "parameters": ["target._directionFix = true;"]}, {"code": 655, "indent": 0, "parameters": ["target._drill_JSp['enabled'] = true;"]}, {"code": 655, "indent": 0, "parameters": ["target._drill_JSp['height'] = 96;"]}, {"code": 655, "indent": 0, "parameters": ["target._drill_JSp['time'] = 30;"]}, {"code": 655, "indent": 0, "parameters": ["target._drill_JSp['speed'] = -1;"]}, {"code": 655, "indent": 0, "parameters": ["target.jump(-2,3)"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Damage3", "volume": 45, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["let target = $gamePlayer;"]}, {"code": 655, "indent": 0, "parameters": ["target._directionFix = false;"]}, {"code": 655, "indent": 0, "parameters": ["target.setDirection(8);"]}, {"code": 356, "indent": 0, "parameters": [">行走图动画序列 : 玩家 : 播放简单状态元集合 : 集合[等待急救（前）]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "C", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<<出现条件>> : 独立开关 : E : 为ON"]}, {"code": 108, "indent": 0, "parameters": ["初始化对话框"]}, {"code": 355, "indent": 0, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["let target = eid;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.steupCEQJ(110,eid,{target:target});"]}, {"code": 655, "indent": 0, "parameters": ["this.wait(4)"]}, {"code": 355, "indent": 0, "parameters": ["let type = \"ninja\";"]}, {"code": 655, "indent": 0, "parameters": ["let index = 15;"]}, {"code": 655, "indent": 0, "parameters": ["this.showCommonEventDialogue(type,String(index))"]}, {"code": 355, "indent": 0, "parameters": ["let type = \"ninja\";"]}, {"code": 655, "indent": 0, "parameters": ["let index = 16;"]}, {"code": 655, "indent": 0, "parameters": ["this.showCommonEventDialogue(type,String(index))"]}, {"code": 213, "indent": 0, "parameters": [-1, 6, true]}, {"code": 108, "indent": 0, "parameters": ["初始化对话框"]}, {"code": 355, "indent": 0, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["let target = -1;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.steupCEQJ(110,eid,{target:target});"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.steupCEQJ(110,eid,{choice:true});"]}, {"code": 655, "indent": 0, "parameters": ["$gamePlayer.setDirection(8)"]}, {"code": 655, "indent": 0, "parameters": ["ctb.useTurnPlayer = false;"]}, {"code": 655, "indent": 0, "parameters": ["this.wait(4)"]}, {"code": 355, "indent": 0, "parameters": ["let type = \"ninja\";"]}, {"code": 655, "indent": 0, "parameters": ["let index = 17;"]}, {"code": 655, "indent": 0, "parameters": ["this.showCommonEventDialogue(type,String(index))"]}, {"code": 108, "indent": 0, "parameters": ["初始化对话框"]}, {"code": 355, "indent": 0, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["let target = eid;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.steupCEQJ(110,eid,{target:target});"]}, {"code": 655, "indent": 0, "parameters": ["this.wait(4)"]}, {"code": 355, "indent": 0, "parameters": ["let type = \"ninja\";"]}, {"code": 655, "indent": 0, "parameters": ["let index = 18;"]}, {"code": 655, "indent": 0, "parameters": ["this.showCommonEventDialogue(type,String(index))"]}, {"code": 355, "indent": 0, "parameters": ["let type = \"ninja\";"]}, {"code": 655, "indent": 0, "parameters": ["let index = 19;"]}, {"code": 655, "indent": 0, "parameters": ["this.showCommonEventDialogue(type,String(index))"]}, {"code": 355, "indent": 0, "parameters": ["let type = \"ninja\";"]}, {"code": 655, "indent": 0, "parameters": ["let index = 20;"]}, {"code": 655, "indent": 0, "parameters": ["this.showCommonEventDialogue(type,String(index))"]}, {"code": 355, "indent": 0, "parameters": ["let type = \"ninja\";"]}, {"code": 655, "indent": 0, "parameters": ["let index = 21;"]}, {"code": 655, "indent": 0, "parameters": ["this.showCommonEventDialogue(type,String(index))"]}, {"code": 355, "indent": 0, "parameters": ["let type = \"ninja\";"]}, {"code": 655, "indent": 0, "parameters": ["let index = 22;"]}, {"code": 655, "indent": 0, "parameters": ["this.showCommonEventDialogue(type,String(index))"]}, {"code": 108, "indent": 0, "parameters": ["初始化对话框"]}, {"code": 355, "indent": 0, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["let target = -1;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.steupCEQJ(110,eid,{target:target});"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.steupCEQJ(110,eid,{choice:true});"]}, {"code": 655, "indent": 0, "parameters": ["$gamePlayer.setDirection(8)"]}, {"code": 655, "indent": 0, "parameters": ["ctb.useTurnPlayer = false;"]}, {"code": 655, "indent": 0, "parameters": ["this.wait(4)"]}, {"code": 355, "indent": 0, "parameters": ["let type = \"ninja\";"]}, {"code": 655, "indent": 0, "parameters": ["let index = 23;"]}, {"code": 655, "indent": 0, "parameters": ["this.showCommonEventDialogue(type,String(index))"]}, {"code": 108, "indent": 0, "parameters": ["初始化对话框"]}, {"code": 355, "indent": 0, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["let target = eid;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.steupCEQJ(110,eid,{target:target});"]}, {"code": 655, "indent": 0, "parameters": ["this.wait(4)"]}, {"code": 355, "indent": 0, "parameters": ["let type = \"ninja\";"]}, {"code": 655, "indent": 0, "parameters": ["let index = 24;"]}, {"code": 655, "indent": 0, "parameters": ["this.showCommonEventDialogue(type,String(index))"]}, {"code": 355, "indent": 0, "parameters": ["let type = \"ninja\";"]}, {"code": 655, "indent": 0, "parameters": ["let index = 25;"]}, {"code": 655, "indent": 0, "parameters": ["this.showCommonEventDialogue(type,String(index))"]}, {"code": 108, "indent": 0, "parameters": ["初始化对话框"]}, {"code": 355, "indent": 0, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["let target = -1;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.steupCEQJ(110,eid,{target:target});"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.steupCEQJ(110,eid,{choice:true});"]}, {"code": 655, "indent": 0, "parameters": ["$gamePlayer.setDirection(8)"]}, {"code": 655, "indent": 0, "parameters": ["ctb.useTurnPlayer = false;"]}, {"code": 655, "indent": 0, "parameters": ["this.wait(4)"]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 355, "indent": 0, "parameters": ["let type = \"ninja\";"]}, {"code": 655, "indent": 0, "parameters": ["let index = 26;"]}, {"code": 655, "indent": 0, "parameters": ["this.showCommonEventDialogue(type,String(index))"]}, {"code": 108, "indent": 0, "parameters": ["初始化对话框"]}, {"code": 355, "indent": 0, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["let target = eid;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.steupCEQJ(110,eid,{target:target});"]}, {"code": 655, "indent": 0, "parameters": ["this.wait(4)"]}, {"code": 355, "indent": 0, "parameters": ["let type = \"ninja\";"]}, {"code": 655, "indent": 0, "parameters": ["let index = 27;"]}, {"code": 655, "indent": 0, "parameters": ["this.showCommonEventDialogue(type,String(index))"]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 213, "indent": 0, "parameters": [0, 8, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 356, "indent": 0, "parameters": [">行走图动画序列 : 本事件 : 播放简单状态元集合 : 集合[静止]"]}, {"code": 355, "indent": 0, "parameters": ["$gameMap.event(this._eventId).jump(0,0)"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Jump2", "volume": 65, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 355, "indent": 0, "parameters": ["let eid = \"ninja\";"]}, {"code": 655, "indent": 0, "parameters": ["let index = 28;"]}, {"code": 655, "indent": 0, "parameters": ["let textArray = window.mapCommonEventDialogue[eid][String(index)];"]}, {"code": 655, "indent": 0, "parameters": ["var template = \"\\\\{\\\\dDCCE[文本[%TEXT%]:预设[11]]\\\\}\";"]}, {"code": 655, "indent": 0, "parameters": ["textArray = textArray.map(t => template.replace(\"%TEXT%\", t));"]}, {"code": 655, "indent": 0, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 35, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 15, "parameters": [30], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 35, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 108, "indent": 0, "parameters": ["初始化对话框"]}, {"code": 355, "indent": 0, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["let target = -1;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.steupCEQJ(110,eid,{target:target});"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.steupCEQJ(110,eid,{choice:true});"]}, {"code": 655, "indent": 0, "parameters": ["$gamePlayer.setDirection(8)"]}, {"code": 655, "indent": 0, "parameters": ["ctb.useTurnPlayer = false;"]}, {"code": 655, "indent": 0, "parameters": ["this.wait(4)"]}, {"code": 355, "indent": 0, "parameters": ["let type = \"ninja\";"]}, {"code": 655, "indent": 0, "parameters": ["let index = 29;"]}, {"code": 655, "indent": 0, "parameters": ["this.showCommonEventDialogue(type,String(index))"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 36, "indent": null}, {"code": 29, "parameters": [6], "indent": null}, {"code": 45, "parameters": ["ConfigManager.alwaysDash = true"], "indent": 0}, {"code": 16, "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [6], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["ConfigManager.alwaysDash = true"], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">消失动作 : 本事件 : 纵向挤扁 : 时间[20] : 纵向比例[1.5]"]}, {"code": 250, "indent": 0, "parameters": [{"name": "瞬間移動・姿を現す・素早い・シュバ！", "volume": 60, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 16, "indent": null}, {"code": 35, "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 35, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 355, "indent": 0, "parameters": ["$gameMap.event(this._eventId).setDirection(8);"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.event(this._eventId).locate(12,9);"]}, {"code": 356, "indent": 0, "parameters": [">显现动作 : 本事件 : 纵向冒出 : 时间[20] : 纵向比例[1.5]"]}, {"code": 250, "indent": 0, "parameters": [{"name": "瞬間移動・姿を現す・素早い・シュバ！", "volume": 60, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 213, "indent": 0, "parameters": [-1, 1, true]}, {"code": 108, "indent": 0, "parameters": ["初始化对话框"]}, {"code": 355, "indent": 0, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["let target = -1;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.steupCEQJ(110,eid,{target:target});"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.steupCEQJ(110,eid,{choice:true});"]}, {"code": 655, "indent": 0, "parameters": ["$gamePlayer.setDirection(8)"]}, {"code": 655, "indent": 0, "parameters": ["ctb.useTurnPlayer = false;"]}, {"code": 655, "indent": 0, "parameters": ["this.wait(4)"]}, {"code": 355, "indent": 0, "parameters": ["let type = \"ninja\";"]}, {"code": 655, "indent": 0, "parameters": ["let index = 30;"]}, {"code": 655, "indent": 0, "parameters": ["this.showCommonEventDialogue(type,String(index))"]}, {"code": 108, "indent": 0, "parameters": ["初始化对话框"]}, {"code": 355, "indent": 0, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["let target = eid;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.steupCEQJ(110,eid,{target:target});"]}, {"code": 655, "indent": 0, "parameters": ["this.wait(4)"]}, {"code": 355, "indent": 0, "parameters": ["let type = \"ninja\";"]}, {"code": 655, "indent": 0, "parameters": ["let index = 31;"]}, {"code": 655, "indent": 0, "parameters": ["this.showCommonEventDialogue(type,String(index))"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 35, "indent": null}, {"code": 29, "parameters": [6], "indent": null}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 35, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [6], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 108, "indent": 0, "parameters": ["初始化对话框"]}, {"code": 355, "indent": 0, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["let target = -1;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.steupCEQJ(110,eid,{target:target});"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.steupCEQJ(110,eid,{choice:true});"]}, {"code": 655, "indent": 0, "parameters": ["$gamePlayer.setDirection(8)"]}, {"code": 655, "indent": 0, "parameters": ["ctb.useTurnPlayer = false;"]}, {"code": 655, "indent": 0, "parameters": ["this.wait(4)"]}, {"code": 355, "indent": 0, "parameters": ["let type = \"ninja\";"]}, {"code": 655, "indent": 0, "parameters": ["let index = 32;"]}, {"code": 655, "indent": 0, "parameters": ["this.showCommonEventDialogue(type,String(index))"]}, {"code": 108, "indent": 0, "parameters": ["初始化对话框"]}, {"code": 355, "indent": 0, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["let target = eid;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.steupCEQJ(110,eid,{target:target});"]}, {"code": 655, "indent": 0, "parameters": ["this.wait(4)"]}, {"code": 355, "indent": 0, "parameters": ["let type = \"ninja\";"]}, {"code": 655, "indent": 0, "parameters": ["let index = 33;"]}, {"code": 655, "indent": 0, "parameters": ["this.showCommonEventDialogue(type,String(index))"]}, {"code": 355, "indent": 0, "parameters": ["let type = \"ninja\";"]}, {"code": 655, "indent": 0, "parameters": ["let index = 34;"]}, {"code": 655, "indent": 0, "parameters": ["this.showCommonEventDialogue(type,String(index))"]}, {"code": 108, "indent": 0, "parameters": ["初始化对话框"]}, {"code": 355, "indent": 0, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["let target = -1;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.steupCEQJ(110,eid,{target:target});"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.steupCEQJ(110,eid,{choice:true});"]}, {"code": 655, "indent": 0, "parameters": ["$gamePlayer.setDirection(8)"]}, {"code": 655, "indent": 0, "parameters": ["ctb.useTurnPlayer = false;"]}, {"code": 655, "indent": 0, "parameters": ["this.wait(4)"]}, {"code": 355, "indent": 0, "parameters": ["let type = \"ninja\";"]}, {"code": 655, "indent": 0, "parameters": ["let index = 35;"]}, {"code": 655, "indent": 0, "parameters": ["this.showCommonEventDialogue(type,String(index))"]}, {"code": 108, "indent": 0, "parameters": ["初始化对话框"]}, {"code": 355, "indent": 0, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["let target = eid;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.steupCEQJ(110,eid,{target:target});"]}, {"code": 655, "indent": 0, "parameters": ["this.wait(4)"]}, {"code": 355, "indent": 0, "parameters": ["let type = \"ninja\";"]}, {"code": 655, "indent": 0, "parameters": ["let index = 36;"]}, {"code": 655, "indent": 0, "parameters": ["this.showCommonEventDialogue(type,String(index))"]}, {"code": 213, "indent": 0, "parameters": [-1, 1, true]}, {"code": 213, "indent": 0, "parameters": [0, 3, false]}, {"code": 355, "indent": 0, "parameters": ["let type = \"ninja\";"]}, {"code": 655, "indent": 0, "parameters": ["let index = 37;"]}, {"code": 655, "indent": 0, "parameters": ["this.showCommonEventDialogue(type,String(index))"]}, {"code": 108, "indent": 0, "parameters": ["初始化对话框"]}, {"code": 355, "indent": 0, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["let target = -1;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.steupCEQJ(110,eid,{target:target});"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.steupCEQJ(110,eid,{choice:true});"]}, {"code": 655, "indent": 0, "parameters": ["$gamePlayer.setDirection(8)"]}, {"code": 655, "indent": 0, "parameters": ["ctb.useTurnPlayer = false;"]}, {"code": 655, "indent": 0, "parameters": ["this.wait(4)"]}, {"code": 355, "indent": 0, "parameters": ["let type = \"ninja\";"]}, {"code": 655, "indent": 0, "parameters": ["let index = 38;"]}, {"code": 655, "indent": 0, "parameters": ["this.showCommonEventDialogue(type,String(index))"]}, {"code": 108, "indent": 0, "parameters": ["初始化对话框"]}, {"code": 355, "indent": 0, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["let target = eid;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.steupCEQJ(110,eid,{target:target});"]}, {"code": 655, "indent": 0, "parameters": ["this.wait(4)"]}, {"code": 355, "indent": 0, "parameters": ["let type = \"ninja\";"]}, {"code": 655, "indent": 0, "parameters": ["let index = 39;"]}, {"code": 655, "indent": 0, "parameters": ["this.showCommonEventDialogue(type,String(index))"]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["初始化对话框"]}, {"code": 355, "indent": 0, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["let target = -1;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.steupCEQJ(110,eid,{target:target});"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.steupCEQJ(110,eid,{choice:true});"]}, {"code": 655, "indent": 0, "parameters": ["$gamePlayer.setDirection(8)"]}, {"code": 655, "indent": 0, "parameters": ["ctb.useTurnPlayer = false;"]}, {"code": 655, "indent": 0, "parameters": ["this.wait(4)"]}, {"code": 355, "indent": 0, "parameters": ["let eid = \"ninja\";"]}, {"code": 655, "indent": 0, "parameters": ["let index = 40;"]}, {"code": 655, "indent": 0, "parameters": ["let textArray = window.mapCommonEventDialogue[eid][String(index)];"]}, {"code": 655, "indent": 0, "parameters": ["var template = \"\\\\{\\\\dDCCE[文本[%TEXT%]:预设[11]]\\\\}\";"]}, {"code": 655, "indent": 0, "parameters": ["textArray = textArray.map(t => template.replace(\"%TEXT%\", t));"]}, {"code": 655, "indent": 0, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 318, "indent": 0, "parameters": [0, 1, 0, 10]}, {"code": 318, "indent": 0, "parameters": [0, 1, 0, 11]}, {"code": 355, "indent": 0, "parameters": ["$gameMap.event(this._eventId).setDirection(2);"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.event(this._eventId).locate(12,3);"]}, {"code": 356, "indent": 0, "parameters": [">行走图动画序列 : 本事件 : 播放简单状态元集合 : 集合[吃东西2]"]}, {"code": 355, "indent": 0, "parameters": ["let target = $gamePlayer;"]}, {"code": 655, "indent": 0, "parameters": ["target._directionFix = false;"]}, {"code": 655, "indent": 0, "parameters": ["target.setDirection(6);"]}, {"code": 655, "indent": 0, "parameters": ["target.locate(10,5);"]}, {"code": 356, "indent": 0, "parameters": [">行走图动画序列 : 玩家 : 播放简单状态元集合 : 集合[等待急救（前）]"]}, {"code": 122, "indent": 0, "parameters": [14, 14, 0, 0, 30]}, {"code": 117, "indent": 0, "parameters": [3]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["初始化对话框"]}, {"code": 355, "indent": 0, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["let target = eid;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.steupCEQJ(110,eid,{target:target});"]}, {"code": 655, "indent": 0, "parameters": ["this.wait(4)"]}, {"code": 355, "indent": 0, "parameters": ["let type = \"ninja\";"]}, {"code": 655, "indent": 0, "parameters": ["let index = 41;"]}, {"code": 655, "indent": 0, "parameters": ["this.showCommonEventDialogue(type,String(index))"]}, {"code": 355, "indent": 0, "parameters": ["let type = \"ninja\";"]}, {"code": 655, "indent": 0, "parameters": ["let index = 42;"]}, {"code": 655, "indent": 0, "parameters": ["this.showCommonEventDialogue(type,String(index))"]}, {"code": 355, "indent": 0, "parameters": ["let type = \"ninja\";"]}, {"code": 655, "indent": 0, "parameters": ["let index = 43;"]}, {"code": 655, "indent": 0, "parameters": ["this.showCommonEventDialogue(type,String(index))"]}, {"code": 108, "indent": 0, "parameters": ["初始化对话框"]}, {"code": 355, "indent": 0, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["let target = -1;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.steupCEQJ(110,eid,{target:target});"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.steupCEQJ(110,eid,{choice:true});"]}, {"code": 655, "indent": 0, "parameters": ["$gamePlayer.setDirection(8)"]}, {"code": 655, "indent": 0, "parameters": ["ctb.useTurnPlayer = false;"]}, {"code": 655, "indent": 0, "parameters": ["this.wait(4)"]}, {"code": 213, "indent": 0, "parameters": [-1, 7, true]}, {"code": 355, "indent": 0, "parameters": ["let type = \"ninja\";"]}, {"code": 655, "indent": 0, "parameters": ["let index = 44;"]}, {"code": 655, "indent": 0, "parameters": ["this.showCommonEventDialogue(type,String(index))"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["$gamePlayer.drill_EASA_setEnabled(true);"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 121, "indent": 0, "parameters": [118, 118, 1]}, {"code": 355, "indent": 0, "parameters": ["let key = [$gameMap.mapId(), this._eventId, 'F'];"]}, {"code": 655, "indent": 0, "parameters": ["$gameSelfSwitches.setValue(key, true);"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "C", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$Ninja", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<<出现条件>> : 独立开关 : F : 为ON"]}, {"code": 108, "indent": 0, "parameters": ["初始化对话框"]}, {"code": 355, "indent": 0, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["let target = eid;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.steupCEQJ(110,eid,{target:target});"]}, {"code": 655, "indent": 0, "parameters": ["this.wait(4)"]}, {"code": 355, "indent": 0, "parameters": ["let type = \"ninja\";"]}, {"code": 655, "indent": 0, "parameters": ["let index = 41;"]}, {"code": 655, "indent": 0, "parameters": ["this.showCommonEventDialogue(type,String(index))"]}, {"code": 355, "indent": 0, "parameters": ["let type = \"ninja\";"]}, {"code": 655, "indent": 0, "parameters": ["let index = 42;"]}, {"code": 655, "indent": 0, "parameters": ["this.showCommonEventDialogue(type,String(index))"]}, {"code": 355, "indent": 0, "parameters": ["let type = \"ninja\";"]}, {"code": 655, "indent": 0, "parameters": ["let index = 43;"]}, {"code": 655, "indent": 0, "parameters": ["this.showCommonEventDialogue(type,String(index))"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 12, "y": 3}, null]}