{"autoplayBgm": false, "autoplayBgs": false, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 29, "note": "<深渊>", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 20, "width": 21, "data": [5984, 5984, 5984, 5984, 5984, 5984, 5984, 6008, 2832, 2816, 2840, 6000, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 6008, 2832, 2816, 2840, 6000, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5988, 6012, 6022, 2832, 2816, 2840, 6000, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 6008, 6418, 6422, 2832, 2816, 2840, 6024, 6012, 5992, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5988, 6012, 6022, 6424, 6428, 2832, 2816, 2840, 6419, 6418, 6000, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 6012, 6012, 6012, 6022, 6418, 6422, 2850, 2837, 2845, 2844, 2841, 6425, 6424, 6024, 5992, 5984, 5984, 5984, 5984, 5984, 5984, 6418, 6418, 6418, 6422, 6424, 6428, 2834, 2854, 2898, 2900, 2833, 2836, 2852, 6419, 6000, 5984, 5984, 5984, 5984, 5984, 5984, 6424, 6424, 6424, 6428, 2850, 2817, 2842, 2898, 2869, 2902, 2832, 2816, 2840, 6425, 6024, 5992, 5984, 5984, 5984, 5984, 5984, 2836, 2836, 2836, 2836, 2817, 2816, 2840, 2904, 2902, 2850, 2817, 2816, 2818, 2852, 6419, 6024, 6012, 6012, 6012, 6012, 6012, 2816, 2816, 2816, 2816, 2820, 2844, 2846, 2849, 2849, 2845, 2824, 2816, 2816, 2840, 6425, 6419, 6418, 6418, 6418, 6418, 6418, 2844, 2844, 2844, 2844, 2854, 6018, 6004, 6004, 6004, 6020, 2832, 2816, 2816, 2822, 2861, 6425, 6424, 6424, 6424, 6424, 6424, 6004, 6004, 6004, 6004, 6004, 5985, 5984, 5984, 5984, 6008, 2832, 2816, 2816, 2840, 2907, 2886, 2884, 2900, 2859, 2838, 2836, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 6008, 2832, 2816, 2816, 2818, 2852, 2880, 2868, 2894, 2909, 2832, 2816, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 6008, 2832, 2816, 2816, 2816, 2840, 2904, 2902, 2859, 2849, 2845, 2844, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5988, 6022, 2832, 2816, 2816, 2816, 2818, 2837, 2861, 6018, 6004, 6004, 6004, 5984, 5984, 5984, 5984, 5984, 5984, 5988, 6012, 6022, 6422, 2832, 2816, 2816, 2816, 2816, 2840, 6018, 5985, 5984, 5984, 5984, 6012, 6012, 6012, 6012, 6012, 6012, 6022, 6418, 6422, 6428, 2832, 2816, 2816, 2816, 2816, 2840, 6000, 5984, 5984, 5984, 5984, 6418, 6418, 6418, 6418, 6418, 6418, 6422, 6424, 6428, 2851, 2845, 2844, 2824, 2816, 2820, 2854, 6000, 5984, 5984, 5984, 5984, 6424, 6424, 6424, 6424, 6424, 6424, 6428, 2850, 2837, 2855, 2994, 2996, 2834, 2844, 2854, 6018, 5985, 5984, 5984, 5984, 5984, 2836, 2816, 2837, 2849, 2849, 2849, 2839, 2845, 2854, 2994, 2965, 2998, 2848, 6018, 6004, 5985, 5984, 5984, 5984, 5984, 5984, 2816, 2816, 2840, 2907, 2886, 2900, 2860, 2994, 2980, 2961, 2984, 2851, 2855, 6000, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 2844, 2844, 2826, 2852, 2904, 2894, 2909, 3000, 2968, 2964, 2998, 2848, 6018, 5985, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 6004, 6020, 2856, 2846, 2838, 2836, 2836, 2852, 2978, 2998, 2851, 2855, 6000, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5986, 6004, 6020, 2856, 2844, 2844, 2840, 3004, 2850, 2842, 6018, 5985, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5986, 6004, 6004, 6020, 2818, 2836, 2821, 2854, 6000, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 6008, 2816, 2816, 2840, 6018, 5985, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 6008, 2816, 2816, 2840, 6000, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 6008, 2816, 2816, 2840, 6000, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 6008, 2816, 2816, 2840, 6000, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3050, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3040, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3040, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3042, 3029, 3047, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3048, 3046, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4242, 4244, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3042, 3028, 3044, 0, 4248, 4218, 4244, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3043, 3041, 3037, 3036, 3038, 3041, 3045, 4248, 4246, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3051, 3041, 3047, 0, 0, 0, 0, 0, 3052, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3474, 3460, 3476, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3456, 3440, 3442, 3476, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3050, 3480, 3448, 3440, 3464, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3025, 3044, 3456, 3440, 3442, 3476, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3024, 3032, 3480, 3448, 3440, 3464, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3024, 3032, 0, 3480, 3468, 3478, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3048, 3046, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4291, 4301, 0, 0, 3050, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4299, 4295, 0, 0, 3042, 3034, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3026, 3046, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3042, 3028, 3044, 0, 0, 0, 0, 0, 3043, 3047, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3048, 3036, 3018, 3044, 0, 0, 0, 0, 3052, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3048, 3038, 3053, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 363, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 256, 0, 0, 0, 0, 1, 0, 386, 386, 0, 0, 0, 0, 0, 0, 0, 0, 256, 257, 257, 404, 274, 0, 0, 0, 0, 0, 0, 0, 394, 0, 0, 0, 0, 0, 0, 0, 0, 272, 273, 273, 0, 317, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 365, 0, 0, 0, 384, 379, 0, 0, 0, 0, 257, 0, 363, 0, 12, 13, 0, 0, 0, 0, 0, 0, 365, 0, 0, 392, 0, 0, 0, 0, 0, 273, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 85, 86, 0, 0, 0, 350, 351, 308, 327, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 93, 8, 0, 0, 0, 358, 0, 0, 317, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 381, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 101, 0, 0, 95, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 404, 0, 0, 0, 0, 0, 363, 103, 0, 0, 0, 0, 0, 257, 0, 0, 0, 0, 257, 404, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 273, 274, 0, 0, 381, 273, 0, 390, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 315, 316, 358, 359, 317, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 363, 0, 0, 365, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 365, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 372, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 301, 283, 286, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 301, 291, 356, 374, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 367, 299, 371, 372, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 295, 349, 309, 299, 379, 380, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 356, 319, 357, 317, 307, 350, 351, 294, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 295, 308, 309, 0, 37, 315, 358, 359, 318, 356, 0, 0, 0, 0, 0, 0, 0, 349, 308, 350, 351, 327, 316, 317, 0, 45, 0, 0, 394, 307, 294, 0, 0, 0, 0, 0, 0, 0, 357, 316, 358, 359, 397, 397, 0, 0, 0, 0, 0, 0, 315, 318, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 283, 325, 334, 335, 285, 0, 307, 294, 371, 372, 372, 372, 383, 0, 283, 334, 335, 285, 283, 333, 284, 286, 363, 364, 356, 287, 285, 315, 318, 356, 382, 372, 383, 381, 295, 286, 355, 365, 287, 286, 0, 355, 0, 374, 372, 375, 355, 293, 0, 307, 294, 379, 380, 381, 295, 327, 0, 374, 375, 0, 0, 0, 0, 0, 0, 372, 372, 373, 367, 0, 315, 326, 283, 334, 335, 285, 317, 0, 0, 0, 0, 0, 0, 0, 0, 0, 372, 372, 373, 301, 0, 0, 315, 291, 363, 365, 287, 285, 0, 0, 0, 0, 0, 0, 0, 0, 0, 383, 380, 356, 367, 32, 0, 21, 291, 371, 375, 365, 287, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 295, 308, 309, 40, 2, 283, 286, 371, 372, 375, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 295, 327, 316, 317, 283, 325, 286, 0, 0, 0, 0, 0, 0, 382, 372, 372, 383, 0, 0, 295, 349, 327, 317, 0, 0, 366, 355, 364, 374, 0, 0, 0, 0, 294, 379, 382, 372, 373, 295, 308, 327, 357, 317, 0, 0, 1, 291, 371, 372, 372, 0, 0, 0, 0, 326, 294, 379, 380, 356, 319, 316, 317, 0, 0, 0, 0, 283, 286, 371, 372, 372, 0, 0, 0, 0, 315, 326, 308, 350, 351, 309, 0, 0, 0, 0, 0, 0, 299, 356, 374, 372, 0, 0, 0, 0, 0, 283, 334, 335, 334, 335, 285, 0, 0, 0, 0, 0, 283, 286, 0, 0, 0, 0, 0, 0, 0, 0, 286, 356, 364, 364, 355, 293, 0, 0, 0, 0, 0, 291, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 374, 372, 372, 373, 311, 285, 8, 0, 0, 283, 286, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 372, 372, 372, 375, 365, 287, 333, 285, 0, 366, 356, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 372, 372, 375, 364, 356, 293, 283, 286, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 372, 373, 301, 366, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 383, 381, 367, 291, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 295, 309, 299, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 319, 317, 299, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 5, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 5, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 5, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 5, 5, 5, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 5, 5, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 0, 0, 255, 255, 255, 255, 255, 255, 255, 0, 0, 5, 0, 0, 0, 5, 5, 5, 0, 5, 5, 5, 0, 0, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 5, 5, 5, 5, 255, 255, 0, 5, 5, 0, 0, 0, 255, 255, 255, 0, 0, 0, 255, 255, 5, 5, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 5, 5, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 5, 5, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 5, 5, 0, 0, 255, 255, 255, 255, 255, 255, 255, 0, 255, 255, 255, 255, 0, 0, 0, 0, 0, 5, 5, 0, 0, 255, 255, 255, 255, 255, 255, 255, 0, 0, 255, 255, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 0, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 255, 255, 255, 0, 0, 0, 0, 0, 5, 5, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 5, 0, 0, 5, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 5, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255], "events": [null, {"id": 1, "name": "EV001", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 0, "characterIndex": 3}, "list": [{"code": 356, "indent": 0, "parameters": ["particle set stardust_w weather"]}, {"code": 241, "indent": 0, "parameters": [{"name": "彷徨う小さな侵入者(Wandering Little Intruder)", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "$gameScreen.zoomScale() == 1"]}, {"code": 356, "indent": 1, "parameters": ["d<PERSON><PERSON><PERSON> 2 60 player"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["particle set splash_walk walk:player:8 def below"]}, {"code": 356, "indent": 0, "parameters": ["particle set dust_walk walk:player:0 def below"]}, {"code": 356, "indent": 0, "parameters": ["particle set grass_walk walk:player:5 def below"]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 16, "y": 21}, {"id": 2, "name": "EV002", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<width: 0.5>"]}, {"code": 408, "indent": 0, "parameters": ["<height: 5>"]}, {"code": 408, "indent": 0, "parameters": ["<offsetX: 0>"]}, {"code": 408, "indent": 0, "parameters": ["<offsetY: 0>"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Move", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 121, "indent": 0, "parameters": [40, 40, 0]}, {"code": 201, "indent": 0, "parameters": [0, 15, 23, 11, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 0, "y": 18}, {"id": 3, "name": "EV003", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<width: 5>"]}, {"code": 408, "indent": 0, "parameters": ["<height: 0.5>"]}, {"code": 408, "indent": 0, "parameters": ["<offsetX: 0>"]}, {"code": 408, "indent": 0, "parameters": ["<offsetY: 0>"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Move", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 121, "indent": 0, "parameters": [40, 40, 0]}, {"code": 201, "indent": 0, "parameters": [0, 30, 11, 1, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 7, "y": 28}, {"id": 4, "name": "EV004", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<width: 0.5>"]}, {"code": 408, "indent": 0, "parameters": ["<height: 5>"]}, {"code": 408, "indent": 0, "parameters": ["<offsetX: 0>"]}, {"code": 408, "indent": 0, "parameters": ["<offsetY: 0>"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Move", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 121, "indent": 0, "parameters": [40, 40, 0]}, {"code": 201, "indent": 0, "parameters": [0, 30, 1, 13, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 20, "y": 10}, {"id": 5, "name": "EV005", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<width: 5>"]}, {"code": 408, "indent": 0, "parameters": ["<height: 0.5>"]}, {"code": 408, "indent": 0, "parameters": ["<offsetX: 0>"]}, {"code": 408, "indent": 0, "parameters": ["<offsetY: 0>"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Move", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 121, "indent": 0, "parameters": [40, 40, 0]}, {"code": 201, "indent": 0, "parameters": [0, 15, 13, 23, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 7, "y": 0}, {"id": 6, "name": "EV006", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<width: 0.5>"]}, {"code": 408, "indent": 0, "parameters": ["<height: 5>"]}, {"code": 408, "indent": 0, "parameters": ["<offsetX: 0>"]}, {"code": 408, "indent": 0, "parameters": ["<offsetY: 0>"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Move", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 121, "indent": 0, "parameters": [40, 40, 0]}, {"code": 201, "indent": 0, "parameters": [0, 35, 23, 11, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 0, "y": 7}, {"id": 7, "name": "怪异的蘑菇", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "$fsm_Forest_object", "direction": 2, "pattern": 2, "characterIndex": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["let Hp = 50;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSelfVariables.set(this, 'HP', Hp);"]}, {"code": 355, "indent": 0, "parameters": ["let Hp = 50;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSelfVariables.set(this, 'MHP', Hp);"]}, {"code": 355, "indent": 0, "parameters": ["$gameSelfVariables.set(this, 'fudou', 99);"]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "C", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 2, "characterIndex": 0}, "list": [{"code": 111, "indent": 0, "parameters": [1, 60, 0, 2, 0]}, {"code": 123, "indent": 1, "parameters": ["C", 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 214, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "$fsm_Forest_object", "direction": 2, "pattern": 2, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Group:\"object\">"]}, {"code": 355, "indent": 0, "parameters": ["let day = $gameSystem.day();"]}, {"code": 655, "indent": 0, "parameters": ["$gameSelfVariables.set(this, 'deadDay', day);"]}, {"code": 126, "indent": 0, "parameters": [117, 0, 0, 1]}, {"code": 123, "indent": 0, "parameters": ["C", 0]}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "$fsm_Forest_object", "direction": 2, "pattern": 2, "characterIndex": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["let day = $gameSystem.day();"]}, {"code": 655, "indent": 0, "parameters": ["$gameSelfVariables.set(this, 'deadDay', day);"]}, {"code": 123, "indent": 0, "parameters": ["C", 0]}, {"code": 123, "indent": 0, "parameters": ["D", 1]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 7, "y": 9}, {"id": 8, "name": "EV008", "note": "Fire 50 #FFFFBB", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 4, "y": 7}, {"id": 9, "name": "EV009", "note": "Fire 50 #FFFFBB", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 5, "y": 7}, null, null, null, null, null, null, null, null, null, null, null]}