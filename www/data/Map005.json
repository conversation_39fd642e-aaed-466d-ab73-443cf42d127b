{"autoplayBgm": false, "autoplayBgs": false, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 15, "height": 50, "note": "<深渊>\n<All Restrict Region: 254,255>\n<All Allow Region: 249,250>", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 13, "width": 50, "data": [5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 6323, 6322, 6322, 6322, 6322, 6322, 6326, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 6321, 6320, 6320, 6320, 6320, 6320, 6324, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 6321, 6320, 6320, 6320, 6320, 6320, 6324, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 6321, 6320, 6320, 6320, 6320, 6320, 6324, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 6321, 6320, 6320, 6320, 6320, 6320, 6324, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 6321, 6320, 6320, 6320, 6320, 6320, 6324, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 6321, 6320, 6320, 6320, 6320, 6320, 6324, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 6321, 6320, 6320, 6320, 6320, 6320, 6324, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 6321, 6320, 6320, 6320, 6320, 6320, 6324, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 6321, 6320, 6320, 6320, 6320, 6320, 6324, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 6321, 6320, 6320, 6320, 6320, 6320, 6324, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 6321, 6320, 6320, 6320, 6320, 6320, 6324, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5892, 5916, 5916, 5926, 6321, 6320, 6320, 6320, 6320, 6320, 6324, 5928, 5916, 5896, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5892, 5916, 5916, 5916, 5916, 5916, 5916, 5896, 5888, 5888, 5888, 5888, 5888, 5888, 5892, 5916, 5926, 6322, 6322, 6326, 6329, 6328, 6328, 6328, 6328, 6328, 6332, 6323, 6322, 5928, 5916, 5916, 5896, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5892, 5926, 6322, 6322, 6322, 6322, 6322, 6322, 5928, 5916, 5916, 5896, 5888, 5888, 5888, 5912, 6322, 6326, 6328, 6328, 6332, 2898, 2900, 3290, 2898, 2884, 2884, 2900, 6329, 6328, 6323, 6322, 6322, 5928, 5896, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 6326, 6328, 6328, 6328, 6328, 6328, 6328, 6323, 6322, 6322, 5928, 5916, 5888, 5892, 5926, 6328, 6332, 2899, 2897, 2886, 2865, 2888, 3280, 2880, 2864, 2864, 2866, 2884, 2900, 6329, 6328, 6328, 6323, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 6332, 2082, 2068, 2068, 2068, 2068, 2084, 6329, 6328, 6328, 6323, 6326, 5888, 5912, 6326, 4003, 4013, 2908, 4014, 2880, 2864, 2888, 3292, 2880, 2864, 2864, 2864, 2864, 2866, 2884, 2884, 2900, 6329, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2083, 2077, 2056, 2048, 2048, 2048, 2050, 2068, 2068, 2084, 6329, 6332, 5888, 5912, 6332, 4000, 5934, 4014, 2898, 2865, 2864, 2866, 2884, 2865, 2864, 2868, 2892, 2892, 2872, 2864, 2864, 2866, 2900, 5928, 5896, 5888, 5888, 5888, 5888, 5888, 5888, 5892, 5916, 5916, 5916, 5916, 5896, 5888, 5888, 5912, 2080, 5934, 2064, 2048, 2048, 2048, 2048, 2048, 2048, 2050, 2068, 2068, 5888, 5912, 4002, 3994, 6327, 2907, 2873, 2864, 2864, 2864, 2864, 2864, 2864, 2888, 5922, 5924, 2882, 2892, 2892, 2872, 2888, 6323, 5904, 5888, 5888, 5892, 5916, 5916, 5916, 5926, 6322, 6322, 6322, 6327, 5928, 5916, 5916, 5913, 2080, 6327, 2066, 2076, 2076, 2076, 2076, 2076, 2076, 2076, 2076, 2076, 5888, 5912, 4008, 3993, 6333, 4014, 2880, 2864, 2864, 2864, 2864, 2864, 2864, 2888, 5928, 5926, 2896, 3282, 3284, 2904, 2889, 6329, 5928, 5916, 5916, 5926, 6322, 6322, 6322, 6326, 6328, 6328, 6328, 6328, 6323, 6323, 6326, 5932, 2080, 6333, 2080, 5931, 5910, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5888, 5890, 5924, 3987, 4013, 2898, 2865, 2864, 2864, 2864, 2864, 2864, 2864, 2888, 6323, 6326, 2896, 3264, 3250, 3284, 2881, 2900, 6323, 6322, 6322, 6326, 6328, 6328, 6328, 6332, 2898, 2884, 2884, 2900, 6329, 6328, 6328, 6327, 2065, 2068, 2074, 6323, 5928, 5896, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 4012, 2898, 2865, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2888, 6329, 6332, 2896, 3288, 3256, 3272, 2880, 2888, 6329, 6328, 6328, 6332, 2898, 2884, 2884, 2884, 2865, 2864, 2864, 2866, 2884, 2884, 2900, 6333, 2088, 2076, 2086, 6329, 6323, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2907, 2873, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2866, 2884, 2884, 2867, 2900, 3288, 3286, 2904, 2894, 2897, 2897, 2897, 2897, 2893, 2892, 2892, 2892, 2892, 2872, 2864, 2868, 2892, 2892, 2894, 2886, 2884, 2884, 2884, 2900, 6329, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5890, 5924, 2880, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2866, 2885, 2909, 5922, 5908, 5908, 5908, 5908, 5908, 5924, 3675, 3665, 3654, 3668, 2904, 2876, 2902, 5922, 5908, 5924, 2904, 2892, 2892, 2892, 2874, 2900, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2880, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2888, 5922, 5889, 5888, 5888, 5888, 5888, 5888, 5912, 5970, 5972, 3672, 3662, 3677, 2908, 5978, 5904, 5888, 5890, 5908, 5908, 5908, 5924, 2880, 2888, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2904, 2872, 2864, 2864, 2864, 2864, 2868, 2892, 2872, 2864, 2864, 2864, 2864, 2864, 2888, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 5952, 5938, 5956, 5956, 5956, 5956, 5962, 5904, 5888, 5888, 5888, 5888, 5888, 5912, 2880, 2888, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5890, 5924, 2904, 2872, 2864, 2864, 2864, 2888, 3294, 2880, 2864, 2864, 2864, 2864, 2864, 2888, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 5952, 5936, 5936, 5936, 5936, 5936, 5960, 5904, 5888, 5888, 5888, 5888, 5888, 5912, 2880, 2888, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5890, 5924, 2880, 2864, 2864, 2864, 2866, 2884, 2865, 2864, 2864, 2864, 2864, 2864, 2888, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 5952, 5936, 5936, 5936, 5936, 5936, 5960, 5904, 5888, 5888, 5888, 5888, 5888, 5912, 2880, 2888, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2880, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2888, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 5952, 5936, 5936, 5936, 5940, 5964, 5974, 5904, 5888, 5888, 5888, 5888, 5888, 5912, 2904, 2902, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2904, 2892, 2872, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2868, 2902, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 5976, 5944, 5936, 5940, 5974, 5923, 5921, 5917, 5916, 5916, 5916, 5896, 5888, 5890, 5908, 5908, 5889, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5890, 5908, 5924, 2904, 2872, 2864, 2864, 2864, 2864, 2868, 2892, 2876, 2902, 5922, 5889, 5888, 5888, 5888, 5888, 5888, 5888, 5890, 5924, 5952, 5940, 5974, 5923, 5927, 6322, 6322, 6322, 6322, 6322, 5928, 5896, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5890, 5924, 2880, 2864, 2864, 2864, 2864, 2888, 3294, 2896, 5922, 5889, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 5976, 5961, 5922, 5914, 6326, 6320, 6320, 6320, 6320, 6320, 6323, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2880, 2864, 2864, 2864, 2864, 2866, 2884, 2890, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5890, 5924, 5980, 5904, 5912, 6324, 6328, 6328, 6328, 6328, 6328, 6321, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2880, 2864, 2864, 2864, 2864, 2864, 2868, 2902, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5890, 5908, 5889, 5912, 6332, 2898, 2885, 2897, 2886, 2900, 6329, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2880, 2864, 2864, 2864, 2864, 2864, 2888, 3294, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2898, 2865, 2888, 3294, 2880, 2866, 2900, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2880, 2868, 2892, 2892, 2892, 2872, 2866, 2900, 5928, 5896, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2880, 2864, 2866, 2884, 2865, 2864, 2888, 5928, 5896, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2880, 2888, 3291, 3281, 3285, 2880, 2864, 2888, 6323, 5928, 5916, 5916, 5916, 5916, 5896, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2880, 2868, 2892, 2872, 2864, 2864, 2888, 6323, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2904, 2874, 2884, 2900, 3292, 2904, 2872, 2888, 6329, 6323, 6322, 6322, 6322, 6322, 5928, 5916, 5916, 5916, 5916, 5896, 5888, 5892, 5926, 2880, 2888, 3294, 2880, 2864, 2864, 2888, 6329, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5890, 5924, 2880, 2864, 2866, 2900, 3294, 2880, 2870, 2909, 6329, 6328, 6328, 6328, 6328, 6323, 6322, 6322, 6322, 6322, 5928, 5916, 5926, 6326, 2880, 2866, 2885, 2893, 2872, 2864, 2866, 2900, 5928, 5896, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2904, 2892, 2872, 2866, 2884, 2865, 2888, 3294, 2898, 2884, 2884, 2884, 2900, 6329, 6328, 6328, 6328, 6328, 6323, 6322, 6326, 6332, 2880, 2864, 2888, 3294, 2880, 2864, 2864, 2888, 6323, 5928, 5916, 5916, 5916, 5916, 5896, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5890, 5908, 5924, 2880, 2864, 2864, 2864, 2866, 2884, 2865, 2868, 2892, 2872, 2866, 2884, 2884, 2884, 2884, 2900, 6329, 6328, 6332, 2898, 2865, 2864, 2866, 2884, 2865, 2864, 2864, 2888, 6329, 6323, 6322, 6322, 6322, 6322, 5928, 5916, 5916, 5896, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2904, 2892, 2872, 2864, 2864, 2864, 2864, 2888, 3294, 2904, 2892, 2892, 2872, 2864, 2864, 2866, 2884, 2884, 2884, 2865, 2868, 2892, 2872, 2868, 2892, 2892, 2872, 2866, 2900, 6329, 6328, 6328, 6328, 6328, 6323, 6322, 6322, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5890, 5908, 5924, 2880, 2864, 2864, 2864, 2864, 2866, 2900, 3291, 3270, 3284, 2904, 2892, 2892, 2892, 2892, 2892, 2892, 2892, 2902, 3294, 2880, 2888, 3291, 3293, 2904, 2892, 2894, 2897, 2897, 2897, 2897, 2909, 6329, 6328, 6328, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2880, 2864, 2864, 2864, 2864, 2864, 2866, 2900, 3288, 3278, 3281, 3281, 3281, 3281, 3281, 3281, 3281, 3281, 3293, 2898, 2865, 2866, 2884, 2900, 3291, 3281, 3281, 3281, 3281, 3281, 3281, 3285, 2898, 2884, 2900, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2904, 2892, 2892, 2892, 2892, 2892, 2892, 2894, 2897, 2897, 2897, 2897, 2897, 2886, 2884, 2884, 2884, 2885, 2897, 2893, 2892, 2892, 2892, 2894, 2897, 2897, 2897, 2897, 2897, 2886, 2900, 3292, 2880, 2864, 2888, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5890, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5924, 2904, 2892, 2892, 2892, 2902, 5922, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5924, 2904, 2894, 2897, 2893, 2892, 2902, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5890, 5908, 5908, 5908, 5908, 5908, 5889, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5890, 5908, 5908, 5908, 5908, 5908, 5908, 5889, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2130, 2116, 2116, 2116, 2116, 2132, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2131, 2125, 2104, 2096, 2096, 2096, 2098, 2116, 2116, 2132, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3090, 3092, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2128, 0, 2112, 2096, 2096, 2096, 2096, 2096, 2096, 2098, 2116, 2116, 0, 0, 0, 0, 0, 0, 0, 3090, 3057, 3080, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2128, 0, 2114, 2124, 2124, 2124, 2124, 2124, 2124, 2124, 2124, 2124, 0, 0, 0, 0, 0, 0, 0, 3072, 3056, 3058, 3092, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2128, 0, 2128, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3090, 3057, 3056, 3056, 3058, 3092, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2113, 2116, 2122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3072, 3056, 3056, 3056, 3056, 3080, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2136, 2124, 2134, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3072, 3056, 3056, 3056, 3056, 3058, 3092, 0, 3819, 3821, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3096, 3064, 3056, 3056, 3056, 3056, 3080, 0, 0, 3102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3096, 3064, 3056, 3056, 3060, 3094, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3072, 3056, 3060, 3094, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3096, 3084, 3094, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3099, 3078, 3076, 3092, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3072, 3056, 3080, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3096, 3064, 3058, 3092, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3096, 3084, 3094, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3099, 3101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3819, 3809, 3821, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3866, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3426, 3428, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3868, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3432, 3430, 0, 3102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3090, 3076, 3092, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3072, 3056, 3058, 3076, 3092, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3096, 3084, 3084, 3084, 3094, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3090, 3076, 3092, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3096, 3064, 3080, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3096, 3094, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 24, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 42, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 42, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 42, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 42, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 18, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 135, 135, 135, 135, 135, 135, 135, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 143, 143, 143, 143, 143, 143, 143, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 151, 151, 151, 151, 151, 151, 151, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 159, 159, 159, 159, 159, 159, 159, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 167, 167, 167, 167, 167, 167, 167, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 24, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 24, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16, 0, 75, 76, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 97, 25, 0, 40, 0, 83, 84, 0, 97, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 0, 25, 25, 0, 33, 0, 0, 0, 36, 37, 38, 0, 0, 0, 77, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 33, 33, 0, 0, 0, 0, 0, 44, 45, 46, 36, 38, 0, 85, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 44, 46, 36, 37, 38, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 44, 45, 46, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 18, 18, 18, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 17, 17, 0, 0, 0, 0, 0, 0, 0, 0, 97, 0, 0, 0, 0, 0, 0, 0, 41, 41, 0, 18, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 41, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 25, 25, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 58, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 33, 33, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 157, 157, 0, 0, 0, 59, 157, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 165, 165, 157, 157, 157, 157, 165, 0, 0, 0, 0, 0, 0, 0, 175, 175, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 173, 173, 165, 165, 165, 165, 173, 0, 0, 0, 0, 0, 0, 0, 183, 183, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 42, 42, 42, 42, 42, 42, 42, 42, 42, 42, 42, 42, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 173, 173, 173, 173, 0, 0, 0, 0, 0, 0, 0, 0, 191, 191, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 42, 42, 42, 42, 42, 42, 42, 42, 42, 42, 42, 42, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 199, 199, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 27, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 52, 52, 53, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 42, 42, 42, 42, 42, 42, 42, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 22, 60, 60, 61, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 42, 42, 42, 42, 42, 42, 42, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 15, 15, 15, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 90, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 0, 0, 15, 15, 15, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 97, 0, 0, 0, 0, 0, 0, 0, 89, 7, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 27, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 41, 0, 0, 0, 25, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 33, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 54, 54, 54, 55, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 62, 62, 62, 63, 0, 0, 18, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 25, 0, 0, 0, 0, 18, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 33, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 254, 254, 22, 254, 254, 254, 254, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 254, 254, 22, 254, 254, 254, 254, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 254, 254, 22, 254, 254, 254, 254, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 254, 254, 22, 254, 254, 254, 254, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 254, 254, 22, 254, 254, 254, 254, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 254, 254, 22, 254, 254, 254, 254, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 254, 254, 22, 254, 254, 254, 254, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 254, 254, 22, 254, 254, 254, 254, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 254, 254, 22, 254, 254, 254, 254, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 254, 254, 22, 254, 254, 254, 254, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 254, 254, 22, 254, 254, 254, 254, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 254, 254, 22, 254, 254, 254, 254, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 254, 254, 22, 254, 254, 254, 254, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 254, 254, 254, 254, 254, 22, 254, 254, 254, 254, 254, 254, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 254, 254, 254, 254, 254, 254, 255, 255, 255, 255, 255, 255, 255, 255, 254, 254, 254, 254, 254, 0, 0, 0, 0, 0, 0, 0, 254, 254, 254, 254, 254, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 255, 255, 255, 255, 255, 254, 254, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 254, 254, 254, 254, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 254, 8, 8, 8, 8, 8, 8, 254, 254, 254, 254, 254, 255, 255, 254, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 254, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 254, 254, 255, 255, 254, 0, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 8, 255, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 255, 255, 0, 0, 254, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 0, 0, 0, 0, 0, 254, 255, 255, 255, 255, 255, 255, 255, 255, 254, 254, 254, 254, 255, 255, 255, 255, 8, 254, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 255, 255, 0, 0, 254, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 0, 0, 0, 0, 0, 254, 255, 255, 255, 255, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 255, 8, 254, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 254, 254, 0, 0, 0, 0, 0, 0, 254, 254, 254, 254, 254, 254, 254, 254, 0, 0, 0, 0, 254, 254, 254, 254, 8, 8, 8, 254, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 254, 254, 0, 0, 0, 0, 0, 0, 254, 254, 254, 254, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 254, 8, 8, 8, 254, 254, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 254, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 249, 249, 249, 249, 0, 0, 249, 255, 255, 255, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 250, 250, 249, 249, 249, 249, 250, 255, 255, 255, 255, 255, 255, 255, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 250, 250, 250, 250, 250, 250, 250, 255, 255, 255, 255, 255, 255, 255, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 250, 250, 250, 250, 250, 250, 250, 255, 255, 255, 255, 255, 255, 255, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 250, 250, 250, 250, 250, 250, 250, 255, 255, 255, 255, 255, 255, 255, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 250, 250, 250, 250, 250, 250, 250, 255, 255, 255, 255, 255, 255, 255, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 250, 250, 250, 250, 250, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 250, 250, 250, 255, 255, 254, 254, 254, 254, 254, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 250, 250, 255, 255, 254, 254, 254, 254, 254, 254, 254, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 250, 255, 255, 254, 254, 254, 254, 254, 254, 254, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 254, 0, 0, 0, 0, 0, 254, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 0, 254, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 254, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 249, 249, 0, 0, 0, 0, 0, 254, 254, 254, 254, 254, 254, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 254, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 250, 250, 249, 0, 0, 0, 0, 0, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 255, 255, 255, 254, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 250, 250, 249, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 254, 254, 254, 254, 254, 254, 254, 254, 254, 0, 0, 0, 0, 0, 0, 0, 0, 254, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 254, 254, 254, 0, 0, 0, 0, 0, 0, 0, 0, 0, 254, 254, 254, 254, 254, 254, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 254, 254, 254, 254, 254, 254, 254, 254, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 254, 254, 254, 254, 254, 254, 254, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255], "events": [null, {"id": 1, "name": "EV001", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["Zzy.TWF.ToTheWorld(true) "]}, {"code": 250, "indent": 0, "parameters": [{"name": "Move1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 201, "indent": 0, "parameters": [0, 28, 7, 38, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 2, "walkAnime": true}], "x": 10, "y": 2}, {"id": 2, "name": "EV002", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 0, "characterIndex": 3}, "list": [{"code": 121, "indent": 0, "parameters": [88, 88, 0]}, {"code": 108, "indent": 0, "parameters": ["恢复玩家自动朝向"]}, {"code": 355, "indent": 0, "parameters": ["ctb.useTurnPlayer = true"]}, {"code": 241, "indent": 0, "parameters": [{"name": "真実はどこ(Where is the truth)", "volume": 75, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["AudioManager.fadeInBgm(3)"]}, {"code": 355, "indent": 0, "parameters": ["$gamePlayer.startOpacity(30, 255)"]}, {"code": 356, "indent": 0, "parameters": ["particle set dust_walk walk:player:0 def below"]}, {"code": 108, "indent": 0, "parameters": ["创造物管理"]}, {"code": 355, "indent": 0, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.event(eid).steupCEQJ(2)"]}, {"code": 117, "indent": 0, "parameters": [100]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 121, "indent": 0, "parameters": [3, 3, 0]}, {"code": 121, "indent": 0, "parameters": [14, 14, 1]}, {"code": 111, "indent": 0, "parameters": [12, "$gameScreen.zoomScale() == 1"]}, {"code": 356, "indent": 1, "parameters": ["d<PERSON><PERSON><PERSON> 2 60 player"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 6, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["雨天重置的创造物"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameSelfVariables.value([$gameMap.mapId(), 1, 'rainyCreation']) > 0"]}, {"code": 108, "indent": 1, "parameters": ["蛋"]}, {"code": 355, "indent": 1, "parameters": ["let num = 6 + Math.randomInt(10);"]}, {"code": 655, "indent": 1, "parameters": ["QJ.SE.spawnRegionNum(1,55,[0],num,true);"]}, {"code": 108, "indent": 1, "parameters": ["重置采集物"]}, {"code": 355, "indent": 1, "parameters": ["var events = $gameMap.drill_COET_getEventsByTag_direct(\"宝箱\");"]}, {"code": 655, "indent": 1, "parameters": ["events.forEach(function(event) {"]}, {"code": 655, "indent": 1, "parameters": ["    var eventId = event.eventId();"]}, {"code": 655, "indent": 1, "parameters": ["    var key = [$gameMap.mapId(), eventId, 'A'];"]}, {"code": 655, "indent": 1, "parameters": ["    $gameSelfSwitches.setValue(key, false);"]}, {"code": 655, "indent": 1, "parameters": ["});"]}, {"code": 108, "indent": 1, "parameters": ["清除标记"]}, {"code": 355, "indent": 1, "parameters": ["$gameSelfVariables.setValue([$gameMap.mapId(), 1, 'rainyCreation'], 0);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["日常重置的创造物"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameSelfVariables.value([$gameMap.mapId(), 1, 'day']) < $gameSystem.day()"]}, {"code": 108, "indent": 1, "parameters": ["更新标记天数"]}, {"code": 355, "indent": 1, "parameters": ["let day = $gameSystem.day();"]}, {"code": 655, "indent": 1, "parameters": ["$gameSelfVariables.setValue([$gameMap.mapId(), 1, 'day'], day)"]}, {"code": 108, "indent": 1, "parameters": ["罐子 "]}, {"code": 355, "indent": 1, "parameters": ["let num = 3 + Math.randomInt(8);"]}, {"code": 655, "indent": 1, "parameters": ["QJ.SE.spawnRegionNum(1,54,[0],num,true);"]}, {"code": 108, "indent": 1, "parameters": ["敌人"]}, {"code": 355, "indent": 1, "parameters": ["let num = 2 + Math.randomInt(4);"]}, {"code": 655, "indent": 1, "parameters": ["QJ.SE.spawnRegionNum(1,53,[0],num,true);"]}, {"code": 655, "indent": 1, "parameters": ["//僵尸"]}, {"code": 355, "indent": 1, "parameters": ["let num = 2 + Math.randomInt(4);"]}, {"code": 655, "indent": 1, "parameters": ["QJ.SE.spawnRegionNum(1,89,[0],num,true);"]}, {"code": 655, "indent": 1, "parameters": ["//恶灵史莱姆"]}, {"code": 355, "indent": 1, "parameters": ["let num = 0 + Math.randomInt(2);"]}, {"code": 655, "indent": 1, "parameters": ["if (num > 0) {"]}, {"code": 655, "indent": 1, "parameters": ["QJ.SE.spawnRegionNum(1,77,[0],num,true);"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 655, "indent": 1, "parameters": ["//恶魔猫"]}, {"code": 355, "indent": 1, "parameters": ["let num = 1 + Math.randomInt(3);"]}, {"code": 655, "indent": 1, "parameters": ["QJ.SE.spawnRegionNum(1,78,[0],num,true);"]}, {"code": 655, "indent": 1, "parameters": ["//鬼火"]}, {"code": 355, "indent": 1, "parameters": ["let num = 0 + Math.randomInt(2);"]}, {"code": 655, "indent": 1, "parameters": ["if (num > 0) {"]}, {"code": 655, "indent": 1, "parameters": ["QJ.SE.spawnRegionNum(1,88,[0],num,true);"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 655, "indent": 1, "parameters": ["//杰克史莱姆"]}, {"code": 355, "indent": 1, "parameters": ["let num = 1 + Math.randomInt(2);"]}, {"code": 655, "indent": 1, "parameters": ["if (num > 0) {"]}, {"code": 655, "indent": 1, "parameters": ["QJ.SE.spawnRegionNum(1,72,[0],num,true);"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 655, "indent": 1, "parameters": ["//堕落者的断手"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$gameMap.drill_COET_getEventsByTag_direct(\"需监听\").length > 0"]}, {"code": 355, "indent": 2, "parameters": ["let monster = $gameMap.drill_COET_getEventsByTag_direct(\"需监听\");  "]}, {"code": 655, "indent": 2, "parameters": [" monster.forEach(function(target) {"]}, {"code": 655, "indent": 2, "parameters": ["let EID = target._eventId;"]}, {"code": 655, "indent": 2, "parameters": ["QJ.MPMZ.tl.ex_enemyConditionCheck(EID)"]}, {"code": 655, "indent": 2, "parameters": ["    });"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$gameMap.drill_COET_getEventsByTag_direct(\"接触伤害\").length > 0"]}, {"code": 355, "indent": 2, "parameters": ["let monster = $gameMap.drill_COET_getEventsByTag_direct(\"接触伤害\");  "]}, {"code": 655, "indent": 2, "parameters": [" monster.forEach(function(target) {"]}, {"code": 655, "indent": 2, "parameters": ["let EID = target._eventId;"]}, {"code": 655, "indent": 2, "parameters": ["QJ.MPMZ.tl.ex_enemyContactDamage(EID)"]}, {"code": 655, "indent": 2, "parameters": ["    });"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 21, "y": 4}, {"id": 3, "name": "EV003", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Flame", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 108, "indent": 0, "parameters": ["<width: 3>"]}, {"code": 408, "indent": 0, "parameters": ["<height: 1>"]}, {"code": 408, "indent": 0, "parameters": ["<offsetX: 0>"]}, {"code": 408, "indent": 0, "parameters": ["<offsetY: 0>"]}, {"code": 355, "indent": 0, "parameters": ["Zzy.TWF.ToTheWorld(true) "]}, {"code": 250, "indent": 0, "parameters": [{"name": "Move1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 121, "indent": 0, "parameters": [40, 40, 0]}, {"code": 201, "indent": 0, "parameters": [0, 50, 11, 2, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 5, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 2, "walkAnime": true}], "x": 41, "y": 29}, {"id": 4, "name": "EV004", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Flame", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 5, "moveType": 0, "priorityType": 0, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 42, "y": 29}, {"id": 5, "name": "采集点", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["=>标签核心 : 添加标签 : 宝箱"]}, {"code": 108, "indent": 0, "parameters": ["particle set mysterious_torch_c-EID this mysterious_torch_c"]}, {"code": 250, "indent": 0, "parameters": [{"name": "キラーン（ひらめき・アイテム発見・獲得）", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["dingk.Loot.directlyAcquireDrops(21,false)"]}, {"code": 356, "indent": 0, "parameters": ["particle clear mysterious_torch_c-EID"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 37, "y": 33}, {"id": 6, "name": "EV006", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["QJ.MPMZ.Shoot({"]}, {"code": 655, "indent": 0, "parameters": ["img:\"null1\",position:[['E',0],['E',0]],"]}, {"code": 655, "indent": 0, "parameters": ["initialRotation:['S',0],imgRotation:['F'],"]}, {"code": 655, "indent": 0, "parameters": [" moveType:['S',0],anchor:[0.5,0.3],"]}, {"code": 655, "indent": 0, "parameters": ["collisionBox:['R',320,300],"]}, {"code": 655, "indent": 0, "parameters": ["existData:["]}, {"code": 655, "indent": 0, "parameters": ["{t:['G',['\"enemy\"','\"object\"']],a:['C',133,[4,0,0]],p:[-1,false,true],c:['T',0,30,true]},"]}, {"code": 655, "indent": 0, "parameters": ["{t:['P'],a:['C',133,[50,6,13]],p:[-1,false,true],c:['T',0,30,true]},\t\t"]}, {"code": 655, "indent": 0, "parameters": ["   ],"]}, {"code": 655, "indent": 0, "parameters": ["});"]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 30, "y": 25}, {"id": 7, "name": "EV007", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["QJ.MPMZ.Shoot({"]}, {"code": 655, "indent": 0, "parameters": ["img:\"null1\",position:[['E',0],['E',0]],"]}, {"code": 655, "indent": 0, "parameters": ["initialRotation:['S',0],imgRotation:['F'],"]}, {"code": 655, "indent": 0, "parameters": [" moveType:['S',0],anchor:[0.15,0.9],"]}, {"code": 655, "indent": 0, "parameters": ["collisionBox:['R',60,60],"]}, {"code": 655, "indent": 0, "parameters": ["existData:["]}, {"code": 655, "indent": 0, "parameters": ["{t:['G',['\"enemy\"','\"object\"']],a:['C',133,[4,0,0]],p:[-1,false,true]},"]}, {"code": 655, "indent": 0, "parameters": ["{t:['P'],a:['C',133,[50,5,41]],p:[-1,false,true],c:['T',0,30,true]},\t\t"]}, {"code": 655, "indent": 0, "parameters": ["   ],"]}, {"code": 655, "indent": 0, "parameters": ["});"]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 10, "y": 39}, {"id": 8, "name": "宝箱", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!fsm_chest01", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>标签核心 : 添加标签 : 宝箱"]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 36, "indent": null}, {"code": 16, "indent": null}, {"code": 15, "parameters": [4], "indent": null}, {"code": 17, "indent": null}, {"code": 15, "parameters": [4], "indent": null}, {"code": 18, "indent": null}, {"code": 15, "parameters": [4], "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [4]}, {"code": 250, "indent": 0, "parameters": [{"name": "UI系　かっこいい装備音　", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 127, "indent": 0, "parameters": [104, 0, 0, 1, false]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!fsm_chest01", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 20, "y": 17}, {"id": 9, "name": "采集点", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["=>标签核心 : 添加标签 : 宝箱"]}, {"code": 108, "indent": 0, "parameters": ["particle set mysterious_torch_c-EID this mysterious_torch_c"]}, {"code": 250, "indent": 0, "parameters": [{"name": "キラーン（ひらめき・アイテム発見・獲得）", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["dingk.Loot.directlyAcquireDrops(21,false)"]}, {"code": 356, "indent": 0, "parameters": ["particle clear mysterious_torch_c-EID"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 13, "y": 20}, {"id": 10, "name": "采集点", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["=>标签核心 : 添加标签 : 宝箱"]}, {"code": 108, "indent": 0, "parameters": ["particle set mysterious_torch_c-EID this mysterious_torch_c"]}, {"code": 250, "indent": 0, "parameters": [{"name": "キラーン（ひらめき・アイテム発見・獲得）", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["dingk.Loot.directlyAcquireDrops(21,false)"]}, {"code": 356, "indent": 0, "parameters": ["particle clear mysterious_torch_c-EID"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 48, "y": 43}, null, null, null, null, null, null, null, null, null, null, null, null, {"id": 23, "name": "EV023", "note": "Fire 100 #FF9900 B50", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!catacomb", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["particle set light_orange_c-EID this light_orange_c spriteset"]}, {"code": 108, "indent": 0, "parameters": ["particle update light_orange_c-EID pos 0 -36"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 43, "parameters": [1], "indent": null}, {"code": 0, "parameters": []}], "repeat": false, "skippable": true, "wait": false}, "moveSpeed": 4, "moveType": 3, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 15, "y": 20}, {"id": 24, "name": "EV024", "note": "Fire 100 #FF9900 B50", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!catacomb", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["particle set light_orange_c-EID this light_orange_c spriteset"]}, {"code": 108, "indent": 0, "parameters": ["particle update light_orange_c-EID pos 0 -36"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 43, "parameters": [1], "indent": null}, {"code": 0, "parameters": []}], "repeat": false, "skippable": true, "wait": false}, "moveSpeed": 4, "moveType": 3, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 4, "y": 18}, {"id": 25, "name": "EV025", "note": "Fire 100 #FF9900 B50", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!catacomb", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["particle set light_orange_c-EID this light_orange_c spriteset"]}, {"code": 108, "indent": 0, "parameters": ["particle update light_orange_c-EID pos 0 -36"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 43, "parameters": [1], "indent": null}, {"code": 0, "parameters": []}], "repeat": false, "skippable": true, "wait": false}, "moveSpeed": 4, "moveType": 3, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 26, "y": 19}, {"id": 26, "name": "EV026", "note": "Fire 100 #FF9900 B50", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!catacomb", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["particle set light_orange_c-EID this light_orange_c spriteset"]}, {"code": 108, "indent": 0, "parameters": ["particle update light_orange_c-EID pos 0 -36"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 43, "parameters": [1], "indent": null}, {"code": 0, "parameters": []}], "repeat": false, "skippable": true, "wait": false}, "moveSpeed": 4, "moveType": 3, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 36, "y": 19}, {"id": 27, "name": "EV027", "note": "Fire 100 #FF9900 B50", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!catacomb", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["particle set light_orange_c-EID this light_orange_c spriteset"]}, {"code": 108, "indent": 0, "parameters": ["particle update light_orange_c-EID pos 0 -36"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 43, "parameters": [1], "indent": null}, {"code": 0, "parameters": []}], "repeat": false, "skippable": true, "wait": false}, "moveSpeed": 4, "moveType": 3, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 42, "y": 21}, {"id": 28, "name": "EV028", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!catacomb", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["particle set light_orange_c-EID this light_orange_c spriteset"]}, {"code": 108, "indent": 0, "parameters": ["particle update light_orange_c-EID pos 0 -36"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 43, "parameters": [1], "indent": null}, {"code": 0, "parameters": []}], "repeat": false, "skippable": true, "wait": false}, "moveSpeed": 4, "moveType": 3, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 40, "y": 39}, {"id": 29, "name": "EV029", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!catacomb", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["particle set light_orange_c-EID this light_orange_c spriteset"]}, {"code": 108, "indent": 0, "parameters": ["particle update light_orange_c-EID pos 0 -36"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 43, "parameters": [1], "indent": null}, {"code": 0, "parameters": []}], "repeat": false, "skippable": true, "wait": false}, "moveSpeed": 4, "moveType": 3, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 31, "y": 38}, {"id": 30, "name": "EV030", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!catacomb", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["particle set light_orange_c-EID this light_orange_c spriteset"]}, {"code": 108, "indent": 0, "parameters": ["particle update light_orange_c-EID pos 0 -36"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 43, "parameters": [1], "indent": null}, {"code": 0, "parameters": []}], "repeat": false, "skippable": true, "wait": false}, "moveSpeed": 4, "moveType": 3, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 19, "y": 37}, {"id": 31, "name": "EV031", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!catacomb", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["particle set light_orange_c-EID this light_orange_c spriteset"]}, {"code": 108, "indent": 0, "parameters": ["particle update light_orange_c-EID pos 0 -36"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 43, "parameters": [1], "indent": null}, {"code": 0, "parameters": []}], "repeat": false, "skippable": true, "wait": false}, "moveSpeed": 4, "moveType": 3, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 32, "y": 32}, {"id": 32, "name": "EV032", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!catacomb", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["particle set light_orange_c-EID this light_orange_c spriteset"]}, {"code": 108, "indent": 0, "parameters": ["particle update light_orange_c-EID pos 0 -36"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 43, "parameters": [1], "indent": null}, {"code": 0, "parameters": []}], "repeat": false, "skippable": true, "wait": false}, "moveSpeed": 4, "moveType": 3, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 38, "y": 32}]}