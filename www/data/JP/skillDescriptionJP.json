{"skillToggle": ["発動中", "未発動"], "1": {"name": ["魔法収納袋"], "subtitle": [""], "description": ["持ち運び可能な魔法道具で、通常を超える量の物を収納できる", "ただし、魔法道具といえども収納量には限界がある……"], "ability": ["武器収納量: ${QJ.MPMZ.tl.ex_playerCheckInventory('weapon')}", "装備収納量: ${QJ.MPMZ.tl.ex_playerCheckInventory('gear')}"]}, "2": {"name": ["閃歩"], "subtitle": [""], "description": ["地面を蹴って瞬間的に距離を縮め、攻撃を回避することができる技", "ただのスタートダッシュのような動作だが、", "本人はあえてカッコいいスキル名をつけた"], "ability": ["スペースキーを押し続けて発動"]}, "3": {"name": ["閃歩太刀"], "subtitle": [""], "description": ["若き剣聖の指導を受け、習得した抜刀術。閃歩のスビートを活かし、", "斬撃の威力を高める。準備次第で敵を吹き飛ばす力を発揮する"], "ability": ["閃歩中に左クリックで斬撃を発動。移動速度に応じダメージが強化される", "閃歩中に右クリック長押しでチャージし、気勢に応じ効果が強化される"], "mobileAbility": ["スキルボタンをタップすると斬撃を発動、移動速度に応じてダメージが上昇", "スキルボタンを長押しするとチャージ、気勢に応じて効果が強化"]}, "4": {"name": ["旋風斬り"], "subtitle": [""], "description": ["武器を振り回しながら回転して斬撃を繰り出す", "攻撃速度は継続時間に応じて加速する", "剣術が苦手な者でも簡単に習得でき、素早く効果を発揮できる技"], "ability": ["マウス右ボタンを長押ししてスキル発動", "回転中にスキルをキャンセルすると武器を投げられる"], "mobileAbility": ["スキルボタンを長押ししてスキル発動", "回転中にスキルをキャンセルすると武器を投げられる"]}, "7": {"name": ["危険察知"], "subtitle": [""], "description": ["通称「心眼」と呼ばれるスキルだが、", "これは生死をさまよう経験を経て覚醒した直感", "つまり、ある意味トラウマの表れかもしれない……"], "ability": ["調べたい対象にマウスを乗せると詳細情報を確認できる"], "mobileAbility": ["調べたい対象をタップすると詳細情報を確認できる"]}, "10": {"name": ["気配遮断"], "subtitle": ["true"], "description": ["影の中に潜むことを得意とする忍者の技術。軽く習っただけだったが、", "自分には潜伏や隠密の才能があるらしく、すぐにコツを掴んだようだ"], "ability": ["スキルを発動中に、Shiftキーでステルス状態に切り替えることができる", "モンスターに発見される警戒範囲を\\c[17]${1+$gameParty.leader().skillMasteryLevel(10)}\\c[108]マス減少", "妹を覗き見する際の警戒値蓄積速度を\\c[17]${15+15*$gameParty.leader().skillMasteryLevel(10)}%\\c[108]減少"]}, "11": {"name": ["背後刺し"], "subtitle": ["true"], "description": ["影に潜むことを得意とする忍者の技。ステルス状態中に発動できる", "無防備な敵の背後に近づき攻撃すれば、大ダメージを与える！"], "ability": ["スキル熟練度: \\c[6]${$gameActors.actor(1).skillMasteryUses(11)}\\c[0]", "成功時のダメージ増加率: \\c[17]${Math.round(200 + (2333 - 200) * Math.pow($gameActors.actor(1).skillMasteryLevel(11) / 9, 1.8))}%\\c[0]"]}, "15": {"name": ["女装"], "subtitle": [""], "description": ["着るはずのない服をまとい……なぜか新たな可能性に目覚めたらしい。", "戦闘力の上昇は一切なし、むしろちょっとした精神的ダメージまで……", "仲間の視線はどこか微妙に、敵の攻撃も少しだけ戸惑うかも……？", "だってこの世界では――可愛いは正義だから！"], "ability": ["装備中の女装アイテム数に応じて罪悪値が上昇！"]}, "18": {"name": ["武器破損"], "subtitle": [""], "description": ["すべての武器には耐久度の制限があり、自分が正確な攻撃動作", "を得意としていないため、最終的に武器が破壊されるリスクがある", "ただし、破壊の瞬間に強力な会心の一撃を放つ！"], "ability": ["最後の一撃のダメージ増加率: \\c[6]${200+(100*$gameActors.actor(1).skillMasteryLevel(48))}%\\c[0]"]}, "26": {"name": ["剣術修行"], "subtitle": ["true"], "description": ["強くなるために剣聖の少女に鍛え方を尋ねた", "「毎日空振り1000回すればいいんだよ！」という答えが返ってきた", "——本当にそれで強くなれるのか！？とりあえずやってみよう……"], "ability": ["素振り練習回数: \\{\\c[6]${$gameActors.actor(1).skillMasteryUses(26)}\\c[0]", "近接武器の基礎攻撃が\\c[6]${Math.ceil(1.8**$gameActors.actor(1).skillMasteryLevel(26))}%\\c[0]ダメージ増加"]}}