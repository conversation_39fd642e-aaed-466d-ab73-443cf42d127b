{"autoplayBgm": false, "autoplayBgs": false, "battleback1Name": "genkan", "battleback2Name": "gray bar", "bgm": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 9, "note": "<宅>", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": true, "tilesetId": 1, "width": 9, "data": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "场景初始化", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 0, "characterIndex": 3}, "list": [{"code": 108, "indent": 0, "parameters": ["防报错措施"]}, {"code": 117, "indent": 0, "parameters": [8]}, {"code": 117, "indent": 0, "parameters": [20]}, {"code": 313, "indent": 0, "parameters": [0, 2, 1, 35]}, {"code": 313, "indent": 0, "parameters": [0, 2, 1, 36]}, {"code": 355, "indent": 0, "parameters": ["QJ.MPMZ.tl._imoutoUtilCheckInitialization()"]}, {"code": 111, "indent": 0, "parameters": [1, 13, 0, 0, 3]}, {"code": 355, "indent": 1, "parameters": ["let ID = $gameVariables.value(13);"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.event(ID).start()"]}, {"code": 122, "indent": 1, "parameters": [13, 13, 0, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameMap.event(3).start()"]}, {"code": 122, "indent": 1, "parameters": [13, 13, 0, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}], "x": 0, "y": 8}, {"id": 2, "name": "清晨出门", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 4, "pattern": 1, "characterIndex": 1}, "list": [{"code": 111, "indent": 0, "parameters": [1, 60, 0, 2, 0]}, {"code": 231, "indent": 1, "parameters": [1, "entrance_R_D_DO", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 60, 0, 1, 0]}, {"code": 231, "indent": 2, "parameters": [1, "entrance_C_D_DO", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [0, 123, 0]}, {"code": 231, "indent": 3, "parameters": [1, "entrance_S_D_DO", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 231, "indent": 3, "parameters": [1, "entrance_S_M_DO", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 108, "indent": 0, "parameters": ["妹妹早起分歧"]}, {"code": 111, "indent": 0, "parameters": [0, 124, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\"It's about time to head out...\""]}, {"code": 355, "indent": 1, "parameters": ["let id = this._eventId;"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.event(id).steupCEQJ(2)"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\"Alright, I'm off then.\""]}, {"code": 242, "indent": 1, "parameters": [2]}, {"code": 246, "indent": 1, "parameters": [2]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 122, "indent": 1, "parameters": [13, 13, 0, 0, 3]}, {"code": 201, "indent": 1, "parameters": [0, 36, 5, 3, 0, 2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 231, "indent": 0, "parameters": [9, "mio_tachie_smile", 1, 0, 572, 950, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": [">显现动作 : 图片[9] : 移动显现 : 时间[60] : 方向角度[180] : 移动距离[180]"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 111, "indent": 0, "parameters": [1, 20, 0, 70, 1]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE genkan_event_sis_40"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE genkan_event_sis_39"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >\"Take care on the way, onii-chan!\""]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.changePictureName(9, \"mio_tachie_smile2\");"]}, {"code": 111, "indent": 0, "parameters": [1, 60, 0, 2, 1]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE genkan_event_sis_42"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nl< >\"The weather doesn't look great today, "]}, {"code": 401, "indent": 1, "parameters": ["so be careful...\""]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE genkan_event_sis_41"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nl< >\"If you feel like you can't keep going, "]}, {"code": 401, "indent": 1, "parameters": ["make sure to come back right away!\""]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [2, "A", 1]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["Seeing my sister so full of energy in the morning, "]}, {"code": 401, "indent": 1, "parameters": ["this unfamiliar sight filled me with an overwhelming "]}, {"code": 401, "indent": 1, "parameters": ["sense of emotion."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["Suppressing the urge to tightly hug her, "]}, {"code": 401, "indent": 1, "parameters": ["I smiled back at her."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\{ \"Alright…! I'm heading out!\" \\}"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\"Got it… I'll leave the house in your hands!\""]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["Waving to her as I walked out the door, "]}, {"code": 401, "indent": 0, "parameters": ["I reminded myself to keep working hard today "]}, {"code": 401, "indent": 0, "parameters": ["for her sake!"]}, {"code": 242, "indent": 0, "parameters": [2]}, {"code": 246, "indent": 0, "parameters": [2]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [13, 13, 0, 0, 3]}, {"code": 201, "indent": 0, "parameters": [0, 36, 5, 3, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 1, "y": 8}, {"id": 3, "name": "从深渊归来", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 111, "indent": 0, "parameters": [12, "$gameSystem.hour() <= 16"]}, {"code": 111, "indent": 1, "parameters": [1, 60, 0, 2, 0]}, {"code": 231, "indent": 2, "parameters": [1, "entrance_R_D_DO", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [1, 60, 0, 1, 0]}, {"code": 231, "indent": 3, "parameters": [1, "entrance_C_D_DO", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 231, "indent": 3, "parameters": [1, "entrance_S_D_DO", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 231, "indent": 1, "parameters": [1, "entrance_S_E_DO_RL", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 5, 0]}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 121, "indent": 1, "parameters": [5, 5, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 241, "indent": 0, "parameters": [{"name": "日常_放課後", "volume": 60, "pitch": 100, "pan": 0}]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 232, "indent": 0, "parameters": [92, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, true]}, {"code": 235, "indent": 0, "parameters": [92]}, {"code": 356, "indent": 0, "parameters": ["SaveNow"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\"I'm back.\""]}, {"code": 108, "indent": 0, "parameters": ["提前回家"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameSystem.hour() <= 16"]}, {"code": 355, "indent": 1, "parameters": ["$gameMap.event(6).start()"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["特殊分支剧情"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameParty.hasItem($dataItems[15]) && !$gameSwitches.value(500)"]}, {"code": 355, "indent": 1, "parameters": ["$gameMap.event(7).start()"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "$gameParty.leader().states().length > 0"]}, {"code": 355, "indent": 1, "parameters": ["let states = $gameParty.leader().states();"]}, {"code": 655, "indent": 1, "parameters": ["let randomIndex = Math.floor(Math.random() * states.length);"]}, {"code": 655, "indent": 1, "parameters": ["randomIndex = states[randomIndex].id;"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.setValue(13, randomIndex);"]}, {"code": 111, "indent": 1, "parameters": [1, 13, 0, 60, 0]}, {"code": 355, "indent": 2, "parameters": ["$gameMap.event(9).start()"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["$gameMap.event(9).start()"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameMap.event(9).start()"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 2, "y": 8}, {"id": 4, "name": "第一次出门", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 2, "characterIndex": 1}, "list": [{"code": 111, "indent": 0, "parameters": [1, 60, 0, 2, 0]}, {"code": 231, "indent": 1, "parameters": [1, "entrance_R_D_DO", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 60, 0, 1, 0]}, {"code": 231, "indent": 2, "parameters": [1, "entrance_C_D_DO", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 231, "indent": 2, "parameters": [1, "entrance_S_D_DO", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\"Alright, all set!\""]}, {"code": 231, "indent": 0, "parameters": [9, "mio_tachie_smile2", 1, 0, 572, 950, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": [">显现动作 : 图片[9] : 移动显现 : 时间[60] : 方向角度[180] : 移动距离[180]"]}, {"code": 230, "indent": 0, "parameters": [35]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_18"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >\"I'm sorry, <PERSON><PERSON>-chan...\\w[60]"]}, {"code": 401, "indent": 0, "parameters": ["I have to send you to a dangerous place again—\""]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\"There you go again... Don't worry, I'll be fine.\""]}, {"code": 231, "indent": 0, "parameters": [9, "mio_tachie_naku", 1, 0, 572, 950, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_19"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >\"But—\""]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\"I've explored it several times already.  "]}, {"code": 401, "indent": 0, "parameters": ["The maze has been stable for years, "]}, {"code": 401, "indent": 0, "parameters": ["so there's no need to worry.\""]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\"Then I'll leave you to take care of the house!\""]}, {"code": 231, "indent": 0, "parameters": [9, "mio_tachie_shinnpai", 1, 0, 572, 950, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_20"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >\"Yeah... be careful...\""]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\"I'm heading out.\""]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.startFadeOut(180);"]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["That's right, my sister has a rare condition."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["She can't go outside, or even be in sunlight.  "]}, {"code": 401, "indent": 0, "parameters": ["And without taking specially "]}, {"code": 401, "indent": 0, "parameters": ["crafted alchemical potions regularly, "]}, {"code": 401, "indent": 0, "parameters": ["her condition worsens even more."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["The ingredients needed for the potion "]}, {"code": 401, "indent": 0, "parameters": ["aren't easily available on the market.  "]}, {"code": 401, "indent": 0, "parameters": ["The only place where all the ingredients "]}, {"code": 401, "indent": 0, "parameters": ["can be found is there—"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["So as her <PERSON><PERSON>-chan, "]}, {"code": 401, "indent": 0, "parameters": ["no matter how many times it takes, "]}, {"code": 401, "indent": 0, "parameters": ["I must venture into that place to "]}, {"code": 401, "indent": 0, "parameters": ["find a way to completely cure my sister."]}, {"code": 242, "indent": 0, "parameters": [2]}, {"code": 122, "indent": 0, "parameters": [13, 13, 0, 0, 1]}, {"code": 201, "indent": 0, "parameters": [0, 36, 6, 6, 0, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 0}, {"id": 5, "name": "第一次深渊死亡回归", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 2, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["报错防范"]}, {"code": 117, "indent": 0, "parameters": [21]}, {"code": 235, "indent": 0, "parameters": [99]}, {"code": 355, "indent": 0, "parameters": ["var actor = $gameParty.leader();"]}, {"code": 655, "indent": 0, "parameters": ["var states = actor.states();"]}, {"code": 655, "indent": 0, "parameters": ["for (var i = 0; i < states.length; i++) {"]}, {"code": 655, "indent": 0, "parameters": ["    actor.removeState(states[i].id);"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 655, "indent": 0, "parameters": ["actor.setHp(1);"]}, {"code": 241, "indent": 0, "parameters": [{"name": "日常_放課後", "volume": 60, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 0, "parameters": [1, "entrance_S_E_DO_RL", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 356, "indent": 0, "parameters": ["DS_方向設定 90"]}, {"code": 225, "indent": 0, "parameters": [6, 8, 30, false]}, {"code": 224, "indent": 0, "parameters": [[0, 0, 0, 185], 30, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "ドシャァ！地面に倒れる、落ちる", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 355, "indent": 0, "parameters": ["$gameSelfVariables.setValue([$gameMap.mapId(), 13, 'days'], 0);"]}, {"code": 655, "indent": 0, "parameters": ["$gameSwitches.setValue(490, false)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\"No, I can't go on...\\w[30]I almost died...\""]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["It's a good thing I escaped at the last moment..."]}, {"code": 401, "indent": 0, "parameters": ["As I thought, trying to take on that place "]}, {"code": 401, "indent": 0, "parameters": ["with my current strength was too much..."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["I should train more,"]}, {"code": 401, "indent": 0, "parameters": ["and only consider tackling that "]}, {"code": 401, "indent": 0, "parameters": ["place after making thorough preparations..."]}, {"code": 231, "indent": 0, "parameters": [9, "mio_tachie_smile", 0, 0, 200, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": [">显现动作 : 图片[9] : 移动显现 : 时间[60] : 方向角度[180] : 移动距离[180]"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_14"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >\"Welcome home, \\w[50]Onii...\\....\\^"]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.changePictureName(9, \"mio_tachie_ase\");"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_15"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 356, "indent": 0, "parameters": [">持续动作 : 图片[9] : 上下震动 : 持续时间[40] : 周期[20] : 震动幅度[3]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >\"Onii-chan!?\""]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_09"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >\"<PERSON><PERSON>-chan, are you okay!? \\w[60]"]}, {"code": 401, "indent": 0, "parameters": ["You look all bruised and battered...!\""]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\"Yeah, I pushed myself a bit too hard "]}, {"code": 401, "indent": 0, "parameters": ["and ended up failing... Ahaha...\""]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.changePictureName(9, \"mio_tachie_shy1\");"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_10"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >\"<PERSON><PERSON>-chan, you really need to take "]}, {"code": 401, "indent": 0, "parameters": ["better care of yourself!\""]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_11"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >\"If you're injured, "]}, {"code": 401, "indent": 0, "parameters": ["we need to bandage you up right away!\""]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 235, "indent": 0, "parameters": [9]}, {"code": 250, "indent": 0, "parameters": [{"name": "Heal2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["let base = 15 + Math.randomInt(11);"]}, {"code": 655, "indent": 0, "parameters": ["let heal = $gameVariables.value(15) * base + 15;"]}, {"code": 655, "indent": 0, "parameters": ["$gameActors.actor(1).gainHp(heal);"]}, {"code": 231, "indent": 0, "parameters": [9, "mio_tachie_nocoat_nununu", 0, 0, 200, 1600, 100, 100, 255, 0]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 111, "indent": 0, "parameters": [1, 15, 0, 4, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["After sister expertly bandaged me up, "]}, {"code": 401, "indent": 1, "parameters": ["I felt much more rejuvenated."]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 15, 0, 2, 1]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["After sister carefully bandaged me up "]}, {"code": 401, "indent": 2, "parameters": ["with somewhat clumsy technique, "]}, {"code": 401, "indent": 2, "parameters": ["I felt a lot better."]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["Sister clumsily wrapped bandages all over my body, "]}, {"code": 401, "indent": 2, "parameters": ["but I still felt a bit of my strength returning."]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["var picture = $gameScreen.picture(9);"]}, {"code": 655, "indent": 0, "parameters": ["var data = {\t\t\t\t\t\t\t\t\"type\":\"smoothMove\","]}, {"code": 655, "indent": 0, "parameters": ["\"valueX\":0,"]}, {"code": 655, "indent": 0, "parameters": ["\"valueY\":-1450,"]}, {"code": 655, "indent": 0, "parameters": ["\"time\":60,"]}, {"code": 655, "indent": 0, "parameters": ["\"cur_time\":0,\"cur_speedX\":0,\"cur_speedY\":0,}"]}, {"code": 655, "indent": 0, "parameters": ["if (picture) { picture._drill_PSh_commandChangeTank.push(data);"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_12"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >\"Now, onii-chan, you just rest up!"]}, {"code": 401, "indent": 0, "parameters": ["Leave dinner to me!\""]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\"Ah, got it!\""]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["Come to think of it, "]}, {"code": 401, "indent": 0, "parameters": ["I'm wrapped up like a mummy and can't move at all..."]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.changePictureName(9, \"mio_tachie_nocoat_smile\");"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_13"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >\"Mhm, onii-chan is such a good boy.\""]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["Is it just my imagination, "]}, {"code": 401, "indent": 0, "parameters": ["or does my sister seem more motivated than usual...?"]}, {"code": 122, "indent": 0, "parameters": [14, 14, 0, 0, 15]}, {"code": 117, "indent": 0, "parameters": [3]}, {"code": 121, "indent": 0, "parameters": [5, 5, 1]}, {"code": 122, "indent": 0, "parameters": [13, 13, 0, 0, 22]}, {"code": 201, "indent": 0, "parameters": [0, 11, 5, 6, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 4, "y": 0}, {"id": 6, "name": "白天提前回家", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 111, "indent": 0, "parameters": [12, "$gameSystem.hour() <= 16"]}, {"code": 111, "indent": 1, "parameters": [1, 60, 0, 2, 0]}, {"code": 231, "indent": 2, "parameters": [1, "entrance_R_D_DO", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [1, 60, 0, 1, 0]}, {"code": 231, "indent": 3, "parameters": [1, "entrance_C_D_DO", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 231, "indent": 3, "parameters": [1, "entrance_S_D_DO", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 231, "indent": 1, "parameters": [1, "entrance_S_E_DO_RL", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 5, 0]}, {"code": 117, "indent": 1, "parameters": [58]}, {"code": 121, "indent": 1, "parameters": [5, 5, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 241, "indent": 0, "parameters": [{"name": "日常_放課後", "volume": 40, "pitch": 100, "pan": 0}]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["早归触发游戏机事件"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameParty.hasItem($dataItems[15]) && !$gameSwitches.value(500)"]}, {"code": 355, "indent": 1, "parameters": ["$gameMap.event(7).start()"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 231, "indent": 0, "parameters": [9, "mio_tachie_OAO", 0, 0, 200, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": [">显现动作 : 图片[9] : 移动显现 : 时间[60] : 方向角度[180] : 移动距离[200]"]}, {"code": 230, "indent": 0, "parameters": [70]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_24"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >\"Onii-chan? You’re home early today.\""]}, {"code": 355, "indent": 0, "parameters": ["this.count = Math.randomInt(4);"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\"Yeah, I’m a bit tired today, "]}, {"code": 401, "indent": 0, "parameters": ["so I decided not to push myself with exploring...\""]}, {"code": 111, "indent": 0, "parameters": [12, "this.count === 3"]}, {"code": 231, "indent": 1, "parameters": [9, "mio_tachie_=ω=", 0, 0, 200, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE genkan_event_sis_28"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nl< >\"Isn’t this what they call... May sickness?\""]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["May...? But it's the middle of a hot summer."]}, {"code": 231, "indent": 1, "parameters": [9, "mio_tachie_IAI", 0, 0, 200, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE genkan_event_sis_29"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nl< >\"I get that feeling a lot too... "]}, {"code": 401, "indent": 1, "parameters": ["I think I’m just taking a nap until noon, "]}, {"code": 401, "indent": 1, "parameters": ["but when I wake up, you’re already making dinner...\""]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["...Isn’t that just because you like sleeping in?"]}, {"code": 119, "indent": 1, "parameters": ["3"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "this.count === 2"]}, {"code": 231, "indent": 2, "parameters": [9, "mio_tachie_happy", 0, 0, 200, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["SV_PLAY_VOICE genkan_event_sis_27"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["\\nl< >\"Sometimes it's nice to take it easy like this. "]}, {"code": 401, "indent": 2, "parameters": ["Let's play together, <PERSON><PERSON>-chan!\""]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [12, "this.count === 1"]}, {"code": 231, "indent": 3, "parameters": [9, "mio_tachie_shinnpai", 0, 0, 200, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 3, "parameters": ["SV_PLAY_VOICE genkan_event_sis_26"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nl< >\"Rest is important, "]}, {"code": 401, "indent": 3, "parameters": ["so make sure you take a good break next time.\""]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 231, "indent": 3, "parameters": [9, "mio_tachie_smile", 0, 0, 200, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 3, "parameters": ["SV_PLAY_VOICE genkan_event_sis_25"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nl< >\"That's right, I'll help you relax, <PERSON><PERSON>-chan!\""]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["Yeah, there’s still time, so let’s take it easy today."]}, {"code": 242, "indent": 0, "parameters": [2]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "明るくて可愛いチェレスタのジングル", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$gameSystem.hour() >= 17"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["$gameSystem.add_hour(1)"]}, {"code": 108, "indent": 2, "parameters": ["计算体力恢复"]}, {"code": 311, "indent": 2, "parameters": [0, 1, 0, 0, 12, false]}, {"code": 117, "indent": 2, "parameters": [8]}, {"code": 230, "indent": 2, "parameters": [30]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 111, "indent": 0, "parameters": [12, "this.count === 3"]}, {"code": 118, "indent": 1, "parameters": ["3"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\"But wait, "]}, {"code": 401, "indent": 1, "parameters": ["there’s still time, so let’s take it easy today.\""]}, {"code": 231, "indent": 1, "parameters": [9, "mio_tachie_happy", 0, 0, 200, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE genkan_event_sis_30"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nl< >\"How about taking a nap together, <PERSON><PERSON>-chan? "]}, {"code": 401, "indent": 1, "parameters": ["It feels really nice.\""]}, {"code": 242, "indent": 1, "parameters": [2]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "明るくて可愛いチェレスタのジングル", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 112, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [12, "$gameSystem.hour() >= 17"]}, {"code": 113, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 355, "indent": 3, "parameters": ["$gameSystem.add_hour(1)"]}, {"code": 108, "indent": 3, "parameters": ["计算体力恢复"]}, {"code": 311, "indent": 3, "parameters": [0, 1, 0, 0, 12, false]}, {"code": 117, "indent": 3, "parameters": [8]}, {"code": 230, "indent": 3, "parameters": [30]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 413, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["In the end, I ended up sleeping with my sister "]}, {"code": 401, "indent": 1, "parameters": ["until it was almost evening."]}, {"code": 119, "indent": 1, "parameters": ["结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "this.count === 2"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["We spent the whole day playing games together."]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [12, "this.count === 1"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["We relaxed and took it easy during the day."]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["My sister enthusiastically gave me a long massage,"]}, {"code": 401, "indent": 3, "parameters": ["and it felt so good that I fell asleep right after..."]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [13, 13, 0, 0, 36]}, {"code": 201, "indent": 0, "parameters": [0, 4, 8, 6, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 3, "y": 8}, {"id": 7, "name": "游戏机事件", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 2, "characterIndex": 1}, "list": [{"code": 111, "indent": 0, "parameters": [12, "$gameSystem.hour() <= 16"]}, {"code": 231, "indent": 1, "parameters": [9, "mio_tachie_OAO", 0, 0, 200, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 1, "parameters": [">显现动作 : 图片[9] : 移动显现 : 时间[60] : 方向角度[180] : 移动距离[200]"]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE genkan_event_sis_24"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nl< >\"Onii-chan? You’re home early today.\""]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 231, "indent": 1, "parameters": [9, "mio_tachie_smile", 1, 0, 572, 950, 100, 100, 255, 0]}, {"code": 356, "indent": 1, "parameters": [">显现动作 : 图片[9] : 移动显现 : 时间[60] : 方向角度[180] : 移动距离[180]"]}, {"code": 230, "indent": 1, "parameters": [35]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE genkan_event_sis_01"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nl< >\"Welcome home, onii-chan.\""]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\"Heh heh heh, my dear sister,"]}, {"code": 401, "indent": 0, "parameters": ["Guess what <PERSON><PERSON><PERSON><PERSON><PERSON> brought back today—!\""]}, {"code": 231, "indent": 0, "parameters": [9, "mio_tachie_OAO", 1, 0, 572, 950, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_16"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >\"It's a huge package—\\w[90]\\^"]}, {"code": 231, "indent": 0, "parameters": [9, "mio_tachie_=ω=", 1, 0, 572, 950, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_17"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >\"Could it be something delicious...!?\""]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["Hmm... My little sister really doesn't "]}, {"code": 401, "indent": 0, "parameters": ["seem to care about anything other than food..."]}, {"code": 242, "indent": 0, "parameters": [2]}, {"code": 122, "indent": 0, "parameters": [13, 13, 0, 0, 34]}, {"code": 201, "indent": 0, "parameters": [0, 4, 6, 8, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 2, "y": 0}, null, {"id": 9, "name": "常规回家后事件", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 1, "characterIndex": 3}, "list": [{"code": 111, "indent": 0, "parameters": [8, 197]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nl< >\"………\""]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["The warm sensation in my hands constantly "]}, {"code": 401, "indent": 1, "parameters": ["reminded me that I’d finally obtained "]}, {"code": 401, "indent": 1, "parameters": ["the medicine I’d been dreaming of."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["But the surreal nature of "]}, {"code": 401, "indent": 1, "parameters": ["the experience made it hard to process."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["The Star Tarot card connected to our front door"]}, {"code": 401, "indent": 1, "parameters": ["—one of the items my father left behind."]}, {"code": 401, "indent": 1, "parameters": ["While he’d spent his life researching a way "]}, {"code": 401, "indent": 1, "parameters": ["to help my sister return to normal,"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["why… did it react so easily this time?"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["……"]}, {"code": 401, "indent": 1, "parameters": ["……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["I couldn’t come to any conclusions. "]}, {"code": 401, "indent": 1, "parameters": ["Overthinking it now was pointless."]}, {"code": 401, "indent": 1, "parameters": ["The fact is, I’ve got the Elixir Rubrum. "]}, {"code": 401, "indent": 1, "parameters": ["I’ll find a chance to let her try it."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["Its color looks like strawberry juice—"]}, {"code": 401, "indent": 1, "parameters": ["maybe it even tastes like "]}, {"code": 401, "indent": 1, "parameters": ["her favorite strawberry milk…"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 231, "indent": 0, "parameters": [9, "mio_tachie_smile", 1, 0, 572, 950, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": [">显现动作 : 图片[9] : 移动显现 : 时间[60] : 方向角度[180] : 移动距离[180]"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_01"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >\"Welcome home, onii-chan.\""]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.changePictureName(9, \"mio_tachie_IAI\");"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.erasePicture(10);"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.erasePicture(11);"]}, {"code": 356, "indent": 0, "parameters": [">持续动作 : 图片[9] : 上下震动 : 持续时间[50] : 周期[25] : 震动幅度[3]"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_02"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >\"I'm already starving...\""]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\"Alright, alright, "]}, {"code": 401, "indent": 0, "parameters": ["I'm getting dinner ready right now.\""]}, {"code": 119, "indent": 0, "parameters": ["1"]}, {"code": 108, "indent": 0, "parameters": ["修正选项组图标"]}, {"code": 355, "indent": 0, "parameters": ["$gameSystem._drill_DCB_curStyle = 25"]}, {"code": 355, "indent": 0, "parameters": ["let style = [];"]}, {"code": 655, "indent": 0, "parameters": ["style.push(\"eventButtons_sis_crafting\");"]}, {"code": 655, "indent": 0, "parameters": ["$gameSwitches.value(480) ? style.push(\"eventButtons_ofuro1\") : null;"]}, {"code": 655, "indent": 0, "parameters": ["style.push(\"eventButtons_toilet\");"]}, {"code": 655, "indent": 0, "parameters": ["//style.push(\"eventButtons_sex1\");"]}, {"code": 655, "indent": 0, "parameters": ["DrillUp.g_DCB_data[24].btn_src = style;"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["I patted my sister's head to comfort her.\\w[30]"]}, {"code": 401, "indent": 0, "parameters": ["Alright, what should I do next—"]}, {"code": 102, "indent": 0, "parameters": [["Prepare dinner", "<<!s[480]>>\\c[15]Bath first", "\\c[15]go to the toilet"], -1, -1, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "Prepare dinner"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["Yeah, it's getting late; "]}, {"code": 401, "indent": 1, "parameters": ["I should go prepare dinner."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\"What do you want to eat tonight?\""]}, {"code": 355, "indent": 1, "parameters": ["$gameScreen.changePictureName(9, \"mio_tachie_happy\");"]}, {"code": 355, "indent": 1, "parameters": ["$gameScreen.picture(9).drill_PCE_playSustainingShakeRotate( 120,60,1);"]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE genkan_event_sis_03"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nl< >\"Anything with meat will do!\""]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["Yeah, just as expected."]}, {"code": 401, "indent": 1, "parameters": ["I really should emphasize the importance "]}, {"code": 401, "indent": 1, "parameters": ["of a balanced diet more."]}, {"code": 122, "indent": 1, "parameters": [13, 13, 0, 0, 1]}, {"code": 201, "indent": 1, "parameters": [0, 11, 5, 5, 0, 2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "<<!s[480]>>\\c[15]Bath first"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["汗かいたし、先にお風呂に入ってしまおうかな。"]}, {"code": 355, "indent": 1, "parameters": ["$gameScreen.changePictureName(9, \"mio_tachie_=ω=\");"]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE genkan_event_sis_04"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nl< >「お兄ちゃん先にお風呂に入るの？"]}, {"code": 401, "indent": 1, "parameters": ["それじゃいってらっしゃい～」"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "\\c[15]go to the toilet"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["I’m going to the toilet for a bit..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\"Sorry, please wait a little longer."]}, {"code": 401, "indent": 1, "parameters": [" I'll prepare dinner later.\""]}, {"code": 355, "indent": 1, "parameters": ["$gameScreen.changePictureName(9, \"mio_tachie_OAO\");"]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE genkan_event_sis_05"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nl< >\"Got it.\""]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 122, "indent": 1, "parameters": [13, 13, 0, 0, 3]}, {"code": 201, "indent": 1, "parameters": [0, 20, 3, 6, 0, 2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 2, "y": 7}, {"id": 10, "name": "深渊死亡回归", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["报错防范"]}, {"code": 117, "indent": 0, "parameters": [21]}, {"code": 235, "indent": 0, "parameters": [99]}, {"code": 355, "indent": 0, "parameters": ["var actor = $gameParty.leader();"]}, {"code": 655, "indent": 0, "parameters": ["var states = actor.states();"]}, {"code": 655, "indent": 0, "parameters": ["for (var i = 0; i < states.length; i++) {"]}, {"code": 655, "indent": 0, "parameters": ["    actor.removeState(states[i].id);"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 655, "indent": 0, "parameters": ["actor.setHp(1);"]}, {"code": 241, "indent": 0, "parameters": [{"name": "日常_放課後", "volume": 60, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 0, "parameters": [1, "entrance_S_E_DO_RL", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 356, "indent": 0, "parameters": ["DS_方向設定 90"]}, {"code": 225, "indent": 0, "parameters": [6, 8, 30, false]}, {"code": 224, "indent": 0, "parameters": [[0, 0, 0, 185], 30, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "ドシャァ！地面に倒れる、落ちる", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 355, "indent": 0, "parameters": ["$gameSelfVariables.setValue([$gameMap.mapId(), 13, 'days'], 0);"]}, {"code": 655, "indent": 0, "parameters": ["$gameSwitches.setValue(490, false)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\"No, I can't go on...\\w[30]I almost died...\""]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["It's a good thing I escaped at the last moment..."]}, {"code": 401, "indent": 0, "parameters": ["As I thought, trying to take on that place "]}, {"code": 401, "indent": 0, "parameters": ["with my current strength was too much..."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["I should train more,"]}, {"code": 401, "indent": 0, "parameters": ["and only consider tackling that "]}, {"code": 401, "indent": 0, "parameters": ["place after making thorough preparations..."]}, {"code": 231, "indent": 0, "parameters": [9, "mio_tachie_smile", 0, 0, 200, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": [">显现动作 : 图片[9] : 移动显现 : 时间[60] : 方向角度[180] : 移动距离[180]"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_14"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >\"Welcome home, \\w[50]onii-...\\....\\^"]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.changePictureName(9, \"mio_tachie_ase\");"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_15"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 356, "indent": 0, "parameters": [">持续动作 : 图片[9] : 上下震动 : 持续时间[40] : 周期[20] : 震动幅度[3]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >\"Onii-chan?!\""]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_09"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >\"<PERSON><PERSON>-chan, are you okay!?\\w[60]"]}, {"code": 401, "indent": 0, "parameters": ["You look all bruised and battered...!\""]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\"Yeah, I pushed myself a bit too hard "]}, {"code": 401, "indent": 0, "parameters": ["and ended up failing... Ahaha...\""]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.changePictureName(9, \"mio_tachie_shy1\");"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_21"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >\"<PERSON><PERSON>-chan, you were pushing yourself "]}, {"code": 401, "indent": 0, "parameters": ["too hard again!\""]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_22"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >\"We need to stop the bleeding quickly!\""]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "Heal2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["let base = 15 + Math.randomInt(11);"]}, {"code": 655, "indent": 0, "parameters": ["let heal = $gameVariables.value(15) * base + 15;"]}, {"code": 655, "indent": 0, "parameters": ["$gameActors.actor(1).gainHp(heal);"]}, {"code": 231, "indent": 0, "parameters": [9, "mio_tachie_nocoat_bimyou2", 0, 0, 200, 1600, 100, 100, 255, 0]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 111, "indent": 0, "parameters": [1, 15, 0, 4, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["After my sister skillfully bandaged my wounds, "]}, {"code": 401, "indent": 1, "parameters": ["I felt much of my strength returning."]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 15, 0, 2, 1]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["After my sister carefully bandaged me up with "]}, {"code": 401, "indent": 2, "parameters": ["somewhat clumsy technique, I felt a lot better."]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["My sister clumsily wrapped bandages all over my body, "]}, {"code": 401, "indent": 2, "parameters": ["but I still felt a bit of my strength returning."]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["var picture = $gameScreen.picture(9);"]}, {"code": 655, "indent": 0, "parameters": ["var data = {\t\t\t\t\t\t\t\t\"type\":\"smoothMove\","]}, {"code": 655, "indent": 0, "parameters": ["\"valueX\":0,"]}, {"code": 655, "indent": 0, "parameters": ["\"valueY\":-1450,"]}, {"code": 655, "indent": 0, "parameters": ["\"time\":60,"]}, {"code": 655, "indent": 0, "parameters": ["\"cur_time\":0,\"cur_speedX\":0,\"cur_speedY\":0,}"]}, {"code": 655, "indent": 0, "parameters": ["if (picture) { picture._drill_PSh_commandChangeTank.push(data);"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 230, "indent": 0, "parameters": [45]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE genkan_event_sis_23"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >\"From now on, "]}, {"code": 401, "indent": 0, "parameters": ["let me take care of you, on<PERSON>-chan!\""]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["Is it just my imagination? "]}, {"code": 401, "indent": 0, "parameters": ["My sister seems more excited than usual..."]}, {"code": 122, "indent": 0, "parameters": [14, 14, 0, 0, 10]}, {"code": 117, "indent": 0, "parameters": [3]}, {"code": 122, "indent": 0, "parameters": [13, 13, 0, 0, 18]}, {"code": 201, "indent": 0, "parameters": [0, 11, 5, 6, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 4, "y": 8}, {"id": 11, "name": "白天提前回家", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["this.count = 0"]}, {"code": 118, "indent": 0, "parameters": ["配音"]}, {"code": 241, "indent": 0, "parameters": [{"name": "日常_放課後", "volume": 60, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["$gameSystem.set_hour(10)"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameSystem.hour() <= 16"]}, {"code": 111, "indent": 1, "parameters": [1, 60, 0, 2, 0]}, {"code": 231, "indent": 2, "parameters": [1, "entrance_R_D_DO", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [1, 60, 0, 1, 0]}, {"code": 231, "indent": 3, "parameters": [1, "entrance_C_D_DO", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 231, "indent": 3, "parameters": [1, "entrance_S_D_DO", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 231, "indent": 1, "parameters": [1, "entrance_S_E_DO_RL", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 231, "indent": 0, "parameters": [9, "mio_tachie_OAO", 0, 0, 200, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": [">显现动作 : 图片[9] : 移动显现 : 时间[60] : 方向角度[180] : 移动距离[200]"]}, {"code": 230, "indent": 0, "parameters": [70]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl< >「哥哥？今天回来得很早诶？」"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["「嗯、今天感觉有点累，所以不勉强去探索了…」"]}, {"code": 111, "indent": 0, "parameters": [12, "this.count === 3"]}, {"code": 231, "indent": 1, "parameters": [9, "mio_tachie_=ω=", 0, 0, 200, 150, 100, 100, 255, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nl< >「这是那个什么吧……五月病来着？」"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["五月……？现在明明是炎热的夏天诶？"]}, {"code": 231, "indent": 1, "parameters": [9, "mio_tachie_IAI", 0, 0, 200, 150, 100, 100, 255, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nl< >「我也经常会有这种感觉呢…"]}, {"code": 401, "indent": 1, "parameters": ["明明只想睡到中午，结果醒来时哥哥已经连晚饭都做好了…」"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["……那只是单纯地喜欢睡懒觉吧？"]}, {"code": 119, "indent": 1, "parameters": ["3"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "this.count === 2"]}, {"code": 231, "indent": 2, "parameters": [9, "mio_tachie_happy", 0, 0, 200, 150, 100, 100, 255, 0]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["\\nl< >「偶尔这样也很不错呢，一起来玩吧哥哥！」"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [12, "this.count === 1"]}, {"code": 231, "indent": 3, "parameters": [9, "mio_tachie_shinnpai", 0, 0, 200, 150, 100, 100, 255, 0]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nl< >「劳逸结合很重要呢，哥哥接下来就好好休息下吧。」"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 231, "indent": 3, "parameters": [9, "mio_tachie_smile", 0, 0, 200, 150, 100, 100, 255, 0]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nl< >「是这样啊、那我来帮哥哥放松一下吧！」"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["是啊、时间还有余裕、今天就好好休息放松下吧。"]}, {"code": 242, "indent": 0, "parameters": [2]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "明るくて可愛いチェレスタのジングル", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$gameSystem.hour() >= 17"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["$gameSystem.add_hour(1)"]}, {"code": 108, "indent": 2, "parameters": ["计算体力恢复"]}, {"code": 311, "indent": 2, "parameters": [0, 1, 0, 0, 12, false]}, {"code": 117, "indent": 2, "parameters": [8]}, {"code": 230, "indent": 2, "parameters": [30]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 111, "indent": 0, "parameters": [12, "this.count === 3"]}, {"code": 118, "indent": 1, "parameters": ["3"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["不过也是呢、时间还有余裕、今天就好好休息吧。"]}, {"code": 231, "indent": 1, "parameters": [9, "mio_tachie_happy", 0, 0, 200, 150, 100, 100, 255, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nl< >「那哥哥也来一起试试午睡吧…很舒服哦？」"]}, {"code": 242, "indent": 1, "parameters": [2]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "明るくて可愛いチェレスタのジングル", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 112, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [12, "$gameSystem.hour() >= 17"]}, {"code": 113, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 355, "indent": 3, "parameters": ["$gameSystem.add_hour(1)"]}, {"code": 108, "indent": 3, "parameters": ["计算体力恢复"]}, {"code": 311, "indent": 3, "parameters": [0, 1, 0, 0, 12, false]}, {"code": 117, "indent": 3, "parameters": [8]}, {"code": 230, "indent": 3, "parameters": [30]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 413, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["结果和妹妹一起睡到了临近晚上才醒来。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "this.count === 2"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["和妹妹一起玩了一整天的游戏。"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [12, "this.count === 1"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["和妹妹一起悠闲地度过了白天。"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["妹妹干劲十足地替我按摩了很久、"]}, {"code": 401, "indent": 3, "parameters": ["因为太舒服就这么睡到了下午…"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["this.count++"]}, {"code": 119, "indent": 0, "parameters": ["配音"]}, {"code": 122, "indent": 0, "parameters": [13, 13, 0, 0, 1]}, {"code": 201, "indent": 0, "parameters": [0, 4, 8, 6, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 8, "y": 5}, {"id": 12, "name": "后续事件检测", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 0, "characterIndex": 1}, "list": [{"code": 230, "indent": 0, "parameters": [60]}, {"code": 111, "indent": 0, "parameters": [0, 493, 1]}, {"code": 355, "indent": 1, "parameters": ["for (var i = 23; i <= 27; i++) {"]}, {"code": 655, "indent": 1, "parameters": ["    var item = $dataItems[i];"]}, {"code": 655, "indent": 1, "parameters": ["    if ($gameParty.numItems(item) >= 1) {"]}, {"code": 655, "indent": 1, "parameters": ["        $gameSelfSwitches.setValue([4, 44, 'A'], true);"]}, {"code": 655, "indent": 1, "parameters": ["        break;"]}, {"code": 655, "indent": 1, "parameters": ["    }"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [4]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 115, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 8, "y": 1}, null, null, {"id": 15, "name": "EV015", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["$gameMap.event(3).start()"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 8, "y": 8}, null]}