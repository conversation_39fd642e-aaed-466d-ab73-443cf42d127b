{"3": {"name": ["Odd Hair Accessory"], "subtitle": "", "description": ["A hair accessory adorned with an unusual ornament", "Is it a rabbit… or a bun…?"], "ability": [""]}, "6": {"name": ["Birth of Misfortune"], "subtitle": "", "description": ["Parasitized by a Phil<PERSON>pher <PERSON>, a weak creature was forcibly", "infused with endless vitality, endlessly cycling between life and", "death until madness took over. Even removed, this power remains"], "ability": ["Increases HP. Enhances HP recovery effects by 25%", "Grants automatic regeneration when equipped"]}, "7": {"name": ["Cursed <PERSON><PERSON>"], "subtitle": "", "description": ["An undeveloped cursed fetus that, despite everything,", "remains alive.Its intense will to survive and overflowing", "vitality can even influence its bearer"], "ability": ["Increases HP. Enhances HP recovery effects by 20%"]}, "8": {"name": ["Mana Crystal Cluster"], "subtitle": "", "description": ["A mineral shaped by the Great Labyrinth, containing trace mana", "Carrying it enhances magical understanding"], "ability": ["Equipping it boosts casting speed and magic power"]}, "9": {"name": ["Abyssal Mana Cluster"], "subtitle": "", "description": ["A rare cluster infused with dense mana,", "radiating palpable magical waves. Highly sought after by alchemists"], "ability": ["Equipping it boosts casting speed and magic power"]}, "10": {"name": ["Focus Circlet"], "subtitle": "", "description": ["A circlet worn by the great sage during their youth as an", "adventurer. It helps focus the mind and blocks external distractions"], "ability": ["Immune to Stun", "Enhances magic power and casting scale"]}, "11": {"name": ["<PERSON><PERSON>loves"], "subtitle": "", "description": ["Gloves worn by the great sage during their youth as", "an adventurer. They gather mana naturally,", "imbuing weapons with magical properties"], "ability": ["Weapons will deal additional magic damage"]}, "12": {"name": ["Borrowed Neko Claws"], "subtitle": [" \"...<PERSON><PERSON>~?\""], "description": ["Neko claws hidden inside a Neko Slime, seemingly infused", "with a cat’s spirit. Equipping them boosts your agility!"], "ability": ["Increases attack power and attack speed"]}, "13": {"name": ["Borrowed <PERSON><PERSON><PERSON>"], "subtitle": [" \"...<PERSON><PERSON>~?\""], "description": ["Neko paws hidden inside a Neko Slime, seemingly infused", "with a cat’s spirit. Equipping them boosts your agility!"], "ability": ["Increases attack and movement speed, reduces Senpo cooldown"]}, "14": {"name": ["Fragrant Grass Ring"], "subtitle": "", "description": ["A ring woven from leaves and rhizomes, emitting a herbal scent", "Once a charm for travelers, it symbolizes vitality and energy"], "ability": ["Max HP +\\c[29]${$gameActors.actor(1).skillMasteryLevel(35)>0?(($gameActors.actor(1).skillMasteryLevel(35)*($gameActors.actor(1).skillMasteryLevel(35) + 1)/2)*15)/$gameActors.actor(1).skillMasteryLevel(35):15}\\c[0]", "Wearing multiple of this gear enhances its effect"]}, "15": {"name": ["Neko Plush Suit"], "subtitle": "", "description": ["A soft and cuddly cat plush costume — fluffy and oversized", "Its roomy design wraps you up and even hides extra goodies~"], "ability": ["Extra carry capacity for weapons +10", "Extra carry capacity for gears +20"]}, "16": {"name": ["Piercing <PERSON>"], "subtitle": "", "description": ["A spiked metal armor exuding danger from every angle,", "offering solid defense and automatically damaging nearby enemies"], "ability": ["Automatically damages nearby enemies"]}, "17": {"name": ["Piercing Spike Armour"], "subtitle": "", "description": ["A spiked metal armor exuding danger from every angle,", "offering solid defense and automatically damaging nearby enemies"], "ability": ["Automatically damages nearby enemies"]}, "18": {"name": ["Piercing Spike Gauntlets"], "subtitle": "", "description": ["A spiked metal armor exuding danger from every angle,", "offering solid defense and automatically damaging nearby enemies"], "ability": ["Automatically damages nearby enemies when attacking"]}, "19": {"name": ["Piercing Spike <PERSON>"], "subtitle": "", "description": ["A spiked metal armor exuding danger from every angle,", "offering solid defense and automatically damaging nearby enemies"], "ability": ["Automatically damages nearby enemies when moving"]}, "20": {"name": ["Knight's Helmet"], "subtitle": "", "description": ["The standard armor of court knights. Its ornate and sturdy design", "symbolizes their adherence to the chivalric code"], "ability": ["Increases defense when HP is below \\c[10]30%\\c[0]"]}, "21": {"name": ["Knight's Armour"], "subtitle": "", "description": ["The standard armor of court knights. Its ornate and sturdy design", "symbolizes their adherence to the chivalric code"], "ability": ["Increases defense when HP is below \\c[10]30%\\c[0]"]}, "22": {"name": ["<PERSON>'s Gauntlets"], "subtitle": "", "description": ["The standard armor of court knights. Its ornate and sturdy design", "symbolizes their adherence to the chivalric code"], "ability": ["Increases attack and defense when HP is below \\c[10]30%\\c[0]"]}, "23": {"name": ["Knight's Greaves"], "subtitle": "", "description": ["The standard armor of court knights. Its ornate and sturdy design", "symbolizes their adherence to the chivalric code"], "ability": ["Increases movement speed when HP is below \\c[10]30%\\c[0]"]}, "26": {"name": ["Feather of Firebird"], "subtitle": [""], "description": ["A Firebird feather.", "symbolizing eternal life, still burning with a warm flame.", "Even a single feather holds the power to revive the dead."], "ability": ["Activates when HP drops to zero, fully restores HP", "The item is destroyed after activation"]}, "27": {"name": ["Toilet Paper"], "subtitle": "", "description": ["A Toilet Paper take from Home", "A handy item for adventurers who suddenly need", "a toilet break in a dangerous dungeon"], "ability": ["Reduces Bleed damage taken by \\c[10]30%\\c[0]"]}, "29": {"name": ["<PERSON><PERSON><PERSON>"], "subtitle": ["\"This one craves screams...\""], "description": ["One of the deadly weapons of a notorious killer,", "still twitching with anticipation"], "ability": ["Deals more damage to lower HP foes, up to \\c[10]30%\\c[0]"]}, "32": {"name": ["Neko Punch"], "subtitle": "", "description": ["\"Hyaa—!! Don’t mess with me nya!\"", "Soft-padded cat paw gloves, the prized weapon of a certain catgirl", "Wearing them grants feline agility—and a craving for fish…"], "ability": ["Attack speed increased. Extra attack hits +1", "Chance of extra attack hits triggering special effects is halved"]}, "33": {"name": ["Apple Headgear"], "subtitle": "", "description": ["A headgear made from apples, so you can't", "really expect any defense. But the apples are so delicious"], "ability": ["There’s a chance to activate healing when injured"]}, "36": {"name": ["<PERSON><PERSON><PERSON>"], "subtitle": "", "description": ["A greedy little Servant with a perpetually unsatisfied look", "It eagerly gathers all dropped items, regardless of their value"], "ability": ["<PERSON><PERSON><PERSON> automatically collects all types of drops for you", "Equipping multiple will only increase the collection range"]}, "37": {"name": [" Piggy Bank"], "subtitle": "", "description": ["A cute Servant that stores collected money", "Something might happen when full, but for now, it's just fragile"], "ability": ["Happily collects money. Current savings: \\c[6]${$gameSelfVariables.value([1, 110, 'deposit'])}\\c[0]yen!", "Easily breaks when attacked, losing all stored money"]}, "38": {"name": [" Greedy Piggy Bank"], "subtitle": "", "description": ["A piggy bank overflowing with wealth, a dream come true", "Yet, even with a belly full of gold, its greed knows no bounds"], "ability": ["Emits Ma<PERSON>’s Demand aura. Current savings: \\c[6]${$gameSelfVariables.value([1, 110, 'deposit'])}\\c[0]yen!", "Collects money endlessly, spitting out interest every 10 seconds"]}, "40": {"name": ["Maid Uniform"], "subtitle": "", "description": ["A trophy obtained after teaching a lesson to the mischievous", "rabbit-eared girl. It still carries the scent of dirt and carrots"], "ability": ["When equipped, disguises as a monster but cannot attack", "Equipping it makes you best friends with <PERSON>"]}, "41": {"name": ["<PERSON>"], "subtitle": "", "description": ["A detachable bunny ear accessory", "No matter who wears it, they’ll look absolutely adorable!"], "ability": ["Blessed with luck! Items are more likely to drop!"]}, "42": {"name": ["Bomb Fiend's Soul"], "subtitle": ["\"Explo————sion!!\""], "description": ["A will that sacrificed everything for explosions", "Now that you bear it, won’t you strive for even bigger blasts?"], "ability": ["Small chance to trigger an explosion when attacking or hit", "Reduce self-taken explosion damage by 50%"]}, "45": {"name": ["Venomous Embrace"], "subtitle": ["\"This dagger's loaded with deadly poison, y'know— (lick\""], "description": ["A poisonous magical shortsword—keep your distance"], "ability": ["An auto-orbiting Servant", "High chance to inflict <PERSON><PERSON> on contact"]}, "46": {"name": ["Fist of Fury"], "subtitle": "", "description": ["A powerful magical fist", "Seething with unknown rage, it remains tightly clenched…"], "ability": ["An auto-orbiting Servant", "Knocks back any enemy it touches"]}, "47": {"name": ["<PERSON><PERSON><PERSON>’s Cursed Hand"], "subtitle": "", "description": ["A severed hand imbued with undead power", "Even without a body, it remains lively and aggressive"], "ability": ["An auto-orbiting Servant", "Curses any target it points its middle finger at"]}, "50": {"name": ["Potato Chips"], "subtitle": "", "description": ["Freshly fried with a warm,", "savory aroma. Watch out for seagulls!"], "ability": ["Attracts seagulls that steal your chips"]}, "53": {"name": ["Hanged Man's Bone Ring"], "subtitle": "", "description": ["A ring made from the bones of the deceased Hang<PERSON> Man", "Though most of its power is lost,", "it still grants the wearer a bit of freedom from gravity"], "ability": ["Reduces fall damage by 50%"]}, "54": {"name": ["Incomplete Golden Grail"], "subtitle": "", "description": ["A saint’s relic yet to be filled, it blesses the holder with", "immense luck. The source of this blessing remains unknown,", "as it quietly awaits the day it will be complete"], "ability": ["Greatly increases luck. Doubles money earned"]}, "55": {"name": ["Arsonist’s Gloves"], "subtitle": ["\"Hah, Such filthy fireworks.\""], "description": ["Thick, heatproof gloves reeking of gunpowder"], "ability": ["+30% explosion radius for self-triggered bombs", "Bombs have a chance to trigger chain explosions"]}, "56": {"name": ["Saint Maiden’s Mercy"], "subtitle": ["\"Good child, good child...\""], "description": ["One of the nun's attires, silky smooth to the touch, unforgettable", "Holy radiance flows through its fibers, offering comfort"], "ability": ["Forcibly enters Sage Mode", "Gain more affinity when patting <PERSON><PERSON><PERSON>’s head"]}, "59": {"name": ["<PERSON><PERSON><PERSON>"], "subtitle": [""], "description": ["The Fallen Magician’s Precious Relic. ", "A pendant crafted from a small amount of Philosopher's Fragment ", "imbued with a faint yet everlasting alchemical power"], "ability": ["Boosts magic attack based on held Philosopher's Fragment count", "Reduces weapon durability use when casting magic by \\c[6]24%\\c[0]"]}, "62": {"name": ["Fallen Mage's Gloves"], "subtitle": "", "description": ["Cursed gloves tainted by dark magic,", "infused with a vengeful spirit eager to attack"], "ability": ["Projectiles track nearby enemies"]}, "63": {"name": ["Fallen Mage's Shoes"], "subtitle": "", "description": ["Cursed shoes tainted by dark magic,", "infused with a vengeful spirit that grants ghost-like agility"], "ability": ["Boosts movement speed, grants void state when using Senpo"]}, "65": {"name": ["Determination Headband"], "subtitle": ["\"Who the hell do you think I am!?\""], "description": ["A headband that stirs passion when worn", "No real effect, but its unyielding spirit is unmatched"], "ability": ["reduces abnormal status chance", "Has a small chance to resist fatal damage"]}, "67": {"name": ["Martial Artist's Bracers"], "subtitle": "", "description": ["Bandaged bracers worn by martial artists during sparring", "They protect joints while improving movement precision"], "ability": ["-12% weapon durability consumption", "Increased attack speed & boosted unarmed combo damage"]}, "68": {"name": ["Martial Artist's Shoes"], "subtitle": "", "description": ["Shoes commonly worn by martial artists who have", "mastered their craft, radiating a powerful aura"], "ability": ["Movement speed increased. <PERSON><PERSON> enhanced"]}, "69": {"name": ["Face Breaker"], "subtitle": ["\"Damn it... I just wanna punch someone in the face...!\""], "description": ["Punching gloves left behind by a legendary boxer.", "You can still feel the overwhelming force lingering on them."], "ability": ["Cannot use weapons while equipped", "Punch attacks may deal bonus damage based on target HP"]}, "70": {"name": ["Scarecrow's Heart"], "subtitle": "", "description": ["A hollow body of a scarecrow", "No warmth, no heartbeat—yet it can live in your place"], "ability": ["Prevents all forms of HP recovery while equipped", "At 0 HP, you remain active a bit longer before falling"]}, "73": {"name": ["<PERSON><PERSON>"], "subtitle": "", "description": ["A ring forged from rock flowing with scorching magma", "Wearing it eases the pain from both frost and flame"], "ability": ["Greatly reduces the chance of being frozen", "Reduces the chance of being burned"]}, "80": {"name": ["Royal Decree"], "subtitle": "", "description": ["A ring symbolizing the supreme glory of a true king", "Greatly boosts follower morale!"], "ability": ["<PERSON><PERSON><PERSON> increases all stats", "Enlarges all servants and enhances their special attributes"]}, "125": {"name": ["<PERSON><PERSON>"], "subtitle": "", "description": ["A cloth doll made with someone's heartfelt prayers. It carries", "wishes, bringing slight changes to regular weather patterns", "However, The doll quietly disappears afterward"], "ability": ["Ensures scorching hot weather the next day when equipped"]}, "126": {"name": ["<PERSON><PERSON>"], "subtitle": "", "description": ["A cloth doll made with someone's heartfelt prayers. It carries", "wishes, bringing slight changes to regular weather patterns", "However, The doll quietly disappears afterward"], "ability": ["Ensures sunny weather the next day when equipped"]}, "127": {"name": ["<PERSON><PERSON>"], "subtitle": "", "description": ["A cloth doll made with someone's heartfelt prayers. It carries", "wishes, bringing slight changes to regular weather patterns", "However, The doll quietly disappears afterward"], "ability": ["Ensures cloudy weather the next day when equipped"]}, "128": {"name": ["<PERSON><PERSON> Am<PERSON>"], "subtitle": "", "description": ["A cloth doll made with someone's heartfelt prayers. It carries", "wishes, bringing slight changes to regular weather patterns", "However, The doll quietly disappears afterward"], "ability": ["Ensures rainy weather the next day when equipped"]}, "147": {"name": ["Woundplast"], "subtitle": "", "description": [""], "ability": []}, "150": {"name": ["Bonus T-shirt"], "subtitle": "", "description": ["A custom-made outfit from the knight,", "supposedly for promoting the game", "But I don’t really want to wear it"], "ability": []}, "152": {"name": ["<PERSON><PERSON><PERSON><PERSON>s <PERSON><PERSON><PERSON>"], "subtitle": "", "description": ["A camisole pajama that <PERSON><PERSON><PERSON> usually wears, ", "decorated with the bows she loves.", "Come to think of it… why is this in my hands?"], "ability": []}, "153": {"name": ["Imouto’s Shorts"], "subtitle": "", "description": ["Soft and fluffy shorts that <PERSON><PERSON><PERSON> usually wears, ", "decorated with the bows she loves.", "Come to think of it… why is this in my hands?"], "ability": []}, "300": {"name": ["Catalyst Amatoria"], "subtitle": ["\"Give me your love—! Otherwise, let’s just die together~~~!\""], "description": ["A forbidden artifact forged by a certain yandere archdemon", "It forcibly makes anyone it touches fall in love with the wearer", "ignoring their will"], "ability": ["Greatly boosts the stats gained when patting <PERSON><PERSON><PERSON>"]}}