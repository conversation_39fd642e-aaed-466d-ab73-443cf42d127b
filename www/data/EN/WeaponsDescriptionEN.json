{"1": {"name": [" Hardwood sword"], "subtitle": "", "description": ["It's said that the hero also began his", "journey from the starting village with a wooden sword", "This has become a goal for many adventurers to emulate"], "ability": ["Deals extra damage when the weapon nears breaking"]}, "2": {"name": [" Hardwood stick"], "subtitle": "", "description": ["A great shape sturdy wooden stick ", "perfect for kids,It’s also decent as a weapon"], "ability": ["Deals extra damage when the weapon nears breaking"]}, "3": {"name": ["Lumber Axe"], "subtitle": "", "description": ["A reliable companion for adventurers or pioneers exploring", "forests. However, as it’s not designed for combat,", "don’t rely on it too much in battle"], "ability": ["Deals increased damage to trees"]}, "4": {"name": [" Clenched Fist"], "subtitle": "", "description": ["No weapon equipped? Then just unleash a flurry of punches!", "It may be a quick fix, but a fist full of spirit packs a serious punch", "Why use a weapon when your fists can do the job?"], "ability": ["Punch damage scales with max HP!"]}, "5": {"name": ["Knight's sword"], "subtitle": "", "description": ["A weapon used by father in his youth,", "well-maintained and seemingly issued to court knights"], "ability": ["When HP<\\c[10]30%\\c[0], this weapon's attack doubles"]}, "6": {"name": ["Lava Staff"], "subtitle": "", "description": ["A wand made from volcanic minerals,easily", "concentrates fire elements However, a slight mistake", "can cause it to explode, engulfing the user"], "ability": ["Hold right mouse button to cast \\c[27]Crimson Lotus Meteor\\c[0]"]}, "7": {"name": ["Ice Staff"], "subtitle": "", "description": ["A wand made from magic crystals, unleashes wide-range", "frost. Very hard to control.Feels like it’s freezing my hand"], "ability": ["Hold right mouse button to cast \\c[27]Frozen Spell\\c[0]"]}, "8": {"name": ["<PERSON>"], "subtitle": "", "description": ["A feather from a weird bird. Extremely light and can", "easily generate enough force to blow someone away"], "ability": ["Movement speed increases when using Whirlwind Slash"]}, "9": {"name": ["Knight's sword"], "subtitle": "", "description": ["Weapons assigned to the palace knights", "They symbolize the path of protection for the knights"], "ability": ["When HP<\\c[10]30%\\c[0], this weapon's attack doubles"]}, "10": {"name": ["Silver Greatsword"], "subtitle": "", "description": ["A greatsword, seemingly forged from silver,", "radiating a dazzling glow. Its surface shines cold and hard,", "though its true material remains unknown"], "ability": ["When HP falls below \\c[29]400\\c[0], the weapon’s full potential is limited!"]}, "11": {"name": [" Heavy Mallet"], "subtitle": "", "description": ["A bulky wooden hammer", "Getting hit on the head is sure to leave you dizzy!"], "ability": ["33% chance to inflict Stun state on attack", "When HP falls below \\c[29]400\\c[0], the weapon’s full potential is limited!"]}, "12": {"name": ["Baseball Bat"], "subtitle": "", "description": ["A metal club. It’s said to be used for sports, ", "but it’s also great for whacking people."], "ability": ["Durability Boost!", "Damage is multiplied for projectiles knocked back"]}, "13": {"name": ["<PERSON>"], "subtitle": ["\"It's too smelly, throw it away!\""], "description": ["Leftover bones from a large fish, with rotting meat", "still clinging to them, emitting an unbearable stench"], "ability": ["It will continue to emit a foul stench"]}, "14": {"name": ["Giant Crab <PERSON>"], "subtitle": "", "description": ["A massive crab claw that clamps on tight and won’t let go", "Once it latches onto something, good luck getting it off"], "ability": ["When thrown, it clamps to the target, dealing constant damage", "Cannot be retrieved unless the target dies"]}, "15": {"name": ["Frostmoon"], "subtitle": ["\"Licked the blade and got my tongue stuck… So cold, so painful!\"", "\"<PERSON><PERSON>-chan, don't lick random stuff!\""], "description": ["A magic sword made of ice, just near it sends a chilling cold"], "ability": ["50% chance to inflict Freeze state on attack"]}, "16": {"name": ["Leaky Electric Sword"], "subtitle": ["\"Zap... zap...!\""], "description": ["A cursed sword filled with wild energy, but it's still faulty", "Be careful—it might shock the user as well"], "ability": ["50% chance to inflict Lightning state on attack"]}, "17": {"name": ["Sunflare"], "subtitle": ["\"<PERSON><PERSON>-chan, it looks scary, please don't hold it.\""], "description": ["A magic sword that has absorbed solar energy over time", "It seems particularly effective against certain monsters"], "ability": ["40% chance to inflict Burn state on attack"]}, "18": {"name": ["Orc Bone Axe"], "subtitle": "", "description": ["A large axe made from the bones of defeated foes. Not", "very sharp, and orcs often prefer to throw it rather than", "use it in close combat"], "ability": ["Deals extra damage when the weapon is thrown"]}, "21": {"name": ["From Hell"], "subtitle": ["\"This thing thirsts for blood...\""], "description": ["One of the weapons used by a notorious serial killer,", "it's covered in blood"], "ability": ["45% chance to inflict Bleeding state on attack"]}, "24": {"name": ["Storm Orc Axe"], "subtitle": "", "description": ["A greataxe wielded by an orc attuned to nature’s power.", "Each swing tears the earth and sends gales howling with the blade"], "ability": ["Has a chance to unleash a burst of wind when attacking", "When HP falls below \\c[29]400\\c[0], the weapon’s full potential is limited!"]}, "26": {"name": ["Starlit Night"], "subtitle": "", "description": ["A staff adorned with gemstones that sparkle like the Milky", "Way. It has the power to create a strong gravitational field,", "pulling intruders into the depths of darkness"], "ability": ["Hold right mouse button to cast Black Hole"]}, "27": {"name": ["Toxic Spore Blade"], "subtitle": "", "description": ["A slim sword dropped by Walking Mushrooms. Its dull blade", "is coated with toxic spores, guaranteeing poison on every strike", "Though low in durability, its toxins make it highly effective"], "ability": ["Attacks have a 50% chance to inflict Poison status"]}, "30": {"name": ["<PERSON><PERSON><PERSON>"], "subtitle": "", "description": ["A beloved Katana that accompanied a girl striving for", "swordsmanship's peak—never leaving her grasp, even in sleep", "Whether sharp or not, it remains a witness to her journey"], "ability": ["Greatly enhances the effects of all learned skills"]}, "35": {"name": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "subtitle": ["\"Huh? I can't see anything! This isn't a sword hilt!?\""], "description": ["Blessed by light, the blade bends light, becoming an invisible sword", "This makes it razor-sharp, and when needed, its radiance unleashes"], "ability": ["Attacks completely ignore the target's defense"]}, "36": {"name": ["Tyrfing"], "subtitle": "", "description": ["A man-made magic sword with a blade forged from mana crystals", "Each slash unleashes a tangible sword wave. For skilled swordsmen,", "it’s a weapon that greatly enhances their power"], "ability": ["Attacks generate sword waves that deal damage"]}, "40": {"name": ["Anti-Bullying Hammer"], "subtitle": ["\"NO BULLYING!!\""], "description": ["A weapon born to punish evil adventurers. ", "Though just a toy hammer, getting bonked still hurts."], "ability": ["Deals recoil damage when used for evil deeds", "The lower your Sin value, the higher its damage"]}, "42": {"name": ["<PERSON><PERSON>s Hand Scythe"], "subtitle": ["\"Remember this, Whirlwind Slash is used like this! \""], "description": ["A short-handled scythe carried by an outsider with a fearsome", "design and a will dwelling within. Wielding it too long", "may cause strange voices to echo in your mind"], "ability": ["automatically homes in on enemies when thrown", "The powerful will grants you mastery over a unique combat skill"]}, "52": {"name": ["Unit 2"], "subtitle": "", "description": ["A communication device bought from a suspicious shop", "Just shout 'Fire!' and you'll receive sniper support from", "somewhere. But, you have to pay phone bill yourself"], "ability": ["Right-click costing 400yen summon sniper support"]}, "53": {"name": ["<PERSON><PERSON> Greatsword"], "subtitle": "", "description": ["A edible weapon crafted with care by <PERSON><PERSON><PERSON> of the NLCH", "While keeping its weapon performance, it's also delicious!"], "ability": ["There’s a chance it might get eaten during an attack…"]}, "54": {"name": ["Carrot Greatsword"], "subtitle": "", "description": ["A top-grade carrot secretly cultivated by the <PERSON>", "Infused with the power of stone, it overgrew into a delicacy ", "so tempting it could make a Maid Bunny cry with desire"], "ability": ["When thrown, it force-feeds the target"]}, "55": {"name": ["Carrot Greatsword"], "subtitle": "", "description": ["A top-grade carrot secretly cultivated by the <PERSON>", "Infused with the power of stone, it overgrew into a delicacy ", "so tempting it could make a Maid Bunny cry with desire"], "ability": ["When thrown, it force-feeds the target"]}, "56": {"name": ["Carrot Greatsword"], "subtitle": "", "description": ["A top-grade carrot secretly cultivated by the <PERSON>", "Infused with the power of stone, it overgrew into a delicacy ", "so tempting it could make a Maid Bunny cry with desire"], "ability": ["When thrown, it force-feeds the target"]}, "57": {"name": ["Carrot Greatsword"], "subtitle": "", "description": ["A top-grade carrot secretly cultivated by the <PERSON>", "Infused with the power of stone, it overgrew into a delicacy ", "so tempting it could make a Maid Bunny cry with desire"], "ability": ["When thrown, it force-feeds the target"]}, "58": {"name": ["Carrot Greatsword"], "subtitle": "", "description": ["A top-grade carrot secretly cultivated by the <PERSON>", "Infused with the power of stone, it overgrew into a delicacy ", "so tempting it could make a Maid Bunny cry with desire"], "ability": ["When thrown, it force-feeds the target"]}, "60": {"name": ["Banana?"], "subtitle": ["\"Is this really a banana...?\""], "description": ["A peeled banana that must be held carefully"], "ability": ["At high speeds, it’s easy to fling the banana away... Eek!?"]}, "61": {"name": ["Giant Snail V<PERSON>uum"], "subtitle": "", "description": ["A vacuum made from a giant snail's shell", "Modified airflow for powerful suction"], "ability": ["Hold mouse to pull objects closer"]}, "62": {"name": ["Ancient Tree Remnant"], "subtitle": "", "description": ["A bizarre branch weapon, obtained from an ancient tree trunk,", "hard yet brimming with vitality and still growing"], "ability": ["Defeating enemies auto-repairs weapon durability", "Excess durability grants +1 ATK per 1% above max"]}, "63": {"name": ["Parasitic Blade"], "subtitle": "", "description": ["A sword consumed by a tree over centuries, where metal and", "life fused into mysterious harmony. Its blade pulses like", "it’s breathing, radiating a strange warmth to the wielder"], "ability": ["Restores \\c[29]${Math.floor(1.2*$gameParty.leader().paramPlus(2))}\\c[0] HP to the wielder upon defeating an enemy"]}, "70": {"name": ["Ancient Glory"], "subtitle": ["\"Unleash the Golden Power ⬆⬆⬇⬇⬅⮕⬅⮕\""], "description": ["A dazzling golden demon sword, one of the treasures of ", "the World Administrator. That man once wielded it, ", "battling in the Abyss for three days and nights without rest"], "ability": ["Unleashes its power when a secret command is entered"]}, "80": {"name": ["Willow Leaf Greatsword"], "subtitle": "", "description": ["\"Pluck leaves, scatter flowers, wound your foes\"", "A very sharp greatsword shaped like willow leaves"], "ability": ["Has a chance to fire willow leaf shurikens when attacking"]}, "81": {"name": ["Infested Blade"], "subtitle": ["\"Ugh, the slime is still wriggling... So gross!!\""], "description": ["A greatsword infested with resilient, life-hungry slime", "Its craving for vitality makes the slime fiercely aggressive"], "ability": ["Deals extra damage to Bleeding enemies", "Drains blood if the target dies while Bleeding"]}, "101": {"name": ["Hardwood Bow"], "subtitle": "", "description": ["A longbow made of Hardwood", "It can shoot arrows, but its performance is very average"], "ability": ["Hold right-click to charge and release a \\c[27]Piercing Arrow\\c[0]", "Piercing Arrow lose damage per target pierced"]}, "102": {"name": ["Hunter's Bow"], "subtitle": "", "description": ["A composite longbow that reduces draw strength", "Less damage, but easier rapid fire"], "ability": ["+Attack Speed, -20% Damage, -20% Durability Loss", "Hold right-click to charge and release a \\c[27]Piercing Arrow\\c[0]"]}, "103": {"name": ["Sunset Cloud"], "subtitle": "", "description": ["A magical bow filled with fire energy", "Not only burns enemies, but also fires explosive arrows"], "ability": ["Hold right-click to charge and release a \\c[27]Explosive Arrow\\c[0]", "Explosive Arrow explode on hit"]}, "104": {"name": ["Snow Watch"], "subtitle": "", "description": ["A magical bow covered in ice. Shoots ice arrows", "and can freeze a wide area with a charged shot"], "ability": ["Hold right-click to charge and release a \\c[27]Icebound Arrow\\c[0]", "Icebound Arrow freeze targets on hit"]}, "105": {"name": ["<PERSON><PERSON><PERSON>"], "subtitle": "", "description": ["A golden bow capable of summoning thunder,", "named for its buzzing sound reminiscent of lightning", "However, be careful—it’s highly conductive"], "ability": ["Hold right-click to charge and release a \\c[27]Thunder Arrow\\c[0]"]}, "106": {"name": ["Hellhound Cursed Bow"], "subtitle": "", "description": ["A product of the Abyss, this oddly shaped bow can", "fire multiple arrows simultaneously. However,", "this eerie magic bow remains sealed, with its true power unreleased"], "ability": ["Hold right-click to charge and release a \\c[27]Piercing Arrow\\c[0]"]}, "107": {"name": ["Blood Hunt"], "subtitle": "", "description": ["A blood-stained longbow.", "Each draw leaves the string wet with its wielder’s own blood,", "until the arrow returns with a sweeter offering"], "ability": ["Hold right-click to charge and release a \\c[27]Blood Piercing Arrow\\c[0]", "Consumes HP to empower shots. Killing arrows drain blood"]}}