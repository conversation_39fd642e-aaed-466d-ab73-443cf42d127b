{"possibleDrops": ["Possible Drops"], "7": {"objectName": ["Bonfire"], "sentouryoku": ["???"], "description": [""], "1": ["Found a bonfire...", "now what should I do?"], "2": ["Take a rest", "Do nothing"]}, "8": {"objectName": ["\\c[116]A↗SA↘BA↗"], "sentouryoku": ["2333"], "description": ["A strange creature that loves eating strawberries", "It has tough skin and absurd defense, but since ", "it’s so focused on strawberries,  ", "it lacks much urge to attack. Because of that,", "passing adventurers often end up feeding it…"], "1": ["ASABA looks at me with anticipation—", "Did it smell something on me...?"], "2": ["ASABA looks at me curiously—", "Did it smell something on me...?"], "3": ["ASABA looks at me with disgust—", "Did I really upset it...?"], "4": ["Should I try feeding ASABA?"], "5": ["Feed it!", "Don’t want to"], "6": ["ASABA finished the food with a blank look—", "Maybe it didn’t like it..."], "7": ["ASABA seems very pleased with the strawberry—", "Granted a strange blessing by ASABA!"], "8": ["ASABA looks at me with disgust—", "Was it that bad...? The food just now..."], "9": [".................?!  \\w[45]", "ASABA is... broken!!"]}, "24": {"objectName": ["\\c[114]<PERSON>"], "sentouryoku": ["?77"], "description": ["A strange girl always dressed as a maid with rabbit ears.", "Possesses the ability to teleport, ", "appearing from all sorts of strange holes.", "Frequently teases adventurers but is mostly harmless."], "1": ["……<PERSON><PERSON>?", "Why are you so ugly usa…?"], "2": ["<PERSON>a…… I smell…", "you have the scent of carrots, usa…"]}, "32": {"objectName": ["Mysterious Gacha"], "sentouryoku": ["9"], "description": ["A gacha machine summoned by a Tarot card.", "It eats up whatever adventurers offer it.", "When satisfied, it happily spins and ", "finally spits out a reward-filled capsule.", "But be careful not to overfeed it—though it's", "a machine, it seems to get food poisoning..."], "1": ["Still in the river!"], "2": ["I found a weird gacha machine...", "Its expression looks like <PERSON><PERSON><PERSON>'s..."], "3": ["Try inserting money", "Try feeding it"], "4": ["But I don’t have enough money to feed it…"], "5": ["Should I really feed it ", "\\ii[\\v[90]]…?"], "6": ["Yeah", "Nope"]}, "41": {"objectName": ["\\c[114]NLCH Delivery!"], "sentouryoku": ["?999"], "description": ["A mobile shop run by two catgirls.", "They show up flashily when adventurers call.", "Goods are mostly Abyss junk they found.", "Watch out, prices can be a total ripoff.", "Look down on them, and get beaten up...", "PS:\\c[2]Blacklisted by the Maid Bunnies ", "\\c[2]for stealing customers."], "1": ["Hi~! Welcome, nya♪ Take your time looking around,", "and feel free to ask me anything, nya!"]}, "43": {"objectName": ["Walking Shroom"], "sentouryoku": ["120"], "description": ["A strange monster often found in jungles, ", "much larger than regular mushrooms.", "It can freely walk on its legs and is aggressive.", "However, it's delicious, making it a favorite ", "target for adventurers and chefs alike."]}, "51": {"objectName": ["\\c[116]Lesser Orc"], "sentouryoku": ["320"], "description": ["A young orc still in its growth phase, ", "already about the size of a human.", "It reacts to living beings and greedily ", "absorbs their life force."]}, "53": {"objectName": ["Zombie"], "sentouryoku": ["210"], "description": ["A corpse reanimated through necromancy.", "It still retains aggression and possesses ", "considerable combat ability."]}, "65": {"objectName": ["\\c[116]<PERSON><PERSON><PERSON> Bird"], "sentouryoku": ["260"], "description": ["A massive avian beast.", "It charges at adventurers with its large body,", "and also uses sonic attacks, so beware."]}, "66": {"objectName": ["\\c[114]Orange Tabby Cat <PERSON>e"], "sentouryoku": ["?10"], "description": ["A slime that mutated after mistakenly ", "eating an adorable kitty,", "and became cute itself as a result.", "It has lost its will to attack,", "but will puff up like a cat when threatened..."], "1": ["The cat slime seems to ", "really like me..."]}, "67": {"objectName": ["\\c[114]Striped Calico Cat Slime"], "sentouryoku": ["?10"], "description": ["A slime that mutated after mistakenly ", "eating an adorable kitty,", "and became cute itself as a result.", "It has lost its will to attack,", "but will puff up like a cat when threatened..."], "1": ["The cat slime seems to ", "really like me..."]}, "68": {"objectName": ["\\c[114]White Tabby Cat <PERSON>e"], "sentouryoku": ["?10"], "description": ["A slime that mutated after mistakenly ", "eating an adorable kitty,", "and became cute itself as a result.", "It has lost its will to attack,", "but will puff up like a cat when threatened..."], "1": ["The cat slime seems to ", "really like me..."]}, "69": {"objectName": ["\\c[114]Siamese Cat Slime"], "sentouryoku": ["?10"], "description": ["A slime that mutated after mistakenly ", "eating an adorable kitty,", "and became cute itself as a result.", "It has lost its will to attack,", "but will puff up like a cat when threatened..."], "1": ["The cat slime seems to ", "really like me..."]}, "70": {"objectName": ["<PERSON>ana <PERSON>"], "sentouryoku": ["?10"], "description": ["A creature…? shaped like a banana.", "A fairy born from the influence of the Abyssal ", "aura, It poses little threat to adventurers.", "However, there are rumors it can ", "easily send people flying…"]}, "71": {"objectName": ["Apple Fairy"], "sentouryoku": ["?10"], "description": ["A creature…? shaped like an apple.", "A fairy born from the influence of the Abyssal ", "aura, It poses little threat to adventurers.", "However, there are rumors it can ", "easily send people flying…"]}, "72": {"objectName": ["\\c[115]Fallen Mage’s Severed Hand"], "sentouryoku": ["480"], "description": ["The severed limb of a dead mage.", "No one knows what its original owner went through,", "But infused with necromancy, this hand stays active.", "Adventurers often see it make obvious gestures ", "when attacking."]}, "73": {"objectName": ["WaterWeird"], "sentouryoku": ["180"], "description": ["An alchemical elemental creature composed of  ", "water that wields elemental magic.", "It has high resistance to physical attacks,", "but cannot survive in environments without water."]}, "77": {"objectName": ["\\c[115]Demon Cat"], "sentouryoku": ["300"], "description": ["A stray cat possessed by demonic power.", "It gained the ability to fly along with ", "dark magical powers. But in the end, ", "it’s still just a cat—", "At most, it just shoots small magic blasts."]}, "78": {"objectName": ["\\c[115]Vengeful Spirit"], "sentouryoku": ["400"], "description": ["A magical creature born from a dead soul’s grudge ", "through necromancy. It endlessly roams graveyards ", "and has high resistance to physical attacks,", "and can spit out deadly ghost fire."]}, "87": {"objectName": ["Herbivore Slime"], "sentouryoku": ["60"], "description": ["A common monster found in the outskirts of ", "the Abyss. It evolved a green color due to its ", "habit of feeding on plants. When approached ", "suddenly, it becomes agitated,and starts ", "spraying corrosive slime."]}, "88": {"objectName": ["<PERSON>"], "sentouryoku": ["999"], "description": ["A slime that accidentally consumed a Jack Bomb.", "Though it didn’t explode right away, ", "it remains extremely unstable, and becomes ", "agitated and self-destructive when provoked."]}, "89": {"objectName": ["\\c[116]<PERSON><PERSON>"], "sentouryoku": ["340"], "description": ["A mutated slime influenced by the environment ", "of a graveyard. It aggressively attacks adventurers ", "like a zombie, becoming more vicious,  ", "and the deathly aura surrounding.", "its body can infect others on contact,", "causing instant death if you're not careful..."]}, "90": {"objectName": ["\\c[116]<PERSON> Slime"], "sentouryoku": ["400"], "description": ["A slime mutated after absorbing magic from ", "an unknown source. It thrives in watery areas ", "and creates thick armor of ice using its magic. ", "It absorbs large amounts of water, ", "rapidly cools it, and blasts it at adventurers."]}, "91": {"objectName": ["\\c[116]Crackling Slime"], "sentouryoku": ["280"], "description": ["A sparkling slime that can discharge electricity ", "all over its body. Even slimes don’t know what it ", "ate to mutate like this. But in its current state of ", "freely discharging electricity, getting close is dangerous..."]}, "92": {"objectName": ["Ore Slime"], "sentouryoku": ["350"], "description": ["A slime that consumed magic-infused crystals, ", "hardening its body. Tough minerals grow on its ", "surface, forming deadly weapons.", "While tougher, it’s still a slime at heart. ", "Also, the ore it drops seems to be quite valuable…"]}, "101": {"objectName": ["\\c[116]Red Cub"], "sentouryoku": ["380"], "description": ["A grotesque creature born from an egg.", "Its malformed, unnatural growth environment ", "has twisted its body into a mass of tangled tissues.", "It possesses a fierce urge to attack, ", "driven by the desire to devour flesh and be reborn."]}, "102": {"objectName": ["Trash Bin"], "sentouryoku": ["???"], "description": ["Even in the deadly Abyss, littering isn’t cool.", "Still, Whatever you toss vanishes by next day...", "Feels like someone’s cleaning up.", "But be careful—don’t toss in flammable stuff.", "Treat it too rough, ", "and you might trigger something nasty."], "1": ["Found a trash can...", "What to do with it?"], "2": ["Search Around", "Drop Weapon", "Drop Gear"], "3": ["It’s just the stuff ", "I just threw away..."], "4": ["(Ugh... My backpack is completely full...", "I’ll have to toss everything at once...)"], "5": ["Throw all away!", "Think again..."], "6": ["...Should I really throw these away?", "\\str[8]"], "7": ["\\fr${itemIcon}\\fr and ${itemLength} equipment in total."]}, "110": {"objectName": ["Scarecrow"], "sentouryoku": ["???"], "description": ["\"I'm just a scarecrow.", "Since I don't have a heart, ", "I can't feel pain.\""]}, "113": {"objectName": ["Horned <PERSON>"], "sentouryoku": ["50"], "description": ["A wild rabbit mutated by magical contamination—", "the horn on its head is the mark of this change.", "However, it’s still in its growth stage ", "and not particularly dangerous.", "Also, its meat remains tender and delicious."]}, "115": {"objectName": ["\\c[115]Burning Slime"], "sentouryoku": ["620"], "description": ["A slime engulfed in blazing flames—", "just getting close to it radiates intense heat.", "Normal attacks are nearly useless against it,", "and adventurers can only be thankful", "that this kind of monster only appears in ", "extreme environments like lava fields."]}, "119": {"objectName": ["\\c[10]Death Devil"], "garble": ["true"], "sentouryoku": ["???"], "description": ["A mere shadow of the ", "Death Devil's will—", "no mortal survives its price.", "Even this echo brings death ", "to all it sees,", "summoner included. "]}, "97": {"objectName": ["\\c[10]Aki"], "sentouryoku": ["\\c[10]It's rude to ask a girl's stats~♥"], "description": ["Looks like a harmless little girl,", "but is actually a free-spirited Archde<PERSON>.", "Friendly toward humans, but loves teasing them ", "and savoring their flustered reactions.", "She runs a shop out of whim, ", "with items and prices changing on a whim as well.", "Basically, she just wants to get her favorite salmon", "from humans however she can..."], "1": ["Oh my~ What a rare guest~ <PERSON><PERSON>~", "I’ve got some good stuff here, take your time♥"], "2": ["Yaho~! What a coincidence~ So nice to see you again~!", "Is it fate~? Or maybe you're just super lucky~?"], "3": ["Hehe, either way~! When luck’s on your side,", "you better watch out—someone might just snatch it away♥"], "4": ["My shop’s a special one, you know~", "I sell things you won’t find anywhere else~"], "5": ["Mhm~ What’s up? Your face is all red~ \\w[45]Hehe~ \\w[45]", "Did I charm you or something? You’re just too cute~"], "6": ["These items are…", "Who even are you…?"], "7": ["Aha♥ Spiii~cy~ Adull~t~ only~", "You’re into this kind of thing, huh?"], "8": ["Of course~ I’ve got them♥", "onii-san... are you interested~?"], "9": ["Oh~ wanna know more about me?", "Fair enough~ I haven’t introduced myself, have I~"], "10": ["Well then, I’m~ The Archdemon from", "the depths of the Abyss! Aki~!"], "11": ["Hey now! Don’t give me that scary face~", "I’m just here to make a little trade today~!"], "12": ["Sniff sniff…", "\\w[90]\\^"], "13": ["Yes! That smell!", "That’s it! Gimme it♥ Pleaaaase~!"], "14": ["Come on~", "You’ll give it to me, won’t you♥"], "15": ["(Ah, so it was the salmon in my bag", "that lured her in...)"], "16": ["Mhm mhm~! This is exactly what I wanted!", "I’ll make sure to thank you properly♥"], "17": ["Please~ Even just a little bit is fine…", "Share it with me? Pretty please♥"], "18": ["(What should I do… Looks like", "this brat really wants some salmon…)"], "19": ["Give nothing"], "20": ["Hoh~    \\w[45]", "Are you trying to defy <PERSON>?"], "21": ["Tsk tsk~ Such a naughty boy~!", "Guess I’ll have to punish you properly♥"], "22": ["\\>\\c[10]《Demonic Contract Ritual: Purehearted Human’s Absolute Curse =", "\\>From Today, Your Life Is Hell Mode》!!     \\w[90]"], "23": ["And with that—", "You’ve been cursed—"], "24": ["So much salmon for me♥ You're too sweet, onii-san~", "Trying to win my heart, huh~?"], "25": ["So cute♥ I just can’t resist~", "Hehe~!"], "26": ["Take this little cutie with you~", "If you want to find me, just send it with a message~♥"], "27": ["You want to buy this item?", "Sure~ I’ll sell it to you~!"], "28": ["Ah! But you know, I don’t really care about money~", "For a demon like me, that stuff’s totally worthless~"], "29": ["Hmm, you’ve got some decent muscles there,", "So I’m sure you can manage~!"], "30": ["...<PERSON><PERSON>, if you want this item~!", "Then trade it for some salmon~!"], "31": ["There’s water all over the place nearby, right?", "Shouldn’t be that hard for you to catch a few fish~?"], "32": ["If you thought that was the end, you’re soooo wrong~♥", "Unless you properly reflect, I’m never letting you off the hook!"], "33": ["Also~ This curse? ", "It’s gonna stick around for a while~♥"], "34": ["Ahaha♥ What a pain, huh~?", "Bet this is super annoying for you, onii-san♥"], "35": ["Aww~ Wanna beg me to lift the curse~?", "What should I do~♥"], "36": ["And what if I say no~? What’ll you do~?", "Aww, don’t make that face~ That’s not gonna work on me~"], "37": ["Now I just wanna bully you more, onii-san♥", "No curse lifting for you~ Ahaha♥"], "38": ["Wow, you’re really desperate, huh? Such a ZAKO♥", "Show me you’re sorry with more sincerity~♥"], "39": ["(If I wanna break the curse…", "I guess I really do have to offer her some fish…)"], "40": ["Waa~! Thanks a bunch", "onii-san~♥"], "41": ["So much salmon for me♥ sluuurp♥", "Ahh~ It’s salmon heaven♥"], "42": ["Alrighty~! The curse is lifted now~!", "Hope we can play again soon, onii-san♥"], "43": ["Well well~ Long time no see, onii-san~", "You showed up at just the right time~♥"], "44": ["Wanna try out a brand-new item?", "It's a Mizumanju I made myself~"], "45": ["The effect, you ask? Hehe~!", "It makes your body extra sensitive♥"], "46": ["And it doesn't just work on humans~", "It’s super effective on monsters too!"], "47": ["So, what do you think? Just ten salmon~?", "Wanna buy it? No? Hurry up and decide♥"], "48": ["I’ll buy it!", "Never mind…"], "49": ["Seriously, <PERSON><PERSON><PERSON> on<PERSON>-san, even your brain turned to <PERSON>AKO~?", "Can’t even cough up a few fish… so useless♥"], "50": ["No money, no salmon—what a weak, pathetic onii-san~", "No wonder you're single and still a virgin~"], "51": ["If you change your mind someday, come back and buy it~", "Not that I care or anything♥"], "52": ["Tch~ So boring, nya~", "Can’t believe you passed on something this amazing♥"], "53": ["Thanks for the purchase, nya~", "Now I can go eat sooo much salmon again~"], "54": ["Tch~ So boring, nya~", "Can’t believe you passed on something this amazing♥"], "55": ["Ah~ I mean, probably nothing will happen♥", "But on<PERSON>-san, you do look suuuper weak~"], "56": ["So I figured I better warn you~", "This, you know, you can use it on yourself too~"], "57": ["And yeah, the effect is making your body suuuper sensitive♥", "So like, definitely don’t try it on yourself, okay~?"], "58": ["Or wait, you actually want to use it?", "onii-san’s totally a hopeless little masochist, huh♥"], "59": ["Kidding~♥ Well then,", "come visit me again next time, okay~♥"], "60": ["Too bad~ It's already sold out~?", "Geez, onii-san, you really are that desperate, huh♥"], "61": ["If you want more, you’ll have to wait till next time~", "Buuut if you bring me salmon, I might make an exception♥"], "62": ["Oh my♥ You want more? So greedy~!", "Buying this much... are you planning something naughty~?"], "63": ["Whoaaa—♥ Such high-quality salmon—!", "you should’ve brought this out way earlier~"], "64": ["If it’s that kind~", "Just give me one and I’ll sell it to you~♥"]}, "98": {"objectName": ["Candy Machine"], "sentouryoku": ["???"], "description": ["A candy machine filled with all sorts of ", "colorful sweets. All of them are handmade konpeitō ", "by the shop owner.", "It seems to be made with some special ingredients, ", "giving off a strange but pleasant aroma…", "But even if you ask out of curiosity what’s inside,", "All you’ll get is a meaningful smile from the girl..."], "1": ["found a candy machine ", "filled with brightly colored sweets—"], "2": ["Let’s see... One spin costs... ", "\\w[60]\\^"], "3": ["\\>Let’s see... One spin costs...", "\\c[6]\\v[86]\\c[0]yen!? \\< That’s expensive!!"], "4": ["<PERSON><PERSON><PERSON> might like these kinds of snacks...", "What should I do? Should I give it a try?"], "5": ["Let’s see... One spin costs... \\w[60]\\c[6]\\v[86]\\c[0]yen", "What should I do? Should I give it a try?"], "6": ["Pay with money", "Trade with fish"], "7": ["But... I don’t have ", "that much money on me..."], "8": ["Yes, yes~♥", "Just offer a bit more salmon then..."]}}