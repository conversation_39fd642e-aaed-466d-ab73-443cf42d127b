{"autoplayBgm": false, "autoplayBgs": false, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "", "pan": 0, "pitch": 100, "volume": 40}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 35, "note": "<深渊>\n<All Allow Region: 248,249,250>", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "Mountains5", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 18, "width": 80, "data": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1557, 2952, 2950, 1590, 1591, 1567, 1590, 1583, 2896, 6072, 6040, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6056, 2860, 1564, 1580, 2370, 2356, 2356, 2372, 1571, 6072, 6040, 6032, 6032, 6032, 6032, 6032, 6032, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1565, 1566, 1566, 1567, 1569, 1570, 1559, 1570, 2896, 1571, 6072, 6060, 6060, 6060, 6040, 6032, 6032, 6036, 6060, 6070, 1564, 1580, 2370, 2337, 2336, 2336, 2360, 1552, 2906, 6048, 6032, 6032, 6036, 6060, 6060, 6060, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1557, 1568, 1620, 1569, 1570, 1577, 1578, 1559, 1570, 2896, 1560, 1563, 2904, 2872, 2864, 6072, 6060, 6060, 6070, 2864, 2888, 1554, 2370, 2337, 2336, 2336, 2340, 2374, 1555, 2896, 6048, 6032, 6036, 6070, 2898, 2884, 2884, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1581, 1576, 1577, 1577, 1578, 1590, 1591, 1567, 2858, 2908, 1568, 1571, 2862, 2904, 2872, 2864, 2864, 2864, 2864, 2868, 2902, 1572, 2336, 2336, 2336, 2336, 2360, 1552, 2898, 2890, 6072, 6060, 6070, 2898, 2865, 2868, 2892, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1565, 1566, 1566, 1566, 1566, 1566, 1567, 1569, 1570, 2833, 2852, 1568, 1560, 1584, 1563, 2904, 2872, 2864, 2864, 2868, 2902, 2858, 1554, 2352, 2336, 2336, 2340, 2374, 1555, 2880, 2870, 2897, 2886, 2884, 2869, 2892, 2902, 2859, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1568, 1592, 1569, 1569, 1569, 1569, 1570, 1569, 1570, 2834, 2854, 0, 1568, 1569, 1560, 1563, 2904, 2892, 2892, 2902, 2850, 2842, 1572, 2352, 2336, 2336, 2360, 1552, 2898, 2869, 2902, 2862, 2904, 2892, 2902, 2851, 2861, 2898, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1568, 1622, 1623, 1593, 1621, 1569, 1570, 1577, 1578, 2860, 1571, 2858, 1568, 1569, 1568, 1560, 1561, 1563, 2859, 2849, 2845, 2854, 1572, 2352, 2336, 2336, 2360, 1571, 2904, 2902, 1564, 1579, 1563, 2859, 2839, 2855, 2898, 2865, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1555, 2906, 1576, 1630, 1631, 1577, 1629, 1577, 1578, 2907, 2886, 2900, 1571, 2848, 0, 0, 1568, 1568, 1569, 1560, 1584, 1584, 1561, 1584, 1562, 2289, 2288, 2288, 2290, 1560, 1561, 1584, 1562, 1569, 1560, 1562, 2848, 2898, 2865, 2864, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1552, 2859, 2839, 2861, 2907, 2903, 4003, 4001, 4001, 4001, 3990, 1655, 2859, 2861, 2904, 2902, 2850, 2819, 2836, 2852, 0, 1568, 1569, 1568, 1569, 1569, 1569, 1569, 1570, 2289, 2288, 2288, 2290, 1568, 1569, 1593, 1570, 1577, 1571, 2859, 2855, 2904, 2892, 2872, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1560, 1563, 2860, 4011, 4001, 4001, 4007, 2859, 2838, 2852, 1662, 3998, 4001, 4001, 4001, 4005, 2856, 2824, 2816, 2822, 2861, 0, 0, 1568, 1569, 1569, 1594, 1569, 1570, 2289, 2288, 2288, 2290, 1568, 1569, 1569, 1552, 2858, 1560, 1584, 1584, 1584, 1563, 2880, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1555, 2858, 1568, 1560, 1584, 2861, 1561, 1585, 1584, 1563, 2856, 2846, 2849, 2853, 2907, 2901, 2862, 4009, 4005, 2856, 2844, 2841, 1571, 2851, 2861, 0, 0, 0, 0, 1577, 1570, 2289, 2384, 2288, 2290, 1568, 1577, 1577, 1552, 2848, 1568, 1569, 1569, 1569, 1552, 2904, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1555, 2859, 2849, 2838, 2836, 2842, 1576, 1568, 1569, 1569, 1569, 1569, 1569, 1560, 1561, 1584, 1563, 2857, 2861, 2905, 2886, 2900, 3985, 3988, 4004, 2833, 2837, 2855, 2994, 2980, 2996, 2862, 1572, 1554, 1570, 2289, 2288, 2288, 2290, 1568, 1552, 2858, 1552, 2860, 1576, 1577, 1577, 1577, 1571, 2859, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1555, 2862, 2946, 2948, 2856, 2824, 2818, 2852, 1576, 1577, 1577, 1577, 1577, 1577, 1568, 1593, 1569, 1560, 1584, 1561, 1563, 2904, 2902, 4008, 3996, 3993, 2832, 2840, 1571, 3000, 2968, 2962, 2996, 1572, 1572, 1570, 2289, 2288, 2384, 2290, 1568, 1571, 2848, 1552, 2907, 2886, 2884, 2884, 2900, 1560, 1561, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1552, 2862, 2946, 2913, 2918, 2957, 2834, 2844, 2846, 2849, 2861, 2855, 2850, 2836, 2852, 1576, 1577, 1577, 1568, 1569, 1569, 1560, 1563, 2850, 2836, 2852, 4000, 2856, 2826, 2837, 2861, 2978, 2988, 2998, 1572, 1554, 1570, 2289, 2288, 2288, 2290, 1568, 1552, 2848, 1560, 1563, 2904, 2892, 2892, 2902, 1568, 1569, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1560, 1563, 2952, 2940, 2950, 2851, 2855, 4003, 4001, 4001, 4001, 4013, 2832, 2816, 2818, 2836, 2836, 2852, 1576, 1577, 1577, 1568, 1571, 2832, 2816, 2840, 3985, 4004, 2856, 2841, 1571, 3004, 2858, 0, 2862, 1572, 1570, 2289, 2288, 2288, 2290, 1568, 1552, 2848, 1573, 1560, 1584, 1584, 1563, 2862, 1576, 1577, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1555, 2899, 2909, 2858, 1568, 1560, 1561, 1584, 1563, 2860, 4011, 4007, 2859, 2849, 2849, 2849, 2845, 2844, 2824, 2816, 2820, 2846, 2849, 2849, 2861, 1576, 1552, 2834, 2844, 2841, 1662, 3978, 1655, 2857, 2838, 2836, 2844, 1572, 2906, 1572, 1570, 2289, 2480, 2480, 2290, 1568, 1571, 2833, 2852, 1573, 1574, 1574, 1560, 1561, 1584, 1584, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1552, 2898, 2885, 2903, 2859, 2855, 1576, 1568, 1593, 1569, 1560, 1561, 2861, 1561, 1579, 1584, 1561, 1584, 1584, 1563, 2856, 2844, 2854, 1564, 1585, 1584, 1561, 1563, 1552, 2848, 3626, 2857, 2861, 1662, 3978, 4004, 2856, 2824, 2822, 2861, 2896, 1554, 2466, 2433, 2432, 2432, 2434, 2468, 1571, 2832, 2822, 2849, 2849, 2861, 1573, 1574, 1574, 1574, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1571, 2904, 2902, 4011, 4001, 4001, 4013, 1576, 1577, 1577, 1568, 1569, 1569, 1569, 1593, 1569, 1653, 1569, 1569, 1560, 1584, 1584, 1584, 1562, 1569, 1606, 1607, 1560, 1571, 2860, 3625, 3606, 3620, 2862, 1662, 3998, 4005, 2856, 2841, 2907, 2903, 1572, 2448, 2432, 2432, 2432, 2432, 2456, 1552, 2834, 2854, 4002, 3988, 4004, 2859, 2849, 2838, 2836, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1555, 2946, 2948, 1560, 1561, 1584, 2861, 1584, 1585, 1563, 4014, 2859, 2861, 1576, 1577, 1577, 1577, 1577, 1577, 1577, 1577, 1569, 1568, 1569, 1569, 1593, 1570, 1577, 1614, 1615, 1568, 1560, 1584, 1563, 3624, 3594, 3620, 2859, 2853, 4009, 4005, 2857, 2849, 2861, 1554, 2448, 2432, 2432, 2432, 2432, 2456, 1552, 2860, 1654, 3973, 3996, 3998, 4001, 4013, 2834, 2844, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1571, 2955, 2945, 2945, 2941, 2937, 1568, 1569, 1569, 1569, 1569, 1569, 1560, 1563, 4011, 4005, 2859, 2861, 2855, 2851, 2861, 2899, 2909, 1572, 1569, 1568, 1577, 1577, 1577, 1578, 2862, 2898, 2900, 1576, 1568, 1569, 1560, 1563, 3624, 3594, 3620, 2857, 2861, 4009, 3990, 3988, 1655, 1554, 2448, 2432, 2432, 2432, 2432, 2456, 1552, 4002, 3973, 4006, 2898, 2900, 2851, 2849, 2843, 4011, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1552, 2859, 2838, 2837, 2849, 2849, 2853, 2956, 1576, 1577, 1569, 1569, 1569, 1569, 1568, 1552, 2862, 4009, 4001, 4001, 4013, 2848, 2907, 2903, 2862, 1554, 1577, 1568, 1571, 2850, 2836, 2852, 2898, 2865, 2866, 2900, 1568, 1569, 1568, 1552, 2862, 3624, 3614, 3629, 2954, 2862, 1662, 3996, 1663, 1572, 2448, 2432, 2432, 2432, 2432, 2456, 1571, 1662, 1663, 2898, 2869, 2902, 2860, 1581, 2857, 2838, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1560, 1563, 2834, 2854, 4003, 4013, 2857, 2849, 2849, 2853, 1577, 1577, 1577, 1577, 1568, 1560, 1561, 1563, 2859, 2849, 2849, 2855, 1564, 1585, 1584, 1562, 2862, 1576, 1552, 2856, 2824, 2840, 2880, 2868, 2892, 2902, 1569, 1569, 1569, 1560, 1584, 1563, 2858, 2946, 2915, 2948, 2859, 2849, 2853, 1554, 2448, 2432, 2432, 2432, 2432, 2456, 1552, 2858, 2898, 2869, 2902, 2862, 1581, 2862, 1581, 2832, 2861, 1559, 0, 0, 0, 0, 0, 1555, 2994, 2996, 2862, 2898, 2900, 2862, 1568, 1552, 2860, 4011, 4007, 2862, 4011, 4001, 4005, 2857, 2861, 2855, 2850, 2852, 1573, 1569, 1569, 1560, 1584, 1584, 1584, 1561, 1562, 1569, 1569, 1570, 2906, 2862, 1560, 1563, 2856, 2854, 2904, 2902, 2859, 2861, 1576, 1574, 1568, 1568, 1569, 1552, 2860, 2952, 2940, 2922, 2932, 2948, 2848, 1554, 2448, 2432, 2432, 2432, 2432, 2456, 1552, 2848, 2904, 2902, 2858, 1557, 2862, 1581, 2859, 2845, 1590, 1583, 0, 1555, 2858, 2994, 2980, 2980, 2961, 2962, 2996, 2904, 2874, 2900, 1576, 1560, 1584, 2861, 1584, 1579, 1563, 2862, 4009, 4001, 4001, 4013, 2832, 2818, 2852, 1569, 1569, 1568, 1569, 1569, 1569, 1569, 1570, 1577, 1577, 1578, 2881, 2900, 1573, 1560, 1584, 1561, 1584, 1584, 1584, 1563, 2859, 2853, 1576, 1568, 1593, 1560, 1584, 1563, 2858, 2952, 2940, 2950, 2848, 1572, 2448, 2432, 2432, 2432, 2432, 2456, 1571, 2860, 3290, 2859, 2855, 1557, 1557, 2862, 6018, 6004, 1559, 1570, 2850, 2836, 2842, 3000, 2988, 2988, 2988, 2988, 2998, 2858, 2904, 2902, 2858, 1568, 1569, 1569, 1569, 1569, 1560, 1563, 2859, 2849, 2849, 2838, 2817, 2816, 2840, 1573, 1577, 1568, 1594, 1569, 1577, 1577, 1578, 2862, 2898, 2884, 2865, 2866, 2900, 1573, 1574, 1574, 1574, 1612, 1574, 1560, 1563, 2857, 2853, 1568, 1569, 1568, 1571, 1571, 2833, 2837, 2849, 2849, 2855, 1554, 2448, 2432, 2432, 2432, 2432, 2456, 6018, 6020, 3280, 6018, 6004, 6004, 6004, 6004, 5985, 5984, 1559, 1578, 2832, 2816, 2818, 2836, 2836, 2837, 2849, 2849, 2849, 2847, 2849, 2849, 2843, 1576, 1577, 1577, 1577, 1577, 1568, 1560, 1561, 1561, 1563, 2856, 2844, 2844, 2846, 2849, 2861, 1576, 1577, 1569, 1552, 2850, 2852, 2898, 2865, 2864, 2868, 2892, 2902, 1564, 1585, 1584, 1561, 1584, 1563, 1573, 1560, 1563, 2860, 1576, 1577, 1568, 1571, 1552, 2834, 2854, 1590, 1585, 1584, 1562, 2448, 2432, 2432, 2432, 2432, 2456, 6000, 6008, 3280, 6000, 5984, 5984, 5984, 5984, 5984, 5984, 1559, 2850, 2817, 2816, 2816, 2816, 2820, 2854, 4003, 4001, 3990, 3988, 3988, 4004, 2857, 2849, 2861, 2855, 2859, 2861, 1576, 1568, 1593, 1569, 1560, 1584, 1584, 1561, 1584, 1561, 1584, 1584, 1562, 1577, 1552, 2856, 2854, 2880, 2864, 2864, 2888, 2862, 1564, 1580, 1569, 1569, 1569, 1569, 1560, 1563, 1573, 1560, 1563, 2850, 2852, 1568, 1571, 1552, 2860, 1564, 1580, 1569, 1569, 1570, 2448, 2432, 2432, 2432, 2432, 2456, 6024, 6022, 3280, 6000, 5984, 5984, 5984, 5984, 5984, 5984, 1559, 2832, 2820, 2844, 2844, 2824, 2840, 4003, 4007, 2858, 4008, 3976, 3972, 3998, 4001, 4001, 4001, 4013, 2898, 2900, 2858, 1576, 1577, 1577, 1568, 1569, 1569, 1569, 1569, 1569, 1569, 1569, 1570, 6018, 6004, 6004, 6020, 2904, 2892, 2892, 2902, 6018, 6004, 6004, 6004, 6004, 6004, 6020, 1568, 1560, 1563, 1573, 1552, 2856, 2854, 1576, 1571, 1560, 1561, 1562, 1570, 1577, 1577, 1570, 2448, 2432, 2432, 2432, 2432, 2456, 1571, 2858, 3280, 6024, 6012, 6012, 6012, 5992, 5984, 5984, 1582, 2856, 2841, 3618, 3620, 2856, 2841, 4000, 2850, 2819, 2852, 4008, 4006, 2851, 2849, 2861, 2898, 2884, 2865, 2888, 2835, 2849, 2849, 2861, 1576, 1577, 1577, 6018, 6004, 6004, 6004, 6004, 6004, 5985, 5984, 5984, 5986, 6004, 6004, 6004, 6004, 5985, 5984, 5984, 5984, 5984, 5984, 5986, 6004, 6004, 6020, 2862, 1571, 2946, 2948, 2858, 1560, 1563, 1577, 1572, 1578, 1564, 1580, 1570, 2448, 2432, 2432, 2432, 2436, 2470, 1555, 2860, 3280, 2928, 2912, 2912, 2912, 6024, 5992, 5984, 2852, 1559, 2848, 3600, 3586, 3620, 2860, 4000, 2832, 2816, 2818, 2836, 2836, 2842, 2898, 2884, 2865, 2864, 2868, 2902, 2860, 6018, 6004, 6004, 6004, 6004, 6004, 5985, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5986, 6004, 6004, 6020, 2936, 2860, 1568, 1552, 2858, 1572, 1584, 1562, 1570, 1661, 2448, 2432, 2432, 2432, 2456, 1571, 2858, 3283, 3287, 2952, 2940, 2940, 2920, 2912, 6000, 5984, 2840, 1559, 2860, 3624, 3612, 3622, 4003, 4007, 2834, 2844, 2844, 2844, 2844, 2854, 2904, 2892, 2892, 2892, 2902, 2862, 6018, 5985, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 6008, 2914, 2948, 1568, 1571, 2848, 1572, 1569, 1570, 1661, 2466, 2433, 2432, 2432, 2432, 2456, 1571, 2848, 3292, 2899, 2897, 2897, 2909, 2928, 2912, 6000, 5984, 2840, 1559, 1561, 1563, 2859, 2861, 4012, 2859, 2855, 1564, 1585, 1584, 1561, 1584, 1584, 1584, 1561, 1561, 6018, 6004, 5985, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5986, 6020, 2936, 1576, 1571, 2848, 1554, 1660, 1661, 2466, 2433, 2432, 2432, 2432, 2436, 2470, 1552, 2848, 2907, 2903, 2946, 2934, 2934, 2921, 2920, 6000, 5984, 2840, 1559, 1574, 1560, 1561, 1584, 2861, 1584, 1585, 1562, 1569, 1569, 1592, 1569, 1569, 6018, 6004, 6004, 5985, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5986, 6004, 6020, 1571, 2848, 1572, 2379, 2381, 2448, 2432, 2432, 2432, 2432, 2456, 1557, 1552, 2860, 2946, 2934, 2921, 2920, 6018, 6004, 6004, 5985, 5984, 2840, 1559, 2858, 1573, 1574, 1569, 1569, 1569, 1569, 1570, 1577, 1577, 1577, 1577, 6018, 5985, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 6008, 1571, 2848, 1554, 2466, 2452, 2433, 2432, 2432, 2432, 2432, 2456, 1557, 6018, 6004, 6004, 6004, 6004, 6004, 5985, 5984, 5984, 5984, 5984, 2854, 1559, 2833, 2836, 2852, 1577, 1577, 1577, 1577, 1578, 2858, 2946, 2932, 6027, 6013, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6014, 6029, 2860, 1554, 2448, 2432, 2432, 2432, 2432, 2432, 2432, 2456, 1557, 6000, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3186, 3172, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3186, 3153, 3152, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3168, 3152, 3152, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3192, 3180, 3160, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3907, 3917, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3192, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3916, 0, 0, 0, 0, 0, 0, 3138, 3140, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3858, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3147, 3133, 3134, 3141, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3840, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3042, 3028, 3044, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3145, 3149, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3906, 3892, 3908, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3864, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3048, 3016, 3010, 3044, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3912, 3880, 3874, 3908, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3146, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3048, 3036, 3046, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3912, 3900, 3910, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3136, 0, 0, 3918, 0, 3906, 3908, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3136, 0, 0, 0, 0, 3912, 3910, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3121, 3140, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3120, 3110, 3149, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3150, 0, 0, 0, 0, 0, 0, 0, 0, 3144, 3142, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3906, 3908, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3915, 3905, 3905, 3901, 3897, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3916, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3138, 3124, 3124, 3140, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3138, 3140, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3120, 3108, 3132, 3142, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3122, 3142, 0, 0, 3186, 3172, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3144, 3142, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3138, 3130, 0, 0, 3187, 3181, 3180, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3138, 3140, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3138, 3140, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3144, 3142, 0, 0, 3196, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3139, 3133, 3142, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3144, 3114, 3140, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3148, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3915, 3905, 3909, 3144, 3142, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3195, 3174, 3172, 3188, 3913, 3917, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3192, 3180, 3182, 3197, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3186, 3172, 3188, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3426, 3428, 0, 0, 0, 0, 0, 0, 0, 3186, 3157, 3180, 3190, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3408, 3398, 3437, 0, 0, 0, 0, 0, 0, 3192, 3190, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3435, 3421, 3430, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 294, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 173, 0, 0, 0, 0, 0, 294, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 256, 257, 294, 0, 0, 0, 0, 256, 272, 0, 173, 181, 0, 0, 0, 0, 0, 302, 0, 0, 0, 0, 256, 256, 257, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 0, 0, 105, 0, 0, 0, 272, 273, 18, 0, 256, 256, 273, 272, 0, 0, 181, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 256, 272, 272, 273, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 315, 0, 272, 272, 344, 317, 0, 0, 0, 0, 0, 0, 0, 0, 0, 307, 257, 256, 273, 272, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 83, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 315, 273, 272, 347, 317, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 51, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 104, 81, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 104, 81, 81, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 84, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 84, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 49, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 173, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 181, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 52, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 59, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 59, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 89, 89, 84, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 59, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 568, 569, 569, 569, 569, 569, 569, 570, 0, 0, 0, 0, 0, 0, 0, 0, 0, 54, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 59, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 59, 0, 0, 0, 0, 0, 0, 579, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 137, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 293, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 138, 145, 0, 0, 0, 0, 0, 0, 0, 0, 256, 256, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 56, 0, 0, 146, 12, 0, 0, 0, 0, 0, 0, 0, 48, 272, 272, 0, 274, 257, 257, 256, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 273, 273, 274, 274, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 173, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 293, 0, 0, 0, 0, 0, 0, 173, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 172, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 180, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 96, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12, 0, 0, 59, 0, 321, 363, 364, 0, 0, 0, 0, 0, 0, 0, 0, 0, 360, 271, 277, 0, 0, 0, 0, 321, 363, 364, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 53, 0, 0, 0, 0, 0, 0, 0, 0, 341, 332, 372, 0, 0, 374, 364, 0, 0, 0, 0, 0, 295, 277, 59, 67, 0, 0, 0, 307, 332, 374, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 0, 67, 326, 308, 337, 339, 332, 372, 0, 0, 0, 295, 336, 327, 59, 67, 0, 0, 0, 48, 315, 299, 0, 0, 0, 0, 295, 336, 308, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 53, 0, 0, 0, 0, 0, 18, 0, 0, 59, 37, 0, 316, 345, 347, 326, 337, 338, 339, 336, 327, 344, 317, 59, 0, 0, 0, 0, 0, 3, 299, 0, 0, 0, 295, 327, 344, 316, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 59, 45, 0, 0, 0, 0, 3, 345, 346, 347, 13, 14, 2, 0, 0, 0, 0, 0, 48, 1, 38, 39, 337, 338, 339, 327, 317, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 175, 0, 0, 0, 0, 37, 84, 104, 0, 0, 0, 0, 18, 0, 1, 0, 38, 39, 0, 0, 0, 0, 0, 38, 39, 46, 47, 345, 346, 12, 280, 281, 282, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 48, 52, 0, 0, 0, 0, 0, 159, 0, 0, 0, 45, 38, 39, 0, 0, 0, 0, 0, 0, 0, 20, 46, 47, 0, 0, 0, 0, 0, 46, 47, 20, 0, 0, 48, 50, 288, 289, 290, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 48, 49, 52, 49, 38, 39, 0, 0, 0, 0, 0, 167, 0, 19, 0, 0, 46, 47, 104, 81, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 296, 297, 298, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 46, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 88, 89, 89, 84, 104, 0, 0, 0, 182, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 77, 79, 304, 305, 306, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 48, 49, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 88, 84, 38, 39, 0, 0, 174, 0, 0, 0, 0, 0, 0, 0, 0, 158, 0, 58, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 48, 49, 49, 49, 38, 39, 0, 0, 108, 109, 110, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 46, 47, 5, 7, 7, 82, 170, 0, 0, 0, 0, 0, 0, 166, 0, 0, 18, 0, 159, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 48, 0, 0, 8, 0, 46, 47, 0, 0, 116, 117, 118, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 88, 5, 0, 0, 0, 6, 0, 0, 68, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 167, 155, 37, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 48, 0, 2, 0, 0, 0, 0, 0, 0, 0, 116, 117, 118, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 33, 34, 0, 5, 163, 45, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 38, 39, 20, 4, 0, 0, 0, 0, 0, 124, 125, 126, 64, 18, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 88, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 48, 52, 49, 156, 0, 46, 47, 4, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 155, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 85, 87, 67, 59, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 48, 49, 38, 39, 0, 164, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 163, 0, 0, 0, 0, 0, 0, 0, 0, 0, 88, 84, 0, 0, 0, 0, 59, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 46, 47, 0, 0, 0, 0, 0, 0, 0, 108, 109, 110, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 88, 89, 90, 67, 0, 59, 0, 0, 0, 0, 171, 0, 0, 0, 0, 21, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 48, 49, 49, 0, 10, 0, 0, 0, 0, 0, 0, 0, 152, 0, 116, 117, 118, 0, 0, 0, 0, 0, 0, 0, 0, 169, 0, 59, 0, 0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 59, 0, 0, 0, 0, 179, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 48, 49, 6, 6, 0, 0, 0, 0, 108, 109, 110, 0, 0, 0, 0, 160, 0, 116, 117, 118, 0, 38, 39, 0, 0, 0, 0, 182, 177, 0, 59, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 552, 577, 553, 553, 553, 553, 553, 554, 555, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 48, 49, 0, 0, 0, 0, 0, 0, 0, 175, 116, 117, 118, 0, 0, 0, 0, 0, 1, 124, 125, 126, 64, 46, 47, 0, 68, 0, 0, 0, 185, 0, 67, 0, 0, 0, 0, 0, 0, 38, 39, 0, 0, 0, 0, 0, 0, 0, 0, 560, 561, 561, 561, 561, 561, 561, 562, 563, 0, 0, 0, 0, 2, 53, 54, 54, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 88, 89, 94, 95, 5, 0, 0, 0, 116, 117, 118, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 157, 0, 0, 18, 0, 0, 0, 0, 0, 0, 0, 0, 0, 46, 47, 0, 0, 0, 0, 0, 0, 0, 0, 576, 577, 577, 577, 577, 577, 577, 578, 0, 0, 27, 0, 0, 53, 0, 53, 54, 282, 55, 0, 0, 0, 0, 0, 48, 49, 52, 49, 52, 49, 49, 0, 0, 2, 0, 102, 103, 88, 89, 89, 89, 116, 117, 118, 59, 0, 0, 0, 0, 0, 8, 1, 0, 16, 0, 169, 0, 165, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 584, 585, 585, 585, 585, 585, 585, 586, 587, 0, 0, 0, 53, 10, 53, 0, 0, 290, 0, 0, 48, 49, 49, 49, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 124, 125, 126, 64, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 177, 0, 0, 6, 0, 0, 0, 0, 0, 0, 9, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 59, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 53, 259, 260, 260, 298, 0, 62, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 108, 109, 110, 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 159, 0, 0, 185, 0, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 0, 59, 0, 0, 0, 0, 283, 260, 320, 260, 320, 260, 260, 260, 286, 0, 0, 306, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 116, 117, 118, 0, 0, 0, 0, 2, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 167, 0, 67, 0, 3, 0, 0, 38, 39, 0, 174, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 78, 0, 0, 0, 0, 1, 0, 0, 59, 0, 0, 0, 0, 291, 0, 0, 361, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 116, 117, 118, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 46, 47, 0, 0, 0, 0, 0, 0, 0, 0, 1, 38, 39, 0, 0, 0, 0, 0, 85, 129, 129, 0, 59, 0, 0, 0, 0, 299, 0, 0, 361, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 124, 125, 126, 64, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 259, 260, 260, 320, 284, 285, 0, 0, 259, 260, 260, 260, 260, 320, 260, 260, 285, 18, 0, 0, 0, 46, 47, 0, 0, 0, 12, 0, 130, 59, 137, 0, 59, 0, 0, 0, 0, 299, 0, 0, 361, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 259, 260, 260, 260, 260, 260, 286, 0, 0, 0, 0, 287, 260, 260, 286, 0, 0, 0, 0, 0, 0, 0, 287, 320, 260, 285, 0, 0, 2, 0, 0, 0, 0, 0, 59, 59, 145, 0, 59, 0, 0, 0, 0, 307, 337, 339, 294, 0, 0, 0, 0, 0, 0, 0, 0, 55, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 38, 39, 0, 0, 0, 0, 259, 260, 260, 320, 260, 260, 262, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 287, 284, 284, 285, 0, 0, 153, 154, 58, 59, 67, 0, 68, 59, 0, 0, 0, 0, 315, 345, 347, 326, 337, 338, 339, 294, 0, 0, 0, 0, 0, 51, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 46, 47, 0, 0, 0, 259, 286, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 293, 2, 0, 161, 162, 0, 0, 0, 0, 59, 59, 0, 0, 0, 0, 0, 3, 0, 315, 345, 346, 347, 326, 294, 0, 0, 281, 282, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 259, 260, 286, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 311, 285, 0, 0, 9, 0, 0, 0, 59, 67, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 219, 318, 0, 0, 289, 290, 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 283, 320, 284, 286, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 287, 284, 285, 0, 0, 0, 0, 67, 181, 0, 0, 0, 0, 53, 0, 1, 0, 0, 0, 259, 260, 320, 286, 0, 0, 297, 298, 0, 0, 0, 108, 109, 110, 0, 0, 0, 0, 0, 283, 286, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 293, 1, 0, 59, 173, 181, 0, 0, 0, 0, 0, 283, 260, 284, 320, 320, 260, 286, 0, 0, 0, 0, 0, 305, 306, 0, 0, 182, 116, 117, 118, 0, 0, 0, 0, 283, 286, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 311, 285, 0, 59, 181, 0, 0, 0, 0, 0, 0, 291, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 83, 89, 89, 116, 117, 118, 0, 0, 67, 0, 321, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 293, 0, 59, 0, 0, 0, 0, 0, 0, 0, 321, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 12, 12, 12, 12, 12, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 15, 15, 15, 15, 15, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 15, 15, 15, 15, 15, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 5, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 249, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 249, 0, 0, 0, 0, 255, 0, 0, 0, 0, 0, 255, 254, 254, 255, 255, 255, 255, 255, 254, 254, 255, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 254, 254, 254, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 249, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 254, 254, 0, 255, 254, 254, 254, 254, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 254, 254, 254, 254, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 254, 254, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 254, 254, 254, 254, 0, 0, 0, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 254, 254, 0, 0, 0, 0, 0, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 249, 249, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 249, 249, 249, 249, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 249, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 249, 249, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 249, 249, 249, 249, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 249, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 0, 0, 0, 0, 118, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 248, 0, 0, 0, 0, 248, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 249, 0, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 248, 0, 0, 0, 0, 248, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 249, 0, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 248, 0, 0, 0, 0, 248, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 249, 249, 249, 0, 0, 0, 5, 5, 5, 5, 0, 0, 15, 15, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 248, 0, 0, 0, 0, 248, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 249, 249, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 249, 0, 0, 0, 248, 0, 0, 0, 0, 248, 0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 249, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 249, 0, 0, 0, 248, 248, 248, 248, 248, 248, 0, 5, 5, 5, 0, 5, 0, 0, 0, 0, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 249, 249, 249, 0, 0, 0, 0, 15, 15, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 249, 0, 5, 0, 248, 248, 248, 248, 248, 248, 0, 5, 5, 0, 15, 15, 0, 0, 0, 0, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 249, 249, 249, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 7, 7, 7, 7, 7, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 249, 249, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 249, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 0, 0, 0, 0, 249, 249, 250, 250, 250, 250, 250, 249, 249, 249, 249, 249, 249, 249, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 0, 0, 0, 0, 7, 7, 7, 7, 7, 7, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 254, 0, 250, 249, 249, 249, 249, 0, 5, 5, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 5, 5, 0, 5, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 0, 0, 248, 248, 248, 248, 248, 248, 0, 5, 0, 5, 5, 0, 0, 0, 0, 0, 254, 0, 249, 0, 0, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 0, 248, 248, 248, 248, 248, 248, 0, 0, 0, 5, 5, 0, 0, 0, 255, 255, 254, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 248, 248, 248, 248, 248, 248, 255, 255, 0, 255, 255, 255, 255, 255, 255, 255, 254, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 118, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 248, 248, 248, 248, 248, 248, 255, 255, 0, 255, 5, 5, 5, 255, 255, 255, 254, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 7, 248, 248, 248, 248, 248, 248, 255, 255, 0, 255, 5, 5, 5, 255, 255, 255, 254, 0, 5, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 7, 248, 248, 248, 248, 248, 248, 254, 254, 0, 255, 5, 5, 5, 255, 255, 255, 254, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 0, 5, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 248, 248, 248, 248, 248, 0, 254, 254, 0, 5, 254, 254, 254, 255, 255, 255, 254, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 5, 5, 5, 5, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 5, 0, 0, 0, 0, 0, 0, 7, 7, 248, 248, 248, 248, 248, 0, 5, 15, 0, 5, 254, 254, 254, 5, 255, 255, 254, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 5, 5, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 7, 7, 7, 7, 248, 248, 248, 248, 248, 0, 0, 0, 0, 0, 0, 0, 0, 5, 255, 255, 254, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 7, 7, 7, 248, 248, 248, 248, 248, 254, 0, 0, 5, 0, 5, 5, 5, 5, 5, 255, 255, 254, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 7, 7, 248, 248, 248, 248, 248, 248, 255, 0, 0, 5, 5, 5, 5, 240, 255, 255, 255, 255, 254, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 248, 248, 248, 248, 248, 248, 248, 248, 255, 240, 240, 240, 240, 240, 240, 240, 255, 255, 255, 255, 254, 254, 254, 254, 254, 0, 0, 0, 254, 254, 254, 254, 254, 254, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 240, 240, 240, 240, 240, 240, 240, 255, 255, 255, 255], "events": [null, {"id": 1, "name": "EV001", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<width: 5>"]}, {"code": 408, "indent": 0, "parameters": ["<height: 0.5>"]}, {"code": 408, "indent": 0, "parameters": ["<offsetX: 0>"]}, {"code": 408, "indent": 0, "parameters": ["<offsetY: 0>"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Move", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 121, "indent": 0, "parameters": [40, 40, 0]}, {"code": 201, "indent": 0, "parameters": [0, 26, 11, 1, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 2, "walkAnime": true}], "x": 5, "y": 34}, {"id": 2, "name": "EV002", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 8, "pattern": 1, "characterIndex": 5}, "list": [{"code": 108, "indent": 0, "parameters": ["<width: 7>"]}, {"code": 408, "indent": 0, "parameters": ["<height: 0.5>"]}, {"code": 408, "indent": 0, "parameters": ["<offsetX: 0>"]}, {"code": 408, "indent": 0, "parameters": ["<offsetY: 0>"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Move", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 121, "indent": 0, "parameters": [40, 40, 0]}, {"code": 201, "indent": 0, "parameters": [0, 48, 11, 1, 2, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": false}], "x": 69, "y": 34}, {"id": 3, "name": "采集点", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["particle set mysterious_torch_c-EID this mysterious_torch_c def below"]}, {"code": 250, "indent": 0, "parameters": [{"name": "キラーン（ひらめき・アイテム発見・獲得）", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 126, "indent": 0, "parameters": [8, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["dingk.Loot.directlyAcquireDrops(21,false)"]}, {"code": 356, "indent": 0, "parameters": ["particle clear mysterious_torch_c-EID"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 76, "y": 30}, {"id": 4, "name": "EV004", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!fsm_Waterobject03", "direction": 4, "pattern": 1, "characterIndex": 1}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": false}], "x": 65, "y": 7}, {"id": 5, "name": "EV005", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!fsm_Waterobject03", "direction": 4, "pattern": 1, "characterIndex": 1}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": false}], "x": 66, "y": 7}, {"id": 6, "name": "EV006", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!fsm_Waterobject03", "direction": 4, "pattern": 1, "characterIndex": 1}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": false}], "x": 67, "y": 7}, {"id": 7, "name": "EV007", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!fsm_Waterobject03", "direction": 4, "pattern": 1, "characterIndex": 1}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": false}], "x": 68, "y": 7}, {"id": 8, "name": "EV008", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!fsm_Waterobject03", "direction": 4, "pattern": 1, "characterIndex": 5}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": false}], "x": 65, "y": 16}, {"id": 9, "name": "EV009", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!fsm_Waterobject03", "direction": 4, "pattern": 1, "characterIndex": 5}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件的声音 : 开启声音距离化"]}, {"code": 250, "indent": 0, "parameters": [{"name": "滝音～中くらいの滝　北海道", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["this.wait(7318)"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 4, "walkAnime": false}], "x": 66, "y": 16}, {"id": 10, "name": "EV010", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!fsm_Waterobject03", "direction": 4, "pattern": 1, "characterIndex": 5}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": false}], "x": 67, "y": 16}, {"id": 11, "name": "EV011", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!fsm_Waterobject03", "direction": 4, "pattern": 1, "characterIndex": 5}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": false}], "x": 68, "y": 16}, {"id": 12, "name": "EV012", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 8, "pattern": 1, "characterIndex": 5}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Move", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 121, "indent": 0, "parameters": [40, 40, 0]}, {"code": 201, "indent": 0, "parameters": [0, 10, 0, 9, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": false}], "x": 79, "y": 18}, {"id": 13, "name": "EV013", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 8, "pattern": 1, "characterIndex": 5}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Move", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 121, "indent": 0, "parameters": [40, 40, 0]}, {"code": 201, "indent": 0, "parameters": [0, 10, 0, 8, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": false}], "x": 79, "y": 17}, {"id": 14, "name": "鱼影", "note": "<Force Restriction: 0,5,255><Bypass Restriction: 248>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "AIN_WaterFish1", "direction": 2, "pattern": 1, "characterIndex": 2}, "list": [{"code": 108, "indent": 0, "parameters": ["=>持续动作 : 空中飘浮 : 飘浮高度[4] : 周期[180] : 幅度[1]"]}, {"code": 355, "indent": 0, "parameters": ["let Hp = 15;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSelfVariables.set(this, 'HP', Hp);"]}, {"code": 655, "indent": 0, "parameters": ["$gameSelfVariables.set(this, 'MHP', Hp);"]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 1, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 2, "moveType": 1, "priorityType": 0, "stepAnime": true, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "AIN_WaterFish1", "direction": 2, "pattern": 1, "characterIndex": 2}, "list": [{"code": 108, "indent": 0, "parameters": ["=>持续动作 : 空中飘浮 : 飘浮高度[4] : 周期[180] : 幅度[1]"]}, {"code": 108, "indent": 0, "parameters": ["<BoxType:\"C[8]\">"]}, {"code": 408, "indent": 0, "parameters": ["<BoxOffset:0,0>"]}, {"code": 108, "indent": 0, "parameters": ["<Group:\"enemy\">"]}, {"code": 111, "indent": 0, "parameters": [12, "$gamePlayer.calcDistance(this._eventId) < 48"]}, {"code": 123, "indent": 1, "parameters": ["B", 0]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "$gameMap.event(this._eventId)._enemyState === 2"]}, {"code": 355, "indent": 1, "parameters": ["let eventID = this._eventId;"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.event(eventID)._alertTimer = 0; "]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.event(eventID)._enemyState = 3;"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.event(eventID)._fleeSpeed = 16;"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "$gameMap.event(this._eventId)._enemyState === 3"]}, {"code": 111, "indent": 1, "parameters": [12, "$gamePlayer.calcDistance(this._eventId) > 500"]}, {"code": 355, "indent": 2, "parameters": ["let eventID = this._eventId;"]}, {"code": 655, "indent": 2, "parameters": ["$gameMap.event(eventID)._alertTimer = 0; "]}, {"code": 655, "indent": 2, "parameters": ["$gameMap.event(eventID)._enemyState = 0;"]}, {"code": 655, "indent": 2, "parameters": ["$gameMap.event(eventID).enterNormalState()"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 45, "parameters": ["this._canBeAlerted = true"], "indent": 0}, {"code": 45, "parameters": ["this._fleeRange = 10"], "indent": null}, {"code": 45, "parameters": ["this._chaseRange = 3"], "indent": null}, {"code": 9, "indent": null}, {"code": 0, "parameters": []}], "repeat": true, "skippable": true, "wait": false}, "moveSpeed": 5, "moveType": 3, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "AIN_WaterFish1", "direction": 2, "pattern": 1, "characterIndex": 2}, "list": [{"code": 108, "indent": 0, "parameters": ["=>持续动作 : 空中飘浮 : 飘浮高度[4] : 周期[180] : 幅度[1]"]}, {"code": 356, "indent": 0, "parameters": [">消失动作 : 本事件 : 标准弹跳 : 时间[30] : 高度[120]"]}, {"code": 250, "indent": 0, "parameters": [{"name": "水面に浮かび上がる", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen._particle.particleGroupSet(0,'splash_cp','this');"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 355, "indent": 0, "parameters": ["dingk.Loot.directlyAcquireDrops(106,false)"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Item3", "volume": 60, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 356, "indent": 0, "parameters": [">事件管理核心 : 本事件 : 彻底删除"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 45, "parameters": ["this._canBeAlerted = true"], "indent": 0}, {"code": 45, "parameters": ["this._fleeRange = 10"], "indent": null}, {"code": 45, "parameters": ["this._chaseRange = 3"], "indent": null}, {"code": 9, "indent": null}, {"code": 0, "parameters": []}], "repeat": true, "skippable": true, "wait": false}, "moveSpeed": 5, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "AIN_WaterFish1", "direction": 2, "pattern": 1, "characterIndex": 2}, "list": [{"code": 108, "indent": 0, "parameters": ["=>持续动作 : 空中飘浮 : 飘浮高度[4] : 周期[180] : 幅度[1]"]}, {"code": 356, "indent": 0, "parameters": [">消失动作 : 本事件 : 标准弹跳 : 时间[30] : 高度[120]"]}, {"code": 250, "indent": 0, "parameters": [{"name": "水面に浮かび上がる", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen._particle.particleGroupSet(0,'splash_cp','this');"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 355, "indent": 0, "parameters": ["let event = $gameMap.event(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["let itemId = 130 + Math.randomInt(5);"]}, {"code": 655, "indent": 0, "parameters": ["let item = $dataItems[itemId];"]}, {"code": 655, "indent": 0, "parameters": ["dingk.Loot.getMapDrops(event,item);"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 356, "indent": 0, "parameters": [">事件管理核心 : 本事件 : 彻底删除"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 45, "parameters": ["this._canBeAlerted = true"], "indent": 0}, {"code": 45, "parameters": ["this._fleeRange = 10"], "indent": null}, {"code": 45, "parameters": ["this._chaseRange = 3"], "indent": null}, {"code": 9, "indent": null}, {"code": 0, "parameters": []}], "repeat": true, "skippable": true, "wait": false}, "moveSpeed": 5, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": false, "trigger": 4, "walkAnime": true}], "x": 66, "y": 27}, {"id": 15, "name": "EV015", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 73, "y": 20}, {"id": 16, "name": "EV016", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 0, "characterIndex": 3}, "list": [{"code": 355, "indent": 0, "parameters": ["$gameMap.event(23).locate(47.5,18)"]}, {"code": 117, "indent": 0, "parameters": [79]}, {"code": 121, "indent": 0, "parameters": [3, 3, 0]}, {"code": 121, "indent": 0, "parameters": [14, 14, 1]}, {"code": 356, "indent": 0, "parameters": [">允许操作玩家移动 : 开启"]}, {"code": 117, "indent": 0, "parameters": [6]}, {"code": 117, "indent": 0, "parameters": [100]}, {"code": 117, "indent": 0, "parameters": [99]}, {"code": 355, "indent": 0, "parameters": ["let id = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.event(id).steupCEQJ(2)"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "$gameScreen.zoomScale() == 1"]}, {"code": 356, "indent": 1, "parameters": ["dpZoom 2"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["particle set splash_walk walk:player:8 def below"]}, {"code": 356, "indent": 0, "parameters": ["particle set dust_walk walk:player:0 def below"]}, {"code": 356, "indent": 0, "parameters": ["particle set grass_walk walk:player:5 def below"]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["雨天重置的创造物"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameSelfVariables.value([$gameMap.mapId(), 1, 'rainyCreation']) > 0"]}, {"code": 108, "indent": 1, "parameters": ["生成蔬菜"]}, {"code": 355, "indent": 1, "parameters": ["let type = \"vege\";"]}, {"code": 655, "indent": 1, "parameters": ["let times = 5;"]}, {"code": 655, "indent": 1, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.steupCEQJ(75,eid,{creationType:type,creationQuantity:times})"]}, {"code": 108, "indent": 1, "parameters": ["生成树"]}, {"code": 355, "indent": 1, "parameters": ["let type = \"tree\";"]}, {"code": 655, "indent": 1, "parameters": ["let times = 7;"]}, {"code": 655, "indent": 1, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.steupCEQJ(75,eid,{creationType:type,creationQuantity:times})"]}, {"code": 108, "indent": 1, "parameters": ["生成采集物"]}, {"code": 355, "indent": 1, "parameters": ["$gameSelfSwitches.setValue([$gameMap.mapId(), 3, 'A'], false);"]}, {"code": 108, "indent": 1, "parameters": ["清除标记"]}, {"code": 355, "indent": 1, "parameters": ["$gameSelfVariables.setValue([$gameMap.mapId(), 1, 'rainyCreation'], 0);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["日常重置的创造物"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameSelfVariables.value([$gameMap.mapId(), 1, 'day']) < $gameSystem.day()"]}, {"code": 108, "indent": 1, "parameters": ["更新标记天数"]}, {"code": 355, "indent": 1, "parameters": ["let day = $gameSystem.day();"]}, {"code": 655, "indent": 1, "parameters": ["$gameSelfVariables.setValue([$gameMap.mapId(), 1, 'day'], day)"]}, {"code": 108, "indent": 1, "parameters": ["敌人"]}, {"code": 111, "indent": 1, "parameters": [12, "Math.random() > 0.5"]}, {"code": 355, "indent": 2, "parameters": ["let num = 4 + Math.randomInt(5);"]}, {"code": 655, "indent": 2, "parameters": ["QJ.SE.spawnRegionNum(1,87,[0],num,true);"]}, {"code": 655, "indent": 2, "parameters": ["//草食史莱姆"]}, {"code": 355, "indent": 2, "parameters": ["let num = 0 + Math.randomInt(2);"]}, {"code": 655, "indent": 2, "parameters": ["if (num > 0) {"]}, {"code": 655, "indent": 2, "parameters": ["QJ.SE.spawnRegionNum(1,90,[0,8],num,true);"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 655, "indent": 2, "parameters": ["//急冻史莱姆"]}, {"code": 355, "indent": 2, "parameters": ["let num = 0 + Math.randomInt(2);"]}, {"code": 655, "indent": 2, "parameters": ["if (num > 0) {"]}, {"code": 655, "indent": 2, "parameters": ["QJ.SE.spawnRegionNum(1,91,[0,8],num,true);"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 655, "indent": 2, "parameters": ["//哔哩哔哩史莱姆"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["let num = Math.randomInt(5) + 3; "]}, {"code": 655, "indent": 2, "parameters": ["for (let i = 0; i < num; i++) {"]}, {"code": 655, "indent": 2, "parameters": ["let type = 66 + Math.randomInt(4);"]}, {"code": 655, "indent": 2, "parameters": ["    QJ.SE.spawnRegionNum(1,type,[0],1,true);"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["let num = 1 + Math.randomInt(3);"]}, {"code": 655, "indent": 1, "parameters": ["QJ.SE.spawnRegionNum(1,70,[0],num,true);"]}, {"code": 655, "indent": 1, "parameters": ["//香蕉猫"]}, {"code": 355, "indent": 1, "parameters": ["let num = 1 + Math.randomInt(3);"]}, {"code": 655, "indent": 1, "parameters": ["QJ.SE.spawnRegionNum(1,71,[0],num,true);"]}, {"code": 655, "indent": 1, "parameters": ["//苹果猫"]}, {"code": 355, "indent": 1, "parameters": ["let num = 0 + Math.randomInt(4);"]}, {"code": 655, "indent": 1, "parameters": ["if (num > 0) {"]}, {"code": 655, "indent": 1, "parameters": ["QJ.SE.spawnRegionNum(1,113,[0],num,true);"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 655, "indent": 1, "parameters": ["//角兔"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$gameMap.drill_COET_getEventsByTag_direct(\"需监听\").length > 0"]}, {"code": 355, "indent": 2, "parameters": ["let monster = $gameMap.drill_COET_getEventsByTag_direct(\"需监听\");  "]}, {"code": 655, "indent": 2, "parameters": [" monster.forEach(function(target) {"]}, {"code": 655, "indent": 2, "parameters": ["let EID = target._eventId;"]}, {"code": 655, "indent": 2, "parameters": ["QJ.MPMZ.tl.ex_enemyAutomaticHitReaction(EID)"]}, {"code": 655, "indent": 2, "parameters": ["    });"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 19, "y": 33}, {"id": 17, "name": "EV017", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [120]}, {"code": 355, "indent": 0, "parameters": ["QJ.MPMZ.Shoot({"]}, {"code": 655, "indent": 0, "parameters": ["img:\"null1\",position:[['E',0],['E',0]],"]}, {"code": 655, "indent": 0, "parameters": ["initialRotation:['S',0],imgRotation:['F'],collisionBox:['C',20],"]}, {"code": 655, "indent": 0, "parameters": [" moveType:['S',0],anchor:[0,0],groupName:['system'],"]}, {"code": 655, "indent": 0, "parameters": ["existData:["]}, {"code": 655, "indent": 0, "parameters": ["{t:['S','$gameMap.regionId( Math.floor($gamePlayer.centerRealX()), Math.floor($gamePlayer.centerRealY()) ) === 248 && !$gamePlayer.isJumping()',true],a:['C',499],c:['T',0,15,true]},"]}, {"code": 655, "indent": 0, "parameters": ["{ t: ['Time', 120] }"]}, {"code": 655, "indent": 0, "parameters": ["   ],"]}, {"code": 655, "indent": 0, "parameters": ["});"]}, {"code": 355, "indent": 0, "parameters": ["QJ.MPMZ.Shoot({"]}, {"code": 655, "indent": 0, "parameters": ["img:\"null1\",position:[['Map',0],['Map',0]],"]}, {"code": 655, "indent": 0, "parameters": ["initialRotation:['S',0],imgRotation:['F'],collisionBox:['R',2064,1056],"]}, {"code": 655, "indent": 0, "parameters": [" moveType:['S',0],anchor:[0,0],"]}, {"code": 655, "indent": 0, "parameters": ["existData:["]}, {"code": 655, "indent": 0, "parameters": ["{t:['S','$gameMap.regionId( Math.floor($gamePlayer.centerRealX()), Math.floor($gamePlayer.centerRealY()) ) === 250 && !$gamePlayer.isJumping() && !$gameSwitches.value(100)',true],a:['C',133,[1,4,10]],p:[-1,false,true],c:['T',0,15,true]},"]}, {"code": 655, "indent": 0, "parameters": ["{t:['G',['\"enemy\"','\"object\"']],a:['F',QJ.MPMZ.tl.ex_enemyFallCheck,[[248,250],{}]],p:[-1,false,true],c:['T',0,15,true]},"]}, {"code": 655, "indent": 0, "parameters": ["{ t: ['Time', 120] }"]}, {"code": 655, "indent": 0, "parameters": ["   ],"]}, {"code": 655, "indent": 0, "parameters": ["});"]}, {"code": 355, "indent": 0, "parameters": ["QJ.MPMZ.Shoot({"]}, {"code": 655, "indent": 0, "parameters": ["img:\"null1\",position:[['E',0],['E',0]],"]}, {"code": 655, "indent": 0, "parameters": ["initialRotation:['S',0],imgRotation:['F'],collisionBox:['R',500,1500],"]}, {"code": 655, "indent": 0, "parameters": [" moveType:['S',0],anchor:[0.5,0.5],"]}, {"code": 655, "indent": 0, "parameters": ["existData:["]}, {"code": 655, "indent": 0, "parameters": ["{t:['S','$gameMap.regionId( Math.floor($gamePlayer.centerRealX()), Math.floor($gamePlayer.centerRealY()) ) === 250 && !$gamePlayer.isJumping() && !$gameSwitches.value(100)',true],a:['C',133,[1,4,10]],p:[-1,false,true],c:['T',0,15,true]},"]}, {"code": 655, "indent": 0, "parameters": ["{t:['G',['\"enemy\"','\"object\"']],a:['F',QJ.MPMZ.tl.ex_enemyFallCheck,[[248,250],{}]],p:[-1,false,true],c:['T',0,15,true]},"]}, {"code": 655, "indent": 0, "parameters": ["{ t: ['Time', 120] }"]}, {"code": 655, "indent": 0, "parameters": ["   ],"]}, {"code": 655, "indent": 0, "parameters": ["});"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 66, "y": 19}, null, null, null, null, null, {"id": 23, "name": "EV023", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!$fsm_Gate_add", "direction": 6, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<width: 2>"]}, {"code": 408, "indent": 0, "parameters": ["<height: 1>"]}, {"code": 408, "indent": 0, "parameters": ["<offsetX: 0>"]}, {"code": 408, "indent": 0, "parameters": ["<offsetY: 0>"]}, {"code": 355, "indent": 0, "parameters": ["let Hp = 750;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSelfVariables.set(this, 'HP', Hp);"]}, {"code": 655, "indent": 0, "parameters": ["$gameSelfVariables.set(this, 'MHP', Hp);"]}, {"code": 355, "indent": 0, "parameters": ["$gameSelfVariables.set(this, 'DEF', 20);"]}, {"code": 355, "indent": 0, "parameters": ["$gameSelfVariables.set(this, 'fudou', 99);"]}, {"code": 108, "indent": 0, "parameters": ["=>标签核心 : 添加标签 : 免疫异常"]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!$fsm_Gate_add", "direction": 6, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<BoxType:['C',32]>"]}, {"code": 108, "indent": 0, "parameters": ["<BoxOffset:0,-15>"]}, {"code": 108, "indent": 0, "parameters": ["<Group:\"object\">"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 2, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!$fsm_Gate_add", "direction": 4, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["实绩记录"]}, {"code": 355, "indent": 0, "parameters": ["if (!$gameNumberArray.value(36).includes(4) && !$gameNumberArray.value(37).includes(4)) {"]}, {"code": 655, "indent": 0, "parameters": ["    $gameNumberArray.value(36).push(4);"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Move", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 201, "indent": 0, "parameters": [0, 37, 8, 21, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 2, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!$fsm_Gate_add", "direction": 6, "pattern": 1, "characterIndex": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["var mapId = 37; "]}, {"code": 655, "indent": 0, "parameters": ["var eventId = 65; "]}, {"code": 655, "indent": 0, "parameters": ["var switchKey = [mapId, eventId, 'A']; "]}, {"code": 655, "indent": 0, "parameters": ["$gameSelfSwitches.setValue(switchKey, true);"]}, {"code": 212, "indent": 0, "parameters": [0, 157, false]}, {"code": 355, "indent": 0, "parameters": ["var weaponImg = \"fsm_Gate_add[6]\";"]}, {"code": 655, "indent": 0, "parameters": ["QJ.MPMZ.Shoot({"]}, {"code": 655, "indent": 0, "parameters": ["img:\"null\",initialRotation:90,existData:[{t:['Time',6]}],moveType:['S',0],position:[['E',0],['E',0]],"]}, {"code": 655, "indent": 0, "parameters": ["particles:["]}, {"code": 655, "indent": 0, "parameters": ["{img:weaponImg,intervalTime:1,bundleNumber:7,synScale:true,offsetMin:[-36,-24,-10],offsetMax:[0,24,10],existTime:80,disappearTime:20,disappearScale:0.5,"]}, {"code": 655, "indent": 0, "parameters": ["scaleXMin:0.5,scaleXMax:1,"]}, {"code": 655, "indent": 0, "parameters": ["moveType:['(()=>{let a = this.remA =  this.remA ? this.remA : (Math.random()*3-1.5);return a*t;})()',"]}, {"code": 655, "indent": 0, "parameters": ["'(()=>{let a = t<30?t:(30+(t-30)/2);return 8/60*a*(60-a);})()']"]}, {"code": 655, "indent": 0, "parameters": ["}]});"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 123, "indent": 0, "parameters": ["B", 0]}, {"code": 123, "indent": 0, "parameters": ["D", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": false}], "x": 47, "y": 18}, {"id": 24, "name": "EV024", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 4, "pattern": 1, "characterIndex": 5}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": false}], "x": 6, "y": 34}, null, null, null, null, null, null, null, null]}