{"autoplayBgm": false, "autoplayBgs": false, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 33, "note": "<深渊>\n<All Allow Region: 249,250>", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 22, "width": 33, "data": [2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2856, 2844, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2844, 2844, 2844, 2841, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2068, 2084, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 7099, 7098, 7102, 2860, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2072, 2832, 2820, 2844, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2854, 2082, 2068, 2068, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2052, 2086, 2832, 2840, 2954, 2856, 2824, 2816, 2816, 2816, 2816, 2816, 2820, 2854, 2082, 2049, 2048, 2048, 2048, 2048, 2052, 2076, 2076, 2076, 2076, 2048, 2048, 2048, 2048, 2048, 2052, 2076, 2076, 2086, 2850, 2821, 2854, 2953, 2957, 2832, 2816, 2820, 2844, 2844, 2824, 2840, 2082, 2049, 2048, 2048, 2048, 2048, 2048, 2086, 2851, 2849, 2839, 2849, 2048, 2048, 2048, 2048, 2052, 2086, 2850, 2836, 2836, 2817, 2840, 2954, 2850, 2836, 2817, 2816, 2840, 2946, 2948, 2856, 2841, 2064, 2048, 2048, 2048, 2048, 2048, 2048, 2850, 2842, 7095, 2860, 2082, 2048, 2048, 2048, 2052, 2086, 2850, 2817, 2816, 2820, 2844, 2854, 2956, 2832, 2816, 2816, 2816, 2840, 2952, 2942, 2957, 2860, 2088, 2056, 2048, 2048, 2048, 2052, 2086, 2856, 2854, 7101, 2082, 2049, 2048, 2048, 2048, 2072, 2850, 2817, 2816, 2816, 2840, 2947, 2957, 2850, 2817, 2820, 2844, 2844, 2826, 2836, 2836, 2852, 2954, 2858, 2088, 2076, 2076, 2076, 2073, 2862, 2082, 2068, 2068, 2049, 2048, 2048, 2048, 2048, 2072, 2832, 2816, 2816, 2816, 2840, 2944, 2850, 2817, 2816, 2840, 7091, 7094, 2856, 2844, 2824, 2840, 2956, 2833, 2836, 2836, 2836, 2852, 2089, 2081, 2057, 2048, 2052, 2088, 2048, 2048, 2048, 2048, 2072, 2832, 2816, 2816, 2816, 2840, 2956, 2834, 2844, 2828, 2854, 7097, 7100, 4062, 7095, 2856, 2826, 2836, 2817, 2816, 2816, 2816, 2818, 2836, 2852, 2064, 2048, 2072, 2862, 2048, 2048, 2048, 2052, 2086, 2832, 2816, 2816, 2816, 2818, 2836, 2842, 7095, 2860, 2082, 2068, 2068, 2084, 7101, 4062, 2832, 2816, 2816, 2816, 2816, 2820, 2844, 2824, 2840, 2064, 2048, 2050, 2068, 2048, 2048, 2048, 2072, 2850, 2817, 2816, 2816, 2816, 2816, 2820, 2854, 7101, 2082, 2049, 2048, 2048, 2050, 2068, 2084, 2856, 2824, 2816, 2816, 2816, 2840, 2958, 2834, 2854, 2064, 2048, 2048, 2048, 2048, 2048, 2048, 2072, 2832, 2816, 2816, 2820, 2844, 2824, 2840, 2082, 2068, 2049, 2048, 2048, 2048, 2048, 2048, 2050, 2084, 2832, 2816, 2816, 2816, 2818, 2836, 2842, 2958, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2072, 2832, 2816, 2820, 2854, 2958, 2834, 2854, 2064, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2072, 2856, 2824, 2816, 2816, 2816, 2816, 2818, 2852, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2072, 2832, 2816, 2840, 2958, 2851, 2855, 2082, 2049, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2054, 2093, 2832, 2816, 2816, 2816, 2816, 2816, 2840, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2072, 2832, 2816, 2818, 2836, 2842, 2082, 2049, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2850, 2821, 2844, 2824, 2816, 2816, 2816, 2840, 2048, 2048, 2048, 2048, 2048, 2048, 2052, 2086, 2832, 2816, 2816, 2816, 2840, 2088, 2056, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2832, 2840, 2958, 2832, 2816, 2816, 2816, 2840, 2048, 2048, 2048, 2048, 2048, 2048, 2072, 2850, 2817, 2816, 2816, 2816, 2818, 2852, 2064, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2856, 2826, 2836, 2817, 2816, 2816, 2816, 2840, 2048, 2048, 2048, 2048, 2048, 2048, 2072, 2832, 2816, 2816, 2816, 2816, 2820, 2854, 2064, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2093, 2832, 2816, 2816, 2820, 2844, 2844, 2841, 2048, 2048, 2048, 2048, 2048, 2048, 2072, 2856, 2824, 2816, 2816, 2816, 2840, 2091, 2057, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2072, 2850, 2817, 2816, 2816, 2840, 7091, 7094, 2848, 2048, 2048, 2048, 2048, 2048, 2048, 2050, 2084, 2834, 2844, 2828, 2844, 2826, 2852, 2088, 2076, 2076, 2060, 2076, 2076, 2076, 2076, 2076, 2060, 2086, 2832, 2816, 2816, 2820, 2854, 7097, 7100, 2860, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2072, 2848, 2958, 2848, 2958, 2832, 2818, 2836, 2836, 2852, 2092, 2076, 2836, 2836, 2836, 2852, 2092, 2850, 2817, 2816, 2816, 2840, 2082, 2068, 2068, 2068, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2072, 2835, 2849, 2831, 2849, 2825, 2816, 2816, 2816, 2818, 2836, 2817, 2816, 2820, 2844, 2826, 2836, 2817, 2816, 2816, 2820, 2854, 2064, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2072, 2848, 2958, 2848, 2958, 2832, 2816, 2816, 2816, 2816, 2820, 2844, 2844, 2854, 2958, 2832, 2816, 2816, 2816, 2820, 2854, 2082, 2049, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2072, 2857, 2849, 2847, 2849, 2845, 2844, 2824, 2816, 2820, 2854, 2947, 2945, 2957, 2850, 2817, 2820, 2844, 2844, 2841, 2082, 2049, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2050, 2084, 7091, 7090, 7090, 7090, 7094, 2856, 2824, 2840, 2955, 2951, 2850, 2836, 2817, 2816, 2840, 7099, 7102, 2860, 2064, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2072, 7097, 7096, 7096, 7096, 7100, 2090, 2856, 2846, 2849, 2849, 2845, 2844, 2844, 2828, 2854, 2082, 2068, 2068, 2049, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2050, 2068, 2068, 2068, 2068, 2068, 2051, 2084, 7095, 7091, 7090, 7090, 7094, 7095, 2860, 2082, 2049, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2072, 7093, 7097, 7096, 7096, 7100, 7093, 2082, 2049, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2052, 2076, 2076, 2073, 7101, 4011, 4001, 4001, 4013, 7101, 2064, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2072, 2859, 2853, 2089, 2093, 2850, 2836, 2836, 2852, 2082, 2049, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2050, 2084, 2857, 2838, 2836, 2817, 2816, 2816, 2840, 2088, 2076, 2056, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2050, 2084, 2832, 2816, 2816, 2816, 2816, 2818, 2836, 2852, 2064, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3146, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3145, 3126, 3140, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3144, 3134, 3149, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3146, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3148, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3146, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 153, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 145, 0, 0, 0, 153, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 153, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 153, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 155, 0, 0, 0, 0, 0, 85, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 155, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 147, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 85, 163, 176, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 153, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 85, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 153, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 85, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 153, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 153, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 85, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 153, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 153, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 153, 137, 137, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 153, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 146, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 147, 155, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 154, 85, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 147, 0, 0, 0, 155, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 162, 85, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 147, 155, 137, 137, 137, 163, 0, 0, 0, 0, 0, 176, 0, 0, 0, 0, 0, 0, 0, 0, 176, 85, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 155, 155, 137, 137, 163, 0, 0, 0, 0, 0, 176, 85, 0, 0, 0, 0, 0, 0, 0, 176, 85, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 155, 163, 0, 0, 0, 0, 0, 176, 0, 0, 0, 0, 0, 0, 0, 0, 176, 85, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 163, 0, 0, 0, 0, 0, 147, 0, 147, 0, 147, 155, 76, 76, 76, 76, 77, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 177, 0, 0, 0, 0, 176, 85, 0, 147, 129, 155, 163, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 105, 0, 0, 0, 0, 0, 0, 0, 0, 177, 0, 176, 85, 85, 0, 85, 155, 137, 163, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 113, 0, 0, 144, 0, 0, 0, 0, 0, 0, 0, 0, 0, 85, 85, 75, 76, 77, 177, 0, 0, 0, 0, 176, 0, 0, 0, 0, 0, 0, 0, 0, 147, 155, 129, 129, 152, 0, 144, 0, 0, 0, 0, 0, 0, 0, 0, 0, 155, 0, 146, 75, 76, 0, 0, 176, 85, 0, 0, 0, 0, 0, 0, 0, 0, 155, 155, 137, 137, 154, 129, 152, 0, 0, 0, 0, 178, 179, 0, 0, 0, 155, 0, 154, 155, 0, 0, 0, 85, 0, 0, 53, 0, 0, 0, 0, 147, 129, 155, 163, 0, 0, 162, 137, 154, 144, 0, 0, 0, 168, 169, 0, 0, 0, 163, 0, 154, 155, 0, 0, 0, 85, 0, 0, 0, 0, 0, 0, 0, 155, 137, 163, 0, 0, 0, 0, 0, 162, 154, 0, 0, 0, 152, 153, 0, 0, 0, 0, 0, 162, 163, 0, 0, 0, 85, 0, 0, 0, 0, 0, 0, 147, 155, 0, 0, 0, 0, 0, 0, 0, 0, 154, 0, 0, 0, 152, 153, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 85, 0, 0, 0, 0, 0, 0, 155, 163, 0, 0, 0, 0, 0, 0, 0, 0, 162, 85, 0, 0, 160, 161, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 85, 0, 0, 0, 0, 0, 85, 155, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 176, 85, 0, 0, 0, 178, 179, 85, 163, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 146, 85, 0, 0, 0, 168, 169, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 154, 0, 0, 0, 0, 152, 153, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 176, 85, 0, 0, 0, 0, 0, 0, 147, 0, 0, 0, 0, 0, 0, 154, 0, 0, 0, 0, 160, 161, 85, 177, 0, 0, 0, 0, 0, 0, 0, 0, 0, 85, 178, 179, 0, 0, 0, 0, 0, 155, 0, 0, 0, 0, 0, 0, 162, 85, 0, 0, 0, 0, 0, 0, 85, 85, 85, 177, 0, 0, 0, 0, 0, 176, 85, 168, 169, 0, 0, 147, 129, 129, 155, 0, 0, 0, 0, 0, 0, 0, 85, 0, 0, 0, 0, 0, 0, 0, 0, 0, 85, 85, 0, 0, 0, 0, 85, 0, 152, 153, 0, 147, 155, 137, 137, 163, 0, 0, 0, 0, 0, 0, 0, 53, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 160, 161, 0, 155, 155, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 85, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 147, 155, 163, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 154, 146, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 53, 147, 155, 163, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 154, 154, 0, 0, 0, 0, 0, 146, 0, 0, 0, 0, 0, 0, 0, 0, 147, 129, 129, 155, 155, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 162, 154, 129, 129, 129, 129, 129, 154, 146, 0, 0, 0, 0, 0, 0, 147, 155, 129, 129, 155, 163, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 162, 137, 137, 137, 137, 137, 154, 154, 0, 0, 16, 17, 0, 0, 155, 155, 137, 137, 163, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 162, 154, 0, 129, 16, 17, 129, 0, 155, 163, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 162, 0, 137, 17, 16, 137, 0, 163, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 105, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 250, 250, 250, 250, 250, 250, 250, 250, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 254, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 254, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 254, 254, 254, 254, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 254, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 254, 254, 254, 254, 254, 250, 250, 250, 250, 250, 249, 250, 250, 250, 250, 250, 250, 250, 250, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 254, 254, 254, 254, 254, 250, 250, 250, 250, 250, 249, 0, 250, 250, 250, 250, 250, 250, 250, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 254, 254, 250, 250, 250, 250, 250, 249, 249, 249, 0, 0, 250, 250, 250, 250, 249, 249, 249, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 254, 250, 250, 250, 250, 250, 250, 249, 0, 254, 254, 254, 250, 250, 250, 250, 249, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 249, 250, 250, 250, 250, 249, 249, 0, 254, 254, 254, 254, 250, 250, 250, 250, 249, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 249, 250, 249, 249, 0, 0, 0, 254, 254, 254, 250, 250, 250, 250, 250, 249, 0, 0, 0, 0, 0, 0, 0, 0, 0, 254, 254, 0, 0, 0, 0, 0, 0, 0, 249, 0, 0, 0, 0, 0, 250, 249, 249, 250, 250, 250, 250, 249, 0, 0, 0, 0, 0, 0, 0, 0, 0, 254, 254, 254, 254, 254, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 254, 250, 249, 249, 250, 250, 250, 249, 0, 0, 0, 0, 0, 0, 0, 0, 254, 254, 254, 254, 254, 254, 254, 254, 0, 0, 0, 0, 0, 0, 0, 0, 0, 254, 250, 0, 0, 250, 250, 250, 249, 0, 0, 0, 0, 0, 0, 0, 0, 254, 254, 254, 250, 250, 254, 254, 254, 0, 0, 0, 0, 240, 240, 0, 0, 0, 254, 250, 0, 0, 250, 250, 250, 249, 0, 0, 0, 0, 0, 0, 0, 254, 254, 254, 250, 250, 250, 250, 250, 254, 254, 0, 0, 0, 240, 240, 0, 0, 249, 250, 250, 0, 0, 250, 250, 250, 249, 0, 0, 0, 0, 0, 0, 0, 254, 250, 250, 250, 250, 250, 250, 250, 250, 254, 0, 0, 0, 240, 240, 0, 0, 249, 250, 250, 250, 250, 250, 250, 250, 249, 0, 0, 0, 0, 0, 0, 254, 254, 250, 250, 250, 250, 250, 250, 250, 250, 254, 0, 0, 0, 0, 0, 0, 0, 249, 250, 250, 250, 250, 250, 250, 250, 249, 0, 0, 0, 0, 0, 0, 254, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 249, 0, 0, 0, 0, 0, 0, 249, 250, 250, 250, 250, 250, 250, 249, 0, 0, 0, 0, 0, 0, 0, 254, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 249, 0, 0, 0, 0, 0, 0, 249, 250, 250, 250, 250, 250, 250, 249, 0, 0, 0, 0, 240, 240, 249, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 249, 0, 0, 0, 0, 0, 0, 249, 250, 250, 250, 250, 250, 250, 254, 0, 0, 0, 0, 240, 240, 249, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 249, 0, 0, 0, 0, 0, 0, 0, 249, 250, 250, 250, 250, 250, 250, 254, 0, 0, 0, 0, 0, 0, 0, 249, 250, 250, 250, 250, 250, 250, 250, 250, 250, 249, 0, 0, 0, 0, 0, 254, 254, 254, 250, 250, 250, 250, 250, 250, 254, 0, 0, 0, 0, 0, 0, 0, 0, 249, 249, 249, 250, 250, 250, 250, 250, 249, 0, 240, 240, 0, 0, 0, 254, 254, 254, 250, 250, 250, 250, 250, 250, 250, 249, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 249, 249, 249, 249, 249, 0, 0, 240, 240, 0, 0, 254, 254, 254, 254, 250, 250, 250, 250, 250, 250, 250, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 254, 254, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 249, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 254, 254, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 254, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 254, 254, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 254, 254, 254, 254, 254, 254, 254, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 254, 254, 254, 254, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 254, 254, 254, 254, 254, 254, 254, 254, 0, 0, 0, 0, 0, 0, 0, 0, 254, 254, 254, 254, 254, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 0, 0, 254, 254, 254, 254, 254, 254, 254, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 254, 254, 254, 254, 0, 0, 254, 254, 254, 254, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 254, 254, 254, 0, 0, 254, 254, 254, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 249, 249, 250, 250, 0, 0, 0, 249, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 249, 249, 249, 0, 0, 0, 249, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 249, 0, 0, 0, 0, 0, 249, 249, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250], "events": [null, {"id": 1, "name": "EV001", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 0, "characterIndex": 3}, "list": [{"code": 355, "indent": 0, "parameters": ["var id = \"zoomBlur-P\" ;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.eraseFilter(id);"]}, {"code": 356, "indent": 0, "parameters": ["particle set magma_r region:250"]}, {"code": 356, "indent": 0, "parameters": ["particle set sparks_w weather"]}, {"code": 356, "indent": 0, "parameters": ["particle set sparks_w2 weather"]}, {"code": 241, "indent": 0, "parameters": [{"name": "Gehenna～焔魔灼熱地獄～", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 117, "indent": 0, "parameters": [6]}, {"code": 117, "indent": 0, "parameters": [100]}, {"code": 117, "indent": 0, "parameters": [99]}, {"code": 108, "indent": 0, "parameters": ["创造物生成"]}, {"code": 355, "indent": 0, "parameters": ["let id = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.event(id).steupCEQJ(2)"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "$gameScreen.zoomScale() == 1"]}, {"code": 356, "indent": 1, "parameters": ["d<PERSON><PERSON><PERSON> 2 10 player"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["particle set dust_walk walk:player:0 def below"]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 115, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["雨天重置的创造物"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameSelfVariables.value([$gameMap.mapId(), 1, 'rainyCreation']) > 0"]}, {"code": 108, "indent": 1, "parameters": ["清除标记"]}, {"code": 355, "indent": 1, "parameters": ["$gameSelfVariables.setValue([$gameMap.mapId(), 1, 'rainyCreation'], 0);"]}, {"code": 108, "indent": 1, "parameters": ["重置采集物"]}, {"code": 355, "indent": 1, "parameters": ["var events = $gameMap.drill_COET_getEventsByTag_direct(\"宝箱\");"]}, {"code": 655, "indent": 1, "parameters": ["events.forEach(function(event) {"]}, {"code": 655, "indent": 1, "parameters": ["    var eventId = event.eventId();"]}, {"code": 655, "indent": 1, "parameters": ["    var key = [$gameMap.mapId(), eventId, 'A'];"]}, {"code": 655, "indent": 1, "parameters": ["    $gameSelfSwitches.setValue(key, false);"]}, {"code": 655, "indent": 1, "parameters": ["});"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["日常重置的创造物"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameSelfVariables.value([$gameMap.mapId(), 1, 'day']) < $gameSystem.day()"]}, {"code": 108, "indent": 1, "parameters": ["更新标记天数"]}, {"code": 355, "indent": 1, "parameters": ["let day = $gameSystem.day();"]}, {"code": 655, "indent": 1, "parameters": ["$gameSelfVariables.setValue([$gameMap.mapId(), 1, 'day'], day)"]}, {"code": 108, "indent": 1, "parameters": ["更新敌人"]}, {"code": 355, "indent": 1, "parameters": ["let num = 1;"]}, {"code": 655, "indent": 1, "parameters": ["QJ.SE.spawnRegionNum(1,115,[0],num,true);"]}, {"code": 655, "indent": 1, "parameters": ["//燃烧史莱姆"]}, {"code": 355, "indent": 1, "parameters": ["let num = 1;"]}, {"code": 655, "indent": 1, "parameters": ["QJ.SE.spawnRegionNum(1,116,[0],num,true);"]}, {"code": 655, "indent": 1, "parameters": ["//火焰妖精"]}, {"code": 355, "indent": 1, "parameters": ["let num = 1;"]}, {"code": 655, "indent": 1, "parameters": ["QJ.SE.spawnRegionNum(1,117,[0],num,true);"]}, {"code": 655, "indent": 1, "parameters": ["//炎魔"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 21, "y": 20}, {"id": 2, "name": "EV002", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [60]}, {"code": 111, "indent": 0, "parameters": [12, "$gameMap.getGroupBulletListQJ('rope').length == 0"]}, {"code": 355, "indent": 1, "parameters": ["if (!$gameTemp._eventReserved) {"]}, {"code": 655, "indent": 1, "parameters": ["QJ.MPMZ.tl.ex_ropeCollisionBox()"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 29, "y": 18}, null, null, null, null, null, {"id": 8, "name": "EV008", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<width: 18>"]}, {"code": 408, "indent": 0, "parameters": ["<height: 1>"]}, {"code": 408, "indent": 0, "parameters": ["<offsetX: 0>"]}, {"code": 408, "indent": 0, "parameters": ["<offsetY: 0>"]}, {"code": 111, "indent": 0, "parameters": [12, "Utils.isOptionValid(\"test\")"]}, {"code": 201, "indent": 1, "parameters": [0, 18, 13, 32, 0, 2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 8, "y": 0}, {"id": 9, "name": "采集点", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["=>标签核心 : 添加标签 : 宝箱"]}, {"code": 108, "indent": 0, "parameters": ["particle set mysterious_torch_c-EID this mysterious_torch_c below"]}, {"code": 108, "indent": 0, "parameters": ["particle update mysterious_torch_c-EID color #871729"]}, {"code": 250, "indent": 0, "parameters": [{"name": "キラーン（ひらめき・アイテム発見・獲得）", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 128, "indent": 0, "parameters": [73, 0, 0, 1, false]}, {"code": 356, "indent": 0, "parameters": ["particle clear mysterious_torch_c-EID"]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 6, "y": 17}]}