[null, {"id": 1, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 1019, "maxTurns": 1, "message1": "%1倒下了！", "message2": "%1被打败了！", "message3": "", "message4": "%1复活了！", "minTurns": 1, "motion": 3, "name": "无法战斗", "note": "", "overlay": 0, "priority": 100, "releaseByDamage": false, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 4, "stepsToRemove": 100, "traits": [{"code": 23, "dataId": 9, "value": 0}], "messageType": 1}, {"id": 2, "autoRemovalTiming": 2, "chanceByDamage": 100, "description": "", "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "测试用无敌BUFF", "note": "<atkB: 99999999>", "overlay": 0, "priority": 0, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": true, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": [{"code": 21, "dataId": 0, "value": 9.99}, {"code": 21, "dataId": 2, "value": 9.99}], "messageType": 1}, {"id": 3, "autoRemovalTiming": 2, "chanceByDamage": 100, "description": "", "iconIndex": 1012, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "兴奋", "note": " <Custom Passive Condition>\n  if ($gameVariables.value(25) > 20 && $gameVariables.value(25) < 100) {\nlet id = 3;\nif($gameVariables.value(25) > 80){\nDataManager.changeDifferenceStateDescription(id,2);\n}else if($gameVariables.value(25) > 40){\nDataManager.changeDifferenceStateDescription(id,1);\n}else{\nDataManager.changeDifferenceStateDescription(id,0);\n}\n    condition = true;\n  } else {\n    condition = false;\n  }\n  </Custom Passive Condition>", "overlay": 0, "priority": 0, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": [], "messageType": 1}, {"id": 4, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 1013, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "兽", "note": "<HELP DESCRIPTION>\n\\fs[14]✦\\fi\\c[110]「呜噢噢噢噢……！」\\fr扭曲为了满脑子只有欲望的Beast!  兴奋度:\\c[18]???%\\c[0]\n</HELP DESCRIPTION>\n\n  <Custom Passive Condition>\n  if ($gameVariables.value(25) >= 100) {\nvar state = $dataStates[3];\nvar text = \"\\\\fs[14]✦\\\\fi\\\\c[110]「呜噢噢噢噢……！」\\\\fr扭曲为了满脑子只有欲望的Beast!  兴奋度:\\\\c[18]???%\\\\c[0]\";\nstate.description = text;\n    condition = true;\n  } else {\n    condition = false;\n  }\n  </Custom Passive Condition>", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 5, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 1024, "maxTurns": 6, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 6, "motion": 0, "name": "中毒", "note": "<Category: ijou>\n<HELP DESCRIPTION>\n\\c[110]\\fi「体内感染了毒素。」\\fr随时间经过受到更多伤害\n</HELP DESCRIPTION>\n\n", "overlay": 0, "priority": 60, "releaseByDamage": false, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": [], "messageType": 1}, {"id": 6, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [{"code": 23, "dataId": 2, "value": 0.5}], "iconIndex": 1025, "maxTurns": 6, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 6, "motion": 0, "name": "出血", "note": "<Category: ijou>\n<HELP DESCRIPTION>\n\\c[110]\\fi「血流不止。」\\fr根据体力上限受到伤害\n</HELP DESCRIPTION>", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 7, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 1027, "maxTurns": 4, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 3, "motion": 0, "name": "打雷", "note": "<Category: ijou>\n<HELP DESCRIPTION>\n\\c[110]\\fi「看啊、这家伙被雷劈了！」\\fr全身麻痹无法动弹！\n</HELP DESCRIPTION>\n\n<eva Rate: 0%>", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 4, "stepsToRemove": 100, "messageType": 1}, {"id": 8, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [{"code": 21, "dataId": 6, "value": 1.2}], "iconIndex": 1026, "maxTurns": 6, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 6, "motion": 0, "name": "炎上", "note": "<Category: ijou>\n<HELP DESCRIPTION>\n\\c[110]\\fi「好烫好烫好烫——！」\\fr对自身范围内所有单位持续的灼烧\n</HELP DESCRIPTION>", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 9, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [{"code": 21, "dataId": 3, "value": 0.5}, {"code": 21, "dataId": 5, "value": 0.5}, {"code": 13, "dataId": 9, "value": 0.5}], "iconIndex": 1028, "maxTurns": 2, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 2, "motion": 0, "name": "冰结", "note": "<Category: ijou>\n<HELP DESCRIPTION>\n\\c[110]\\fi「变成冰棍吧！」\\fr被冻住了！\n</HELP DESCRIPTION>\n\n <eva Rate: 0%>", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 10, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [], "iconIndex": 1012, "maxTurns": 8, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 8, "motion": 0, "name": "饱腹感", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 11, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 1030, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "眩晕", "note": "<Category: ijou>\n<HELP DESCRIPTION>\n\\c[110]\\fi「头晕目眩！」\\fr无法行动！\n</HELP DESCRIPTION>\n\n<eva Rate: 0%>", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 12, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [{"code": 21, "dataId": 3, "value": 9.99}, {"code": 21, "dataId": 5, "value": 9.99}], "iconIndex": 825, "maxTurns": 999, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 999, "motion": 0, "name": "秒杀挂", "note": "<Custom Apply Effect>\n$gamePlayer.requestAnimation(52)\n</Custom Apply Effect>\n\n<Custom Remove Effect>\n$gamePlayer.requestAnimation(54)\n</Custom Remove Effect>", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 13, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 1019, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "【恶魔诅咒】", "note": "<Category: Bypass Recover All Removal>\n<Category: Bypass Death Removal>\n\n<HELP DESCRIPTION>\n\\c[110]\\fi「呋呋、这就是反抗我的下场噢~♥」\\fr被奇怪的小鬼店长诅咒了。\n</HELP DESCRIPTION>", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 14, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [{"code": 21, "dataId": 2, "value": 0.66}, {"code": 21, "dataId": 4, "value": 0.66}, {"code": 21, "dataId": 3, "value": 0.66}, {"code": 21, "dataId": 5, "value": 0.66}, {"code": 21, "dataId": 5, "value": 0.66}], "iconIndex": 1030, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "热死了…", "note": "<HELP DESCRIPTION>\n\\c[110]\\fi「热死了……」\\fr因为炎热浑身无力…\n</HELP DESCRIPTION>", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 15, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 1046, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "怪物迷彩", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 16, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 1020, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "杂鱼哥哥", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 17, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 18, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [{"code": 21, "dataId": 0, "value": 2}], "iconIndex": 738, "maxTurns": 4, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 4, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 19, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 20, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 21, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [{"code": 23, "dataId": 3, "value": 0.3}], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "黑暗料理", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 22, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [{"code": 23, "dataId": 3, "value": 1.2}], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "完美料理", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 23, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "立绘标记：T恤", "note": "  <Custom Apply Effect>\nuser.removeState(24);\nuser.removeState(25);\nuser.removeState(26);\n$gameMap.steupCEQJ(33,1)\n  </Custom Apply Effect>", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 24, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "立绘标记：常态", "note": "  <Custom Apply Effect>\nuser.removeState(23);\nuser.removeState(25);\nuser.removeState(26);\n$gameMap.steupCEQJ(33,1)\n  </Custom Apply Effect>", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 25, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "立绘标记：洗澡中", "note": "  <Custom Apply Effect>\nuser.removeState(23);\nuser.removeState(24);\nuser.removeState(26);\n$gameMap.steupCEQJ(33,1)\n  </Custom Apply Effect>", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 26, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "立绘标记：洗澡后", "note": "  <Custom Apply Effect>\nuser.removeState(23);\nuser.removeState(24);\nuser.removeState(25);\n$gameMap.steupCEQJ(33,1)\n  </Custom Apply Effect>", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 27, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "妹妹接近中", "note": "<Category: imoutoState>\n  <Custom Apply Effect>\nQJ.MPMZ.tl._imoutoUtilStateDescriptionRefresh();\n  </Custom Apply Effect>", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 28, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "妹妹游戏中", "note": "<Category: imoutoState>\n<Custom Apply Effect>\nuser.removeState(27);\nQJ.MPMZ.tl._imoutoUtilStateDescriptionRefresh();\n</Custom Apply Effect>", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 29, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "妹妹洗澡中", "note": "<Category: imoutoState>\n<Custom Apply Effect>\nvar state = [27,28,30,31,32];\nstate.forEach(function(state_id) {\n$gameActors.actor(2).removeState(state_id);\n});\nQJ.MPMZ.tl._imoutoUtilStateDescriptionRefresh();\n</Custom Apply Effect>", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 30, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "妹妹待机中", "note": "<Category: imoutoState>\n<Custom Apply Effect>\nuser.removeState(27);\nQJ.MPMZ.tl._imoutoUtilStateDescriptionRefresh();\n</Custom Apply Effect>", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 31, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "妹妹睡眠中", "note": "<Category: imoutoState>\n<HELP DESCRIPTION>\n\\fs[14]✦\\c[110]覚醒度\\c[0] \\V[19]%\n\\fs[14]✦\\fi\\c[110]\n</HELP DESCRIPTION>\n\n  <Custom Apply Effect>\nvar state = [27,28,29,30,32];\nstate.forEach(function(state_id) {\nuser.removeState(state_id);\n});\nQJ.MPMZ.tl._imoutoUtilStateDescriptionRefresh();\n  </Custom Apply Effect>", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 32, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "妹妹饥饿中", "note": "<Category: imoutoState>\n<HELP DESCRIPTION>\n\\fs[14]✦\\fi\\c[110]（ハンバーグ、ステーキ、\n    大きなチキンレッグ…）\n</HELP DESCRIPTION>\n\n  <Custom Apply Effect>\nuser.removeState(27);\nQJ.MPMZ.tl._imoutoUtilStateDescriptionRefresh();\n  </Custom Apply Effect>", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 33, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "妹妹乘凉中", "note": "<Category: imoutoState>\n  <Custom Apply Effect>\nQJ.MPMZ.tl._imoutoUtilStateDescriptionRefresh();\n  </Custom Apply Effect>", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 34, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "妹妹刷牙中", "note": "<Category: imoutoState>\n  <Custom Apply Effect>\nQJ.MPMZ.tl._imoutoUtilStateDescriptionRefresh();\n  </Custom Apply Effect>", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 35, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "妹妹发情中", "note": "<Category: imoutoState>\n<Category: Bypass Recover All Removal>\n\n<HELP DESCRIPTION>\n✦\\c[27]（感觉身体变得轻飘飘的……）\n</HELP DESCRIPTION>\n\n  <Custom Apply Effect>\n      $gameSystem.addGameTimeEvent({\n        key: 'state35',\n        command: 'remove',\n        delayMinutes: 600,\n        charId: 2, \n        target: 35, \n        condition: 'true' \n      });\t\n  </Custom Apply Effect>\n", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 36, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "妹妹犯困中", "note": "<Category: imoutoState>\n<Category: Bypass Recover All Removal>\n\n<HELP DESCRIPTION>\n✦\\c[22]精神变得松懈下来——\n</HELP DESCRIPTION>\n\n  <Custom Apply Effect>\n      $gameSystem.addGameTimeEvent({\n        key: 'state36',\n        command: 'remove',\n        delayMinutes: 600,\n        charId: 2, \n        target: 36, \n        condition: 'true' \n      });\t\n$gameSelfSwitches.setValue([4, 6, 'B'], true);\n\n  </Custom Apply Effect>\n", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 37, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "摸头过热", "note": "<Category: imoutoState>\n<HELP DESCRIPTION>\n✦\\fi\\c[110]摸的时间太久、\n妹妹有点生气了…\n</HELP DESCRIPTION>", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 38, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "安眠药效", "note": "<HELP DESCRIPTION>\n✦\\c[27]安眠効果のある料理を食べた。\n</HELP DESCRIPTION>", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 39, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "想喝饮料了…", "note": "<Category: imoutoState>\n<Category: Bypass Recover All Removal>\n<HELP DESCRIPTION>\n✦\\c[110]（想喝饮料了……）\n</HELP DESCRIPTION>\n", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 40, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [{"code": 14, "dataId": 41, "value": 1}], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "被辣哭了", "note": "<Category: Bypass Recover All Removal>\n<Category: imoutoState>\n\n  <Custom Apply Effect>\nQJ.MPMZ.tl._imoutoUtilStateDescriptionRefresh();\n  </Custom Apply Effect>", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 41, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "吃饱喝足", "note": "<Category: imoutoState>\n<Category: Bypass Recover All Removal>\n\n<HELP DESCRIPTION>\n✦\\c[29]吃饱喝足、\n妹妹现在感觉很幸福！\n</HELP DESCRIPTION>\n\n  <Custom Apply Effect>\nif( $gameVariables.value(20) < 80){\n$gameVariables.setValue(20, 80);\n}\n      $gameSystem.addGameTimeEvent({\n        key: 'state41',\n        command: 'remove',\n        delayMinutes: 600,\n        charId: 2, \n        target: 41, \n        condition: 'true' \n      });\t\n  </Custom Apply Effect>", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 42, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 43, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 44, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 45, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 1011, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "妹妹约定-不要受伤", "note": "<Category: yakusoku>\n<Category: Bypass Recover All Removal>\n<Category: Bypass Death Removal>\n\n  <Custom Apply Effect>\n$gameSwitches.setValue(490, true)\n  </Custom Apply Effect>", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 46, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 1011, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "妹妹约定-肉料理", "note": "<Category: yakusoku>\n<Category: Bypass Recover All Removal>", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 47, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 48, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 49, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 50, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 51, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 52, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 53, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 54, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 55, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 1019, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "禁止攻击", "note": "<HELP DESCRIPTION>\n\\c[110]\\fi「这里可是我的领域噢，可不能做坏事呢♥！」\\fr攻击行为禁止\n</HELP DESCRIPTION>\n\n  <Custom Apply Effect>\n$gameSwitches.setValue(14, true);\n  </Custom Apply Effect>\n\n  <Custom Remove Effect>\n$gameSwitches.setValue(14, false);\n  </Custom Remove Effect>", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 56, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "哥哥：性欲显现", "note": "<Custom Apply Effect>\nvar data = {\n'type': \"公共事件\",\n'pipeType': \"并行\",\n'commonEventId': 24,\n'callBack_str': \"\",};\n$gameMap.drill_LCT_addPipeEvent( data );\n</Custom Apply Effect>\n\n<Custom Remove Effect>\nvar data = {\n'type': \"公共事件\",\n'pipeType': \"并行\",\n'commonEventId': 24,\n'callBack_str': \"\",};\n$gameMap.drill_LCT_addPipeEvent( data );\n</Custom Remove Effect>", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 57, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [{"code": 21, "dataId": 0, "value": 1.2}, {"code": 21, "dataId": 2, "value": 1.2}, {"code": 21, "dataId": 4, "value": 1.2}, {"code": 21, "dataId": 3, "value": 1.2}, {"code": 21, "dataId": 5, "value": 1.2}, {"code": 21, "dataId": 6, "value": 1.2}], "iconIndex": 1008, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "賢者タイム", "note": "<HELP DESCRIPTION>\n\\c[110]\\fi「现在我感觉自己无所不能。」\\fr进入了贤者模式，全属性上升\n</HELP DESCRIPTION>\n\n  <Custom Passive Condition>\n  if ($gameVariables.value(25) < 0) {\n    condition = true;\n  } else {\n    condition = false;\n  }\n  </Custom Passive Condition>", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 58, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 1019, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "食物中毒", "note": "<HELP DESCRIPTION>\n\\c[110]\\fi「吃坏了肚子……」\\fr腹痛不止！\n</HELP DESCRIPTION>\n", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 59, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [], "iconIndex": 1010, "maxTurns": 999, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 999, "motion": 0, "name": "到底忘记了什么…", "note": "\n<HELP DESCRIPTION>\n……感觉忘记了什么重要的事情、总有一天会想起来吧？\n</HELP DESCRIPTION>", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 60, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 1009, "maxTurns": 999, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 999, "motion": 0, "name": "浑身恶臭", "note": "<Category: ijou>\n\n<HELP DESCRIPTION>\n身上不断散发着恶臭…\n</HELP DESCRIPTION>\n\n  <Custom Apply Effect>\n$gameSwitches.setValue(151, true)\n  </Custom Apply Effect>\n\n  <Custom Remove Effect>\n$gameSwitches.setValue(151, false)\n  </Custom Remove Effect>", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 61, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [{"code": 21, "dataId": 6, "value": 0.5}, {"code": 21, "dataId": 2, "value": 0.75}, {"code": 21, "dataId": 4, "value": 0.75}], "iconIndex": 1019, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "屁股痛…", "note": "<Category: ijou>\n<HELP DESCRIPTION>\n\\c[110]\\fi「菊花残、满地伤…」\\fr因屁股疼无法奔跑和冲刺！\n</HELP DESCRIPTION>", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 62, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 1014, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "专注", "note": "<eva Rate: 35%>\n<HELP DESCRIPTION>\n集中精力为下一击做准备，移动速度下降\n</HELP DESCRIPTION>\n\n  <Custom Remove Effect>\n$gamePlayer._moveSpeed = 8;\n$gameSystem._drill_PAlM_enabled = true;\n  </Custom Remove Effect>", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 63, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 1029, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "闪步", "note": "<eva Rate: 150%>\n<eva Flat: +150%>\n<HELP DESCRIPTION>\n\\c[110]\\fi「真红的三倍速」\n</HELP DESCRIPTION>\n\n  <Custom Apply Effect>\n$gameSwitches.setValue(203, true);\nlet rateLevel = 1.5;\nlet flatLevel = 1.5;\nif (user.hasSkill(39)) {\nlet extra = 1 + (0.3 * user.skillMasteryLevel(39));\nrateLevel *= extra;\nrateLevel = Math.floor(rateLevel * 10) / 10;\n}\n$dataStates[63].rateXParams[1] = rateLevel;\n$dataStates[63].flatXParams[1] = flatLevel\n  </Custom Apply Effect>", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 64, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 1032, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "闪步禁止", "note": "<HELP DESCRIPTION>\n闪步冷却中。\n</HELP DESCRIPTION>\n", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 65, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 1032, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "眩晕", "note": "<Category: ijou>\n<HELP DESCRIPTION>\n转得头晕了……\n</HELP DESCRIPTION>\n\n<eva Rate: 0%>\n\n  <Custom Apply Effect>\n$gameSystem._drill_PAlM_enabled = false;\n  </Custom Apply Effect>\n\n  <Custom Remove Effect>\n$gameSystem._drill_PAlM_enabled = true;\n  </Custom Remove Effect>", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 66, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 1018, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "攀瀑", "note": "<eva Flat: +500%>", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 67, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 1018, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "游泳中", "note": "<eva Rate: 30%>\n\n<HELP DESCRIPTION>\n浮在水面上。移速下降，无法攻击\n</HELP DESCRIPTION>\n", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 68, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 1016, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "吟唱中", "note": "<eva Rate: 20%>\n\n<HELP DESCRIPTION>\n魔法吟唱中。\n</HELP DESCRIPTION> \n\n <Custom Apply Effect>\n$gamePlayer._moveSpeed = 8;\n  </Custom Apply Effect>\n\n", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 69, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 70, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 1014, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "全集中", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 71, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [{"code": 22, "dataId": 7, "value": 0.07}], "iconIndex": 1042, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "美味い！！", "note": "<HELP DESCRIPTION>\n\\c[110]\\fi「吃到了美味的东西，肚子里暖暖的〜」\\fr每秒体力小回复\n</HELP DESCRIPTION>\n\n  <Custom Apply Effect>\n    let time = 3;\n    let stateId = 71;\n    let target = \"state\" + stateId;\n    if ($gameSystem.hasGameTimeEvent(target)) {\n        $gameSystem.adjustGameTimeEventDelay(target, time);\n    } else {\n        $gameSystem.addGameTimeEvent({\n            key: target,\n            command: 'remove',\n            delayMinutes: time,\n            target: stateId,\n            condition: 'true'\n        });\n    }\n  </Custom Apply Effect>", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 72, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [{"code": 21, "dataId": 2, "value": 1.5}], "iconIndex": 1015, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "力量提升", "note": "<HELP DESCRIPTION>\n\\c[110]\\fi「力量……涌上来了！」\\fr攻击力\\c[10]+50%\\c[0]\n</HELP DESCRIPTION>\n\n  <Custom Apply Effect>\n$gameActors.actor(1).setStateCounter(72, 11);\n    let time = 10;\n    let stateId = 72;\n    let target = \"state\" + stateId;\n    if ($gameSystem.hasGameTimeEvent(target)) {\n        $gameSystem.adjustGameTimeEventDelay(target, time);\n    } else {\n        $gameSystem.addGameTimeEvent({\n            key: target,\n            command: 'remove',\n            delayMinutes: time,\n            target: stateId,\n            condition: 'true'\n        });\n    }\n  </Custom Apply Effect>", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 73, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [{"code": 21, "dataId": 4, "value": 1.5}], "iconIndex": 1016, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "魔力提升", "note": "<HELP DESCRIPTION>\n\\c[110]\\fi「我逐渐理解一切。」\\fr魔攻力\\c[10]+50%\\c[0]\n</HELP DESCRIPTION>\n\n  <Custom Apply Effect>\n    let time = 10;\n    let stateId = 73;\n    let target = \"state\" + stateId;\n    if ($gameSystem.hasGameTimeEvent(target)) {\n        $gameSystem.adjustGameTimeEventDelay(target, time);\n    } else {\n        $gameSystem.addGameTimeEvent({\n            key: target,\n            command: 'remove',\n            delayMinutes: time,\n            target: stateId,\n            condition: 'true'\n        });\n    }\n  </Custom Apply Effect>", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 74, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 1017, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "速度提升", "note": "<eva Rate: 150%>\n\n<HELP DESCRIPTION>\n\\c[110]\\fi「跟得上吗……我的SPEED！」\\fr移動速度上昇する\n</HELP DESCRIPTION>\n\n  <Custom Apply Effect>\n    let time = 10;\n    let stateId = 74;\n    let target = \"state\" + stateId;\n    if ($gameSystem.hasGameTimeEvent(target)) {\n        $gameSystem.adjustGameTimeEventDelay(target, time);\n    } else {\n        $gameSystem.addGameTimeEvent({\n            key: target,\n            command: 'remove',\n            delayMinutes: time,\n            target: stateId,\n            condition: 'true'\n        });\n    }\n  </Custom Apply Effect>", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 75, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [{"code": 21, "dataId": 2, "value": 1.5}, {"code": 23, "dataId": 6, "value": 2}], "iconIndex": 1031, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "巨神兵", "note": "<HELP DESCRIPTION>\n\\c[110]\\fi「神兵天降。」\\fr状态持续期间武器巨大化、提升攻击范围和伤害\n</HELP DESCRIPTION>\n\n  <Custom Apply Effect>\n    let time = 5;\n    let stateId = 75;\n    let target = \"state\" + stateId;\n    if ($gameSystem.hasGameTimeEvent(target)) {\n        $gameSystem.adjustGameTimeEventDelay(target, time);\n    } else {\n        $gameSystem.addGameTimeEvent({\n            key: target,\n            command: 'remove',\n            delayMinutes: time,\n            target: stateId,\n            condition: 'true'\n        });\n    }\n  </Custom Apply Effect>\n\n\n", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 76, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 77, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [{"code": 21, "dataId": 0, "value": 1.1}], "iconIndex": 1042, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "火鸟的祝福", "note": "<Category: Bypass Death Removal>\n\n  <Custom Remove Effect>\n$gameScreen._particle.particleClear('fireBirdBlessing')\n  </Custom Remove Effect>", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 78, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [{"code": 21, "dataId": 7, "value": 2}], "iconIndex": 1033, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "命运之轮-正", "note": "<Category: Bypass Death Removal>\n\n<HELP DESCRIPTION>\n\\c[110]\\fi「命运的流动……」\\fr得到命运的加护，更容易遇到开心的事情了！\n</HELP DESCRIPTION>\n\n<Rare Weight: *10>\n<Epic Weight: *10>\n<Legendary Weight: *15>\n", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 79, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [{"code": 21, "dataId": 7, "value": 0.5}], "iconIndex": 1034, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "命运之轮-逆", "note": "<Category: Bypass Death Removal>\n\n<HELP DESCRIPTION>\n\\c[110]\\fi「命运流动……」\\fr得到命运的加护，更容易遇到难过的事情了！\n</HELP DESCRIPTION>\n\n<Common Weight: *10000>\n<Uncommon Weight: *500>\n", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 80, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 1029, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "闪步太刀", "note": "<eva Flat: +300%>\n<HELP DESCRIPTION>\n\\c[110]\\fi「真红的三倍速」\n</HELP DESCRIPTION>", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 81, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [{"code": 22, "dataId": 1, "value": 1.2}], "iconIndex": 1028, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "冰地板", "note": "  <Custom Apply Effect>\nQJ.MPMZ.Shoot({\n   img:\"null1\",\n   existData: [ \n      { t: [\"Time\", 60],  a: [\"S\", \"$gameParty.leader().removeState(81)\"] },\n   ],\n});\n  </Custom Apply Effect>\n\n  <Custom Remove Effect>\n$gameSwitches.setValue(14, false);\n$gamePlayer.drill_EASA_setEnabled( true );\n  </Custom Remove Effect>", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 82, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 83, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 84, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 85, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 1042, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "生命力", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 86, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [{"code": 21, "dataId": 5, "value": 0}], "iconIndex": 1047, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "中指诅咒", "note": "<def Plus: -30>\n<luk Plus: -200>\n\n  <Custom Apply Effect>\n  if ($gameSystem.hasGameTimeEvent(\"state86\")) {\n      $gameSystem.adjustGameTimeEventDelay('state86', 3);\n    } else {\n      $gameSystem.addGameTimeEvent({\n        key: 'state86',\n        command: 'remove',\n        delayMinutes: 3,\n        target: 86, \n        condition: 'true' \n      });\t\t  \n  }\n  $gamePlayer.requestAnimation(60);\n  </Custom Apply Effect>", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 87, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 88, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 89, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 90, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 91, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 92, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 93, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 94, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 95, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 96, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 97, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 98, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [{"code": 22, "dataId": 7, "value": 0.02}], "iconIndex": 1041, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "妹妹的胖次", "note": "<AS下限HP:50>\n<AS防具装備:130>\n\n<HELP DESCRIPTION>\n\\c[110]\\fi「就感觉妹妹在身边一样……」\\fr体力自动回复中。\n</HELP DESCRIPTION>\n\n  <Custom Apply Effect>\n$gameSwitches.setValue(105, true)\n  </Custom Apply Effect>", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 99, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 1040, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "骑士之剑", "note": "<atkB: 30>\n<Passive Condition: HP Below 30%>\n\n<HELP DESCRIPTION>\n\\c[110]\\fi「我等乃是守护者！」\\fr攻击力提升30。\n</HELP DESCRIPTION>", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 100, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [{"code": 23, "dataId": 6, "value": 1.3}], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "调皮的杰克", "note": "  <Custom Passive Condition>\n  var Equipped = $gameSwitches.value(150);\n  if (Equipped){\n  if (user.hp / user.mhp <= 0.3) {\n    condition = true;\n  } else {\n    condition = false;\n   }\n  } else {\n    condition = false;\n  }\n  </Custom Passive Condition>", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 101, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [{"code": 21, "dataId": 2, "value": 1.3}, {"code": 21, "dataId": 4, "value": 1.3}], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "夜雾幻影", "note": "  <Custom Passive Condition>\n  if ($gameSwitches.value(102)) {\n    condition = true;\n  } else {\n    condition = false;\n   }\n  </Custom Passive Condition>", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 102, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [{"code": 23, "dataId": 6, "value": 1.4}], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "火の加具土", "note": "  <Custom Passive Condition>\n  if ($gameSwitches.value(153) && $gameActors.actor(3).getStateCounter(8) > 0) {\n    condition = true;\n  } else {\n    condition = false;\n   }\n  </Custom Passive Condition>", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 103, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [{"code": 21, "dataId": 6, "value": 1.5}], "iconIndex": 15, "maxTurns": 99, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 99, "motion": 0, "name": "幽冥", "note": "<HELP DESCRIPTION>\n\\c[110]\\fi虚無化。\\fr状態持続中はほとんどの攻撃の影響を受けず、移動速度上昇する。\n</HELP DESCRIPTION>\n\n  <Custom Apply Effect>\n$gameSwitches.setValue(100, true)\n  </Custom Apply Effect>\n\n  <Custom Remove Effect>\n$gamePlayer.startOpacity(15, 255);\n$gameSwitches.setValue(100, false)\n  </Custom Remove Effect>", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 104, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [{"code": 21, "dataId": 6, "value": 1.5}, {"code": 22, "dataId": 1, "value": 1}, {"code": 23, "dataId": 6, "value": 2}], "iconIndex": 1031, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "皇冠剑", "note": "<HELP DESCRIPTION>\n\\c[110]\\fi「你散发着荣光」\\fr全属性上升、暂时性无敌\n</HELP DESCRIPTION>\n\n<Custom Apply Effect>\n$dataStates[104].flatParams[2] = 0;\n$dataStates[104].flatParams[4] = 0;\n$gameSwitches.setValue(100, true);\nvar ATKflat = user.paramBase(2) + user.paramPlus(2);\nvar MATflat = user.paramBase(4) + user.paramPlus(4);\nATKflat *= 2;\nMATflat *= 2;\n$dataStates[104].flatParams[2] += ATKflat;\n$dataStates[104].flatParams[4] += MATflat;\n</Custom Apply Effect>\n\n  <Custom Remove Effect>\n$gameSwitches.setValue(100, false)\n  </Custom Remove Effect>", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 105, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [{"code": 21, "dataId": 7, "value": 2}, {"code": 43, "dataId": 43, "value": 1}], "iconIndex": 1043, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "丰收的祝福", "note": "<HELP DESCRIPTION>\n\\c[110]\\fi「与汝同享此福」\\fr 获得了圣杯的祝福，获取金钱翻倍。\n</HELP DESCRIPTION>", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 106, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [{"code": 21, "dataId": 2, "value": 0.5}], "iconIndex": 1031, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "力不从心", "note": "  <Custom Passive Condition>\n  if (user.hp <= 400) {\n    condition = true;\n  } else {\n    condition = false;\n  }\n  </Custom Passive Condition>", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 107, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 1040, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "骑士足具:移速提升", "note": "<Category: refreshNeeded>\n<AS下限HP:30>\n<AS防具装備:23>\n\n<HELP DESCRIPTION>\n\\c[110]\\fi「我等乃是守护者！」\\fr移动速度提升！\n</HELP DESCRIPTION>\n\n<Custom Apply Effect>\n$dataStates[107].flatXParams = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0];\nvar skillLevel = $gameActors.actor(1).skillMasteryLevel(119);\nvar speedFlat = 0.25 + (0.2 * skillLevel);\n$dataStates[107].flatXParams[1] += speedFlat;\n</Custom Apply Effect>", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 108, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 1040, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "骑士护臂:攻防提升", "note": "<Category: refreshNeeded>\n<AS下限HP:30>\n<AS防具装備:22>\n\n<HELP DESCRIPTION>\n\\c[110]\\fi「我等乃是守护者！」\\fr攻击力、防御力提升！\n</HELP DESCRIPTION>\n\n<Custom Apply Effect>\n$dataStates[108].flatParams = [0, 0, 0, 0, 0, 0, 0, 0];\nvar skillLevel = $gameActors.actor(1).skillMasteryLevel(120);\nvar ATKflat = Math.floor(0.1 * user.atk * skillLevel);\nvar DEFflat = 2 * skillLevel;\n$dataStates[108].flatParams[2] += ATKflat;\n$dataStates[108].flatParams[3] += DEFflat;\n</Custom Apply Effect>", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 109, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 1040, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "骑士铠甲:防御提升", "note": "<Category: refreshNeeded>\n<AS下限HP:30>\n<AS防具装備:21>\n\n<HELP DESCRIPTION>\n\\c[110]\\fi「我等乃是守护者！」\\fr防御力提升！\n</HELP DESCRIPTION>\n\n<Custom Apply Effect>\n$dataStates[109].flatParams = [0, 0, 0, 0, 0, 0, 0, 0];\nvar skillLevel = $gameActors.actor(1).skillMasteryLevel(118);\nvar DEFflat = 5 * skillLevel;\n$dataStates[109].flatParams[3] += DEFflat;\n</Custom Apply Effect>", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 110, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 1040, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "骑士头盔:体防提升", "note": "<Category: refreshNeeded>\n<AS下限HP:30>\n<AS防具装備:20>\n\n<Custom Apply Effect>\n$dataStates[110].flatParams = [0, 0, 0, 0, 0, 0, 0, 0];\nvar skillLevel = $gameActors.actor(1).skillMasteryLevel(117);\nvar MHPflat = 100 * skillLevel;\nvar DEFflat = 2 * skillLevel;\n$dataStates[110].flatParams[0] += MHPflat;\n$dataStates[110].flatParams[3] += DEFflat;\n</Custom Apply Effect>", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 111, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 112, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 113, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 114, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 115, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 1042, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "稻草人的心", "note": "<AS防具装備:70>", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 116, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 1043, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "玛门的索取", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 117, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 1035, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "天上之石的共鸣", "note": "<matB: 10*$gameParty.numItems($dataItems[20])>\n", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 118, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 119, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 120, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 121, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 122, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [{"code": 22, "dataId": 7, "value": 2.33}], "iconIndex": 1042, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "无限回复", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 123, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 124, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 125, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 126, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 127, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 128, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 129, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 130, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 131, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 132, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 133, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 134, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 135, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 136, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 137, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 138, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 139, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 140, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 141, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 142, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 143, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 144, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 145, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 146, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 147, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 148, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 149, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 150, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 151, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 152, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 153, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 154, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 155, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 156, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 157, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 158, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 159, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 160, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 161, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 162, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 163, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 164, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 165, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 166, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 167, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 168, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 169, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 170, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 171, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 172, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 173, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 174, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 175, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 176, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 177, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 178, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 179, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 180, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 181, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 182, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 183, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 184, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 185, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 186, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 187, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 188, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 189, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 190, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 191, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 192, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 193, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 194, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 195, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 196, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 197, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 198, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 199, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}, {"id": 200, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "messageType": 1}]