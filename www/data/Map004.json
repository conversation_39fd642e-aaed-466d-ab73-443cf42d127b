{"autoplayBgm": false, "autoplayBgs": false, "battleback1Name": "room", "battleback2Name": "gray bar", "bgm": {"name": "木漏れ日の調べ", "pan": 0, "pitch": 100, "volume": 90}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 13, "note": "<宅>", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": true, "tilesetId": 1, "width": 13, "data": [3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 287, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "常态妹妹小人初始化", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 111, "indent": 0, "parameters": [12, "Utils.isOptionValid(\"test\")"]}, {"code": 355, "indent": 1, "parameters": ["$gameSelfVariables.setValue([1, 2, 'healing'],1)"]}, {"code": 121, "indent": 1, "parameters": [493, 493, 0]}, {"code": 121, "indent": 1, "parameters": [500, 500, 0]}, {"code": 122, "indent": 1, "parameters": [20, 20, 0, 0, -35]}, {"code": 313, "indent": 1, "parameters": [0, 2, 0, 41]}, {"code": 126, "indent": 1, "parameters": [23, 0, 0, 3]}, {"code": 126, "indent": 1, "parameters": [24, 0, 0, 3]}, {"code": 126, "indent": 1, "parameters": [25, 0, 0, 3]}, {"code": 126, "indent": 1, "parameters": [26, 0, 0, 3]}, {"code": 126, "indent": 1, "parameters": [27, 0, 0, 3]}, {"code": 126, "indent": 1, "parameters": [30, 0, 0, 1]}, {"code": 126, "indent": 1, "parameters": [31, 0, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["解除妹妹小人的点击限制"]}, {"code": 355, "indent": 0, "parameters": ["$gameSelfSwitches.setValue([$gameMap.mapId(), 15, 'D'], false);"]}, {"code": 314, "indent": 0, "parameters": [0, 2]}, {"code": 111, "indent": 0, "parameters": [12, "!$gameActors.actor(2).isStateAffected(36)"]}, {"code": 122, "indent": 1, "parameters": [19, 19, 0, 2, 45, 85]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "$gameSelfSwitches.value([$gameMap.mapId(), 42, 'A'])"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "!AudioManager._currentBgm || AudioManager._currentBgm.volume < 30"]}, {"code": 117, "indent": 2, "parameters": [25]}, {"code": 355, "indent": 2, "parameters": ["AudioManager.fadeInBgm(2)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$gameScreen.picture(5) && $gameScreen._pictures[5]._name === \"sis_chibi_normal0\" && $gameScreen.picture(5)._opacity > 250"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 231, "indent": 2, "parameters": [1, "sister_room_night_fine", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [2, "bunny<PERSON><PERSON>", 0, 0, 461, 562, 100, 100, 0, 0]}, {"code": 108, "indent": 2, "parameters": ["妹妹眨眼动画"]}, {"code": 355, "indent": 2, "parameters": ["QJ.MPMZ.deleteProjectile('ImoutoBlinking');"]}, {"code": 655, "indent": 2, "parameters": ["let IMG = \"sis_room/sis_room_dozingOff1\";"]}, {"code": 655, "indent": 2, "parameters": ["$gameScreen.showPicture(5, IMG, 0, 260, 310, 100, 100, 0, 0);"]}, {"code": 655, "indent": 2, "parameters": ["for (let i = 2; i <= 4; i++) {"]}, {"code": 655, "indent": 2, "parameters": ["    ImageManager.reservePicture(\"sis_room/sis_room_dozingOff\" + i);"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 355, "indent": 2, "parameters": ["QJ.MPMZ.Shoot({"]}, {"code": 655, "indent": 2, "parameters": ["   img:\"null1\",groupName: ['ImoutoBlinking'],"]}, {"code": 655, "indent": 2, "parameters": ["   existData: [ ],"]}, {"code": 655, "indent": 2, "parameters": ["   moveF:["]}, {"code": 655, "indent": 2, "parameters": ["     [60,0,QJ.MPMZ.tl._imoutoUtilImoutoBlinking]"]}, {"code": 655, "indent": 2, "parameters": ["   ]"]}, {"code": 655, "indent": 2, "parameters": ["});\t"]}, {"code": 231, "indent": 2, "parameters": [6, "chair hand", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 356, "indent": 2, "parameters": [">图片快捷操作 : 批量图片[2,5,6] : 修改单属性 : 透明度[255] : 时间[40]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "Utils.isMobileDevice()"]}, {"code": 356, "indent": 2, "parameters": ["P_CALL_CE 5 17 3"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["P_CALL_CE 5 17 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 356, "indent": 1, "parameters": ["P_CALL_CE 5 15 4"]}, {"code": 356, "indent": 1, "parameters": ["P_CALL_CE 5 16 5"]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 313, "indent": 1, "parameters": [0, 2, 0, 30]}, {"code": 117, "indent": 1, "parameters": [28]}, {"code": 108, "indent": 1, "parameters": ["重置监听器"]}, {"code": 355, "indent": 1, "parameters": ["QJ.MPMZ.tl._imoutoUtilCheckInitialization()"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["刷新标题画面动画"]}, {"code": 355, "indent": 0, "parameters": ["chahuiUtil.RefreshTitleScreenAnimation(\"idleInBedroom1\")"]}, {"code": 108, "indent": 0, "parameters": ["《客厅事件判断————————————————————————》"]}, {"code": 355, "indent": 0, "parameters": ["QJ.MPMZ.tl._imoutoUtilLivingRoomEventTriggerCheck.call(this)"]}, {"code": 108, "indent": 0, "parameters": ["《————————————————————————————————————》"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 117, "indent": 0, "parameters": [8]}, {"code": 313, "indent": 0, "parameters": [0, 2, 0, 30]}, {"code": 117, "indent": 0, "parameters": [25]}, {"code": 355, "indent": 0, "parameters": ["AudioManager.fadeInBgm(2)"]}, {"code": 231, "indent": 0, "parameters": [1, "sister_room_night_fine", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [2, "bunny<PERSON><PERSON>", 0, 0, 461, 562, 100, 100, 0, 0]}, {"code": 108, "indent": 0, "parameters": ["妹妹眨眼动画"]}, {"code": 355, "indent": 0, "parameters": ["let IMG = \"sis_room/sis_room_dozingOff1\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPicture(5, IMG, 0, 260, 310, 100, 100, 0, 0);"]}, {"code": 655, "indent": 0, "parameters": ["for (let i = 2; i <= 4; i++) {"]}, {"code": 655, "indent": 0, "parameters": ["    ImageManager.reservePicture(\"sis_room/sis_room_dozingOff\" + i);"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 355, "indent": 0, "parameters": ["QJ.MPMZ.Shoot({"]}, {"code": 655, "indent": 0, "parameters": ["   img:\"null1\",groupName: ['ImoutoBlinking'],"]}, {"code": 655, "indent": 0, "parameters": ["   existData: [ ],"]}, {"code": 655, "indent": 0, "parameters": ["   moveF:["]}, {"code": 655, "indent": 0, "parameters": ["     [60,0,QJ.MPMZ.tl._imoutoUtilImoutoBlinking]"]}, {"code": 655, "indent": 0, "parameters": ["   ]"]}, {"code": 655, "indent": 0, "parameters": ["});\t"]}, {"code": 231, "indent": 0, "parameters": [6, "chair hand", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 356, "indent": 0, "parameters": [">图片快捷操作 : 批量图片[2,5,6] : 修改单属性 : 透明度[255] : 时间[40]"]}, {"code": 356, "indent": 0, "parameters": ["P_CALL_CE 5 17 1"]}, {"code": 356, "indent": 0, "parameters": ["P_CALL_CE 5 15 4"]}, {"code": 356, "indent": 0, "parameters": ["P_CALL_CE 5 16 5"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [28]}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 1, "y": 12}, {"id": 2, "name": "妹妹玩游戏", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 123, "indent": 0, "parameters": ["D", 1]}, {"code": 108, "indent": 0, "parameters": ["DLC化实验"]}, {"code": 355, "indent": 0, "parameters": ["let mapId = 7;"]}, {"code": 655, "indent": 0, "parameters": ["chahuiUtil.checkMapEventExists.call(this,mapId)"]}, {"code": 231, "indent": 0, "parameters": [1, "sister_room_night_fine", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 313, "indent": 0, "parameters": [0, 2, 0, 28]}, {"code": 241, "indent": 0, "parameters": [{"name": "The-Little-Witch_s-Home", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["AudioManager.fadeInBgm(2)"]}, {"code": 245, "indent": 0, "parameters": [{"name": "The-Little-Witch_s-Home-8bit", "volume": 0, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["var IMG = 'alt_sister_normal_back';"]}, {"code": 655, "indent": 0, "parameters": ["var path = \"game_itazura\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(4, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var IMG = 'alt_sister_normal_coatHem';"]}, {"code": 655, "indent": 0, "parameters": ["var path = \"game_itazura\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(7, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var IMG = 'alt_sister_normal_hand0';"]}, {"code": 655, "indent": 0, "parameters": ["var path = \"game_itazura\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(8, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var IMG = 'alt_sister_normal_kao0';"]}, {"code": 655, "indent": 0, "parameters": ["var path = \"game_itazura\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(9, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 108, "indent": 0, "parameters": ["场景物件"]}, {"code": 355, "indent": 0, "parameters": ["var IMG = 'alt_gameConsole_active';"]}, {"code": 655, "indent": 0, "parameters": ["var path = \"game_itazura\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(30, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 121, "indent": 0, "parameters": [53, 53, 1]}, {"code": 108, "indent": 0, "parameters": ["妹妹一个人玩游戏摇手柄动画"]}, {"code": 355, "indent": 0, "parameters": ["for (let i = 0; i <= 6; i++) {"]}, {"code": 655, "indent": 0, "parameters": ["    ImageManager.reservePicture(\"game_itazura/alt_sister_normal_hand\" + i);"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 655, "indent": 0, "parameters": ["this.setWaitMode('image')"]}, {"code": 355, "indent": 0, "parameters": ["QJ.MPMZ.Shoot({"]}, {"code": 655, "indent": 0, "parameters": ["         img:\"null1\",groupName: ['ImoutoSoloPlay'],"]}, {"code": 655, "indent": 0, "parameters": ["         existData: [ "]}, {"code": 655, "indent": 0, "parameters": ["           { t: ['S',\"$gameSelfSwitches.value([$gameMap.mapId(), 2, 'D'])\",false]}"]}, {"code": 655, "indent": 0, "parameters": ["         ],"]}, {"code": 655, "indent": 0, "parameters": ["         moveF:["]}, {"code": 655, "indent": 0, "parameters": ["           [4,0,QJ.MPMZ.tl._imoutoUtilImoutoSoloPlay]"]}, {"code": 655, "indent": 0, "parameters": ["         ]"]}, {"code": 655, "indent": 0, "parameters": ["});"]}, {"code": 111, "indent": 0, "parameters": [12, "Utils.isMobileDevice()"]}, {"code": 356, "indent": 1, "parameters": ["P_CALL_CE 4 17 3"]}, {"code": 356, "indent": 1, "parameters": ["P_CALL_CE 9 17 3"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 356, "indent": 1, "parameters": ["P_CALL_CE 4 17 1"]}, {"code": 356, "indent": 1, "parameters": ["P_CALL_CE 9 17 1"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["P_CALL_CE 9 15 4"]}, {"code": 356, "indent": 0, "parameters": ["P_CALL_CE 9 16 5"]}, {"code": 117, "indent": 0, "parameters": [28]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 4, "pattern": 1, "characterIndex": 1}, "list": [{"code": 111, "indent": 0, "parameters": [12, "this.preProcess"]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(0);"]}, {"code": 231, "indent": 1, "parameters": [16, "mio_tachie_kao_happy", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 111, "indent": 1, "parameters": [12, "Math.random() > 0.5"]}, {"code": 356, "indent": 2, "parameters": ["SV_PLAY_VOICE sis_room_play_01"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["SV_PLAY_VOICE sis_room_play_06"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(1);"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["const maxPictures = 30; "]}, {"code": 655, "indent": 1, "parameters": ["for (let pictureId = 2; pictureId <= maxPictures; pictureId++) {"]}, {"code": 655, "indent": 1, "parameters": ["    let picture = $gameScreen.picture(pictureId);"]}, {"code": 655, "indent": 1, "parameters": ["    if (picture && picture.opacity() === 255) {"]}, {"code": 655, "indent": 1, "parameters": ["        picture.drill_PCE_stopEffect();"]}, {"code": 655, "indent": 1, "parameters": ["        $gameScreen.erasePicture(pictureId);"]}, {"code": 655, "indent": 1, "parameters": ["        $gameScreen.setPictureRemoveCommon(pictureId);"]}, {"code": 655, "indent": 1, "parameters": ["    }"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["播放妹妹玩游戏动画"]}, {"code": 355, "indent": 0, "parameters": ["var IMG = 'alt_sister_normal_back';"]}, {"code": 655, "indent": 0, "parameters": ["var path = \"game_itazura\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(4, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var IMG = 'alt_sister_normal_coatHem';"]}, {"code": 655, "indent": 0, "parameters": ["var path = \"game_itazura\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(7, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var IMG = 'alt_sister_normal_hand0';"]}, {"code": 655, "indent": 0, "parameters": ["var path = \"game_itazura\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(8, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var IMG = 'alt_sister_normal_kao0';"]}, {"code": 655, "indent": 0, "parameters": ["var path = \"game_itazura\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(9, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 108, "indent": 0, "parameters": ["场景物件"]}, {"code": 355, "indent": 0, "parameters": ["var IMG = 'alt_gameConsole_active';"]}, {"code": 655, "indent": 0, "parameters": ["var path = \"game_itazura\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(30, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 355, "indent": 0, "parameters": ["$gameNumberArray.setValue(41,[4,5,6,7,8,9]);"]}, {"code": 108, "indent": 0, "parameters": ["妹妹一个人玩游戏摇手柄动画"]}, {"code": 355, "indent": 0, "parameters": ["for (let i = 0; i <= 6; i++) {"]}, {"code": 655, "indent": 0, "parameters": ["    ImageManager.reservePicture(\"game_itazura/alt_sister_normal_hand\" + i);"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 655, "indent": 0, "parameters": ["this.setWaitMode('image')"]}, {"code": 355, "indent": 0, "parameters": ["QJ.MPMZ.Shoot({"]}, {"code": 655, "indent": 0, "parameters": ["         img:\"null1\",groupName: ['ImoutoSoloPlay'],"]}, {"code": 655, "indent": 0, "parameters": ["         existData: [ "]}, {"code": 655, "indent": 0, "parameters": ["         ],"]}, {"code": 655, "indent": 0, "parameters": ["         moveF:["]}, {"code": 655, "indent": 0, "parameters": ["           [4,0,QJ.MPMZ.tl._imoutoUtilImoutoSoloPlay]"]}, {"code": 655, "indent": 0, "parameters": ["         ]"]}, {"code": 655, "indent": 0, "parameters": ["});"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["…………"]}, {"code": 401, "indent": 0, "parameters": ["…………"]}, {"code": 122, "indent": 0, "parameters": [85, 85, 0, 2, 0, 3]}, {"code": 111, "indent": 0, "parameters": [1, 85, 0, 1, 2]}, {"code": 111, "indent": 1, "parameters": [1, 85, 0, 0, 2]}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(2);"]}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(3);"]}, {"code": 355, "indent": 2, "parameters": ["QJ.MPMZ.deleteProjectile('ImoutoSoloPlay');"]}, {"code": 230, "indent": 2, "parameters": [1]}, {"code": 356, "indent": 2, "parameters": [">图片快捷操作 : 批量图片[4,5,6,7,8,9] : 修改单属性 : 透明度[0] : 时间[40]"]}, {"code": 230, "indent": 2, "parameters": [30]}, {"code": 117, "indent": 2, "parameters": [26]}, {"code": 231, "indent": 2, "parameters": [16, "mio_tachie_kao_naku", 0, 0, 1000, 1600, 100, 100, 255, 0]}, {"code": 355, "indent": 2, "parameters": ["var pic_ids = $gameNumberArray.value(41);"]}, {"code": 655, "indent": 2, "parameters": ["pic_ids.forEach(function(pic_id) {"]}, {"code": 655, "indent": 2, "parameters": ["    var picture = $gameScreen.picture(pic_id);"]}, {"code": 655, "indent": 2, "parameters": ["var data = {\t\t\t\t\t\t\t\t\"type\":\"smoothMove\","]}, {"code": 655, "indent": 2, "parameters": ["\"valueX\":0,"]}, {"code": 655, "indent": 2, "parameters": ["\"valueY\":-1450,"]}, {"code": 655, "indent": 2, "parameters": ["\"time\":60,"]}, {"code": 655, "indent": 2, "parameters": ["\"cur_time\":0,\"cur_speedX\":0,\"cur_speedY\":0,}"]}, {"code": 655, "indent": 2, "parameters": ["    if (picture) { picture._drill_PSh_commandChangeTank.push(data);}});"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 117, "indent": 2, "parameters": [32]}, {"code": 356, "indent": 2, "parameters": ["SV_PLAY_VOICE sis_room_play_02"]}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(4);"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(5);"]}, {"code": 355, "indent": 2, "parameters": ["QJ.MPMZ.deleteProjectile('ImoutoSoloPlay');"]}, {"code": 230, "indent": 2, "parameters": [1]}, {"code": 356, "indent": 2, "parameters": [">图片快捷操作 : 批量图片[4,5,6,7,8,9] : 修改单属性 : 透明度[0] : 时间[40]"]}, {"code": 230, "indent": 2, "parameters": [30]}, {"code": 117, "indent": 2, "parameters": [26]}, {"code": 231, "indent": 2, "parameters": [16, "mio_tachie_kao_bimyou1", 0, 0, 1000, 1600, 100, 100, 255, 0]}, {"code": 355, "indent": 2, "parameters": ["var pic_ids = $gameNumberArray.value(41);"]}, {"code": 655, "indent": 2, "parameters": ["pic_ids.forEach(function(pic_id) {"]}, {"code": 655, "indent": 2, "parameters": ["    var picture = $gameScreen.picture(pic_id);"]}, {"code": 655, "indent": 2, "parameters": ["var data = {\t\t\t\t\t\t\t\t\"type\":\"smoothMove\","]}, {"code": 655, "indent": 2, "parameters": ["\"valueX\":0,"]}, {"code": 655, "indent": 2, "parameters": ["\"valueY\":-1450,"]}, {"code": 655, "indent": 2, "parameters": ["\"time\":60,"]}, {"code": 655, "indent": 2, "parameters": ["\"cur_time\":0,\"cur_speedX\":0,\"cur_speedY\":0,}"]}, {"code": 655, "indent": 2, "parameters": ["    if (picture) { picture._drill_PSh_commandChangeTank.push(data);}});"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 117, "indent": 2, "parameters": [32]}, {"code": 356, "indent": 2, "parameters": ["SV_PLAY_VOICE sis_room_play_03"]}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(6);"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "UI-ポン　コミカルな唇を鳴らす音", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 111, "indent": 1, "parameters": [1, 20, 0, 75, 2]}, {"code": 122, "indent": 2, "parameters": [20, 20, 1, 2, 5, 10]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 122, "indent": 1, "parameters": [17, 17, 1, 2, 5, 10]}, {"code": 117, "indent": 1, "parameters": [12]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 117, "indent": 1, "parameters": [16]}, {"code": 122, "indent": 1, "parameters": [14, 14, 0, 2, 45, 60]}, {"code": 117, "indent": 1, "parameters": [3]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 119, "indent": 1, "parameters": ["bad"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 85, 0, 3, 1]}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(7);"]}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(8);"]}, {"code": 355, "indent": 2, "parameters": ["QJ.MPMZ.deleteProjectile('ImoutoSoloPlay');"]}, {"code": 230, "indent": 2, "parameters": [1]}, {"code": 356, "indent": 2, "parameters": [">图片快捷操作 : 批量图片[4,5,6,7,8,9] : 修改单属性 : 透明度[0] : 时间[40]"]}, {"code": 230, "indent": 2, "parameters": [30]}, {"code": 117, "indent": 2, "parameters": [26]}, {"code": 231, "indent": 2, "parameters": [16, "mio_tachie_kao_smile", 0, 0, 1000, 1600, 100, 100, 255, 0]}, {"code": 355, "indent": 2, "parameters": ["var pic_ids = $gameNumberArray.value(41);"]}, {"code": 655, "indent": 2, "parameters": ["pic_ids.forEach(function(pic_id) {"]}, {"code": 655, "indent": 2, "parameters": ["    var picture = $gameScreen.picture(pic_id);"]}, {"code": 655, "indent": 2, "parameters": ["var data = {\t\t\t\t\t\t\t\t\"type\":\"smoothMove\","]}, {"code": 655, "indent": 2, "parameters": ["\"valueX\":0,"]}, {"code": 655, "indent": 2, "parameters": ["\"valueY\":-1450,"]}, {"code": 655, "indent": 2, "parameters": ["\"time\":60,"]}, {"code": 655, "indent": 2, "parameters": ["\"cur_time\":0,\"cur_speedX\":0,\"cur_speedY\":0,}"]}, {"code": 655, "indent": 2, "parameters": ["    if (picture) { picture._drill_PSh_commandChangeTank.push(data);}});"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 117, "indent": 2, "parameters": [32]}, {"code": 356, "indent": 2, "parameters": ["SV_PLAY_VOICE sis_room_play_05"]}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(9);"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(10);"]}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(11);"]}, {"code": 355, "indent": 2, "parameters": ["QJ.MPMZ.deleteProjectile('ImoutoSoloPlay');"]}, {"code": 230, "indent": 2, "parameters": [1]}, {"code": 356, "indent": 2, "parameters": [">图片快捷操作 : 批量图片[4,5,6,7,8,9] : 修改单属性 : 透明度[0] : 时间[40]"]}, {"code": 230, "indent": 2, "parameters": [30]}, {"code": 117, "indent": 2, "parameters": [26]}, {"code": 231, "indent": 2, "parameters": [16, "mio_tachie_kao_happy", 0, 0, 1000, 1600, 100, 100, 255, 0]}, {"code": 355, "indent": 2, "parameters": ["var pic_ids = $gameNumberArray.value(41);"]}, {"code": 655, "indent": 2, "parameters": ["pic_ids.forEach(function(pic_id) {"]}, {"code": 655, "indent": 2, "parameters": ["    var picture = $gameScreen.picture(pic_id);"]}, {"code": 655, "indent": 2, "parameters": ["var data = {\t\t\t\t\t\t\t\t\"type\":\"smoothMove\","]}, {"code": 655, "indent": 2, "parameters": ["\"valueX\":0,"]}, {"code": 655, "indent": 2, "parameters": ["\"valueY\":-1450,"]}, {"code": 655, "indent": 2, "parameters": ["\"time\":60,"]}, {"code": 655, "indent": 2, "parameters": ["\"cur_time\":0,\"cur_speedX\":0,\"cur_speedY\":0,}"]}, {"code": 655, "indent": 2, "parameters": ["    if (picture) { picture._drill_PSh_commandChangeTank.push(data);}});"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 117, "indent": 2, "parameters": [32]}, {"code": 356, "indent": 2, "parameters": ["SV_PLAY_VOICE sis_room_play_04"]}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(12);"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "UI-ポン　コミカルな唇を鳴らす音", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 111, "indent": 1, "parameters": [1, 20, 0, 75, 2]}, {"code": 122, "indent": 2, "parameters": [20, 20, 1, 2, 15, 20]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 122, "indent": 1, "parameters": [17, 17, 1, 2, 15, 20]}, {"code": 117, "indent": 1, "parameters": [12]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 117, "indent": 1, "parameters": [16]}, {"code": 122, "indent": 1, "parameters": [14, 14, 0, 2, 45, 60]}, {"code": 117, "indent": 1, "parameters": [3]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 119, "indent": 1, "parameters": ["good"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["bad"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(13);"]}, {"code": 119, "indent": 0, "parameters": ["演出结束"]}, {"code": 118, "indent": 0, "parameters": ["good"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(14);"]}, {"code": 119, "indent": 0, "parameters": ["演出结束"]}, {"code": 118, "indent": 0, "parameters": ["演出结束"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameSelfSwitches.value([$gameMap.mapId(), 42, 'A'])"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["QJ.MPMZ.deleteProjectile('ImoutoSoloPlay');"]}, {"code": 230, "indent": 0, "parameters": [4]}, {"code": 355, "indent": 0, "parameters": ["const maxPictures = 30; "]}, {"code": 655, "indent": 0, "parameters": ["for (let pictureId = 4; pictureId <= maxPictures; pictureId++) {"]}, {"code": 655, "indent": 0, "parameters": ["    let picture = $gameScreen.picture(pictureId);"]}, {"code": 655, "indent": 0, "parameters": ["    if (picture) {"]}, {"code": 655, "indent": 0, "parameters": ["        picture.drill_PCE_stopEffect();"]}, {"code": 655, "indent": 0, "parameters": ["        $gameScreen.erasePicture(pictureId);"]}, {"code": 655, "indent": 0, "parameters": ["    }"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 230, "indent": 0, "parameters": [4]}, {"code": 355, "indent": 0, "parameters": ["$gameMap.event(1).start()"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 117, "indent": 0, "parameters": [16]}, {"code": 108, "indent": 0, "parameters": ["修正选项组图标"]}, {"code": 355, "indent": 0, "parameters": ["$gameSystem._drill_DCB_curStyle = 25"]}, {"code": 355, "indent": 0, "parameters": ["let style = [\"eventButtons_talk\",\"eventButtons_talk2\"];"]}, {"code": 655, "indent": 0, "parameters": ["DrillUp.g_DCB_data[24].btn_src = style;"]}, {"code": 108, "indent": 0, "parameters": ["选项赋予名称"]}, {"code": 355, "indent": 0, "parameters": ["const idx   = 16;"]}, {"code": 655, "indent": 0, "parameters": ["const eid   = String(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["const mapId = $gameMap.mapId();"]}, {"code": 655, "indent": 0, "parameters": ["const key   = `MapEventDialogue${mapId}`;"]}, {"code": 655, "indent": 0, "parameters": ["const dialogueTable = window[key] || {};"]}, {"code": 655, "indent": 0, "parameters": ["const textArray     = dialogueTable[eid]?.[String(idx)];"]}, {"code": 655, "indent": 0, "parameters": ["const option1 = textArray[0];"]}, {"code": 655, "indent": 0, "parameters": ["const option2 = textArray[1];"]}, {"code": 655, "indent": 0, "parameters": [" $gameStrings.setValue(6,option1);"]}, {"code": 655, "indent": 0, "parameters": [" $gameStrings.setValue(7,option2);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(15);"]}, {"code": 102, "indent": 0, "parameters": [["\\str[6]", "\\c[15]\\str[7]"], -1, -1, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\str[6]"]}, {"code": 117, "indent": 1, "parameters": [33]}, {"code": 111, "indent": 1, "parameters": [2, "D", 0]}, {"code": 123, "indent": 2, "parameters": ["D", 1]}, {"code": 121, "indent": 2, "parameters": [40, 40, 0]}, {"code": 242, "indent": 2, "parameters": [3]}, {"code": 245, "indent": 2, "parameters": [{"name": "The-Little-Witch_s-Home-8bit", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 2, "parameters": ["AudioManager.fadeInBgs(5)"]}, {"code": 122, "indent": 2, "parameters": [13, 13, 0, 0, 24]}, {"code": 230, "indent": 2, "parameters": [30]}, {"code": 201, "indent": 2, "parameters": [0, 7, 8, 5, 0, 2]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\c[15]\\str[7]"]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(17);"]}, {"code": 242, "indent": 1, "parameters": [1]}, {"code": 111, "indent": 1, "parameters": [1, 20, 0, 40, 2]}, {"code": 122, "indent": 2, "parameters": [20, 20, 1, 2, 10, 20]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 122, "indent": 1, "parameters": [14, 14, 0, 0, 30]}, {"code": 117, "indent": 1, "parameters": [3]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["QJ.MPMZ.deleteProjectile('ImoutoSoloPlay');"]}, {"code": 230, "indent": 1, "parameters": [5]}, {"code": 355, "indent": 1, "parameters": ["const maxPictures = 90; "]}, {"code": 655, "indent": 1, "parameters": ["for (let pictureId = 1; pictureId <= maxPictures; pictureId++) {"]}, {"code": 655, "indent": 1, "parameters": ["    let picture = $gameScreen.picture(pictureId);"]}, {"code": 655, "indent": 1, "parameters": ["    if (picture) {"]}, {"code": 655, "indent": 1, "parameters": ["        $gameScreen.erasePicture(pictureId);"]}, {"code": 655, "indent": 1, "parameters": ["    }"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 230, "indent": 1, "parameters": [5]}, {"code": 355, "indent": 1, "parameters": ["$gameMap.event(1).start()"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 2, "y": 12}, {"id": 3, "name": "食物中毒事件", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["以防万一清除多余的事件效果"]}, {"code": 355, "indent": 0, "parameters": ["chahuiUtil.abortEventById.call(this, -1)"]}, {"code": 355, "indent": 0, "parameters": ["var random = 1 + Math.randomInt(4);"]}, {"code": 655, "indent": 0, "parameters": ["var seName = \"Hungry03-\" + random;"]}, {"code": 655, "indent": 0, "parameters": ["var randomPitch = Math.randomInt(40) + 80;"]}, {"code": 655, "indent": 0, "parameters": ["var se = { name: seName, volume: 100, pitch: randomPitch, pan: 0 };"]}, {"code": 655, "indent": 0, "parameters": ["AudioManager.playSe(se);"]}, {"code": 242, "indent": 0, "parameters": [1]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 108, "indent": 0, "parameters": ["随机角度震动屏幕"]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.setShakeRandom()"]}, {"code": 225, "indent": 0, "parameters": [2, 8, 60, false]}, {"code": 224, "indent": 0, "parameters": [[0, 0, 0, 153], 60, false]}, {"code": 355, "indent": 0, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["let index = 0;"]}, {"code": 655, "indent": 0, "parameters": ["let textArray = window.MapEventDialogue4[eid][String(index)];"]}, {"code": 655, "indent": 0, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 355, "indent": 0, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["let index = 1;"]}, {"code": 655, "indent": 0, "parameters": ["let textArray = window.MapEventDialogue4[eid][String(index)];"]}, {"code": 655, "indent": 0, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 241, "indent": 0, "parameters": [{"name": "毒キノコにでもあたった？", "volume": 90, "pitch": 120, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["AudioManager.fadeInBgm(1)"]}, {"code": 111, "indent": 0, "parameters": [12, "!$gameActors.actor(2).isStateAffected(27)"]}, {"code": 108, "indent": 1, "parameters": ["隐藏小人"]}, {"code": 117, "indent": 1, "parameters": [16]}, {"code": 117, "indent": 1, "parameters": [31]}, {"code": 356, "indent": 1, "parameters": [">图片快捷操作 : 批量图片[2,5] : 修改单属性 : 透明度[0] : 时间[30]"]}, {"code": 117, "indent": 1, "parameters": [26]}, {"code": 313, "indent": 1, "parameters": [0, 2, 0, 27]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 231, "indent": 1, "parameters": [16, "mio_tachie_kao_OAO", 0, 0, 1000, 1600, 100, 100, 255, 0]}, {"code": 355, "indent": 1, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.steupCEQJ(38,eid)"]}, {"code": 230, "indent": 1, "parameters": [20]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 231, "indent": 1, "parameters": [16, "mio_tachie_kao_OAO", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE foodPoisoningEvent_01"]}, {"code": 355, "indent": 0, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["let index = 2;"]}, {"code": 655, "indent": 0, "parameters": ["let textArray = window.MapEventDialogue4[eid][String(index)];"]}, {"code": 655, "indent": 0, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 355, "indent": 0, "parameters": ["var random = 1 + Math.randomInt(4);"]}, {"code": 655, "indent": 0, "parameters": ["var seName = \"Hungry03-\" + random;"]}, {"code": 655, "indent": 0, "parameters": ["var randomPitch = Math.randomInt(40) + 80;"]}, {"code": 655, "indent": 0, "parameters": ["var se = { name: seName, volume: 100, pitch: randomPitch, pan: 0 };"]}, {"code": 655, "indent": 0, "parameters": ["AudioManager.playSe(se);"]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 108, "indent": 0, "parameters": ["随机角度震动屏幕"]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.setShakeRandom()"]}, {"code": 225, "indent": 0, "parameters": [2, 8, 60, false]}, {"code": 224, "indent": 0, "parameters": [[0, 0, 0, 153], 60, false]}, {"code": 355, "indent": 0, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["let index = 3;"]}, {"code": 655, "indent": 0, "parameters": ["let textArray = window.MapEventDialogue4[eid][String(index)];"]}, {"code": 655, "indent": 0, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 355, "indent": 0, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["let index = 4;"]}, {"code": 655, "indent": 0, "parameters": ["let textArray = window.MapEventDialogue4[eid][String(index)];"]}, {"code": 655, "indent": 0, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 355, "indent": 0, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["let index = 5;"]}, {"code": 655, "indent": 0, "parameters": ["let textArray = window.MapEventDialogue4[eid][String(index)];"]}, {"code": 655, "indent": 0, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 355, "indent": 0, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["let index = 6;"]}, {"code": 655, "indent": 0, "parameters": ["let textArray = window.MapEventDialogue4[eid][String(index)];"]}, {"code": 655, "indent": 0, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 355, "indent": 0, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["let index = 7;"]}, {"code": 655, "indent": 0, "parameters": ["let textArray = window.MapEventDialogue4[eid][String(index)];"]}, {"code": 655, "indent": 0, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 108, "indent": 0, "parameters": ["痔疮差分"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameSelfVariables.value([25, 2, 'counter']) > 3"]}, {"code": 231, "indent": 1, "parameters": [16, "mio_tachie_kao_ase", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 355, "indent": 1, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 1, "parameters": ["let index = 8;"]}, {"code": 655, "indent": 1, "parameters": ["let textArray = window.MapEventDialogue4[eid][String(index)];"]}, {"code": 655, "indent": 1, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 231, "indent": 1, "parameters": [16, "mio_tachie_kao_=ω=", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE foodPoisoningEvent_02"]}, {"code": 355, "indent": 1, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 1, "parameters": ["let index = 9;"]}, {"code": 655, "indent": 1, "parameters": ["let textArray = window.MapEventDialogue4[eid][String(index)];"]}, {"code": 655, "indent": 1, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 242, "indent": 0, "parameters": [1]}, {"code": 250, "indent": 0, "parameters": [{"name": "通路を走る音", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 122, "indent": 0, "parameters": [13, 13, 0, 0, 6]}, {"code": 201, "indent": 0, "parameters": [0, 25, 4, 4, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 9, "y": 12}, {"id": 4, "name": "亲密接触部位判定", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 46, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 1, "characterIndex": 3}, "list": [{"code": 121, "indent": 0, "parameters": [46, 46, 1]}, {"code": 111, "indent": 0, "parameters": [4, 2, 6, 23]}, {"code": 355, "indent": 1, "parameters": ["if( $gameScreen.picture(20) ) {"]}, {"code": 655, "indent": 1, "parameters": [" $gameScreen.picture(20).drill_PDr_setCanDrag( false )"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 355, "indent": 1, "parameters": ["$gameMap.event(50).steupCEQJ(2)"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["if( $gameScreen.picture(20) ) {"]}, {"code": 655, "indent": 1, "parameters": [" $gameScreen.picture(20).drill_PDr_setCanDrag( false )"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 355, "indent": 1, "parameters": ["$gameMap.event(18).steupCEQJ(1)"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 26, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 4, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["摸头"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameVariables.value(205) >= (6 + 3 * $gameVariables.value(15))"]}, {"code": 355, "indent": 1, "parameters": ["let id = this._eventId;"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.event(id).steupCEQJ(4)"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["if( $gameScreen.picture(20) ) {"]}, {"code": 655, "indent": 0, "parameters": [" $gameScreen.picture(20).drill_PDr_setCanDrag( false )"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 122, "indent": 0, "parameters": [205, 205, 1, 0, 1]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 111, "indent": 0, "parameters": [4, 2, 6, 35]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE sis_room_toilet_10"]}, {"code": 231, "indent": 1, "parameters": [16, "mio_tachie_kao_gaman1", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 122, "indent": 1, "parameters": [204, 204, 0, 4, "\"呼吸\""]}, {"code": 117, "indent": 1, "parameters": [35]}, {"code": 355, "indent": 1, "parameters": ["  const IDX    = 4; "]}, {"code": 655, "indent": 1, "parameters": ["  const mapId  = $gameMap.mapId(); const eidStr = String(this._eventId); "]}, {"code": 655, "indent": 1, "parameters": ["  const key    = `MapEventDialogue${mapId}`; const table      = window[key];"]}, {"code": 655, "indent": 1, "parameters": ["  const textArray  = Array.isArray(table?.[eidStr]?.[IDX]) ? table[eidStr][IDX] : null;"]}, {"code": 655, "indent": 1, "parameters": ["  if (!textArray || textArray.length === 0) {} else {"]}, {"code": 655, "indent": 1, "parameters": ["  const randIdx = Math.random() * textArray.length | 0;"]}, {"code": 655, "indent": 1, "parameters": ["  let text    = textArray[randIdx];"]}, {"code": 655, "indent": 1, "parameters": ["  text = \"\\\\dDCOG[11:2:2:2]\\\\fs[32]\" + text;"]}, {"code": 655, "indent": 1, "parameters": ["  $gameTemp.drill_GFTT_createSimple([1480,215], text, 5, 9, 150);"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 119, "indent": 1, "parameters": ["end"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [4, 2, 6, 25]}, {"code": 231, "indent": 1, "parameters": [16, "mio_tachie_kao_ida", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 122, "indent": 1, "parameters": [204, 204, 0, 4, "\"上下震动\""]}, {"code": 117, "indent": 1, "parameters": [35]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE sis_room_ofuro_27"]}, {"code": 355, "indent": 1, "parameters": ["$gameSystem._drill_COI_map_mouse = false;"]}, {"code": 655, "indent": 1, "parameters": ["TouchInput._mousePressed = false"]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(5);"]}, {"code": 355, "indent": 1, "parameters": ["$gameSystem._drill_COI_map_mouse = true"]}, {"code": 119, "indent": 1, "parameters": ["end"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "Math.random() > 0.7"]}, {"code": 231, "indent": 1, "parameters": [16, "mio_tachie_kao_=ω=", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 231, "indent": 1, "parameters": [16, "mio_tachie_kao_-A-", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [204, 204, 0, 4, "\"呼吸\""]}, {"code": 117, "indent": 0, "parameters": [35]}, {"code": 355, "indent": 0, "parameters": ["this.count = 0"]}, {"code": 355, "indent": 0, "parameters": ["  const IDX    = 6; const mapId  = $gameMap.mapId();"]}, {"code": 655, "indent": 0, "parameters": ["  const eidStr = String(this._eventId);  const key    = `MapEventDialogue${mapId}`;"]}, {"code": 655, "indent": 0, "parameters": ["   const table  = window[key];  const textArray  = table[eidStr][IDX];"]}, {"code": 655, "indent": 0, "parameters": ["var text;"]}, {"code": 655, "indent": 0, "parameters": ["do {"]}, {"code": 655, "indent": 0, "parameters": ["    var randomIndex = Math.floor(Math.random() * textArray.length);"]}, {"code": 655, "indent": 0, "parameters": ["    text = textArray[randomIndex];"]}, {"code": 655, "indent": 0, "parameters": ["} while (text === null);"]}, {"code": 655, "indent": 0, "parameters": ["this.count = randomIndex; text = \"\\\\dDCOG[11:2:2:2]\\\\fs[32]\" + text; randomIndex += 7;"]}, {"code": 655, "indent": 0, "parameters": ["$gameTemp.drill_GFTT_createSimple( [1480,215], text, 5, 9, 150 );"]}, {"code": 655, "indent": 0, "parameters": ["var name = \"sis_room_tachie_touch_\" + randomIndex.padZero(2);"]}, {"code": 655, "indent": 0, "parameters": ["this.execPlayVoice([name, \"90\", \"100\", \"0\"], false);"]}, {"code": 118, "indent": 0, "parameters": ["end"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameScreen.picture(16) && $gameScreen.picture(16)._name == \"mio_tachie_kao_=ω=\""]}, {"code": 108, "indent": 1, "parameters": ["心情变化"]}, {"code": 355, "indent": 1, "parameters": ["let value = $gameVariables.value(20);"]}, {"code": 655, "indent": 1, "parameters": ["if (value <= 80) {"]}, {"code": 655, "indent": 1, "parameters": ["value += Math.randomInt(4);"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.setValue(20,value);"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 108, "indent": 1, "parameters": ["好感度变化"]}, {"code": 355, "indent": 1, "parameters": ["let value = $gameVariables.value(17);"]}, {"code": 655, "indent": 1, "parameters": ["value += this.count;"]}, {"code": 655, "indent": 1, "parameters": ["value += $gameParty.leader().skillMasteryLevel(61);"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.setValue(17,value);"]}, {"code": 117, "indent": 1, "parameters": [12]}, {"code": 250, "indent": 1, "parameters": [{"name": "UI-ポン　コミカルな唇を鳴らす音", "volume": 70, "pitch": 100, "pan": 0}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$gameScreen.picture(16) && $gameScreen.picture(16)._name == \"mio_tachie_kao_-A-\""]}, {"code": 108, "indent": 2, "parameters": ["心情变化"]}, {"code": 355, "indent": 2, "parameters": ["let value = $gameVariables.value(20);"]}, {"code": 655, "indent": 2, "parameters": ["if (value <= 80) {"]}, {"code": 655, "indent": 2, "parameters": ["value += Math.randomInt(5);"]}, {"code": 655, "indent": 2, "parameters": ["$gameVariables.setValue(20,value);"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 108, "indent": 2, "parameters": ["好感度变化"]}, {"code": 355, "indent": 2, "parameters": ["let value = $gameVariables.value(17);"]}, {"code": 655, "indent": 2, "parameters": ["value += this.count;"]}, {"code": 655, "indent": 2, "parameters": ["value += $gameParty.leader().skillMasteryLevel(61);"]}, {"code": 655, "indent": 2, "parameters": ["$gameVariables.setValue(17,value);"]}, {"code": 117, "indent": 2, "parameters": [12]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["if( $gameScreen.picture(20) ) {"]}, {"code": 655, "indent": 0, "parameters": [" $gameScreen.picture(20).drill_PDr_setCanDrag( true )"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 122, "indent": 0, "parameters": [14, 14, 0, 0, 10]}, {"code": 117, "indent": 0, "parameters": [3]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 118, "indent": 0, "parameters": ["end"]}, {"code": 108, "indent": 0, "parameters": ["重置亲密接触CD"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameMap.getGroupBulletListQJ('skinshipListeners').length > 0) {"]}, {"code": 655, "indent": 0, "parameters": ["    let id = $gameMap.getGroupBulletListQJ('skinshipListeners')[0];"]}, {"code": 655, "indent": 0, "parameters": ["    let bullet = $gameMap.bulletQJ(Number(id));"]}, {"code": 655, "indent": 0, "parameters": ["    bullet._suspend = false;"]}, {"code": 655, "indent": 0, "parameters": ["    bullet._coolDown += 5;"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["预加载"]}, {"code": 355, "indent": 0, "parameters": ["$gameMap.chahuiPreloadPicture(\"imoto_tachie\", \"ImotoTachie\");"]}, {"code": 655, "indent": 0, "parameters": ["this.setWaitMode('image')"]}, {"code": 111, "indent": 0, "parameters": [12, "!this.ImoutoReaction"]}, {"code": 119, "indent": 1, "parameters": ["player"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 121, "indent": 0, "parameters": [14, 14, 0]}, {"code": 355, "indent": 0, "parameters": ["if( $gameScreen.picture(20) ) {"]}, {"code": 655, "indent": 0, "parameters": [" $gameScreen.picture(20).drill_PDr_setCanDrag( false )"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["看内裤"]}, {"code": 111, "indent": 0, "parameters": [12, "this.ImoutoReaction === \"A\""]}, {"code": 355, "indent": 1, "parameters": ["$gameSystem._drill_COI_map_mouse = false;"]}, {"code": 655, "indent": 1, "parameters": ["TouchInput._mousePressed = false"]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 231, "indent": 1, "parameters": [14, "mio_tachie_T-shirt2", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 231, "indent": 1, "parameters": [16, "mio_tachie_kao_nununu", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 235, "indent": 1, "parameters": [12]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 122, "indent": 1, "parameters": [204, 204, 0, 4, "\"左右震动\""]}, {"code": 117, "indent": 1, "parameters": [35]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE hotWeatherEvent_06"]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(0);"]}, {"code": 231, "indent": 1, "parameters": [16, "mio_tachie_kao_duqi", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 355, "indent": 1, "parameters": ["chahuiUtil.imoutoOutfitloading(1000,150)"]}, {"code": 122, "indent": 1, "parameters": [16, 16, 1, 2, 5, 10]}, {"code": 122, "indent": 1, "parameters": [20, 20, 2, 0, 5]}, {"code": 119, "indent": 1, "parameters": ["end"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["露出欧派"]}, {"code": 111, "indent": 0, "parameters": [12, "this.ImoutoReaction === \"B\""]}, {"code": 355, "indent": 1, "parameters": ["$gameSystem._drill_COI_map_mouse = false;"]}, {"code": 655, "indent": 1, "parameters": ["TouchInput._mousePressed = false"]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 231, "indent": 1, "parameters": [16, "mio_tachie_kao_shy3", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE Imouto_skinShip10"]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(1);"]}, {"code": 250, "indent": 1, "parameters": [{"name": "カートゥーン系走る音", "volume": 70, "pitch": 120, "pan": 0}]}, {"code": 235, "indent": 1, "parameters": [15]}, {"code": 355, "indent": 1, "parameters": ["var pic_ids = $gameNumberArray.value(41);"]}, {"code": 655, "indent": 1, "parameters": ["pic_ids.forEach(function(pic_id) {"]}, {"code": 655, "indent": 1, "parameters": ["    var picture = $gameScreen.picture(pic_id);"]}, {"code": 655, "indent": 1, "parameters": ["    if (picture) { "]}, {"code": 655, "indent": 1, "parameters": ["picture.drill_PFOE_playHidingMoveDisappear( 20,90,720 );"]}, {"code": 655, "indent": 1, "parameters": ["}});"]}, {"code": 230, "indent": 1, "parameters": [45]}, {"code": 250, "indent": 1, "parameters": [{"name": "着替え１、シャツを着る極短、装備変更", "volume": 90, "pitch": 80, "pan": 0}]}, {"code": 355, "indent": 1, "parameters": ["$gameScreen.changePictureName(13, \"imoto_tachie/mio_tachie_boobShake1\");"]}, {"code": 355, "indent": 1, "parameters": ["$gameScreen.changePictureName(14, \"mio_tachie_T-shirt1\");"]}, {"code": 355, "indent": 1, "parameters": ["$gameScreen.changePictureName(16, \"mio_tachie_kao_shy1\");"]}, {"code": 230, "indent": 1, "parameters": [90]}, {"code": 355, "indent": 1, "parameters": ["var pic_ids = $gameNumberArray.value(41);"]}, {"code": 655, "indent": 1, "parameters": ["pic_ids.forEach(function(pic_id) {"]}, {"code": 655, "indent": 1, "parameters": ["    var picture = $gameScreen.picture(pic_id);"]}, {"code": 655, "indent": 1, "parameters": ["    if (picture) { "]}, {"code": 655, "indent": 1, "parameters": ["picture.drill_PFIE_playShowingMoveAppear( 60,90,600 );"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 655, "indent": 1, "parameters": ["});"]}, {"code": 230, "indent": 1, "parameters": [45]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE Imouto_skinShip11"]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(2);"]}, {"code": 122, "indent": 1, "parameters": [16, 16, 1, 2, 10, 15]}, {"code": 122, "indent": 1, "parameters": [20, 20, 0, 0, 30]}, {"code": 119, "indent": 1, "parameters": ["end"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["player"]}, {"code": 108, "indent": 0, "parameters": ["播放T恤互动动画"]}, {"code": 111, "indent": 0, "parameters": [12, "this.skinShip"]}, {"code": 250, "indent": 1, "parameters": [{"name": "ポリエステルの布の衣擦れ_1", "volume": 100, "pitch": 150, "pan": 0}]}, {"code": 355, "indent": 1, "parameters": ["let type = this.skinShip;"]}, {"code": 655, "indent": 1, "parameters": ["QJ.MPMZ.Shoot({"]}, {"code": 655, "indent": 1, "parameters": ["      img:\"null1\","]}, {"code": 655, "indent": 1, "parameters": ["      existData: [ ],"]}, {"code": 655, "indent": 1, "parameters": ["      moveF:["]}, {"code": 655, "indent": 1, "parameters": ["            [15,0,QJ.MPMZ.tl._imoutoUtilTuggingOnTshirt,[type]]"]}, {"code": 655, "indent": 1, "parameters": ["       ],"]}, {"code": 655, "indent": 1, "parameters": ["      deadJS:["]}, {"code": 655, "indent": 1, "parameters": ["            `$gameMap.event(4).steupCEQJ(3,{ImoutoReaction:'${type}'})`"]}, {"code": 655, "indent": 1, "parameters": ["       ]"]}, {"code": 655, "indent": 1, "parameters": ["});"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 115, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["end"]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 122, "indent": 0, "parameters": [14, 14, 0, 0, 5]}, {"code": 117, "indent": 0, "parameters": [3]}, {"code": 121, "indent": 0, "parameters": [14, 14, 1]}, {"code": 355, "indent": 0, "parameters": ["let value = $gameSelfVariables.value([$gameMap.mapId(), 50, 'times']);"]}, {"code": 655, "indent": 0, "parameters": ["value += 1;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSelfVariables.setValue([$gameMap.mapId(), 50, 'times'], value);"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameSelfVariables.value([$gameMap.mapId(), 50, 'times']) >= 4"]}, {"code": 355, "indent": 1, "parameters": ["let id = 50;"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.event(id).steupCEQJ(2)"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["if( $gameScreen.picture(20) ) {"]}, {"code": 655, "indent": 0, "parameters": [" $gameScreen.picture(20).drill_PDr_setCanDrag( true )"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 355, "indent": 0, "parameters": ["$gameSystem._drill_COI_map_mouse = true"]}, {"code": 108, "indent": 0, "parameters": ["重置亲密接触CD"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameMap.getGroupBulletListQJ('skinshipListeners').length > 0) {"]}, {"code": 655, "indent": 0, "parameters": ["    let id = $gameMap.getGroupBulletListQJ('skinshipListeners')[0];"]}, {"code": 655, "indent": 0, "parameters": ["    let bullet = $gameMap.bulletQJ(Number(id));"]}, {"code": 655, "indent": 0, "parameters": ["    bullet._suspend = false;"]}, {"code": 655, "indent": 0, "parameters": ["    bullet._coolDown = 25;"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 313, "indent": 0, "parameters": [0, 2, 0, 37]}, {"code": 121, "indent": 0, "parameters": [46, 46, 1]}, {"code": 231, "indent": 0, "parameters": [16, "mio_tachie_kao_ida", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 122, "indent": 0, "parameters": [204, 204, 0, 4, "\"小跳\""]}, {"code": 117, "indent": 0, "parameters": [35]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE sis_room_tachie_touch_01"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(7);"]}, {"code": 122, "indent": 0, "parameters": [20, 20, 2, 0, 8]}, {"code": 231, "indent": 0, "parameters": [16, "mio_tachie_kao_duqi", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 111, "indent": 0, "parameters": [4, 2, 6, 23]}, {"code": 355, "indent": 1, "parameters": ["$gameMap.event(50).steupCEQJ(2)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameMap.event(18).steupCEQJ(1)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 12, "y": 11}, {"id": 5, "name": "亲密接触判定", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 28, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [12, "$gameMessage.isBusy()"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["if( $gameScreen.picture(20) ) {"]}, {"code": 655, "indent": 0, "parameters": [" $gameScreen.picture(20).drill_PDr_setCanDrag( false )"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 111, "indent": 0, "parameters": [4, 2, 6, 35]}, {"code": 355, "indent": 1, "parameters": ["$gameSystem._drill_COI_map_mouse = false;"]}, {"code": 655, "indent": 1, "parameters": ["TouchInput._mousePressed = false"]}, {"code": 231, "indent": 1, "parameters": [16, "mio_tachie_kao_nununu2", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 122, "indent": 1, "parameters": [204, 204, 0, 4, "\"左右震动\""]}, {"code": 117, "indent": 1, "parameters": [35]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE sis_room_toilet_11"]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(2);"]}, {"code": 119, "indent": 1, "parameters": ["end"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [4, 2, 6, 23]}, {"code": 355, "indent": 1, "parameters": ["$gameMap.event(5).steupCEQJ(4,{actionType:\"stroke\"});"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [4, 2, 6, 25]}, {"code": 111, "indent": 1, "parameters": [12, "$gameSelfSwitches.value([$gameMap.mapId(), 10, 'B'])"]}, {"code": 355, "indent": 2, "parameters": ["$gameSystem._drill_COI_map_mouse = false;"]}, {"code": 655, "indent": 2, "parameters": ["TouchInput._mousePressed = false"]}, {"code": 250, "indent": 2, "parameters": [{"name": "037myuu_YumeSE_FukidashiHatena01", "volume": 70, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 2, "parameters": [13, "mio_tachie_bathtowel1", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 230, "indent": 2, "parameters": [1]}, {"code": 231, "indent": 2, "parameters": [16, "mio_tachie_kao_shy3", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 122, "indent": 2, "parameters": [204, 204, 0, 4, "\"上下震动\""]}, {"code": 117, "indent": 2, "parameters": [35]}, {"code": 356, "indent": 2, "parameters": ["SV_PLAY_VOICE sis_room_ofuro_31"]}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(3);"]}, {"code": 250, "indent": 2, "parameters": [{"name": "着替え１、シャツを着る極短、装備変更", "volume": 90, "pitch": 80, "pan": 0}]}, {"code": 231, "indent": 2, "parameters": [13, "mio_tachie_bathtowel0", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [16, "mio_tachie_kao_shy1", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 122, "indent": 2, "parameters": [204, 204, 0, 4, "\"左右震动\""]}, {"code": 117, "indent": 2, "parameters": [35]}, {"code": 356, "indent": 2, "parameters": ["SV_PLAY_VOICE sis_room_ofuro_32"]}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(4);"]}, {"code": 119, "indent": 2, "parameters": ["end"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 119, "indent": 2, "parameters": ["洗澡后"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["洗澡后"]}, {"code": 111, "indent": 0, "parameters": [1, 18, 0, 1, 2]}, {"code": 355, "indent": 1, "parameters": ["$gameSystem._drill_COI_map_mouse = false;"]}, {"code": 655, "indent": 1, "parameters": ["TouchInput._mousePressed = false"]}, {"code": 231, "indent": 1, "parameters": [16, "mio_tachie_kao_shy3", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 122, "indent": 1, "parameters": [204, 204, 0, 4, "\"左右震动\""]}, {"code": 117, "indent": 1, "parameters": [35]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE sis_room_tachie_touch_03"]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(5);"]}, {"code": 231, "indent": 1, "parameters": [16, "mio_tachie_kao_smile2", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameSystem._drill_COI_map_mouse = false;"]}, {"code": 655, "indent": 1, "parameters": ["TouchInput._mousePressed = false"]}, {"code": 231, "indent": 1, "parameters": [16, "mio_tachie_kao_duqi", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 122, "indent": 1, "parameters": [204, 204, 0, 4, "\"左右震动\""]}, {"code": 117, "indent": 1, "parameters": [35]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE sis_room_tachie_touch_04"]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(6);"]}, {"code": 231, "indent": 1, "parameters": [16, "mio_tachie_kao_duqi", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 119, "indent": 0, "parameters": ["end"]}, {"code": 118, "indent": 0, "parameters": ["end"]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 122, "indent": 0, "parameters": [14, 14, 0, 0, 5]}, {"code": 117, "indent": 0, "parameters": [3]}, {"code": 121, "indent": 0, "parameters": [14, 14, 1]}, {"code": 355, "indent": 0, "parameters": ["if( $gameScreen.picture(20) ) {"]}, {"code": 655, "indent": 0, "parameters": [" $gameScreen.picture(20).drill_PDr_setCanDrag( true )"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 355, "indent": 0, "parameters": ["$gameSystem._drill_COI_map_mouse = true"]}, {"code": 108, "indent": 0, "parameters": ["重置亲密接触CD"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameMap.getGroupBulletListQJ('skinshipListeners').length > 0) {"]}, {"code": 655, "indent": 0, "parameters": ["    let id = $gameMap.getGroupBulletListQJ('skinshipListeners')[0];"]}, {"code": 655, "indent": 0, "parameters": ["    let bullet = $gameMap.bulletQJ(Number(id));"]}, {"code": 655, "indent": 0, "parameters": ["    bullet._suspend = false;"]}, {"code": 655, "indent": 0, "parameters": ["    bullet._coolDown += 5;"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 111, "indent": 0, "parameters": [4, 2, 6, 23]}, {"code": 355, "indent": 1, "parameters": ["let value = $gameSelfVariables.value([$gameMap.mapId(), 50, 'times']);"]}, {"code": 655, "indent": 1, "parameters": ["value += 1;"]}, {"code": 655, "indent": 1, "parameters": ["$gameSelfVariables.setValue([$gameMap.mapId(), 50, 'times'], value);"]}, {"code": 111, "indent": 1, "parameters": [12, "$gameSelfVariables.value([$gameMap.mapId(), 50, 'times']) >= 4"]}, {"code": 355, "indent": 2, "parameters": ["let id = 50;"]}, {"code": 655, "indent": 2, "parameters": ["$gameMap.event(id).steupCEQJ(2)"]}, {"code": 115, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 29, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [12, "$gameMessage.isBusy()"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["end"]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 122, "indent": 0, "parameters": [14, 14, 0, 0, 5]}, {"code": 117, "indent": 0, "parameters": [3]}, {"code": 121, "indent": 0, "parameters": [14, 14, 1]}, {"code": 355, "indent": 0, "parameters": ["if( $gameScreen.picture(20) ) {"]}, {"code": 655, "indent": 0, "parameters": [" $gameScreen.picture(20).drill_PDr_setCanDrag( true )"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 355, "indent": 0, "parameters": ["$gameSystem._drill_COI_map_mouse = true"]}, {"code": 108, "indent": 0, "parameters": ["重置亲密接触CD"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameMap.getGroupBulletListQJ('skinshipListeners').length > 0) {"]}, {"code": 655, "indent": 0, "parameters": ["    let id = $gameMap.getGroupBulletListQJ('skinshipListeners')[0];"]}, {"code": 655, "indent": 0, "parameters": ["    let bullet = $gameMap.bulletQJ(Number(id));"]}, {"code": 655, "indent": 0, "parameters": ["    bullet._suspend = false;"]}, {"code": 655, "indent": 0, "parameters": ["    bullet._coolDown += 5;"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 111, "indent": 0, "parameters": [4, 2, 6, 23]}, {"code": 355, "indent": 1, "parameters": ["let value = $gameSelfVariables.value([$gameMap.mapId(), 50, 'times']);"]}, {"code": 655, "indent": 1, "parameters": ["value += 1;"]}, {"code": 655, "indent": 1, "parameters": ["$gameSelfVariables.setValue([$gameMap.mapId(), 50, 'times'], value);"]}, {"code": 111, "indent": 1, "parameters": [12, "$gameSelfVariables.value([$gameMap.mapId(), 50, 'times']) >= 4"]}, {"code": 355, "indent": 2, "parameters": ["let id = 50;"]}, {"code": 655, "indent": 2, "parameters": ["$gameMap.event(id).steupCEQJ(2)"]}, {"code": 115, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 30, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [12, "$gameMessage.isBusy()"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 14, 1]}, {"code": 121, "indent": 1, "parameters": [14, 14, 0]}, {"code": 355, "indent": 1, "parameters": ["if( $gameScreen.picture(20) ) {"]}, {"code": 655, "indent": 1, "parameters": [" $gameScreen.picture(20).drill_PDr_setCanDrag( false )"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 355, "indent": 1, "parameters": ["$gameSystem._drill_COI_map_mouse = false;"]}, {"code": 655, "indent": 1, "parameters": ["TouchInput._mousePressed = false"]}, {"code": 111, "indent": 1, "parameters": [4, 2, 6, 35]}, {"code": 231, "indent": 2, "parameters": [16, "mio_tachie_kao_nununu2", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 122, "indent": 2, "parameters": [204, 204, 0, 4, "\"左右震动\""]}, {"code": 356, "indent": 2, "parameters": ["SV_PLAY_VOICE sis_room_toilet_11"]}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(2);"]}, {"code": 117, "indent": 2, "parameters": [35]}, {"code": 119, "indent": 2, "parameters": ["end"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 231, "indent": 1, "parameters": [16, "mio_tachie_kao_happy", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 122, "indent": 1, "parameters": [204, 204, 0, 4, "\"左右震动\""]}, {"code": 117, "indent": 1, "parameters": [35]}, {"code": 111, "indent": 1, "parameters": [12, "Math.randomInt(101) > 50"]}, {"code": 356, "indent": 2, "parameters": ["SV_PLAY_VOICE sis_room_tachie_touch_11"]}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(13);"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["SV_PLAY_VOICE sis_room_tachie_touch_02"]}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(14);"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 231, "indent": 1, "parameters": [16, "mio_tachie_kao_smile2", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 119, "indent": 1, "parameters": ["end"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 119, "indent": 0, "parameters": ["end"]}, {"code": 118, "indent": 0, "parameters": ["end"]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 122, "indent": 0, "parameters": [14, 14, 0, 0, 5]}, {"code": 117, "indent": 0, "parameters": [3]}, {"code": 121, "indent": 0, "parameters": [14, 14, 1]}, {"code": 355, "indent": 0, "parameters": ["if( $gameScreen.picture(20) ) {"]}, {"code": 655, "indent": 0, "parameters": [" $gameScreen.picture(20).drill_PDr_setCanDrag( true )"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 355, "indent": 0, "parameters": ["$gameSystem._drill_COI_map_mouse = true"]}, {"code": 108, "indent": 0, "parameters": ["重置亲密接触CD"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameMap.getGroupBulletListQJ('skinshipListeners').length > 0) {"]}, {"code": 655, "indent": 0, "parameters": ["    let id = $gameMap.getGroupBulletListQJ('skinshipListeners')[0];"]}, {"code": 655, "indent": 0, "parameters": ["    let bullet = $gameMap.bulletQJ(Number(id));"]}, {"code": 655, "indent": 0, "parameters": ["    bullet._suspend = false;"]}, {"code": 655, "indent": 0, "parameters": ["    bullet._coolDown += 5;"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [12, "!this.actionType || !this.touchPoint"]}, {"code": 119, "indent": 1, "parameters": ["end"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["$gameSystem._drill_COI_map_mouse = false;"]}, {"code": 111, "indent": 0, "parameters": [1, 20, 0, 35, 2]}, {"code": 111, "indent": 1, "parameters": [12, "this.actionType === \"poke\""]}, {"code": 250, "indent": 2, "parameters": [{"name": "041myuu_YumeSE_FukidashiAngry01", "volume": 40, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 2, "parameters": [30]}, {"code": 231, "indent": 2, "parameters": [16, "mio_tachie_kao_nununu2", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 122, "indent": 2, "parameters": [204, 204, 0, 4, "\"上下震动\""]}, {"code": 117, "indent": 2, "parameters": [35]}, {"code": 355, "indent": 2, "parameters": ["var name = \"Imouto_skinShip07_\" + Math.randomInt(2);"]}, {"code": 655, "indent": 2, "parameters": ["var args = [name, \"90\", \"100\", \"0\"];"]}, {"code": 655, "indent": 2, "parameters": ["this.execPlayVoice(args, false);"]}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(21,0);"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 250, "indent": 2, "parameters": [{"name": "037myuu_YumeSE_FukidashiHatena01", "volume": 30, "pitch": 80, "pan": 0}]}, {"code": 230, "indent": 2, "parameters": [30]}, {"code": 231, "indent": 2, "parameters": [16, "mio_tachie_kao_shy1", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 122, "indent": 2, "parameters": [204, 204, 0, 4, "\"上下震动\""]}, {"code": 117, "indent": 2, "parameters": [35]}, {"code": 355, "indent": 2, "parameters": ["var name = \"Imouto_skinShip08_\" + Math.randomInt(2);"]}, {"code": 655, "indent": 2, "parameters": ["var args = [name, \"90\", \"100\", \"0\"];"]}, {"code": 655, "indent": 2, "parameters": ["this.execPlayVoice(args, false);"]}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(21,1);"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 119, "indent": 1, "parameters": ["end"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "this.touchPoint === \"navel\""]}, {"code": 250, "indent": 1, "parameters": [{"name": "037myuu_YumeSE_FukidashiHatena01", "volume": 30, "pitch": 80, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 231, "indent": 1, "parameters": [16, "mio_tachie_kao_naku", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 122, "indent": 1, "parameters": [204, 204, 0, 4, "\"上下震动\""]}, {"code": 117, "indent": 1, "parameters": [35]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE Imouto_PatTummy"]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(18);"]}, {"code": 119, "indent": 1, "parameters": ["end"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "this.touchPoint === \"ear\""]}, {"code": 250, "indent": 1, "parameters": [{"name": "037myuu_YumeSE_FukidashiHatena01", "volume": 30, "pitch": 80, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 231, "indent": 1, "parameters": [16, "mio_tachie_kao_nununu", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE Imouto_skinShip01"]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(19);"]}, {"code": 119, "indent": 1, "parameters": ["end"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "this.touchPoint === \"clavicle\""]}, {"code": 250, "indent": 1, "parameters": [{"name": "037myuu_YumeSE_FukidashiHatena01", "volume": 30, "pitch": 80, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 111, "indent": 1, "parameters": [2, "D", 0]}, {"code": 231, "indent": 2, "parameters": [16, "mio_tachie_kao_shy3", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["SV_PLAY_VOICE Imouto_skinShip06"]}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(22);"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 231, "indent": 2, "parameters": [16, "mio_tachie_kao_smile2", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["SV_PLAY_VOICE Imouto_skinShip09"]}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(20);"]}, {"code": 123, "indent": 2, "parameters": ["D", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 119, "indent": 1, "parameters": ["end"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["end"]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 122, "indent": 0, "parameters": [14, 14, 0, 0, 5]}, {"code": 117, "indent": 0, "parameters": [3]}, {"code": 121, "indent": 0, "parameters": [14, 14, 1]}, {"code": 355, "indent": 0, "parameters": ["if( $gameScreen.picture(20) ) {"]}, {"code": 655, "indent": 0, "parameters": [" $gameScreen.picture(20).drill_PDr_setCanDrag( true )"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 355, "indent": 0, "parameters": ["$gameSystem._drill_COI_map_mouse = true"]}, {"code": 108, "indent": 0, "parameters": ["重置亲密接触CD"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameMap.getGroupBulletListQJ('skinshipListeners').length > 0) {"]}, {"code": 655, "indent": 0, "parameters": ["    let id = $gameMap.getGroupBulletListQJ('skinshipListeners')[0];"]}, {"code": 655, "indent": 0, "parameters": ["    let bullet = $gameMap.bulletQJ(Number(id));"]}, {"code": 655, "indent": 0, "parameters": ["    bullet._suspend = false;"]}, {"code": 655, "indent": 0, "parameters": ["    bullet._coolDown += 5;"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 12, "y": 10}, null, {"id": 7, "name": "夜间场景初始化", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 111, "indent": 0, "parameters": [12, "Utils.isOptionValid(\"test\")"]}, {"code": 355, "indent": 1, "parameters": ["$gameSelfVariables.setValue([1, 2, 'nightVisit'], 3)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["妹妹呆毛怪物"]}, {"code": 111, "indent": 0, "parameters": [12, "this.ahog<PERSON><PERSON><PERSON><PERSON>"]}, {"code": 355, "indent": 1, "parameters": ["let id = this._eventId;"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.event(id).steupCEQJ(4)"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["防其他场景干扰"]}, {"code": 355, "indent": 0, "parameters": ["var MAX = 100;"]}, {"code": 655, "indent": 0, "parameters": ["var dataBind = $gameSystem._drill_GFV_bindTank[ 8 ];"]}, {"code": 655, "indent": 0, "parameters": ["dataBind['slot_list'][ 0 ]['level_max'] = MAX;"]}, {"code": 655, "indent": 0, "parameters": ["dataBind['commandParamChanged'] = true;"]}, {"code": 241, "indent": 0, "parameters": [{"name": "おやすみ、夢の中", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["AudioManager.fadeInBgm(2)"]}, {"code": 313, "indent": 0, "parameters": [0, 2, 0, 31]}, {"code": 122, "indent": 0, "parameters": [19, 19, 0, 2, 15, 30]}, {"code": 111, "indent": 0, "parameters": [4, 2, 6, 41]}, {"code": 313, "indent": 1, "parameters": [0, 2, 1, 41]}, {"code": 122, "indent": 1, "parameters": [19, 19, 2, 2, 50, 75]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [4, 2, 6, 36]}, {"code": 122, "indent": 1, "parameters": [19, 19, 2, 2, 200, 300]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 231, "indent": 0, "parameters": [1, "sister_room_night2_fine", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 118, "indent": 0, "parameters": ["重新生成"]}, {"code": 108, "indent": 0, "parameters": ["睡姿变化"]}, {"code": 355, "indent": 0, "parameters": ["let imageName = \"sis_chibi_sleep1_night\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPicture(5, imageName, 0, 0, 0, 100, 100, 255, 0);"]}, {"code": 111, "indent": 0, "parameters": [12, "!this.secondNightVisit"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": [">图片与鼠标控制核心 : 图片[5] : 修改为像素判定"]}, {"code": 356, "indent": 0, "parameters": [">鼠标悬停触发图片 : 图片[5] : 绑定设置[1] : 绑定单次触发-悬停[一帧]时 : 执行公共事件[15]"]}, {"code": 356, "indent": 0, "parameters": [">鼠标悬停触发图片 : 图片[5] : 绑定设置[2] : 绑定单次触发-离开悬停[一帧]时 : 执行公共事件[16]"]}, {"code": 117, "indent": 0, "parameters": [6]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 111, "indent": 0, "parameters": [12, "this.secondNightVisit"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["…………"]}, {"code": 401, "indent": 1, "parameters": ["…………"]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(17);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "!$gameScreen.picture(5)"]}, {"code": 119, "indent": 1, "parameters": ["重新生成"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.event(eid).steupCEQJ(2);"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 1, "characterIndex": 3}, "list": [{"code": 111, "indent": 0, "parameters": [12, "this.needSkip"]}, {"code": 119, "indent": 1, "parameters": ["选项"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["触发夜袭流程"]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen._pictureCidArray = [];"]}, {"code": 655, "indent": 0, "parameters": ["let times = $gameSelfVariables.value([1, 2, 'nightVisit']);"]}, {"code": 655, "indent": 0, "parameters": ["times += 1;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSelfVariables.setValue([1, 2, 'nightVisit'], times);"]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 250, "indent": 0, "parameters": [{"name": "Open5", "volume": 75, "pitch": 150, "pan": 0}]}, {"code": 231, "indent": 0, "parameters": [2, "sister_room_night_visit", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["let imageName = $gameScreen.picture(5)._name + \"A\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPicture(6, imageName, 0, 0, 0, 100, 100, 0, 0);"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 356, "indent": 0, "parameters": [">图片快捷操作 : 批量图片[2,6] : 修改单属性 : 透明度[255] : 时间[60]"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 118, "indent": 0, "parameters": ["选项"]}, {"code": 108, "indent": 0, "parameters": ["选项赋予名称"]}, {"code": 355, "indent": 0, "parameters": ["const idx   = 1;"]}, {"code": 655, "indent": 0, "parameters": ["const eid   = String(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["const mapId = $gameMap.mapId();"]}, {"code": 655, "indent": 0, "parameters": ["const key   = `MapEventDialogue${mapId}`;"]}, {"code": 655, "indent": 0, "parameters": ["const dialogueTable = window[key] || {};"]}, {"code": 655, "indent": 0, "parameters": ["const textArray     = dialogueTable[eid]?.[String(idx)];"]}, {"code": 655, "indent": 0, "parameters": ["const option1 = textArray[0];"]}, {"code": 655, "indent": 0, "parameters": ["const option2 = textArray[1];"]}, {"code": 655, "indent": 0, "parameters": ["const option3 = textArray[2];"]}, {"code": 655, "indent": 0, "parameters": [" $gameStrings.setValue(6,option1);"]}, {"code": 655, "indent": 0, "parameters": [" $gameStrings.setValue(7,option2);"]}, {"code": 655, "indent": 0, "parameters": [" $gameStrings.setValue(8,option3);"]}, {"code": 108, "indent": 0, "parameters": ["修正选项组图标"]}, {"code": 355, "indent": 0, "parameters": ["$gameSystem._drill_DCB_curStyle = 25"]}, {"code": 355, "indent": 0, "parameters": ["let style = [];"]}, {"code": 655, "indent": 0, "parameters": ["$gameSelfVariables.value([1, 2, 'nightVisit']) > 1 ? style.push(\"eventButtons_hot\") : null,"]}, {"code": 655, "indent": 0, "parameters": ["style.push(\"eventButtons_sleep\");"]}, {"code": 655, "indent": 0, "parameters": ["DrillUp.g_DCB_data[24].btn_src = style;"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(0);"]}, {"code": 102, "indent": 0, "parameters": [["<<v[1]!==99>>\\str[6]", "<<$gameSelfVariables.value([1, 2, 'nightVisit'])<2>>\\c[15]\\str[7]", "\\c[15]\\str[8]"], -1, -1, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "<<v[1]!==99>>\\str[6]"]}, {"code": 119, "indent": 1, "parameters": ["1"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "<<$gameSelfVariables.value([1, 2, 'nightVisit'])<2>>\\c[15]\\str[7]"]}, {"code": 355, "indent": 1, "parameters": ["let id = this._eventId;"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.event(id).steupCEQJ(3)"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "\\c[15]\\str[8]"]}, {"code": 118, "indent": 1, "parameters": ["1"]}, {"code": 108, "indent": 1, "parameters": ["选项赋予名称"]}, {"code": 355, "indent": 1, "parameters": ["const idx   = 5;"]}, {"code": 655, "indent": 1, "parameters": ["const eid   = String(this._eventId);"]}, {"code": 655, "indent": 1, "parameters": ["const mapId = $gameMap.mapId();"]}, {"code": 655, "indent": 1, "parameters": ["const key   = `MapEventDialogue${mapId}`;"]}, {"code": 655, "indent": 1, "parameters": ["const dialogueTable = window[key] || {};"]}, {"code": 655, "indent": 1, "parameters": ["const textArray     = dialogueTable[eid]?.[String(idx)];"]}, {"code": 655, "indent": 1, "parameters": ["const option1 = textArray[0];"]}, {"code": 655, "indent": 1, "parameters": ["const option2 = textArray[1];"]}, {"code": 655, "indent": 1, "parameters": [" $gameStrings.setValue(6,option1);"]}, {"code": 655, "indent": 1, "parameters": [" $gameStrings.setValue(7,option2);"]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(4);"]}, {"code": 108, "indent": 1, "parameters": ["修正选项组图标"]}, {"code": 355, "indent": 1, "parameters": ["$gameSystem._drill_DCB_curStyle = 25"]}, {"code": 355, "indent": 1, "parameters": ["let style = [\"eventButtons_sleep2\", \"eventButtons_returns\"];"]}, {"code": 655, "indent": 1, "parameters": ["DrillUp.g_DCB_data[24].btn_src = style;"]}, {"code": 102, "indent": 1, "parameters": [["\\str[6]", "\\c[15]\\str[7]"], 1, -1, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\str[6]"]}, {"code": 117, "indent": 2, "parameters": [9]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\c[15]\\str[7]"]}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(6);"]}, {"code": 119, "indent": 2, "parameters": ["选项"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(7);"]}, {"code": 231, "indent": 0, "parameters": [70, "", 0, 0, 0, 0, 100, 100, 0, 2]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[70] : 创建动画序列 : 动画序列[5]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[70] : 播放简单状态元集合 : 集合[集中线]"]}, {"code": 232, "indent": 0, "parameters": [70, 0, 0, 0, 0, 0, 100, 100, 255, 2, 30, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "Explosion2", "volume": 40, "pitch": 90, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(8);"]}, {"code": 108, "indent": 0, "parameters": ["随机角度震动屏幕"]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.setShakeRandom()"]}, {"code": 225, "indent": 0, "parameters": [1, 7, 999, false]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(9);"]}, {"code": 111, "indent": 0, "parameters": [12, "Math.random() > 0.5"]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(10);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(11);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 232, "indent": 0, "parameters": [70, 0, 0, 0, 0, 0, 100, 100, 0, 2, 30, false]}, {"code": 122, "indent": 0, "parameters": [14, 14, 0, 0, 15]}, {"code": 117, "indent": 0, "parameters": [3]}, {"code": 108, "indent": 0, "parameters": ["锻炼成果"]}, {"code": 355, "indent": 0, "parameters": ["var actor = $gameActors.actor(1);"]}, {"code": 655, "indent": 0, "parameters": ["if (actor.is<PERSON><PERSON><PERSON><PERSON><PERSON>(10)) {"]}, {"code": 655, "indent": 0, "parameters": ["   QJ.MPMZ.tl.ex_playerStealthProficiencyIncreased(5);"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 655, "indent": 0, "parameters": ["actor.addMaxHp(2);"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen._shakeDuration = 0;"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(12);"]}, {"code": 235, "indent": 0, "parameters": [70]}, {"code": 122, "indent": 0, "parameters": [19, 19, 1, 2, 10, 15]}, {"code": 111, "indent": 0, "parameters": [12, "$gameVariables.value(19) >= 50 || ($gameSystem.hour() > 1 && $gameSystem.hour() < 18)"]}, {"code": 250, "indent": 1, "parameters": [{"name": "014myuu_YumeSE_SystemBuzzer03", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": [">持续动作 : 图片[6] : 左右震动 : 持续时间[20] : 周期[10] : 震动幅度[2]"]}, {"code": 230, "indent": 1, "parameters": [10]}, {"code": 355, "indent": 1, "parameters": ["var name = \"sis_room_itazura01_\" + Math.randomInt(3);"]}, {"code": 655, "indent": 1, "parameters": ["var args = [name, \"40\", \"100\", \"0\"];"]}, {"code": 655, "indent": 1, "parameters": ["this.execPlayVoice(args, false);"]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(13);"]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(14);"]}, {"code": 117, "indent": 1, "parameters": [9]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["let id = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.event(id).steupCEQJ(2,{needSkip:true})"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [12, "this.followUp"]}, {"code": 119, "indent": 1, "parameters": ["end"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 241, "indent": 0, "parameters": [{"name": "霊体工場(Spirit factory)", "volume": 40, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["AudioManager.fadeInBgm(3)"]}, {"code": 231, "indent": 0, "parameters": [1, "sister_room_night2_fine", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [60, "", 0, 0, 0, 0, 100, 100, 255, 2]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[60] : 创建动画序列 : 动画序列[5]"]}, {"code": 355, "indent": 0, "parameters": ["var id = \"noise1\";"]}, {"code": 655, "indent": 0, "parameters": ["var filterTarget = 0;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.createFilter(id, \"noise\", filterTarget);"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.setFilter(id, [0.05]);"]}, {"code": 108, "indent": 0, "parameters": ["预加载"]}, {"code": 355, "indent": 0, "parameters": ["for (let i = 1; i <= 72; i++) {"]}, {"code": 655, "indent": 0, "parameters": ["    ImageManager.reservePicture(\"nightmare/nightVisit_Imouto_AhogeMonster\" + i);"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 655, "indent": 0, "parameters": ["for (let i = 43; i <= 58; i++) {"]}, {"code": 655, "indent": 0, "parameters": ["    ImageManager.reservePicture(\"nightmare/nightVisit_Imouto_AhogeMonster_loop\" + i);"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 355, "indent": 0, "parameters": ["let IMG = \"nightVisit_Imouto_AhogeMonster1\";"]}, {"code": 655, "indent": 0, "parameters": ["let path = \"nightmare\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(10, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["选项赋予名称"]}, {"code": 355, "indent": 0, "parameters": ["const idx   = 16;"]}, {"code": 655, "indent": 0, "parameters": ["const eid   = String(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["const mapId = $gameMap.mapId();"]}, {"code": 655, "indent": 0, "parameters": ["const key   = `MapEventDialogue${mapId}`;"]}, {"code": 655, "indent": 0, "parameters": ["const dialogueTable = window[key] || {};"]}, {"code": 655, "indent": 0, "parameters": ["const textArray     = dialogueTable[eid]?.[String(idx)];"]}, {"code": 655, "indent": 0, "parameters": ["const option1 = textArray[0];"]}, {"code": 655, "indent": 0, "parameters": ["$gameStrings.setValue(6,option1);"]}, {"code": 355, "indent": 0, "parameters": ["QJ.MPMZ.Shoot({"]}, {"code": 655, "indent": 0, "parameters": ["   img:\"null1\",groupName: ['nightmare'],"]}, {"code": 655, "indent": 0, "parameters": ["   existData: [ ],"]}, {"code": 655, "indent": 0, "parameters": ["   moveF:["]}, {"code": 655, "indent": 0, "parameters": ["     [90,0,QJ.MPMZ.tl._nightVisitImoutoAhogeMonster],"]}, {"code": 655, "indent": 0, "parameters": ["   ]"]}, {"code": 655, "indent": 0, "parameters": ["});\t"]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(15);"]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 355, "indent": 0, "parameters": ["var id = \"noise1\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.moveFilter(id, [0.1], 150);"]}, {"code": 230, "indent": 0, "parameters": [210]}, {"code": 102, "indent": 0, "parameters": [["\\str[6]"], -1, -1, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\str[6]"]}, {"code": 355, "indent": 1, "parameters": ["if ($gameMap.getGroupBulletListQJ('nightmare').length > 0) {"]}, {"code": 655, "indent": 1, "parameters": ["let bulletList = $gameMap.getGroupBulletListQJ('nightmare');"]}, {"code": 655, "indent": 1, "parameters": ["    let bullet = bulletList[0];"]}, {"code": 655, "indent": 1, "parameters": ["        bullet = $gameMap._mapBulletsQJ[bullet];"]}, {"code": 655, "indent": 1, "parameters": ["    if (bullet) {"]}, {"code": 655, "indent": 1, "parameters": ["        bullet._startled = true;"]}, {"code": 655, "indent": 1, "parameters": ["       if (bullet._frames <= 42) bullet._scaring = true;"]}, {"code": 655, "indent": 1, "parameters": ["    }"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["end"]}, {"code": 242, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">对话框皮肤 : 只姓名框窗口 : 修改样式 : 样式[3]"]}, {"code": 355, "indent": 0, "parameters": ["let voice = { name: \"nightmare_warning\", volume: 90, pitch: 100, pan: 0 };"]}, {"code": 655, "indent": 0, "parameters": ["AudioManager.playVoice(voice, false, 1);"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 66 : 1"]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl<？？？>\\c[10]「見たね——！？」  \\w[90]"]}, {"code": 356, "indent": 0, "parameters": [">对话框皮肤 : 只姓名框窗口 : 修改样式 : 样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Neon", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["var id = \"noise1\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.moveFilter(id, [15], 60);"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Noise", "volume": 70, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["var id = \"noise1\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.moveFilter(id, [0], 60);"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.eraseFilterAfterMove(id);"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 355, "indent": 0, "parameters": ["let code = \"$gameMap.event(1).steupCEQJ(1,{nightmare:true})\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameStrings.setValue(20,code);"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 201, "indent": 0, "parameters": [0, 21, 5, 3, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 3, "y": 12}, {"id": 8, "name": "妹妹第一次料理后续", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 2, "characterIndex": 1}, "list": [{"code": 231, "indent": 0, "parameters": [1, "sister_room_night_fine", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 314, "indent": 0, "parameters": [0, 1]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 313, "indent": 0, "parameters": [0, 2, 0, 26]}, {"code": 231, "indent": 0, "parameters": [16, "mio_tachie_kao_naku", 0, 0, 1000, 1600, 100, 100, 255, 0]}, {"code": 355, "indent": 0, "parameters": ["chahuiUtil.imoutoOutfitloading(1000,1600)"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["………………"]}, {"code": 401, "indent": 0, "parameters": ["………………"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(0);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(1);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(2);"]}, {"code": 355, "indent": 0, "parameters": ["var pic_ids = $gameNumberArray.value(41);"]}, {"code": 655, "indent": 0, "parameters": ["pic_ids.forEach(function(pic_id) {"]}, {"code": 655, "indent": 0, "parameters": ["    var picture = $gameScreen.picture(pic_id);"]}, {"code": 655, "indent": 0, "parameters": ["var data = {\t\t\t\t\t\t\t\t\"type\":\"smoothMove\","]}, {"code": 655, "indent": 0, "parameters": ["\"valueX\":0,"]}, {"code": 655, "indent": 0, "parameters": ["\"valueY\":-1450,"]}, {"code": 655, "indent": 0, "parameters": ["\"time\":60,"]}, {"code": 655, "indent": 0, "parameters": ["\"cur_time\":0,\"cur_speedX\":0,\"cur_speedY\":0,}"]}, {"code": 655, "indent": 0, "parameters": ["    if (picture) { picture._drill_PSh_commandChangeTank.push(data);}});"]}, {"code": 230, "indent": 0, "parameters": [40]}, {"code": 250, "indent": 0, "parameters": [{"name": "ゴソッ衣擦れ", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": ["DS_方向設定 90"]}, {"code": 225, "indent": 0, "parameters": [9, 8, 60, false]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE sis_room_achievement_15"]}, {"code": 355, "indent": 0, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["let index = 3;"]}, {"code": 655, "indent": 0, "parameters": ["let mapId = $gameMap.mapId();"]}, {"code": 655, "indent": 0, "parameters": ["let key   = `MapEventDialogue${mapId}`;"]}, {"code": 655, "indent": 0, "parameters": ["let textArray = window[key][eid][String(index)];"]}, {"code": 655, "indent": 0, "parameters": ["var template = \"\\\\{\\\\dDCCE[文本[%TEXT%]:预设[11]]\\\\}\";"]}, {"code": 655, "indent": 0, "parameters": ["textArray = textArray.map(t => template.replace(\"%TEXT%\", t));"]}, {"code": 655, "indent": 0, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE sis_room_achievement_16"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(4);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(5);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(6);"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE sis_room_achievement_17"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(7);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(8);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(9);"]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.changePictureName(16, \"mio_tachie_kao_nununu\");"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE sis_room_achievement_18"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(10);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(11);"]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.changePictureName(16, \"mio_tachie_kao_smile2\");"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE sis_room_achievement_19"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(12);"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE sis_room_achievement_20"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(13);"]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.changePictureName(16, \"mio_tachie_kao_smile\");"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE sis_room_achievement_21"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(14);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(15);"]}, {"code": 122, "indent": 0, "parameters": [14, 14, 0, 0, 30]}, {"code": 117, "indent": 0, "parameters": [3]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 121, "indent": 0, "parameters": [499, 499, 0]}, {"code": 355, "indent": 0, "parameters": ["for (let pictureId = 10; pictureId <= 20; pictureId++) {"]}, {"code": 655, "indent": 0, "parameters": ["    let picture = $gameScreen.picture(pictureId);"]}, {"code": 655, "indent": 0, "parameters": ["    if (picture) {"]}, {"code": 655, "indent": 0, "parameters": ["        picture.drill_PCE_stopEffect();"]}, {"code": 655, "indent": 0, "parameters": ["        $gameScreen.erasePicture(pictureId);"]}, {"code": 655, "indent": 0, "parameters": ["        $gameScreen.setPictureRemoveCommon(pictureId);"]}, {"code": 655, "indent": 0, "parameters": ["    }"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 355, "indent": 0, "parameters": ["$gameMap.event(1).start()"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 5}, null, null, null, {"id": 12, "name": "场景初始化", "note": "<重置独立开关>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 0, "characterIndex": 3}, "list": [{"code": 108, "indent": 0, "parameters": ["加载多语言地图对话模板"]}, {"code": 355, "indent": 0, "parameters": ["const mapId = $gameMap.mapId();"]}, {"code": 655, "indent": 0, "parameters": ["const key = 'MapEventDialogue' + mapId; "]}, {"code": 655, "indent": 0, "parameters": ["if (!window[key]) { "]}, {"code": 655, "indent": 0, "parameters": ["chahuiUtil.loadMapEventDialogue();"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 355, "indent": 0, "parameters": ["const maxPictures = 94; "]}, {"code": 655, "indent": 0, "parameters": ["for (let pictureId = 1; pictureId <= maxPictures; pictureId++) {"]}, {"code": 655, "indent": 0, "parameters": ["    let picture = $gameScreen.picture(pictureId);"]}, {"code": 655, "indent": 0, "parameters": ["    if (picture) {"]}, {"code": 655, "indent": 0, "parameters": ["        $gameScreen.erasePicture(pictureId);"]}, {"code": 655, "indent": 0, "parameters": ["    }"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen._pictureCidArray = [];"]}, {"code": 355, "indent": 0, "parameters": ["$gameSelfSwitches.setValue([$gameMap.mapId(), 44, 'D'], false);"]}, {"code": 108, "indent": 0, "parameters": ["补充监听器"]}, {"code": 355, "indent": 0, "parameters": ["QJ.MPMZ.tl._imoutoUtilCheckInitialization()"]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 356, "indent": 0, "parameters": ["PB_BGS_全停止"]}, {"code": 111, "indent": 0, "parameters": [12, "chahuiUtil.checkScriptExecutability()"]}, {"code": 355, "indent": 1, "parameters": ["let code = $gameStrings.value(20);"]}, {"code": 655, "indent": 1, "parameters": ["eval(code);"]}, {"code": 655, "indent": 1, "parameters": ["$gameStrings.setValue(20, \"\");"]}, {"code": 119, "indent": 1, "parameters": ["end"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 13, 0, 0, 3]}, {"code": 355, "indent": 1, "parameters": ["let ID = $gameVariables.value(13);"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.event(ID).start()"]}, {"code": 122, "indent": 1, "parameters": [13, 13, 0, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameMap.event(34).start()"]}, {"code": 122, "indent": 1, "parameters": [13, 13, 0, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 111, "indent": 0, "parameters": [0, 493, 1]}, {"code": 355, "indent": 1, "parameters": ["for (var i = 23; i <= 27; i++) {"]}, {"code": 655, "indent": 1, "parameters": ["    var item = $dataItems[i];"]}, {"code": 655, "indent": 1, "parameters": ["    if ($gameParty.numItems(item) >= 1) {"]}, {"code": 655, "indent": 1, "parameters": ["        $gameSelfSwitches.setValue([4, 44, 'A'], true);"]}, {"code": 655, "indent": 1, "parameters": ["        break;"]}, {"code": 655, "indent": 1, "parameters": ["    }"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["end"]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 12}, null, null, {"id": 15, "name": "休息中妹妹互动选项", "note": "<重置独立开关>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 1, "characterIndex": 3}, "list": [{"code": 117, "indent": 0, "parameters": [16]}, {"code": 111, "indent": 0, "parameters": [12, "$gameSelfSwitches.value([$gameMap.mapId(), 42, 'A'])"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [2, "D", 0]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "$gameActors.actor(1).isStateAffected(58) && Math.random() > 0.2"]}, {"code": 108, "indent": 1, "parameters": ["食物中毒事件"]}, {"code": 355, "indent": 1, "parameters": ["let id = 39;"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.event(id).steupCEQJ(3)"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 123, "indent": 0, "parameters": ["D", 0]}, {"code": 118, "indent": 0, "parameters": ["一级选项"]}, {"code": 108, "indent": 0, "parameters": ["修正选项组图标"]}, {"code": 355, "indent": 0, "parameters": ["chahuiUtil.imoutoChibiButtonInitialization()"]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 102, "indent": 0, "parameters": [["摸摸头", "呼叫妹妹", "亲密接触", "<<DrillUp.g_DCB_data[13].btn_src.length<=3>>快捷功能"], -2, -1, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "摸摸头"]}, {"code": 355, "indent": 1, "parameters": ["for (let i = 0; i < 3; i++) {"]}, {"code": 655, "indent": 1, "parameters": ["    ImageManager.reservePicture(\"sis_room/sis_chibi_normal_patpat\" + i);"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 655, "indent": 1, "parameters": ["this.setWaitMode('image')"]}, {"code": 355, "indent": 1, "parameters": ["let id = this._eventId;"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.event(id).steupCEQJ(2)"]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "呼叫妹妹"]}, {"code": 355, "indent": 1, "parameters": ["let id = this._eventId;"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.event(id).steupCEQJ(4)"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "亲密接触"]}, {"code": 117, "indent": 1, "parameters": [16]}, {"code": 117, "indent": 1, "parameters": [31]}, {"code": 313, "indent": 1, "parameters": [0, 2, 0, 27]}, {"code": 356, "indent": 1, "parameters": [">图片快捷操作 : 批量图片[2,5] : 修改单属性 : 透明度[0] : 时间[45]"]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 117, "indent": 1, "parameters": [26]}, {"code": 111, "indent": 1, "parameters": [12, "$gameSystem.hour() >= 21"]}, {"code": 231, "indent": 2, "parameters": [16, "mio_tachie_kao_-A-", 0, 0, 1000, 1600, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 231, "indent": 2, "parameters": [16, "mio_tachie_kao_mu2", 0, 0, 1000, 1600, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.steupCEQJ(38,eid)"]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 355, "indent": 1, "parameters": ["let eid = 18;"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.event(eid).steupCEQJ(4);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [3, "<<DrillUp.g_DCB_data[13].btn_src.length<=3>>快捷功能"]}, {"code": 355, "indent": 1, "parameters": ["let id = this._eventId;"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.event(id).steupCEQJ(3)"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 403, "indent": 0, "parameters": [6, null]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 355, "indent": 1, "parameters": ["chahuiUtil.quickInteractionIconInitialize()"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 123, "indent": 0, "parameters": ["D", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["快捷摸头互动"]}, {"code": 123, "indent": 0, "parameters": ["D", 0]}, {"code": 121, "indent": 0, "parameters": [14, 14, 0]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 355, "indent": 0, "parameters": ["var IMG = 'sis_chibi_normal_patpat0';"]}, {"code": 655, "indent": 0, "parameters": ["var path = \"sis_room\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(5, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 230, "indent": 0, "parameters": [4]}, {"code": 355, "indent": 0, "parameters": ["let path = \"sis_room/sis_chibi_normal_patpat1\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.changePictureName(5, path);"]}, {"code": 230, "indent": 0, "parameters": [4]}, {"code": 355, "indent": 0, "parameters": ["let path = \"sis_room/sis_chibi_normal_patpat2\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.changePictureName(5, path);"]}, {"code": 250, "indent": 0, "parameters": [{"name": "柔らかい(ゼリースライムぽわん)_1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["[5].forEach(function(k) {"]}, {"code": 655, "indent": 0, "parameters": [" if($gameScreen.picture( k )) {"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.picture( k ).drill_PCE_stopEffect();"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.picture( k ).drill_PCE_playSustainingBreathing( 30,15,3 )"]}, {"code": 655, "indent": 0, "parameters": ["  }"]}, {"code": 655, "indent": 0, "parameters": ["});"]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.showPicture(20, \"\", 0, 430, 470, 100, 100, 255, 0);"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen._particle.particleGroupSet(0,'crystal_c-P','picture:20','tag:20','crystal_c');"]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen._particle.particleUpdate(['tag:20','alpha','0.4']);"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen._particle.particleUpdate(['tag:20','color','#ffa3ce']);"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen._particle.reservePluginCommand(60,{},[\"clear\",'tag:20'],1);"]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 355, "indent": 0, "parameters": ["let index = QJ.MPMZ.tl._imoutoUtilPatPatEffect();"]}, {"code": 655, "indent": 0, "parameters": ["QJ.MPMZ.tl._imoutoUtilMoodText(index);"]}, {"code": 655, "indent": 0, "parameters": ["let id = this._eventId; "]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.event(id).steupCEQJ(5,{type:index})"]}, {"code": 250, "indent": 0, "parameters": [{"name": "UI-ポン　コミカルな唇を鳴らす音", "volume": 70, "pitch": 100, "pan": 0}]}, {"code": 117, "indent": 0, "parameters": [12]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["快捷功能"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameActors.actor(1).isStateAffected(58) && Math.random() > 0.4"]}, {"code": 108, "indent": 1, "parameters": ["食物中毒事件"]}, {"code": 355, "indent": 1, "parameters": ["let id = 39;"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.event(id).steupCEQJ(3)"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 123, "indent": 0, "parameters": ["D", 0]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 117, "indent": 0, "parameters": [31]}, {"code": 356, "indent": 0, "parameters": [">图片快捷操作 : 批量图片[2,5] : 修改单属性 : 透明度[0] : 时间[45]"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 123, "indent": 0, "parameters": ["D", 1]}, {"code": 117, "indent": 0, "parameters": [26]}, {"code": 313, "indent": 0, "parameters": [0, 2, 0, 27]}, {"code": 111, "indent": 0, "parameters": [12, "$gameSystem.hour() >= 21"]}, {"code": 231, "indent": 1, "parameters": [16, "mio_tachie_kao_-A-", 0, 0, 1000, 1600, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 231, "indent": 1, "parameters": [16, "mio_tachie_kao_mu2", 0, 0, 1000, 1600, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.steupCEQJ(38,eid)"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 108, "indent": 0, "parameters": ["快捷功能：喝茶"]}, {"code": 111, "indent": 0, "parameters": [12, "DrillUp.g_DCB_data[13].btn_src[3] === \"button_tea\" "]}, {"code": 355, "indent": 1, "parameters": ["$gameMap.event(44).steupCEQJ(1)"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["快捷功能：玩游戏"]}, {"code": 111, "indent": 0, "parameters": [12, "DrillUp.g_DCB_data[13].btn_src[3] === \"button_game\" "]}, {"code": 355, "indent": 1, "parameters": ["$gameMap.event(2).steupCEQJ(2,{preProcess:true});"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["快捷功能：洗澡"]}, {"code": 111, "indent": 0, "parameters": [12, "DrillUp.g_DCB_data[13].btn_src[3] === \"button_bath\" "]}, {"code": 355, "indent": 1, "parameters": ["$gameMap.event(23).steupCEQJ(1);"]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 123, "indent": 1, "parameters": ["D", 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["快捷功能：看电视"]}, {"code": 111, "indent": 0, "parameters": [12, "DrillUp.g_DCB_data[13].btn_src[3] === \"button_television\" "]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["叫妹妹过来"]}, {"code": 123, "indent": 0, "parameters": ["D", 0]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 117, "indent": 0, "parameters": [31]}, {"code": 356, "indent": 0, "parameters": [">图片快捷操作 : 批量图片[2,5] : 修改单属性 : 透明度[0] : 时间[30]"]}, {"code": 117, "indent": 0, "parameters": [26]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 111, "indent": 0, "parameters": [4, 2, 6, 35]}, {"code": 111, "indent": 1, "parameters": [12, "$gameSelfVariables.value([$gameMap.mapId(), 51, 'times']) === 0"]}, {"code": 355, "indent": 2, "parameters": ["$gameMap.event(39).steupCEQJ(2)"]}, {"code": 115, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [12, "Math.random() > 0.7 && !$gameSelfSwitches.value([$gameMap.mapId(), 51, 'D'])"]}, {"code": 355, "indent": 3, "parameters": ["$gameMap.event(39).steupCEQJ(2)"]}, {"code": 115, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 231, "indent": 1, "parameters": [16, "mio_tachie_kao_gaman1", 0, 0, 1000, 1600, 100, 100, 255, 0]}, {"code": 355, "indent": 1, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.steupCEQJ(38,eid)"]}, {"code": 230, "indent": 1, "parameters": [20]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE sis_room_toilet_08"]}, {"code": 355, "indent": 1, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 1, "parameters": ["let index = 7;"]}, {"code": 655, "indent": 1, "parameters": ["let textArray = window.MapEventDialogue4[eid][String(index)];"]}, {"code": 655, "indent": 1, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 231, "indent": 1, "parameters": [16, "mio_tachie_kao_gaman2", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE sis_room_toilet_09"]}, {"code": 355, "indent": 1, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 1, "parameters": ["let index = 8;"]}, {"code": 655, "indent": 1, "parameters": ["let textArray = window.MapEventDialogue4[eid][String(index)];"]}, {"code": 655, "indent": 1, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 313, "indent": 1, "parameters": [0, 2, 0, 27]}, {"code": 355, "indent": 1, "parameters": ["$gameMap.event(18).start()"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "$gameSelfSwitches.value([$gameMap.mapId(), 6, 'B'])"]}, {"code": 231, "indent": 1, "parameters": [16, "mio_tachie_kao_=ω=", 0, 0, 1000, 1600, 100, 100, 255, 0]}, {"code": 355, "indent": 1, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.steupCEQJ(38,eid)"]}, {"code": 230, "indent": 1, "parameters": [20]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE sis_room_tachie_kimochi02"]}, {"code": 355, "indent": 1, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 1, "parameters": ["let index = 9;"]}, {"code": 655, "indent": 1, "parameters": ["let textArray = window.MapEventDialogue4[eid][String(index)];"]}, {"code": 655, "indent": 1, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 313, "indent": 1, "parameters": [0, 2, 0, 27]}, {"code": 117, "indent": 1, "parameters": [29]}, {"code": 355, "indent": 1, "parameters": ["$gameMap.event(18).start()"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 20, 0, 75, 1]}, {"code": 231, "indent": 1, "parameters": [16, "mio_tachie_kao_happy", 0, 0, 1000, 1600, 100, 100, 255, 0]}, {"code": 355, "indent": 1, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.steupCEQJ(38,eid)"]}, {"code": 230, "indent": 1, "parameters": [20]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE sis_room_tachie_kimochi01"]}, {"code": 355, "indent": 1, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 1, "parameters": ["let index = 10;"]}, {"code": 655, "indent": 1, "parameters": ["let textArray = window.MapEventDialogue4[eid][String(index)];"]}, {"code": 655, "indent": 1, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 119, "indent": 1, "parameters": ["end"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 20, 0, 50, 1]}, {"code": 231, "indent": 1, "parameters": [16, "mio_tachie_kao_OAO", 0, 0, 1000, 1600, 100, 100, 255, 0]}, {"code": 355, "indent": 1, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.steupCEQJ(38,eid)"]}, {"code": 230, "indent": 1, "parameters": [20]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE sis_room_tachie_kimochi03"]}, {"code": 355, "indent": 1, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 1, "parameters": ["let index = 11;"]}, {"code": 655, "indent": 1, "parameters": ["let textArray = window.MapEventDialogue4[eid][String(index)];"]}, {"code": 655, "indent": 1, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 119, "indent": 1, "parameters": ["end"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 20, 0, 30, 1]}, {"code": 231, "indent": 1, "parameters": [16, "mio_tachie_kao_mu2", 0, 0, 1000, 1600, 100, 100, 255, 0]}, {"code": 355, "indent": 1, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.steupCEQJ(38,eid)"]}, {"code": 230, "indent": 1, "parameters": [20]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE sis_room_tachie_kimochi04"]}, {"code": 355, "indent": 1, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 1, "parameters": ["let index = 12;"]}, {"code": 655, "indent": 1, "parameters": ["let textArray = window.MapEventDialogue4[eid][String(index)];"]}, {"code": 655, "indent": 1, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 119, "indent": 1, "parameters": ["end"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 20, 0, 15, 1]}, {"code": 231, "indent": 1, "parameters": [16, "mio_tachie_kao_duqi", 0, 0, 1000, 1600, 100, 100, 255, 0]}, {"code": 355, "indent": 1, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.steupCEQJ(38,eid)"]}, {"code": 230, "indent": 1, "parameters": [20]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE sis_room_tachie_kimochi05"]}, {"code": 355, "indent": 1, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 1, "parameters": ["let index = 13;"]}, {"code": 655, "indent": 1, "parameters": ["let textArray = window.MapEventDialogue4[eid][String(index)];"]}, {"code": 655, "indent": 1, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 231, "indent": 1, "parameters": [16, "mio_tachie_kao_okoru", 0, 0, 1000, 1600, 100, 100, 255, 0]}, {"code": 355, "indent": 1, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.steupCEQJ(38,eid)"]}, {"code": 230, "indent": 1, "parameters": [20]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE sis_room_tachie_kimochi06"]}, {"code": 355, "indent": 1, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 1, "parameters": ["let index = 14;"]}, {"code": 655, "indent": 1, "parameters": ["let textArray = window.MapEventDialogue4[eid][String(index)];"]}, {"code": 655, "indent": 1, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["end"]}, {"code": 111, "indent": 0, "parameters": [4, 1, 6, 16]}, {"code": 111, "indent": 1, "parameters": [1, 20, 0, 50, 1]}, {"code": 231, "indent": 2, "parameters": [16, "mio_tachie_kao_OAO", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["SV_PLAY_VOICE sis_room_tachie_kimochi07"]}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(20);"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 313, "indent": 0, "parameters": [0, 2, 0, 27]}, {"code": 117, "indent": 0, "parameters": [29]}, {"code": 355, "indent": 0, "parameters": ["$gameMap.event(18).start()"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "C", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [12, "!this.type"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["let index = this.type + 14;"]}, {"code": 655, "indent": 0, "parameters": ["let textArray = window.MapEventDialogue4[eid][String(index)];"]}, {"code": 655, "indent": 0, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 122, "indent": 0, "parameters": [14, 14, 0, 0, 30]}, {"code": 117, "indent": 0, "parameters": [3]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 111, "indent": 0, "parameters": [12, "$gameSelfSwitches.value([$gameMap.mapId(), 42, 'A'])"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "$gameActors.actor(2).isStateAffected(36) || $gameSystem.hour() >= 21"]}, {"code": 355, "indent": 1, "parameters": ["let path = \"sis_room/sis_chibi_normal_patpat1\";"]}, {"code": 655, "indent": 1, "parameters": ["$gameScreen.changePictureName(5, path);"]}, {"code": 230, "indent": 1, "parameters": [4]}, {"code": 355, "indent": 1, "parameters": ["let path = \"sis_room/sis_chibi_normal_patpat0\";"]}, {"code": 655, "indent": 1, "parameters": ["$gameScreen.changePictureName(5, path);"]}, {"code": 230, "indent": 1, "parameters": [4]}, {"code": 355, "indent": 1, "parameters": ["let IMG = \"sis_chibi_normal_rubEyes0\";"]}, {"code": 655, "indent": 1, "parameters": ["let path = \"sis_room\";"]}, {"code": 655, "indent": 1, "parameters": ["$gameScreen.showPictureFromPath(5, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 108, "indent": 1, "parameters": ["确认视频路径"]}, {"code": 355, "indent": 1, "parameters": ["let vName = \"sis_chibi_normal_rubEyes\";"]}, {"code": 655, "indent": 1, "parameters": ["$gameScreen.setVideoPictureName(vName, true, false);"]}, {"code": 108, "indent": 1, "parameters": ["绑定视频"]}, {"code": 355, "indent": 1, "parameters": ["let pid = 5;"]}, {"code": 655, "indent": 1, "parameters": ["$gameScreen.showPicture(pid, '', 0, 240, 200, 100, 100, 255, 0);"]}, {"code": 655, "indent": 1, "parameters": ["const pic = $gameScreen.picture(pid);"]}, {"code": 655, "indent": 1, "parameters": ["if (pic) {"]}, {"code": 655, "indent": 1, "parameters": ["pic.set<PERSON><PERSON><PERSON>(true);"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 108, "indent": 1, "parameters": ["刷新标题画面动画"]}, {"code": 355, "indent": 1, "parameters": ["chahuiUtil.RefreshTitleScreenAnimation(\"rubEyes\")"]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["let path = \"sis_room/sis_chibi_normal_patpat1\";"]}, {"code": 655, "indent": 1, "parameters": ["$gameScreen.changePictureName(5, path);"]}, {"code": 230, "indent": 1, "parameters": [4]}, {"code": 355, "indent": 1, "parameters": ["let path = \"sis_room/sis_chibi_normal_patpat0\";"]}, {"code": 655, "indent": 1, "parameters": ["$gameScreen.changePictureName(5, path);"]}, {"code": 230, "indent": 1, "parameters": [4]}, {"code": 111, "indent": 1, "parameters": [12, "!$gameSelfSwitches.value([$gameMap.mapId(), 6, 'B'])"]}, {"code": 108, "indent": 2, "parameters": ["妹妹眨眼动画"]}, {"code": 355, "indent": 2, "parameters": ["QJ.MPMZ.deleteProjectile('ImoutoBlinking');"]}, {"code": 655, "indent": 2, "parameters": ["let IMG = \"sis_room/sis_room_dozingOff1\";"]}, {"code": 655, "indent": 2, "parameters": ["$gameScreen.showPicture(5, IMG, 0, 260, 310, 100, 100, 255, 0);"]}, {"code": 655, "indent": 2, "parameters": ["for (let i = 2; i <= 4; i++) {"]}, {"code": 655, "indent": 2, "parameters": ["    ImageManager.reservePicture(\"sis_room/sis_room_dozingOff\" + i);"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 355, "indent": 2, "parameters": ["QJ.MPMZ.Shoot({"]}, {"code": 655, "indent": 2, "parameters": ["   img:\"null1\",groupName: ['ImoutoBlinking'],"]}, {"code": 655, "indent": 2, "parameters": ["   existData: [ ],"]}, {"code": 655, "indent": 2, "parameters": ["   moveF:["]}, {"code": 655, "indent": 2, "parameters": ["     [60,0,QJ.MPMZ.tl._imoutoUtilImoutoBlinking]"]}, {"code": 655, "indent": 2, "parameters": ["   ]"]}, {"code": 655, "indent": 2, "parameters": ["});\t"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 313, "indent": 0, "parameters": [0, 2, 0, 30]}, {"code": 123, "indent": 0, "parameters": ["D", 1]}, {"code": 121, "indent": 0, "parameters": [14, 14, 1]}, {"code": 355, "indent": 0, "parameters": ["chahuiUtil.quickInteractionIconInitialize()"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 0}, null, {"id": 17, "name": "新手教程", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 4, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["随机标题画面"]}, {"code": 121, "indent": 0, "parameters": [14, 14, 0]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.setPictureRemoveCommon(5)"]}, {"code": 121, "indent": 0, "parameters": [401, 402, 1]}, {"code": 356, "indent": 0, "parameters": [">标题界面 : 修改背景音乐 : 音乐[1]"]}, {"code": 356, "indent": 0, "parameters": [">标题背景 : 背景[2] : 显示"]}, {"code": 356, "indent": 0, "parameters": [">标题背景 : 背景[3] : 隐藏"]}, {"code": 356, "indent": 0, "parameters": ["show_actor_hud"]}, {"code": 231, "indent": 0, "parameters": [21, "instructionalImage", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 111, "indent": 0, "parameters": [12, "Utils.isMobileDevice()"]}, {"code": 355, "indent": 1, "parameters": ["QJ.MPMZ.tl._imoutoUtilBeginnersGuide(\"guide0\");"]}, {"code": 655, "indent": 1, "parameters": ["QJ.MPMZ.tl._imoutoUtilBeginnersGuide(\"guide1\");"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["let lang = ConfigManager.language;"]}, {"code": 655, "indent": 1, "parameters": ["if (lang > 2) lang = 2;"]}, {"code": 655, "indent": 1, "parameters": ["let IMG = \"instructionalImage2_\" + lang;"]}, {"code": 655, "indent": 1, "parameters": ["$gameScreen.showPictureFromPath(22, \"gameTutorial\", IMG, 0, 0, 0, 100, 100, 0, 0);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 232, "indent": 0, "parameters": [22, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, false]}, {"code": 232, "indent": 0, "parameters": [21, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, true]}, {"code": 111, "indent": 0, "parameters": [12, "$gameScreen.picture(22)"]}, {"code": 356, "indent": 1, "parameters": [">持续动作 : 图片[22] : 渐变闪烁 : 持续时间[无限] : 周期[90]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["     \\w[60]"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameScreen.picture(22)"]}, {"code": 356, "indent": 1, "parameters": [">持续动作 : 图片[22] : 立即终止动作"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["QJ.MPMZ.deleteProjectile('BeginnersGuide',{d:[0,30]});"]}, {"code": 111, "indent": 0, "parameters": [12, "Utils.isMobileDevice()"]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 355, "indent": 1, "parameters": ["QJ.MPMZ.tl._imoutoUtilBeginnersGuide(\"mobileSaveReminder1\");"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["     \\w[180]"]}, {"code": 355, "indent": 1, "parameters": ["QJ.MPMZ.deleteProjectile('BeginnersGuide',{d:[0,30]});"]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 355, "indent": 1, "parameters": ["QJ.MPMZ.tl._imoutoUtilBeginnersGuide(\"mobileSaveReminder2\");"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["     \\w[180]"]}, {"code": 355, "indent": 1, "parameters": ["QJ.MPMZ.deleteProjectile('BeginnersGuide',{d:[0,30]});"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 232, "indent": 0, "parameters": [22, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, false]}, {"code": 232, "indent": 0, "parameters": [21, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, true]}, {"code": 235, "indent": 0, "parameters": [21]}, {"code": 235, "indent": 0, "parameters": [22]}, {"code": 356, "indent": 0, "parameters": ["SaveNow"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 355, "indent": 0, "parameters": ["QJ.MPMZ.tl._imoutoUtilCheckInitialization()"]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 121, "indent": 0, "parameters": [14, 14, 1]}, {"code": 111, "indent": 0, "parameters": [12, "Utils.isMobileDevice()"]}, {"code": 356, "indent": 1, "parameters": ["P_CALL_CE 5 17 3"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 356, "indent": 1, "parameters": ["P_CALL_CE 5 17 1"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["P_CALL_CE 5 15 4"]}, {"code": 356, "indent": 0, "parameters": ["P_CALL_CE 5 16 5"]}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 4, "pattern": 1, "characterIndex": 1}, "list": [{"code": 121, "indent": 0, "parameters": [14, 14, 0]}, {"code": 111, "indent": 0, "parameters": [4, 2, 6, 25]}, {"code": 231, "indent": 1, "parameters": [9, "instructionalImage4_2", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 231, "indent": 1, "parameters": [9, "instructionalImage4_1", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "Utils.isMobileDevice()"]}, {"code": 355, "indent": 1, "parameters": ["QJ.MPMZ.tl._imoutoUtilBeginnersGuide(\"guide2\");"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["let lang = ConfigManager.language;"]}, {"code": 655, "indent": 1, "parameters": ["if (lang > 2) lang = 2;"]}, {"code": 655, "indent": 1, "parameters": ["let IMG = \"instructionalImage5_\" + lang;"]}, {"code": 655, "indent": 1, "parameters": ["$gameScreen.showPictureFromPath(25, \"gameTutorial\", IMG, 0, 0, 0, 100, 100, 0, 0);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 232, "indent": 0, "parameters": [25, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, false]}, {"code": 232, "indent": 0, "parameters": [9, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, true]}, {"code": 356, "indent": 0, "parameters": [">图片快捷操作 : 批量图片[9,25] : 修改单属性 : 透明度[255] : 时间[60]"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameScreen.picture(25)"]}, {"code": 356, "indent": 1, "parameters": [">持续动作 : 图片[25] : 渐变闪烁 : 持续时间[无限] : 周期[90]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["     \\w[60]"]}, {"code": 111, "indent": 0, "parameters": [12, "Utils.isMobileDevice()"]}, {"code": 355, "indent": 1, "parameters": ["QJ.MPMZ.deleteProjectile('BeginnersGuide',{d:[0,30]});"]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 355, "indent": 1, "parameters": ["QJ.MPMZ.tl._imoutoUtilBeginnersGuide(\"guide3\");"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["let lang = ConfigManager.language;"]}, {"code": 655, "indent": 1, "parameters": ["if (lang > 2) lang = 2;"]}, {"code": 655, "indent": 1, "parameters": ["let IMG = \"instructionalImage8_\" + lang;"]}, {"code": 655, "indent": 1, "parameters": ["$gameScreen.showPictureFromPath(26, \"gameTutorial\", IMG, 0, 0, 0, 100, 100, 0, 0);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "$gameScreen.picture(25)"]}, {"code": 356, "indent": 1, "parameters": [">持续动作 : 图片[25] : 立即终止动作"]}, {"code": 356, "indent": 1, "parameters": [">图片快捷操作 : 图片[25] : 修改单属性 : 透明度[0] : 时间[30]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "$gameScreen.picture(26)"]}, {"code": 356, "indent": 1, "parameters": [">图片快捷操作 : 批量图片[26] : 修改单属性 : 透明度[255] : 时间[60]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 111, "indent": 0, "parameters": [12, "$gameScreen.picture(26)"]}, {"code": 356, "indent": 1, "parameters": [">持续动作 : 图片[26] : 渐变闪烁 : 持续时间[无限] : 周期[90]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["     \\w[60]"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameScreen.picture(26)"]}, {"code": 356, "indent": 1, "parameters": [">持续动作 : 图片[26] : 立即终止动作"]}, {"code": 232, "indent": 1, "parameters": [26, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, false]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["QJ.MPMZ.deleteProjectile('BeginnersGuide',{d:[0,30]});"]}, {"code": 232, "indent": 0, "parameters": [9, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, true]}, {"code": 235, "indent": 0, "parameters": [9]}, {"code": 235, "indent": 0, "parameters": [25]}, {"code": 235, "indent": 0, "parameters": [26]}, {"code": 121, "indent": 0, "parameters": [14, 14, 1]}, {"code": 121, "indent": 0, "parameters": [32, 32, 1]}, {"code": 121, "indent": 0, "parameters": [33, 33, 1]}, {"code": 121, "indent": 0, "parameters": [14, 14, 1]}, {"code": 108, "indent": 0, "parameters": ["亲密接触监听器"]}, {"code": 121, "indent": 0, "parameters": [46, 46, 0]}, {"code": 230, "indent": 0, "parameters": [4]}, {"code": 355, "indent": 0, "parameters": ["QJ.MPMZ.tl._imoutoUtilSkinship()"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 2, "y": 3}, {"id": 18, "name": "妹妹立绘互动菜单", "note": "<重置独立开关>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 1, "characterIndex": 3}, "list": [{"code": 111, "indent": 0, "parameters": [12, "this.endPoint"]}, {"code": 119, "indent": 1, "parameters": ["一级选项"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 117, "indent": 0, "parameters": [31]}, {"code": 121, "indent": 0, "parameters": [26, 26, 1]}, {"code": 121, "indent": 0, "parameters": [46, 46, 1]}, {"code": 118, "indent": 0, "parameters": ["立绘互动"]}, {"code": 117, "indent": 0, "parameters": [32]}, {"code": 111, "indent": 0, "parameters": [12, "$gameActors.actor(1).isStateAffected(58) && Math.random() > 0.2"]}, {"code": 108, "indent": 1, "parameters": ["食物中毒事件"]}, {"code": 355, "indent": 1, "parameters": ["let id = 3;"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.event(id).steupCEQJ(1)"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["触发了睡眠事件"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameSelfSwitches.value([$gameMap.mapId(), 42, 'A'])"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["洗澡前后分歧"]}, {"code": 111, "indent": 0, "parameters": [4, 2, 6, 25]}, {"code": 111, "indent": 1, "parameters": [2, "A", 0]}, {"code": 230, "indent": 2, "parameters": [1]}, {"code": 123, "indent": 2, "parameters": ["A", 1]}, {"code": 230, "indent": 2, "parameters": [1]}, {"code": 119, "indent": 2, "parameters": ["洗澡后"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["厕所事件判定"]}, {"code": 108, "indent": 0, "parameters": ["《客厅事件判断————————————————————————》"]}, {"code": 355, "indent": 0, "parameters": ["QJ.MPMZ.tl._imoutoUtilLivingRoomEventTriggerCheck.call(this)"]}, {"code": 108, "indent": 0, "parameters": ["《————————————————————————————————————》"]}, {"code": 118, "indent": 0, "parameters": ["一级选项"]}, {"code": 108, "indent": 0, "parameters": ["选项赋予名称"]}, {"code": 355, "indent": 0, "parameters": ["const idx   = 0;"]}, {"code": 655, "indent": 0, "parameters": ["const eid   = String(this._eventId);  const mapId = $gameMap.mapId();"]}, {"code": 655, "indent": 0, "parameters": ["const key   = `MapEventDialogue${mapId}`;  const dialogueTable = window[key] || {};"]}, {"code": 655, "indent": 0, "parameters": ["const textArray = dialogueTable[eid]?.[String(idx)];"]}, {"code": 655, "indent": 0, "parameters": ["const option1 = textArray[0];  $gameStrings.setValue(6,option1);"]}, {"code": 655, "indent": 0, "parameters": ["const option2 = textArray[1];  $gameStrings.setValue(7,option2);"]}, {"code": 655, "indent": 0, "parameters": ["const option3 = textArray[2];  $gameStrings.setValue(8,option3);"]}, {"code": 655, "indent": 0, "parameters": ["const option4 = textArray[3];  $gameStrings.setValue(9,option4);"]}, {"code": 655, "indent": 0, "parameters": ["const option5 = textArray[4];  $gameStrings.setValue(10,option5);"]}, {"code": 108, "indent": 0, "parameters": ["修正选项组图标"]}, {"code": 356, "indent": 0, "parameters": [">对话选项按钮组 : 切换为按钮组 : 样式[15]"]}, {"code": 355, "indent": 0, "parameters": ["let style = [];"]}, {"code": 655, "indent": 0, "parameters": ["style.push(\"eventButtons_talk\",\"eventButtons_touch\");"]}, {"code": 655, "indent": 0, "parameters": ["$gameSwitches.value(493)||$gameSwitches.value(500) ? style.push(\"eventButtons_unknown\") : null;"]}, {"code": 655, "indent": 0, "parameters": ["style.push(\"eventButtons_sleep\");"]}, {"code": 655, "indent": 0, "parameters": ["DrillUp.g_DCB_data[14].btn_src = style;"]}, {"code": 102, "indent": 0, "parameters": [["\\str[6]", "\\c[15]\\str[7]", "<<!s[493]&&!s[500]>>\\c[15]\\str[8]", "<<1==1>>\\c[15]\\str[9]", "\\c[15]\\str[10]"], -2, -1, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\str[6]"]}, {"code": 355, "indent": 1, "parameters": ["$gameMap.event(25).start()"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\c[15]\\str[7]"]}, {"code": 355, "indent": 1, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.event(eid).steupCEQJ(4);"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "<<!s[493]&&!s[500]>>\\c[15]\\str[8]"]}, {"code": 355, "indent": 1, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.event(eid).steupCEQJ(3);"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [3, "<<1==1>>\\c[15]\\str[9]"]}, {"code": 355, "indent": 1, "parameters": ["let eid = 23;"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.event(eid).steupCEQJ(1);"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [4, "\\c[15]\\str[10]"]}, {"code": 355, "indent": 1, "parameters": ["$gameMap.event(24).start()"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 403, "indent": 0, "parameters": [6, null]}, {"code": 355, "indent": 1, "parameters": ["$gameSelfSwitches.setValue([$gameMap.mapId(), 13, 'A'], false);"]}, {"code": 118, "indent": 1, "parameters": ["洗澡后"]}, {"code": 355, "indent": 1, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.event(eid).steupCEQJ(2);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["妹妹立绘退场"]}, {"code": 111, "indent": 0, "parameters": [4, 2, 6, 25]}, {"code": 231, "indent": 1, "parameters": [16, "mio_tachie_kao_=ω=", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE sis_room_ofuro_40"]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(1);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["var pic_ids = $gameNumberArray.value(41);"]}, {"code": 655, "indent": 0, "parameters": ["pic_ids.forEach(function(pic_id) {"]}, {"code": 655, "indent": 0, "parameters": ["    var picture = $gameScreen.picture(pic_id);"]}, {"code": 655, "indent": 0, "parameters": ["    if (picture) { "]}, {"code": 655, "indent": 0, "parameters": ["picture.drill_PFOE_playHidingMoveDisappear( 60,90,720 );"]}, {"code": 655, "indent": 0, "parameters": ["}});"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 108, "indent": 0, "parameters": ["需要区分洗澡前后场景"]}, {"code": 111, "indent": 0, "parameters": [4, 2, 6, 25]}, {"code": 122, "indent": 1, "parameters": [86, 86, 0, 0, 0]}, {"code": 314, "indent": 1, "parameters": [0, 2]}, {"code": 313, "indent": 1, "parameters": [0, 2, 0, 30]}, {"code": 108, "indent": 1, "parameters": ["妹妹图层、服装重置"]}, {"code": 355, "indent": 1, "parameters": ["QJ.MPMZ.tl._imoutoUtilImoutoOutfitReset()"]}, {"code": 355, "indent": 1, "parameters": ["var pic_ids = [4,7,8,9,10];"]}, {"code": 655, "indent": 1, "parameters": ["pic_ids.forEach(function(pic_id) {"]}, {"code": 655, "indent": 1, "parameters": ["   $gameScreen.erasePicture(pic_id);"]}, {"code": 655, "indent": 1, "parameters": ["});"]}, {"code": 122, "indent": 1, "parameters": [14, 14, 0, 0, 10]}, {"code": 117, "indent": 1, "parameters": [3]}, {"code": 230, "indent": 1, "parameters": [90]}, {"code": 355, "indent": 1, "parameters": ["$gameMap.event(1).start()"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 231, "indent": 1, "parameters": [2, "bunny<PERSON><PERSON>", 0, 0, 461, 562, 100, 100, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["let text = $gameStrings.value(40).split(\"\\n\");"]}, {"code": 655, "indent": 1, "parameters": ["let picture = $gameScreen.picture(2);"]}, {"code": 655, "indent": 1, "parameters": ["let bind = DrillUp.g_MPFP_list[6];"]}, {"code": 655, "indent": 1, "parameters": ["if (!picture._drill_MPFP_bean) {"]}, {"code": 655, "indent": 1, "parameters": ["  picture._drill_MPFP_bean = new Drill_MPFP_Bean();"]}, {"code": 655, "indent": 1, "parameters": ["  $gameTemp._drill_MPFP_needRestatistics = true; picture.drill_COPWM_checkData(); }"]}, {"code": 655, "indent": 1, "parameters": ["picture._drill_MPFP_bean.drill_bean_setVisible(true);picture._drill_MPFP_bean.drill_bean_setContextList(text);"]}, {"code": 655, "indent": 1, "parameters": ["picture._drill_MPFP_bean.drill_bean_setSkinStyle(bind['style_mode'], bind['style_lockedId']);"]}, {"code": 356, "indent": 1, "parameters": [">图片快捷操作 : 批量图片[2,5] : 修改单属性 : 透明度[255] : 时间[60]"]}, {"code": 230, "indent": 1, "parameters": [70]}, {"code": 356, "indent": 1, "parameters": [">图片快捷操作 : 图片[3] : 修改单属性 : 透明度[255] : 时间[1]"]}, {"code": 314, "indent": 1, "parameters": [0, 2]}, {"code": 313, "indent": 1, "parameters": [0, 2, 0, 30]}, {"code": 108, "indent": 1, "parameters": ["解除妹妹小人的点击限制"]}, {"code": 355, "indent": 1, "parameters": ["$gameSelfSwitches.setValue([$gameMap.mapId(), 15, 'D'], false);"]}, {"code": 108, "indent": 1, "parameters": ["快捷互动按钮"]}, {"code": 355, "indent": 1, "parameters": ["chahuiUtil.quickInteractionIconInitialize()"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["选项赋予名称"]}, {"code": 355, "indent": 0, "parameters": ["const idx   = 2;"]}, {"code": 655, "indent": 0, "parameters": ["const eid   = String(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["const mapId = $gameMap.mapId();"]}, {"code": 655, "indent": 0, "parameters": ["const key   = `MapEventDialogue${mapId}`;"]}, {"code": 655, "indent": 0, "parameters": ["const dialogueTable = window[key] || {};"]}, {"code": 655, "indent": 0, "parameters": ["const textArray     = dialogueTable[eid]?.[String(idx)];"]}, {"code": 655, "indent": 0, "parameters": ["const option1 = textArray[0];"]}, {"code": 655, "indent": 0, "parameters": ["const option2 = textArray[1];"]}, {"code": 655, "indent": 0, "parameters": [" $gameStrings.setValue(6,option1);"]}, {"code": 655, "indent": 0, "parameters": [" $gameStrings.setValue(7,option2);"]}, {"code": 108, "indent": 0, "parameters": ["修正选项组图标"]}, {"code": 355, "indent": 0, "parameters": ["$gameSystem._drill_DCB_curStyle = 25"]}, {"code": 355, "indent": 0, "parameters": ["let style = [];"]}, {"code": 655, "indent": 0, "parameters": ["$gameSwitches.value(493) ? style.push(\"eventButtons_tea\") : null;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSwitches.value(500) ? style.push(\"eventButtons_game\") : null;"]}, {"code": 655, "indent": 0, "parameters": ["DrillUp.g_DCB_data[24].btn_src = style;"]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 102, "indent": 0, "parameters": [["<<!s[493]>>\\c[15]\\str[6]", "<<!s[500]>>\\c[15]\\str[7]"], -2, -1, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "<<!s[493]>>\\c[15]\\str[6]"]}, {"code": 111, "indent": 1, "parameters": [4, 2, 6, 25]}, {"code": 231, "indent": 2, "parameters": [16, "mio_tachie_kao_nununu", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["SV_PLAY_VOICE sis_room_ofuro_35"]}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(3);"]}, {"code": 117, "indent": 2, "parameters": [29]}, {"code": 119, "indent": 2, "parameters": ["一级选项"]}, {"code": 115, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["触发喝茶事件"]}, {"code": 355, "indent": 1, "parameters": ["$gameMap.event(44).steupCEQJ(1)"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "<<!s[500]>>\\c[15]\\str[7]"]}, {"code": 111, "indent": 1, "parameters": [4, 2, 6, 25]}, {"code": 231, "indent": 2, "parameters": [16, "mio_tachie_kao_nununu", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["SV_PLAY_VOICE sis_room_ofuro_36"]}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(4);"]}, {"code": 117, "indent": 2, "parameters": [29]}, {"code": 119, "indent": 2, "parameters": ["一级选项"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["触发玩游戏事件"]}, {"code": 355, "indent": 1, "parameters": ["$gameMap.event(2).steupCEQJ(2,{preProcess:true});"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 403, "indent": 0, "parameters": [6, null]}, {"code": 119, "indent": 1, "parameters": ["一级选项"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["一级选项"]}, {"code": 355, "indent": 0, "parameters": ["let id = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.event(id).steupCEQJ(1,{endPoint:true})"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["亲密接触前置流程"]}, {"code": 111, "indent": 0, "parameters": [12, "!$gameSelfSwitches.value([$gameMap.mapId(), this._eventId, 'F'])"]}, {"code": 108, "indent": 1, "parameters": ["亲密接触新手教程"]}, {"code": 355, "indent": 1, "parameters": ["$gameSelfSwitches.setValue([$gameMap.mapId(), this._eventId, 'F'], true);"]}, {"code": 355, "indent": 1, "parameters": ["$gameMap.event(17).steupCEQJ(2)"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "$gameVariables.value(205) >= (6 + 4 * $gameVariables.value(15))"]}, {"code": 122, "indent": 1, "parameters": [204, 204, 0, 4, "\"左右震动\""]}, {"code": 117, "indent": 1, "parameters": [35]}, {"code": 250, "indent": 1, "parameters": [{"name": "014myuu_YumeSE_SystemBuzzer03", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE sis_room_tachie_touch_13"]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(5);"]}, {"code": 355, "indent": 1, "parameters": ["let id = this._eventId;"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.event(id).steupCEQJ(1,{endPoint:true})"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 121, "indent": 0, "parameters": [32, 32, 1]}, {"code": 121, "indent": 0, "parameters": [33, 33, 1]}, {"code": 121, "indent": 0, "parameters": [14, 14, 1]}, {"code": 108, "indent": 0, "parameters": ["亲密接触监听器"]}, {"code": 121, "indent": 0, "parameters": [46, 46, 0]}, {"code": 230, "indent": 0, "parameters": [4]}, {"code": 355, "indent": 0, "parameters": ["QJ.MPMZ.tl._imoutoUtilSkinship()"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["去客厅看动画"]}, {"code": 108, "indent": 0, "parameters": ["防误触"]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen._pictureCidArray = [];"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 111, "indent": 0, "parameters": [12, "!$gameScreen.picture(16) || !$gameScreen.picture(16)._y > 150"]}, {"code": 117, "indent": 1, "parameters": [16]}, {"code": 117, "indent": 1, "parameters": [31]}, {"code": 356, "indent": 1, "parameters": [">图片快捷操作 : 批量图片[2,5] : 修改单属性 : 透明度[0] : 时间[30]"]}, {"code": 117, "indent": 1, "parameters": [26]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 231, "indent": 1, "parameters": [16, "mio_tachie_kao_ida", 0, 0, 1000, 1600, 100, 100, 255, 0]}, {"code": 355, "indent": 1, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.steupCEQJ(38,eid)"]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 231, "indent": 1, "parameters": [16, "mio_tachie_kao_ida", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [204, 204, 0, 4, "\"上下震动\""]}, {"code": 117, "indent": 0, "parameters": [35]}, {"code": 111, "indent": 0, "parameters": [12, "this.later "]}, {"code": 111, "indent": 1, "parameters": [12, "this.later > 60"]}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(6);"]}, {"code": 231, "indent": 2, "parameters": [16, "mio_tachie_kao_=ω=", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(7);"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(8);"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(10);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 231, "indent": 0, "parameters": [16, "mio_tachie_kao_=ω=", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(11);"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameSelfVariables.value([54, 5, 'animeEpisode']) > 1"]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(13);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(12);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 231, "indent": 0, "parameters": [16, "mio_tachie_kao_happy", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 122, "indent": 0, "parameters": [204, 204, 0, 4, "\"上下震动\""]}, {"code": 117, "indent": 0, "parameters": [35]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(14);"]}, {"code": 355, "indent": 0, "parameters": ["var pic_ids = $gameNumberArray.value(41);"]}, {"code": 655, "indent": 0, "parameters": ["pic_ids.forEach(function(pic_id) {"]}, {"code": 655, "indent": 0, "parameters": ["    var picture = $gameScreen.picture(pic_id);"]}, {"code": 655, "indent": 0, "parameters": ["var data = {  \"type\":\"smoothMove\","]}, {"code": 655, "indent": 0, "parameters": ["\"valueX\":1100,"]}, {"code": 655, "indent": 0, "parameters": ["\"valueY\":0,"]}, {"code": 655, "indent": 0, "parameters": ["\"time\":60,"]}, {"code": 655, "indent": 0, "parameters": ["\"cur_time\":0,\"cur_speedX\":0,\"cur_speedY\":0,}"]}, {"code": 655, "indent": 0, "parameters": ["    if (picture) { picture._drill_PSh_commandChangeTank.push(data);}});"]}, {"code": 250, "indent": 0, "parameters": [{"name": "カートゥーン系走る音", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["………………"]}, {"code": 401, "indent": 0, "parameters": ["………………"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameSelfVariables.value([54, 5, 'animeEpisode']) > 1"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(15);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "木の床を歩く足音", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 242, "indent": 0, "parameters": [2]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 122, "indent": 0, "parameters": [13, 13, 0, 0, 5]}, {"code": 201, "indent": 0, "parameters": [0, 54, 6, 5, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 1}, null, {"id": 20, "name": "序章-初回奖励选项", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 2, "characterIndex": 1}, "list": [{"code": 121, "indent": 0, "parameters": [14, 14, 0]}, {"code": 356, "indent": 0, "parameters": [">对话框皮肤 : 只姓名框窗口 : 修改样式 : 样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">外发光效果 : 固定对话框外发光 : 颜色[11] : 厚度[2] : 偏移[3,3]"]}, {"code": 355, "indent": 0, "parameters": ["ImageManager.reservePicture(\"sis_feature_focus0\"); "]}, {"code": 655, "indent": 0, "parameters": ["ImageManager.reservePicture(\"sis_feature_focus1\");"]}, {"code": 655, "indent": 0, "parameters": ["ImageManager.reservePicture(\"sis_feature_focus2\");"]}, {"code": 655, "indent": 0, "parameters": ["ImageManager.reservePicture(\"sis_feature_focus3\");"]}, {"code": 231, "indent": 0, "parameters": [1, "sister_room_night_fine", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [5, "sis_feature_focus0", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 108, "indent": 0, "parameters": ["模糊滤镜"]}, {"code": 355, "indent": 0, "parameters": ["[1,5].forEach(function(pic_id) {"]}, {"code": 655, "indent": 0, "parameters": ["let pic = $gameScreen.picture(pic_id);"]}, {"code": 655, "indent": 0, "parameters": ["if (pic) {"]}, {"code": 655, "indent": 0, "parameters": ["var id = \"blur\" +pic_id ;"]}, {"code": 655, "indent": 0, "parameters": ["var filterTarget = 5000 + pic_id;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.createFilter(id, \"blur\", filterTarget);"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.setFilter( id ,[8]);"]}, {"code": 655, "indent": 0, "parameters": ["   }"]}, {"code": 655, "indent": 0, "parameters": ["})"]}, {"code": 224, "indent": 0, "parameters": [[0, 0, 0, 255], 900, false]}, {"code": 355, "indent": 0, "parameters": ["[1,5].forEach(function(pic_id) {"]}, {"code": 655, "indent": 0, "parameters": ["let pic = $gameScreen.picture(pic_id);"]}, {"code": 655, "indent": 0, "parameters": ["if (pic) {"]}, {"code": 655, "indent": 0, "parameters": ["var id = \"blur\" +pic_id ;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.moveFilter(id, [1], 300);"]}, {"code": 655, "indent": 0, "parameters": ["   }"]}, {"code": 655, "indent": 0, "parameters": ["})"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 60 : 1"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE prologue_sis_01"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(0);"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 224, "indent": 0, "parameters": [[0, 0, 0, 240], 600, false]}, {"code": 355, "indent": 0, "parameters": ["[1,5].forEach(function(pic_id) {"]}, {"code": 655, "indent": 0, "parameters": ["let pic = $gameScreen.picture(pic_id);"]}, {"code": 655, "indent": 0, "parameters": ["if (pic) {"]}, {"code": 655, "indent": 0, "parameters": ["var id = \"blur\" +pic_id ;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.setFilter( id ,[8]);"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.moveFilter(id, [1], 300);"]}, {"code": 655, "indent": 0, "parameters": ["   }"]}, {"code": 655, "indent": 0, "parameters": ["})"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 30 : 1"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE prologue_sis_02"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(1);"]}, {"code": 224, "indent": 0, "parameters": [[0, 0, 0, 200], 400, false]}, {"code": 355, "indent": 0, "parameters": ["[1,5].forEach(function(pic_id) {"]}, {"code": 655, "indent": 0, "parameters": ["let pic = $gameScreen.picture(pic_id);"]}, {"code": 655, "indent": 0, "parameters": ["if (pic) {"]}, {"code": 655, "indent": 0, "parameters": ["var id = \"blur\" +pic_id ;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.setFilter( id ,[4]);"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.moveFilter(id, [0], 240);"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.eraseFilterAfterMove(id);"]}, {"code": 655, "indent": 0, "parameters": ["   }"]}, {"code": 655, "indent": 0, "parameters": ["})"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(2);"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE prologue_sis_03"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(3);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(4);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(5);"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 231, "indent": 0, "parameters": [5, "sis_feature_focus1", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 0, "parameters": [6]}, {"code": 231, "indent": 0, "parameters": [5, "sis_feature_focus2", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 231, "indent": 0, "parameters": [5, "sis_feature_focus3", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 108, "indent": 0, "parameters": ["选项赋予名称"]}, {"code": 355, "indent": 0, "parameters": ["const idx   = 7;"]}, {"code": 655, "indent": 0, "parameters": ["const eid   = String(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["const mapId = $gameMap.mapId();"]}, {"code": 655, "indent": 0, "parameters": ["const key   = `MapEventDialogue${mapId}`;"]}, {"code": 655, "indent": 0, "parameters": ["const dialogueTable = window[key] || {};"]}, {"code": 655, "indent": 0, "parameters": ["const textArray     = dialogueTable[eid]?.[String(idx)];"]}, {"code": 655, "indent": 0, "parameters": ["const option1 = textArray[0];"]}, {"code": 655, "indent": 0, "parameters": ["const option2 = textArray[1];"]}, {"code": 655, "indent": 0, "parameters": [" $gameStrings.setValue(6,option1);"]}, {"code": 655, "indent": 0, "parameters": [" $gameStrings.setValue(7,option2);"]}, {"code": 108, "indent": 0, "parameters": ["修正选项组图标"]}, {"code": 355, "indent": 0, "parameters": ["$gameSystem._drill_DCB_curStyle = 25"]}, {"code": 355, "indent": 0, "parameters": ["let style = [\"eventButtons_talk2\", \"eventButtons_talk2\"];"]}, {"code": 655, "indent": 0, "parameters": ["DrillUp.g_DCB_data[24].btn_src = style;"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE prologue_sis_04"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(6);"]}, {"code": 102, "indent": 0, "parameters": [["<<$gameVariables.value(1)!==99>>\\str[6]", "\\c[15]\\str[7]"], -1, -1, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "<<$gameVariables.value(1)!==99>>\\str[6]"]}, {"code": 356, "indent": 1, "parameters": [">图片快捷操作 : 图片[5] : 修改单属性 : 透明度[0] : 时间[30]"]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 250, "indent": 1, "parameters": [{"name": "衣擦れ　腕と体・腕をぶつける音", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 1, "parameters": [5, "mio_tachie_@_@ - 副本", 0, 0, -50, -1400, 120, 120, 255, 0]}, {"code": 356, "indent": 1, "parameters": [">显现动作 : 图片[5] : 放大出现 : 时间[30] : 缓冲时间[30]"]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(8);"]}, {"code": 241, "indent": 1, "parameters": [{"name": "おっかなびっくり", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 1, "parameters": ["AudioManager.fadeInBgm(6)"]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(9);"]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE prologue_sis_11"]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(10);"]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(11);"]}, {"code": 250, "indent": 1, "parameters": [{"name": "柔らかい(ゼリースライムぽわん)_1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [45]}, {"code": 250, "indent": 1, "parameters": [{"name": "柔らかいもの(揺れ触る等)ポヨン", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(12);"]}, {"code": 356, "indent": 1, "parameters": [">持续动作 : 图片[5] : 左右震动 : 持续时间[40] : 周期[10] : 震动幅度[7]"]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE prologue_sis_12"]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(13);"]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(14);"]}, {"code": 356, "indent": 1, "parameters": [">持续动作 : 图片[5] : 左右震动 : 持续时间[无限] : 周期[6] : 震动幅度[1]"]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE prologue_sis_13"]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(15);"]}, {"code": 356, "indent": 1, "parameters": [">持续动作 : 图片[5] : 立即终止动作"]}, {"code": 356, "indent": 1, "parameters": [">消失动作 : 图片[5] : 移动消失 : 时间[20] : 方向角度[270] : 移动距离[1600]"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Damage4", "volume": 90, "pitch": 80, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["DS_方向設定 90"]}, {"code": 225, "indent": 1, "parameters": [9, 8, 60, false]}, {"code": 224, "indent": 1, "parameters": [[0, 0, 0, 185], 90, false]}, {"code": 230, "indent": 1, "parameters": [45]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(16);"]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(17);"]}, {"code": 231, "indent": 1, "parameters": [5, "mio_tachie_duqi", 0, 0, 1000, 1600, 100, 100, 255, 0]}, {"code": 356, "indent": 1, "parameters": [">图片快捷操作 : 图片[5] : 增减速移动到 : 相对位置[0,-1450] : 时间[60]"]}, {"code": 230, "indent": 1, "parameters": [40]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE prologue_sis_14"]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(18);"]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(19);"]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE prologue_sis_15"]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(20);"]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(21);"]}, {"code": 231, "indent": 1, "parameters": [5, "mio_tachie_@_@", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 1, "parameters": [">持续动作 : 图片[5] : 上下震动 : 持续时间[30] : 周期[15] : 震动幅度[5]"]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE prologue_sis_16"]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(22);"]}, {"code": 108, "indent": 1, "parameters": ["妹妹坐下演出"]}, {"code": 355, "indent": 1, "parameters": ["let id = this._eventId;"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.event(id).steupCEQJ(6)"]}, {"code": 230, "indent": 1, "parameters": [150]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(23);"]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(24);"]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(25);"]}, {"code": 242, "indent": 1, "parameters": [1]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 122, "indent": 1, "parameters": [20, 20, 2, 0, 30]}, {"code": 122, "indent": 1, "parameters": [16, 16, 1, 2, 25, 45]}, {"code": 117, "indent": 1, "parameters": [8]}, {"code": 313, "indent": 1, "parameters": [0, 2, 0, 30]}, {"code": 356, "indent": 1, "parameters": ["P_CALL_CE 5 17 1"]}, {"code": 356, "indent": 1, "parameters": ["P_CALL_CE 5 15 4"]}, {"code": 356, "indent": 1, "parameters": ["P_CALL_CE 5 16 5"]}, {"code": 117, "indent": 1, "parameters": [28]}, {"code": 356, "indent": 1, "parameters": [">独立开关 : 事件[17] : A : 开启"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\c[15]\\str[7]"]}, {"code": 231, "indent": 1, "parameters": [5, "sis_feature_focus3", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE prologue_sis_05"]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(26);"]}, {"code": 241, "indent": 1, "parameters": [{"name": "セレスタの森", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 1, "parameters": ["AudioManager.fadeInBgm(5)"]}, {"code": 108, "indent": 1, "parameters": ["妹妹坐下演出"]}, {"code": 355, "indent": 1, "parameters": ["let id = this._eventId;"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.event(id).steupCEQJ(6)"]}, {"code": 230, "indent": 1, "parameters": [90]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(27);"]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(28);"]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE prologue_sis_06"]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(29);"]}, {"code": 108, "indent": 1, "parameters": ["选项赋予名称"]}, {"code": 355, "indent": 1, "parameters": ["const idx   = 31;"]}, {"code": 655, "indent": 1, "parameters": ["const eid   = String(this._eventId);  const mapId = $gameMap.mapId();"]}, {"code": 655, "indent": 1, "parameters": ["const key   = `MapEventDialogue${mapId}`;  const dialogueTable = window[key] || {};"]}, {"code": 655, "indent": 1, "parameters": ["const textArray = dialogueTable[eid]?.[String(idx)];"]}, {"code": 655, "indent": 1, "parameters": ["const option1 = textArray[0];  $gameStrings.setValue(6,option1);"]}, {"code": 655, "indent": 1, "parameters": ["const option2 = textArray[1];  $gameStrings.setValue(7,option2);"]}, {"code": 655, "indent": 1, "parameters": ["const option3 = textArray[2];  $gameStrings.setValue(8,option3);"]}, {"code": 655, "indent": 1, "parameters": ["const option4 = textArray[3];  $gameStrings.setValue(9,option4);"]}, {"code": 655, "indent": 1, "parameters": ["const option5 = textArray[4];  $gameStrings.setValue(10,option5);"]}, {"code": 108, "indent": 1, "parameters": ["修正选项组图标"]}, {"code": 355, "indent": 1, "parameters": ["$gameSystem._drill_DCB_curStyle = 25;"]}, {"code": 655, "indent": 1, "parameters": ["let style = [];"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(4) < 0 ? style.push(\"eventButtons_father\") : null;"]}, {"code": 655, "indent": 1, "parameters": ["style.push(\"eventButtons_moon\");"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.value(4) < 0 ? style.push(\"eventButtons_bowknot\") : null;"]}, {"code": 655, "indent": 1, "parameters": ["style.push(\"eventButtons_steak\", \"eventButtons_unknown\");"]}, {"code": 655, "indent": 1, "parameters": ["DrillUp.g_DCB_data[24].btn_src = style;"]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(30);"]}, {"code": 102, "indent": 1, "parameters": [["<<v[4] >= 0>>\\str[6]", "\\str[7]", "<<v[4] >= 0>>\\c[15]\\str[8]", "\\c[15]\\str[9]", "\\c[15]\\str[10]"], -1, -1, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "<<v[4] >= 0>>\\str[6]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\str[7]"]}, {"code": 355, "indent": 2, "parameters": ["let id = this._eventId;"]}, {"code": 655, "indent": 2, "parameters": ["$gameMap.event(id).steupCEQJ(2)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "<<v[4] >= 0>>\\c[15]\\str[8]"]}, {"code": 355, "indent": 2, "parameters": ["let id = this._eventId;"]}, {"code": 655, "indent": 2, "parameters": ["$gameMap.event(id).steupCEQJ(3)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "\\c[15]\\str[9]"]}, {"code": 355, "indent": 2, "parameters": ["let id = this._eventId;"]}, {"code": 655, "indent": 2, "parameters": ["$gameMap.event(id).steupCEQJ(4)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [4, "\\c[15]\\str[10]"]}, {"code": 355, "indent": 2, "parameters": ["let id = this._eventId;"]}, {"code": 655, "indent": 2, "parameters": ["$gameMap.event(id).steupCEQJ(5)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 0, "characterIndex": 3}, "list": [{"code": 356, "indent": 0, "parameters": [">图片快捷操作 : 批量图片[2,5,6] : 修改单属性 : 透明度[0] : 时间[40]"]}, {"code": 230, "indent": 0, "parameters": [40]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 235, "indent": 0, "parameters": [4]}, {"code": 231, "indent": 0, "parameters": [5, "mio_tachie_=ω=", 0, 0, 200, 1600, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": [">图片快捷操作 : 图片[5] : 增减速移动到 : 相对位置[0,-1450] : 时间[60]"]}, {"code": 230, "indent": 0, "parameters": [40]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE prologue_sis_17"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(32);"]}, {"code": 231, "indent": 0, "parameters": [5, "mio_tachie_happy", 0, 0, 200, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": [">持续动作 : 图片[5] : 左右摇晃 : 持续时间[无限] : 周期[120] : 摇晃幅度[2]"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE prologue_sis_18"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(33);"]}, {"code": 356, "indent": 0, "parameters": [">持续动作 : 图片[5] : 立即终止动作"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(34);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(35);"]}, {"code": 231, "indent": 0, "parameters": [5, "mio_tachie_OAO", 0, 0, 200, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE prologue_sis_19"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(36);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(37);"]}, {"code": 355, "indent": 0, "parameters": ["ImageManager.reservePicture(\"magician'sRoom1\"); "]}, {"code": 655, "indent": 0, "parameters": ["ImageManager.reservePicture(\"magician'sRoom2\");"]}, {"code": 655, "indent": 0, "parameters": ["ImageManager.reservePicture(\"magician'sRoom3\");"]}, {"code": 242, "indent": 0, "parameters": [2]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 231, "indent": 0, "parameters": [1, "magician'sRoom1", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 241, "indent": 0, "parameters": [{"name": "Les-Hortensias-sous-la-Pluie", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["AudioManager.fadeInBgm(5)"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(38);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(39);"]}, {"code": 250, "indent": 0, "parameters": [{"name": "服の衣擦れの音", "volume": 90, "pitch": 50, "pan": 0}]}, {"code": 231, "indent": 0, "parameters": [1, "magician'sRoom2", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 231, "indent": 0, "parameters": [1, "magician'sRoom3", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 231, "indent": 0, "parameters": [1, "c04_0087_c02-2", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "瓦Rを掘るレスキューワーカー", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(40);"]}, {"code": 246, "indent": 0, "parameters": [2]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(41);"]}, {"code": 250, "indent": 0, "parameters": [{"name": "キラーン（ひらめき・アイテム発見・獲得）", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["dingk.Loot.directlyAcquireDrops(1,true)"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(42);"]}, {"code": 231, "indent": 0, "parameters": [5, "mio_tachie_=ω=", 0, 0, 200, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE prologue_sis_20"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(43);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(44);"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE prologue_sis_22"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(45);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(46);"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 235, "indent": 0, "parameters": [5]}, {"code": 231, "indent": 0, "parameters": [1, "sister_room_night_fine", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": [">独立开关 : 事件[1] : A : 开启"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(47);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(48);"]}, {"code": 356, "indent": 0, "parameters": [">独立开关 : 事件[17] : A : 开启"]}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 123, "indent": 0, "parameters": ["B", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "C", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 0, "characterIndex": 3}, "list": [{"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(49);"]}, {"code": 356, "indent": 0, "parameters": [">图片快捷操作 : 批量图片[2,5,6] : 修改单属性 : 透明度[0] : 时间[40]"]}, {"code": 230, "indent": 0, "parameters": [40]}, {"code": 231, "indent": 0, "parameters": [70, "mio_tachie_ida", 0, 0, 200, 1600, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": [">图片快捷操作 : 图片[70] : 增减速移动到 : 相对位置[0,-1450] : 时间[30]"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 235, "indent": 0, "parameters": [4]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE prologue_sis_07"]}, {"code": 356, "indent": 0, "parameters": [">持续动作 : 图片[70] : 原地小跳 : 持续时间[60] : 周期[60] : 跳跃高度[200]"]}, {"code": 250, "indent": 0, "parameters": [{"name": "037myuu_YumeSE_FukidashiHatena01", "volume": 75, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(50);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(51);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(52);"]}, {"code": 231, "indent": 0, "parameters": [70, "mio_tachie_happy", 0, 0, 200, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": [">持续动作 : 图片[70] : 上下震动 : 持续时间[60] : 周期[30] : 震动幅度[3]"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE prologue_sis_08"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(53);"]}, {"code": 356, "indent": 0, "parameters": [">持续动作 : 图片[70] : 左右摇晃 : 持续时间[400] : 周期[100] : 摇晃幅度[3]"]}, {"code": 356, "indent": 0, "parameters": [">图片快捷操作 : 图片[70] : 增减速移动到 : 相对位置[-960,0] : 时间[320]"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE prologue_sis_09"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(54);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(55);"]}, {"code": 313, "indent": 0, "parameters": [0, 1, 0, 46]}, {"code": 235, "indent": 0, "parameters": [70]}, {"code": 355, "indent": 0, "parameters": ["$gameMap.event(1).steupCEQJ(2)"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 355, "indent": 0, "parameters": ["$gameMap.event(17).steupCEQJ(1)"]}, {"code": 123, "indent": 0, "parameters": ["C", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 0, "characterIndex": 3}, "list": [{"code": 356, "indent": 0, "parameters": [">图片快捷操作 : 批量图片[2,5,6] : 修改单属性 : 透明度[0] : 时间[40]"]}, {"code": 230, "indent": 0, "parameters": [40]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 235, "indent": 0, "parameters": [4]}, {"code": 231, "indent": 0, "parameters": [5, "mio_tachie_=ω=", 0, 0, 200, 1600, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": [">图片快捷操作 : 图片[5] : 增减速移动到 : 相对位置[0,-1450] : 时间[80]"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE prologue_sis_21"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(56);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(57);"]}, {"code": 356, "indent": 0, "parameters": [">消失动作 : 图片[5] : 移动消失 : 时间[60] : 方向角度[90] : 移动距离[500]"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 235, "indent": 0, "parameters": [5]}, {"code": 355, "indent": 0, "parameters": ["$gameMap.event(1).steupCEQJ(2)"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(58);"]}, {"code": 313, "indent": 0, "parameters": [0, 1, 0, 59]}, {"code": 355, "indent": 0, "parameters": ["$gameMap.event(17).steupCEQJ(1)"]}, {"code": 123, "indent": 0, "parameters": ["D", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 356, "indent": 0, "parameters": [">消失动作 : 图片[5] : 移动消失 : 时间[40] : 方向角度[90] : 移动距离[400]"]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 231, "indent": 0, "parameters": [1, "sister_room_night_fine", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [2, "bunny<PERSON><PERSON>", 0, 0, 461, 562, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [5, "sis_chibi_normal0", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [6, "chair hand", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["let text = $gameStrings.value(40).split(\"\\n\");"]}, {"code": 655, "indent": 0, "parameters": ["let picture = $gameScreen.picture(2);"]}, {"code": 655, "indent": 0, "parameters": ["let bind = DrillUp.g_MPFP_list[6];"]}, {"code": 655, "indent": 0, "parameters": ["if (!picture._drill_MPFP_bean) {"]}, {"code": 655, "indent": 0, "parameters": ["  picture._drill_MPFP_bean = new Drill_MPFP_Bean();"]}, {"code": 655, "indent": 0, "parameters": ["  $gameTemp._drill_MPFP_needRestatistics = true; picture.drill_COPWM_checkData(); }"]}, {"code": 655, "indent": 0, "parameters": ["picture._drill_MPFP_bean.drill_bean_setVisible(true);picture._drill_MPFP_bean.drill_bean_setContextList(text);"]}, {"code": 655, "indent": 0, "parameters": ["picture._drill_MPFP_bean.drill_bean_setSkinStyle(bind['style_mode'], bind['style_lockedId']);"]}, {"code": 356, "indent": 0, "parameters": [">图片快捷操作 : 批量图片[2,5,6] : 修改单属性 : 透明度[255] : 时间[40]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 3}, null, null, null, {"id": 24, "name": "睡觉选项分支", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 1, "characterIndex": 3}, "list": [{"code": 108, "indent": 0, "parameters": ["由小人触发的情况"]}, {"code": 111, "indent": 0, "parameters": [12, "!$gameActors.actor(2).isStateAffected(27)"]}, {"code": 117, "indent": 1, "parameters": [16]}, {"code": 117, "indent": 1, "parameters": [31]}, {"code": 313, "indent": 1, "parameters": [0, 2, 0, 27]}, {"code": 356, "indent": 1, "parameters": [">图片快捷操作 : 批量图片[2,5] : 修改单属性 : 透明度[0] : 时间[45]"]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 117, "indent": 1, "parameters": [26]}, {"code": 111, "indent": 1, "parameters": [12, "$gameSystem.hour() >= 21"]}, {"code": 231, "indent": 2, "parameters": [16, "mio_tachie_kao_-A-", 0, 0, 1000, 1600, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 231, "indent": 2, "parameters": [16, "mio_tachie_kao_mu2", 0, 0, 1000, 1600, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.steupCEQJ(38,eid)"]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [4, 2, 6, 35]}, {"code": 231, "indent": 1, "parameters": [16, "mio_tachie_kao_shy2", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE sis_room_toilet_15"]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(0);"]}, {"code": 231, "indent": 1, "parameters": [16, "mio_tachie_kao_shy3", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE sis_room_toilet_16"]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(1);"]}, {"code": 119, "indent": 1, "parameters": ["一人睡觉"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "$gameSystem.hour() >= 21 || $gameSystem.hour() < 12"]}, {"code": 108, "indent": 1, "parameters": ["修正选项组图标"]}, {"code": 355, "indent": 1, "parameters": ["$gameSystem._drill_DCB_curStyle = 25"]}, {"code": 355, "indent": 1, "parameters": ["let style = [\"eventButtons_sleep2\", \"eventButtons_returns\"];"]}, {"code": 655, "indent": 1, "parameters": ["DrillUp.g_DCB_data[24].btn_src = style;"]}, {"code": 108, "indent": 1, "parameters": ["选项赋予名称"]}, {"code": 355, "indent": 1, "parameters": ["const idx   = 2;"]}, {"code": 655, "indent": 1, "parameters": ["const eid   = String(this._eventId);"]}, {"code": 655, "indent": 1, "parameters": ["const mapId = $gameMap.mapId();"]}, {"code": 655, "indent": 1, "parameters": ["const key   = `MapEventDialogue${mapId}`;"]}, {"code": 655, "indent": 1, "parameters": ["const dialogueTable = window[key] || {};"]}, {"code": 655, "indent": 1, "parameters": ["const textArray     = dialogueTable[eid]?.[String(idx)];"]}, {"code": 655, "indent": 1, "parameters": ["const option1 = textArray[0];"]}, {"code": 655, "indent": 1, "parameters": ["const option2 = textArray[1];"]}, {"code": 655, "indent": 1, "parameters": [" $gameStrings.setValue(6,option1);"]}, {"code": 655, "indent": 1, "parameters": [" $gameStrings.setValue(7,option2);"]}, {"code": 230, "indent": 1, "parameters": [4]}, {"code": 102, "indent": 1, "parameters": [["\\str[6]", "\\c[15]\\str[7]"], 1, -1, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\str[6]"]}, {"code": 355, "indent": 2, "parameters": ["let id = this._eventId;"]}, {"code": 655, "indent": 2, "parameters": ["$gameMap.event(id).steupCEQJ(2)"]}, {"code": 115, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\c[15]\\str[7]"]}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(3);"]}, {"code": 355, "indent": 2, "parameters": ["$gameMap.event(18).start()"]}, {"code": 115, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$gameSystem.hour() >= 17"]}, {"code": 355, "indent": 2, "parameters": ["let id = this._eventId;"]}, {"code": 655, "indent": 2, "parameters": ["$gameMap.event(id).steupCEQJ(3)"]}, {"code": 115, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 115, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["正常时间段入睡"]}, {"code": 231, "indent": 0, "parameters": [16, "mio_tachie_kao_smile", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 111, "indent": 0, "parameters": [12, "Math.random() > 0.5"]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE sis_room_sleep_04"]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(4);"]}, {"code": 119, "indent": 1, "parameters": ["一人睡觉"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE sis_room_sleep_03"]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(5);"]}, {"code": 119, "indent": 1, "parameters": ["一人睡觉"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["一人睡觉"]}, {"code": 242, "indent": 0, "parameters": [2]}, {"code": 122, "indent": 0, "parameters": [13, 13, 0, 0, 1]}, {"code": 201, "indent": 0, "parameters": [0, 21, 9, 9, 0, 2]}, {"code": 115, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["早早入睡"]}, {"code": 108, "indent": 0, "parameters": ["修正选项组图标"]}, {"code": 355, "indent": 0, "parameters": ["$gameSystem._drill_DCB_curStyle = 25"]}, {"code": 355, "indent": 0, "parameters": ["let style = [\"eventButtons_sleep2\", \"eventButtons_returns\"];"]}, {"code": 655, "indent": 0, "parameters": ["DrillUp.g_DCB_data[24].btn_src = style;"]}, {"code": 108, "indent": 0, "parameters": ["选项赋予名称"]}, {"code": 355, "indent": 0, "parameters": ["const idx   = 2;"]}, {"code": 655, "indent": 0, "parameters": ["const eid   = String(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["const mapId = $gameMap.mapId();"]}, {"code": 655, "indent": 0, "parameters": ["const key   = `MapEventDialogue${mapId}`;"]}, {"code": 655, "indent": 0, "parameters": ["const dialogueTable = window[key] || {};"]}, {"code": 655, "indent": 0, "parameters": ["const textArray     = dialogueTable[eid]?.[String(idx)];"]}, {"code": 655, "indent": 0, "parameters": ["const option1 = textArray[0];"]}, {"code": 655, "indent": 0, "parameters": ["const option2 = textArray[1];"]}, {"code": 655, "indent": 0, "parameters": [" $gameStrings.setValue(6,option1);"]}, {"code": 655, "indent": 0, "parameters": [" $gameStrings.setValue(7,option2);"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameSelfVariables.get(this, 'earlySleep') >= 3"]}, {"code": 231, "indent": 1, "parameters": [16, "mio_tachie_kao_smile2", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE sis_room_sleep_07"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 231, "indent": 1, "parameters": [16, "mio_tachie_kao_OAO", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE sis_room_sleep_01"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["if ($gameSelfVariables.get(this, 'earlySleep') >= 3) {"]}, {"code": 655, "indent": 0, "parameters": ["this.showMapEventDialogue(6);"]}, {"code": 655, "indent": 0, "parameters": ["} else {"]}, {"code": 655, "indent": 0, "parameters": ["this.showMapEventDialogue(7);"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 102, "indent": 0, "parameters": [["\\str[6]", "\\c[15]\\str[7]"], 1, -1, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\str[6]"]}, {"code": 108, "indent": 1, "parameters": ["累积早睡次数"]}, {"code": 355, "indent": 1, "parameters": ["$gameSelfVariables.add(this, 'earlySleep', 1)"]}, {"code": 231, "indent": 1, "parameters": [16, "mio_tachie_kao_smile2", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE sis_room_sleep_02"]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(8);"]}, {"code": 119, "indent": 1, "parameters": ["一人睡觉"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\c[15]\\str[7]"]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(3);"]}, {"code": 355, "indent": 1, "parameters": ["$gameMap.event(18).start()"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["一人睡觉"]}, {"code": 242, "indent": 0, "parameters": [2]}, {"code": 122, "indent": 0, "parameters": [13, 13, 0, 0, 1]}, {"code": 201, "indent": 0, "parameters": [0, 21, 9, 9, 0, 2]}, {"code": 115, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 8, "y": 0}, {"id": 25, "name": "和妹妹聊天", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 1, "characterIndex": 3}, "list": [{"code": 108, "indent": 0, "parameters": ["识别语言生成选项文本"]}, {"code": 108, "indent": 0, "parameters": ["修正选项组图标"]}, {"code": 355, "indent": 0, "parameters": ["$gameSystem._drill_DCB_curStyle = 25"]}, {"code": 355, "indent": 0, "parameters": ["let style = [];"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.value(20) < 50 ? style.push(\"eventButtons_bowknot\") : null;"]}, {"code": 655, "indent": 0, "parameters": ["style.push(\"eventButtons_talk2\", \"eventButtons_unknown\");"]}, {"code": 655, "indent": 0, "parameters": ["DrillUp.g_DCB_data[24].btn_src = style;"]}, {"code": 108, "indent": 0, "parameters": ["选项赋予名称"]}, {"code": 355, "indent": 0, "parameters": ["const idx   = 0;"]}, {"code": 655, "indent": 0, "parameters": ["const eid   = String(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["const mapId = $gameMap.mapId();"]}, {"code": 655, "indent": 0, "parameters": ["const key   = `MapEventDialogue${mapId}`;"]}, {"code": 655, "indent": 0, "parameters": ["const dialogueTable = window[key] || {};"]}, {"code": 655, "indent": 0, "parameters": ["const textArray     = dialogueTable[eid]?.[String(idx)];"]}, {"code": 655, "indent": 0, "parameters": ["const option1 = textArray[1];"]}, {"code": 655, "indent": 0, "parameters": ["const option2 = textArray[2];"]}, {"code": 655, "indent": 0, "parameters": ["const option3 = textArray[3];"]}, {"code": 655, "indent": 0, "parameters": [" $gameStrings.setValue(6,option1);"]}, {"code": 655, "indent": 0, "parameters": [" $gameStrings.setValue(7,option2);"]}, {"code": 655, "indent": 0, "parameters": [" $gameStrings.setValue(8,option3);"]}, {"code": 230, "indent": 0, "parameters": [4]}, {"code": 102, "indent": 0, "parameters": [["<<v[1]!==99>>\\str[5]", "<<v[20]>=50>>\\c[15]\\str[6]", "\\c[15]\\str[7]", "\\c[15]\\str[8]"], -2, -1, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "<<v[1]!==99>>\\str[5]"]}, {"code": 355, "indent": 1, "parameters": ["$gameMap.event(18).start()"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "<<v[20]>=50>>\\c[15]\\str[6]"]}, {"code": 108, "indent": 1, "parameters": ["讨好妹妹"]}, {"code": 355, "indent": 1, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.event(eid).steupCEQJ(4);"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "\\c[15]\\str[7]"]}, {"code": 108, "indent": 1, "parameters": ["冒险话题"]}, {"code": 355, "indent": 1, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.event(eid).steupCEQJ(2);"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [3, "\\c[15]\\str[8]"]}, {"code": 108, "indent": 1, "parameters": ["和妹妹闲聊"]}, {"code": 355, "indent": 1, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.event(eid).steupCEQJ(3);"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 403, "indent": 0, "parameters": [6, null]}, {"code": 355, "indent": 1, "parameters": ["$gameMap.event(18).start()"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 4, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["冒险故事对话模板"]}, {"code": 355, "indent": 0, "parameters": ["let index = 6 + Math.randomInt(5);"]}, {"code": 655, "indent": 0, "parameters": ["this.showMapEventDialogue(index);"]}, {"code": 108, "indent": 0, "parameters": ["反应概率权重修正"]}, {"code": 355, "indent": 0, "parameters": ["let kimochi = $gameVariables.value(20);"]}, {"code": 655, "indent": 0, "parameters": ["let verybad, bad, good, verygood;"]}, {"code": 655, "indent": 0, "parameters": ["const normal = 30; "]}, {"code": 655, "indent": 0, "parameters": ["verybad = kimochi <= 30 ? 50 - kimochi : 5;"]}, {"code": 655, "indent": 0, "parameters": ["bad = kimochi <= 50 ? 50 - kimochi : 10;"]}, {"code": 655, "indent": 0, "parameters": ["good = kimochi >= 50 ? kimochi : 10;"]}, {"code": 655, "indent": 0, "parameters": ["verygood = kimochi >= 70 ? kimochi : 5;"]}, {"code": 655, "indent": 0, "parameters": ["$gameNumberArray.setValue(42, [verybad, bad, normal, good, verygood]);"]}, {"code": 108, "indent": 0, "parameters": ["妹妹反应判定"]}, {"code": 355, "indent": 0, "parameters": ["var baseWeights = $gameNumberArray.value(42); "]}, {"code": 655, "indent": 0, "parameters": ["var randomInex = chahuiUtil.weightedRandom(baseWeights);"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.setValue(36, randomInex);"]}, {"code": 108, "indent": 0, "parameters": ["执行妹妹反应演出"]}, {"code": 355, "indent": 0, "parameters": ["$gameMap.event(29).steupCEQJ(1)"]}, {"code": 115, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["和妹妹闲聊"]}, {"code": 355, "indent": 0, "parameters": ["let index = Math.randomInt(5);"]}, {"code": 655, "indent": 0, "parameters": ["this.showMapEventDialogue(2, index);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(1);"]}, {"code": 108, "indent": 0, "parameters": ["反应概率权重修正"]}, {"code": 355, "indent": 0, "parameters": ["let kimochi = $gameVariables.value(20);"]}, {"code": 655, "indent": 0, "parameters": ["let verybad, bad, good, verygood;"]}, {"code": 655, "indent": 0, "parameters": ["const normal = 40; "]}, {"code": 655, "indent": 0, "parameters": ["verybad = kimochi <= 30 ? 30 - kimochi : 5;"]}, {"code": 655, "indent": 0, "parameters": ["bad = kimochi <= 50 ? 50 - kimochi : 10;"]}, {"code": 655, "indent": 0, "parameters": ["good = kimochi >= 50 ? kimochi : 10;"]}, {"code": 655, "indent": 0, "parameters": ["verygood = kimochi >= 70 ? kimochi : 5;"]}, {"code": 655, "indent": 0, "parameters": ["$gameNumberArray.setValue(42, [verybad, bad, normal, good, verygood]);"]}, {"code": 108, "indent": 0, "parameters": ["妹妹反应判定"]}, {"code": 355, "indent": 0, "parameters": ["var baseWeights = $gameNumberArray.value(42); "]}, {"code": 655, "indent": 0, "parameters": ["var randomInex = chahuiUtil.weightedRandom(baseWeights);"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.setValue(36, randomInex);"]}, {"code": 108, "indent": 0, "parameters": ["执行妹妹反应演出"]}, {"code": 355, "indent": 0, "parameters": ["$gameMap.event(29).steupCEQJ(1)"]}, {"code": 115, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 4, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["讨好妹妹"]}, {"code": 355, "indent": 0, "parameters": ["let index = 3 + Math.randomInt(3);"]}, {"code": 655, "indent": 0, "parameters": ["this.showMapEventDialogue(index);"]}, {"code": 108, "indent": 0, "parameters": ["反应概率权重修正"]}, {"code": 355, "indent": 0, "parameters": ["let kimochi = $gameVariables.value(20);"]}, {"code": 655, "indent": 0, "parameters": ["let verybad = 1;"]}, {"code": 655, "indent": 0, "parameters": ["let bad = 1;"]}, {"code": 655, "indent": 0, "parameters": ["let normal = 40 - Math.floor((50 - kimochi) / 2);"]}, {"code": 655, "indent": 0, "parameters": ["let good = Math.floor(60 - kimochi);"]}, {"code": 655, "indent": 0, "parameters": ["let verygood = Math.floor(80 - kimochi * 1.2);"]}, {"code": 655, "indent": 0, "parameters": ["$gameNumberArray.setValue(42, [verybad, bad, normal, good, verygood]);"]}, {"code": 108, "indent": 0, "parameters": ["妹妹反应判定"]}, {"code": 355, "indent": 0, "parameters": ["var baseWeights = $gameNumberArray.value(42); "]}, {"code": 655, "indent": 0, "parameters": ["var randomInex = chahuiUtil.weightedRandom(baseWeights);"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.setValue(36, randomInex);"]}, {"code": 108, "indent": 0, "parameters": ["妹妹反应"]}, {"code": 355, "indent": 0, "parameters": ["$gameMap.event(29).steupCEQJ(2)"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 6, "y": 0}, null, null, null, {"id": 29, "name": "聊天后妹妹的反应", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 0, "characterIndex": 2}, "list": [{"code": 108, "indent": 0, "parameters": ["聊天后妹妹的反应"]}, {"code": 111, "indent": 0, "parameters": [4, 2, 6, 35]}, {"code": 231, "indent": 1, "parameters": [16, "mio_tachie_kao_gaman2", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE sis_room_toilet_13"]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(0);"]}, {"code": 122, "indent": 1, "parameters": [17, 17, 1, 2, 1, 2]}, {"code": 122, "indent": 1, "parameters": [16, 16, 1, 2, 6, 15]}, {"code": 117, "indent": 1, "parameters": [12]}, {"code": 250, "indent": 1, "parameters": [{"name": "UI-ポン　コミカルな唇を鳴らす音", "volume": 70, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(1);"]}, {"code": 119, "indent": 1, "parameters": ["end"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 36, 0, 0, 0]}, {"code": 231, "indent": 1, "parameters": [16, "mio_tachie_kao_happy", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE sis_room_talk_01"]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(2);"]}, {"code": 117, "indent": 1, "parameters": [12]}, {"code": 108, "indent": 1, "parameters": ["心情变化"]}, {"code": 355, "indent": 1, "parameters": ["let type = $gameVariables.value(36);"]}, {"code": 655, "indent": 1, "parameters": ["let value = $gameVariables.value(20);"]}, {"code": 655, "indent": 1, "parameters": ["if (value <= 80) {"]}, {"code": 655, "indent": 1, "parameters": ["type *= 2;"]}, {"code": 655, "indent": 1, "parameters": ["value += (8 - type) + Math.randomInt(10 - type);"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.setValue(20,value);"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 108, "indent": 1, "parameters": ["好感度变化"]}, {"code": 355, "indent": 1, "parameters": ["let type = $gameVariables.value(36);"]}, {"code": 655, "indent": 1, "parameters": ["let value = $gameVariables.value(17);"]}, {"code": 655, "indent": 1, "parameters": ["type *= 2;"]}, {"code": 655, "indent": 1, "parameters": ["value += (10 - type) + Math.randomInt(10 - type);"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.setValue(17,value);"]}, {"code": 117, "indent": 1, "parameters": [12]}, {"code": 250, "indent": 1, "parameters": [{"name": "UI-ポン　コミカルな唇を鳴らす音", "volume": 70, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(3);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 36, 0, 1, 0]}, {"code": 231, "indent": 2, "parameters": [16, "mio_tachie_kao_smile", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["SV_PLAY_VOICE sis_room_talk_02"]}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(4);"]}, {"code": 108, "indent": 2, "parameters": ["心情变化"]}, {"code": 355, "indent": 2, "parameters": ["let type = $gameVariables.value(36);"]}, {"code": 655, "indent": 2, "parameters": ["let value = $gameVariables.value(20);"]}, {"code": 655, "indent": 2, "parameters": ["if (value <= 80) {"]}, {"code": 655, "indent": 2, "parameters": ["type *= 2;"]}, {"code": 655, "indent": 2, "parameters": ["value += (8 - type) + Math.randomInt(10 - type);"]}, {"code": 655, "indent": 2, "parameters": ["$gameVariables.setValue(20,value);"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 108, "indent": 2, "parameters": ["好感度变化"]}, {"code": 355, "indent": 2, "parameters": ["let type = $gameVariables.value(36);"]}, {"code": 655, "indent": 2, "parameters": ["let value = $gameVariables.value(17);"]}, {"code": 655, "indent": 2, "parameters": ["type *= 2;"]}, {"code": 655, "indent": 2, "parameters": ["value += (10 - type) + Math.randomInt(10 - type);"]}, {"code": 655, "indent": 2, "parameters": ["$gameVariables.setValue(17,value);"]}, {"code": 117, "indent": 2, "parameters": [12]}, {"code": 250, "indent": 2, "parameters": [{"name": "UI-ポン　コミカルな唇を鳴らす音", "volume": 70, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(3);"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [1, 36, 0, 2, 0]}, {"code": 231, "indent": 3, "parameters": [16, "mio_tachie_kao_OAO", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 3, "parameters": ["SV_PLAY_VOICE sis_room_talk_03"]}, {"code": 355, "indent": 3, "parameters": ["this.showMapEventDialogue(5);"]}, {"code": 108, "indent": 3, "parameters": ["心情变化"]}, {"code": 355, "indent": 3, "parameters": ["let type = $gameVariables.value(36);"]}, {"code": 655, "indent": 3, "parameters": ["let value = $gameVariables.value(20);"]}, {"code": 655, "indent": 3, "parameters": ["if (value <= 80) {"]}, {"code": 655, "indent": 3, "parameters": ["type *= 2;"]}, {"code": 655, "indent": 3, "parameters": ["value += (8 - type) + Math.randomInt(10 - type);"]}, {"code": 655, "indent": 3, "parameters": ["$gameVariables.setValue(20,value);"]}, {"code": 655, "indent": 3, "parameters": ["}"]}, {"code": 108, "indent": 3, "parameters": ["好感度变化"]}, {"code": 355, "indent": 3, "parameters": ["let type = $gameVariables.value(36);"]}, {"code": 655, "indent": 3, "parameters": ["let value = $gameVariables.value(17);"]}, {"code": 655, "indent": 3, "parameters": ["type *= 2;"]}, {"code": 655, "indent": 3, "parameters": ["value += (10 - type) + Math.randomInt(10 - type);"]}, {"code": 655, "indent": 3, "parameters": ["$gameVariables.setValue(17,value);"]}, {"code": 117, "indent": 3, "parameters": [12]}, {"code": 250, "indent": 3, "parameters": [{"name": "UI-ポン　コミカルな唇を鳴らす音", "volume": 70, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 3, "parameters": ["this.showMapEventDialogue(6);"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [1, 36, 0, 3, 0]}, {"code": 231, "indent": 4, "parameters": [16, "mio_tachie_kao_=ω=", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 4, "parameters": ["SV_PLAY_VOICE sis_room_talk_04"]}, {"code": 355, "indent": 4, "parameters": ["this.showMapEventDialogue(7);"]}, {"code": 108, "indent": 4, "parameters": ["心情变化"]}, {"code": 355, "indent": 4, "parameters": ["let type = $gameVariables.value(36);"]}, {"code": 655, "indent": 4, "parameters": ["let value = $gameVariables.value(20);"]}, {"code": 655, "indent": 4, "parameters": ["if (value <= 80) {"]}, {"code": 655, "indent": 4, "parameters": ["type *= 2;"]}, {"code": 655, "indent": 4, "parameters": ["value += (8 - type) + Math.randomInt(10 - type);"]}, {"code": 655, "indent": 4, "parameters": ["$gameVariables.setValue(20,value);"]}, {"code": 655, "indent": 4, "parameters": ["}"]}, {"code": 108, "indent": 4, "parameters": ["好感度变化"]}, {"code": 355, "indent": 4, "parameters": ["let type = $gameVariables.value(36);"]}, {"code": 655, "indent": 4, "parameters": ["let value = $gameVariables.value(17);"]}, {"code": 655, "indent": 4, "parameters": ["type *= 2;"]}, {"code": 655, "indent": 4, "parameters": ["value += (10 - type) + Math.randomInt(10 - type);"]}, {"code": 655, "indent": 4, "parameters": ["$gameVariables.setValue(17,value);"]}, {"code": 117, "indent": 4, "parameters": [12]}, {"code": 250, "indent": 4, "parameters": [{"name": "UI-ポン　コミカルな唇を鳴らす音", "volume": 70, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 4, "parameters": ["this.showMapEventDialogue(6);"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 111, "indent": 4, "parameters": [1, 36, 0, 4, 0]}, {"code": 231, "indent": 5, "parameters": [16, "mio_tachie_kao_ase", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 5, "parameters": ["SV_PLAY_VOICE sis_room_talk_05"]}, {"code": 355, "indent": 5, "parameters": ["this.showMapEventDialogue(8);"]}, {"code": 108, "indent": 5, "parameters": ["好感度变化"]}, {"code": 355, "indent": 5, "parameters": ["let type = $gameVariables.value(36);"]}, {"code": 655, "indent": 5, "parameters": ["let value = $gameVariables.value(17);"]}, {"code": 655, "indent": 5, "parameters": ["type *= 2;"]}, {"code": 655, "indent": 5, "parameters": ["value += (10 - type) + Math.randomInt(10 - type);"]}, {"code": 655, "indent": 5, "parameters": ["$gameVariables.setValue(17,value);"]}, {"code": 117, "indent": 5, "parameters": [12]}, {"code": 250, "indent": 5, "parameters": [{"name": "UI-ポン　コミカルな唇を鳴らす音", "volume": 70, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 5, "parameters": ["this.showMapEventDialogue(9);"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 111, "indent": 5, "parameters": [1, 36, 0, 5, 0]}, {"code": 231, "indent": 6, "parameters": [16, "mio_tachie_kao_duqi", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 6, "parameters": ["SV_PLAY_VOICE sis_room_talk_06"]}, {"code": 355, "indent": 6, "parameters": ["this.showMapEventDialogue(10);"]}, {"code": 111, "indent": 6, "parameters": [1, 20, 0, 30, 1]}, {"code": 122, "indent": 7, "parameters": [20, 20, 2, 2, 0, 5]}, {"code": 0, "indent": 7, "parameters": []}, {"code": 412, "indent": 6, "parameters": []}, {"code": 355, "indent": 6, "parameters": ["this.showMapEventDialogue(11);"]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 411, "indent": 5, "parameters": []}, {"code": 0, "indent": 6, "parameters": []}, {"code": 412, "indent": 5, "parameters": []}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["end"]}, {"code": 122, "indent": 0, "parameters": [14, 14, 0, 0, 30]}, {"code": 117, "indent": 0, "parameters": [3]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 117, "indent": 0, "parameters": [29]}, {"code": 355, "indent": 0, "parameters": ["$gameMap.event(18).start()"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 0, "characterIndex": 2}, "list": [{"code": 108, "indent": 0, "parameters": ["讨好妹妹的反应"]}, {"code": 111, "indent": 0, "parameters": [4, 2, 6, 35]}, {"code": 231, "indent": 1, "parameters": [16, "mio_tachie_kao_gaman1", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE sis_room_toilet_14"]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(12);"]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(13);"]}, {"code": 122, "indent": 1, "parameters": [17, 17, 1, 2, 2, 5]}, {"code": 122, "indent": 1, "parameters": [16, 16, 1, 2, 4, 8]}, {"code": 119, "indent": 1, "parameters": ["end"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 36, 0, 5, 0]}, {"code": 231, "indent": 1, "parameters": [16, "mio_tachie_kao_happy", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 122, "indent": 1, "parameters": [204, 204, 0, 4, "\"上下震动\""]}, {"code": 117, "indent": 1, "parameters": [35]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE sis_room_talk_09"]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(14);"]}, {"code": 117, "indent": 1, "parameters": [12]}, {"code": 111, "indent": 1, "parameters": [1, 20, 0, 80, 2]}, {"code": 122, "indent": 2, "parameters": [20, 20, 1, 2, 15, 25]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 15, 0, 2, 2]}, {"code": 122, "indent": 2, "parameters": [17, 17, 1, 2, 15, 20]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 122, "indent": 2, "parameters": [17, 17, 1, 2, 3, 5]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [2]}, {"code": 250, "indent": 1, "parameters": [{"name": "UI-ポン　コミカルな唇を鳴らす音", "volume": 70, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(15);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 36, 0, 4, 0]}, {"code": 231, "indent": 2, "parameters": [16, "mio_tachie_kao_smile", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": ["SV_PLAY_VOICE sis_room_talk_10"]}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(16);"]}, {"code": 117, "indent": 2, "parameters": [12]}, {"code": 111, "indent": 2, "parameters": [1, 20, 0, 80, 2]}, {"code": 122, "indent": 3, "parameters": [20, 20, 1, 0, 10]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 111, "indent": 2, "parameters": [1, 15, 0, 2, 2]}, {"code": 122, "indent": 3, "parameters": [17, 17, 1, 2, 8, 12]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 122, "indent": 3, "parameters": [17, 17, 1, 2, 3, 5]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 230, "indent": 2, "parameters": [2]}, {"code": 250, "indent": 2, "parameters": [{"name": "UI-ポン　コミカルな唇を鳴らす音", "volume": 70, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(17);"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [1, 36, 0, 3, 0]}, {"code": 231, "indent": 3, "parameters": [16, "mio_tachie_kao_smile2", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 3, "parameters": ["SV_PLAY_VOICE sis_room_talk_11"]}, {"code": 355, "indent": 3, "parameters": ["this.showMapEventDialogue(18);"]}, {"code": 117, "indent": 3, "parameters": [12]}, {"code": 111, "indent": 3, "parameters": [1, 20, 0, 80, 2]}, {"code": 122, "indent": 4, "parameters": [20, 20, 1, 2, 0, 5]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 111, "indent": 3, "parameters": [1, 15, 0, 2, 2]}, {"code": 122, "indent": 4, "parameters": [17, 17, 1, 2, 4, 8]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 122, "indent": 4, "parameters": [17, 17, 1, 2, 0, 1]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 230, "indent": 3, "parameters": [2]}, {"code": 250, "indent": 3, "parameters": [{"name": "UI-ポン　コミカルな唇を鳴らす音", "volume": 70, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 3, "parameters": ["this.showMapEventDialogue(19);"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [1, 36, 0, 2, 0]}, {"code": 231, "indent": 4, "parameters": [16, "mio_tachie_kao_=ω=", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 4, "parameters": ["SV_PLAY_VOICE sis_room_talk_12"]}, {"code": 355, "indent": 4, "parameters": ["this.showMapEventDialogue(20);"]}, {"code": 117, "indent": 4, "parameters": [12]}, {"code": 111, "indent": 4, "parameters": [1, 20, 0, 80, 2]}, {"code": 122, "indent": 5, "parameters": [20, 20, 1, 2, 0, 2]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 111, "indent": 4, "parameters": [1, 15, 0, 2, 2]}, {"code": 122, "indent": 5, "parameters": [17, 17, 1, 0, 5]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 230, "indent": 4, "parameters": [2]}, {"code": 250, "indent": 4, "parameters": [{"name": "UI-ポン　コミカルな唇を鳴らす音", "volume": 70, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 4, "parameters": ["this.showMapEventDialogue(21);"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 111, "indent": 4, "parameters": [1, 36, 0, 1, 0]}, {"code": 231, "indent": 5, "parameters": [16, "mio_tachie_kao_eee", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 5, "parameters": ["SV_PLAY_VOICE sis_room_talk_13"]}, {"code": 355, "indent": 5, "parameters": ["this.showMapEventDialogue(22);"]}, {"code": 117, "indent": 5, "parameters": [12]}, {"code": 111, "indent": 5, "parameters": [1, 20, 0, 30, 1]}, {"code": 122, "indent": 6, "parameters": [20, 20, 2, 0, 5]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 412, "indent": 5, "parameters": []}, {"code": 122, "indent": 5, "parameters": [17, 17, 1, 0, 2]}, {"code": 230, "indent": 5, "parameters": [2]}, {"code": 250, "indent": 5, "parameters": [{"name": "UI-ポン　コミカルな唇を鳴らす音", "volume": 70, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 5, "parameters": ["this.showMapEventDialogue(23);"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 111, "indent": 5, "parameters": [1, 36, 0, 0, 0]}, {"code": 231, "indent": 6, "parameters": [16, "mio_tachie_kao_shy1", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 6, "parameters": ["SV_PLAY_VOICE sis_room_talk_14"]}, {"code": 355, "indent": 6, "parameters": ["this.showMapEventDialogue(24);"]}, {"code": 117, "indent": 6, "parameters": [12]}, {"code": 111, "indent": 6, "parameters": [1, 20, 0, 30, 1]}, {"code": 122, "indent": 7, "parameters": [20, 20, 2, 0, 10]}, {"code": 0, "indent": 7, "parameters": []}, {"code": 412, "indent": 6, "parameters": []}, {"code": 122, "indent": 6, "parameters": [17, 17, 2, 0, 1]}, {"code": 230, "indent": 6, "parameters": [2]}, {"code": 250, "indent": 6, "parameters": [{"name": "UI-ポン　コミカルな唇を鳴らす音", "volume": 70, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 6, "parameters": ["this.showMapEventDialogue(25);"]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 411, "indent": 5, "parameters": []}, {"code": 0, "indent": 6, "parameters": []}, {"code": 412, "indent": 5, "parameters": []}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["end"]}, {"code": 122, "indent": 0, "parameters": [14, 14, 0, 2, 15, 25]}, {"code": 117, "indent": 0, "parameters": [3]}, {"code": 117, "indent": 0, "parameters": [14]}, {"code": 117, "indent": 0, "parameters": [29]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 355, "indent": 0, "parameters": ["$gameMap.event(18).start()"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 5, "y": 1}, {"id": 30, "name": "妹妹摸头反应", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 26, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 4, "pattern": 1, "characterIndex": 1}, "list": [{"code": 111, "indent": 0, "parameters": [12, "$gameVariables.value(205) >= (6 + 3 * $gameVariables.value(15))"]}, {"code": 355, "indent": 1, "parameters": ["let id = this._eventId;"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.event(id).steupCEQJ(2)"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["if( $gameScreen.picture(20) ) {"]}, {"code": 655, "indent": 0, "parameters": [" $gameScreen.picture(20).drill_PDr_setCanDrag( false )"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 122, "indent": 0, "parameters": [205, 205, 1, 0, 1]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 111, "indent": 0, "parameters": [4, 2, 6, 35]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE sis_room_toilet_10"]}, {"code": 231, "indent": 1, "parameters": [16, "mio_tachie_kao_gaman1", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 122, "indent": 1, "parameters": [204, 204, 0, 4, "\"呼吸\""]}, {"code": 117, "indent": 1, "parameters": [35]}, {"code": 355, "indent": 1, "parameters": ["  const IDX    = 2; "]}, {"code": 655, "indent": 1, "parameters": ["  const mapId  = $gameMap.mapId(); const eidStr = String(this._eventId); "]}, {"code": 655, "indent": 1, "parameters": ["  const key    = `MapEventDialogue${mapId}`; const table      = window[key];"]}, {"code": 655, "indent": 1, "parameters": ["  const textArray  = Array.isArray(table?.[eidStr]?.[IDX]) ? table[eidStr][IDX] : null;"]}, {"code": 655, "indent": 1, "parameters": ["  if (!textArray || textArray.length === 0) {} else {"]}, {"code": 655, "indent": 1, "parameters": ["  const randIdx = Math.random() * textArray.length | 0;"]}, {"code": 655, "indent": 1, "parameters": ["  let text    = textArray[randIdx];"]}, {"code": 655, "indent": 1, "parameters": ["  text = \"\\\\dDCOG[11:2:2:2]\\\\fs[32]\" + text;"]}, {"code": 655, "indent": 1, "parameters": ["  $gameTemp.drill_GFTT_createSimple([1480,215], text, 5, 9, 150);"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 119, "indent": 1, "parameters": ["end"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [4, 2, 6, 25]}, {"code": 231, "indent": 1, "parameters": [16, "mio_tachie_kao_ida", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 122, "indent": 1, "parameters": [204, 204, 0, 4, "\"上下震动\""]}, {"code": 117, "indent": 1, "parameters": [35]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE sis_room_ofuro_27"]}, {"code": 355, "indent": 1, "parameters": ["$gameSystem._drill_COI_map_mouse = false;"]}, {"code": 655, "indent": 1, "parameters": ["TouchInput._mousePressed = false"]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(1);"]}, {"code": 355, "indent": 1, "parameters": ["$gameSystem._drill_COI_map_mouse = true"]}, {"code": 119, "indent": 1, "parameters": ["end"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "Math.random() > 0.7"]}, {"code": 231, "indent": 1, "parameters": [16, "mio_tachie_kao_=ω=", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 231, "indent": 1, "parameters": [16, "mio_tachie_kao_-A-", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [204, 204, 0, 4, "\"呼吸\""]}, {"code": 117, "indent": 0, "parameters": [35]}, {"code": 355, "indent": 0, "parameters": ["this.count = 0"]}, {"code": 355, "indent": 0, "parameters": ["  const IDX    = 2; const mapId  = $gameMap.mapId();"]}, {"code": 655, "indent": 0, "parameters": ["  const eidStr = String(this._eventId);  const key    = `MapEventDialogue${mapId}`;"]}, {"code": 655, "indent": 0, "parameters": ["   const table  = window[key];  const textArray  = table[eidStr][IDX];"]}, {"code": 655, "indent": 0, "parameters": ["var text;"]}, {"code": 655, "indent": 0, "parameters": ["do {"]}, {"code": 655, "indent": 0, "parameters": ["    var randomIndex = Math.floor(Math.random() * textArray.length);"]}, {"code": 655, "indent": 0, "parameters": ["    text = textArray[randomIndex];"]}, {"code": 655, "indent": 0, "parameters": ["} while (text === null);"]}, {"code": 655, "indent": 0, "parameters": ["this.count = randomIndex; text = \"\\\\dDCOG[11:2:2:2]\\\\fs[32]\" + text; randomIndex += 7;"]}, {"code": 655, "indent": 0, "parameters": ["$gameTemp.drill_GFTT_createSimple( [1480,215], text, 5, 9, 150 );"]}, {"code": 655, "indent": 0, "parameters": ["var name = \"sis_room_tachie_touch_\" + randomIndex.padZero(2);"]}, {"code": 655, "indent": 0, "parameters": ["this.execPlayVoice([name, \"90\", \"100\", \"0\"], false);"]}, {"code": 118, "indent": 0, "parameters": ["end"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameScreen.picture(16) && $gameScreen.picture(16)._name == \"mio_tachie_kao_=ω=\""]}, {"code": 108, "indent": 1, "parameters": ["心情变化"]}, {"code": 355, "indent": 1, "parameters": ["let value = $gameVariables.value(20);"]}, {"code": 655, "indent": 1, "parameters": ["if (value <= 80) {"]}, {"code": 655, "indent": 1, "parameters": ["value += Math.randomInt(4);"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.setValue(20,value);"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 108, "indent": 1, "parameters": ["好感度变化"]}, {"code": 355, "indent": 1, "parameters": ["let value = $gameVariables.value(17);"]}, {"code": 655, "indent": 1, "parameters": ["value += this.count;"]}, {"code": 655, "indent": 1, "parameters": ["value += $gameParty.leader().skillMasteryLevel(61);"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.setValue(17,value);"]}, {"code": 117, "indent": 1, "parameters": [12]}, {"code": 250, "indent": 1, "parameters": [{"name": "UI-ポン　コミカルな唇を鳴らす音", "volume": 70, "pitch": 100, "pan": 0}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$gameScreen.picture(16) && $gameScreen.picture(16)._name == \"mio_tachie_kao_-A-\""]}, {"code": 108, "indent": 2, "parameters": ["心情变化"]}, {"code": 355, "indent": 2, "parameters": ["let value = $gameVariables.value(20);"]}, {"code": 655, "indent": 2, "parameters": ["if (value <= 80) {"]}, {"code": 655, "indent": 2, "parameters": ["value += Math.randomInt(5);"]}, {"code": 655, "indent": 2, "parameters": ["$gameVariables.setValue(20,value);"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 108, "indent": 2, "parameters": ["好感度变化"]}, {"code": 355, "indent": 2, "parameters": ["let value = $gameVariables.value(17);"]}, {"code": 655, "indent": 2, "parameters": ["value += this.count;"]}, {"code": 655, "indent": 2, "parameters": ["value += $gameParty.leader().skillMasteryLevel(61);"]}, {"code": 655, "indent": 2, "parameters": ["$gameVariables.setValue(17,value);"]}, {"code": 117, "indent": 2, "parameters": [12]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["if( $gameScreen.picture(20) ) {"]}, {"code": 655, "indent": 0, "parameters": [" $gameScreen.picture(20).drill_PDr_setCanDrag( true )"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 122, "indent": 0, "parameters": [14, 14, 0, 0, 10]}, {"code": 117, "indent": 0, "parameters": [3]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 118, "indent": 0, "parameters": ["end"]}, {"code": 108, "indent": 0, "parameters": ["重置亲密接触CD"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameMap.getGroupBulletListQJ('skinshipListeners').length > 0) {"]}, {"code": 655, "indent": 0, "parameters": ["    let id = $gameMap.getGroupBulletListQJ('skinshipListeners')[0];"]}, {"code": 655, "indent": 0, "parameters": ["    let bullet = $gameMap.bulletQJ(Number(id));"]}, {"code": 655, "indent": 0, "parameters": ["    bullet._suspend = false;"]}, {"code": 655, "indent": 0, "parameters": ["    bullet._coolDown += 5;"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 313, "indent": 0, "parameters": [0, 2, 0, 37]}, {"code": 121, "indent": 0, "parameters": [46, 46, 1]}, {"code": 231, "indent": 0, "parameters": [16, "mio_tachie_kao_ida", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 122, "indent": 0, "parameters": [204, 204, 0, 4, "\"小跳\""]}, {"code": 117, "indent": 0, "parameters": [35]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE sis_room_tachie_touch_01"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(3);"]}, {"code": 122, "indent": 0, "parameters": [20, 20, 2, 0, 8]}, {"code": 231, "indent": 0, "parameters": [16, "mio_tachie_kao_duqi", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 111, "indent": 0, "parameters": [4, 2, 6, 23]}, {"code": 355, "indent": 1, "parameters": ["$gameMap.event(50).steupCEQJ(2)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameMap.event(18).steupCEQJ(1)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 11, "y": 10}, {"id": 31, "name": "立绘登场演出", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 4, "pattern": 1, "characterIndex": 1}, "list": [{"code": 355, "indent": 0, "parameters": ["var pic_ids = $gameNumberArray.value(41);"]}, {"code": 655, "indent": 0, "parameters": ["pic_ids.forEach(function(pic_id) {"]}, {"code": 655, "indent": 0, "parameters": ["    var picture = $gameScreen.picture(pic_id);"]}, {"code": 655, "indent": 0, "parameters": ["var data = {"]}, {"code": 655, "indent": 0, "parameters": ["\"type\":\"smoothMove\","]}, {"code": 655, "indent": 0, "parameters": ["\"valueX\":0,  \"valueY\":-1450,"]}, {"code": 655, "indent": 0, "parameters": ["\"time\":60,  \"cur_speedX\":0,  \"cur_speedY\":0,}"]}, {"code": 655, "indent": 0, "parameters": ["  if (picture) { "]}, {"code": 655, "indent": 0, "parameters": ["picture._drill_PSh_commandChangeTank.push(data);}});"]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 250, "indent": 0, "parameters": [{"name": "037myuu_YumeSE_FukidashiHatena01", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [40]}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 1, "y": 0}, null, null, {"id": 34, "name": "第一次获得游戏主机", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 2, "characterIndex": 1}, "list": [{"code": 231, "indent": 0, "parameters": [1, "sister_room_night_fine", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 245, "indent": 0, "parameters": [{"name": "The-Little-Witch_s-Home", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["AudioManager.fadeInBgsByLine(2, 1); "]}, {"code": 356, "indent": 0, "parameters": ["PB_BGS_CHANGE_LINE 2"]}, {"code": 245, "indent": 0, "parameters": [{"name": "The-Little-Witch_s-Home-8bit", "volume": 0, "pitch": 100, "pan": 0}]}, {"code": 313, "indent": 0, "parameters": [0, 2, 0, 24]}, {"code": 117, "indent": 0, "parameters": [26]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 250, "indent": 0, "parameters": [{"name": "Equip2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["var IMG = 'alt_gameConsole_active';"]}, {"code": 655, "indent": 0, "parameters": ["var path = \"game_itazura\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(30, path, IMG, 0, 0, 0, 100, 100, 0, 0)"]}, {"code": 356, "indent": 0, "parameters": [">图片快捷操作 : 图片[30] : 修改单属性 : 透明度[255] : 时间[45]"]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(0);"]}, {"code": 231, "indent": 0, "parameters": [16, "mio_tachie_kao_happy", 0, 0, 1000, 1600, 100, 100, 255, 0]}, {"code": 355, "indent": 0, "parameters": ["var pic_ids = $gameNumberArray.value(41);"]}, {"code": 655, "indent": 0, "parameters": ["pic_ids.forEach(function(pic_id) {"]}, {"code": 655, "indent": 0, "parameters": ["    var picture = $gameScreen.picture(pic_id);"]}, {"code": 655, "indent": 0, "parameters": ["var data = {\t\t\t\t\t\t\t\t\"type\":\"smoothMove\","]}, {"code": 655, "indent": 0, "parameters": ["\"valueX\":0,"]}, {"code": 655, "indent": 0, "parameters": ["\"valueY\":-1450,"]}, {"code": 655, "indent": 0, "parameters": ["\"time\":60,"]}, {"code": 655, "indent": 0, "parameters": ["\"cur_time\":0,\"cur_speedX\":0,\"cur_speedY\":0,}"]}, {"code": 655, "indent": 0, "parameters": ["    if (picture) { picture._drill_PSh_commandChangeTank.push(data);}});"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE sis_room_achievement_01"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(1);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(2);"]}, {"code": 231, "indent": 0, "parameters": [16, "mio_tachie_kao_bimyou2", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 122, "indent": 0, "parameters": [204, 204, 0, 4, "\"上下震动\""]}, {"code": 117, "indent": 0, "parameters": [35]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE sis_room_achievement_02"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(3);"]}, {"code": 231, "indent": 0, "parameters": [16, "mio_tachie_kao_smile", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(4);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(5);"]}, {"code": 231, "indent": 0, "parameters": [16, "mio_tachie_kao_happy", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 122, "indent": 0, "parameters": [204, 204, 0, 4, "\"小跳\""]}, {"code": 117, "indent": 0, "parameters": [35]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE sis_room_achievement_03"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(6);"]}, {"code": 231, "indent": 0, "parameters": [16, "mio_tachie_kao_shy2", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 122, "indent": 0, "parameters": [204, 204, 0, 4, "\"上下震动\""]}, {"code": 117, "indent": 0, "parameters": [35]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE sis_room_achievement_04"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(7);"]}, {"code": 355, "indent": 0, "parameters": ["var pic_ids = $gameNumberArray.value(41);"]}, {"code": 655, "indent": 0, "parameters": ["pic_ids.forEach(function(pic_id) {"]}, {"code": 655, "indent": 0, "parameters": ["    var picture = $gameScreen.picture(pic_id);"]}, {"code": 655, "indent": 0, "parameters": ["    if (picture) { "]}, {"code": 655, "indent": 0, "parameters": ["picture.drill_PFOE_playHidingMoveDisappear( 60,90,720 );"]}, {"code": 655, "indent": 0, "parameters": ["}});"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 355, "indent": 0, "parameters": ["var IMG = 'alt_sister_normal_back';"]}, {"code": 655, "indent": 0, "parameters": ["var path = \"game_itazura\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(4, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var IMG = 'alt_sister_normal_coatHem';"]}, {"code": 655, "indent": 0, "parameters": ["var path = \"game_itazura\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(7, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var IMG = 'alt_sister_normal_hand0';"]}, {"code": 655, "indent": 0, "parameters": ["var path = \"game_itazura\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(8, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var IMG = 'alt_sister_normal_kao0';"]}, {"code": 655, "indent": 0, "parameters": ["var path = \"game_itazura\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(9, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 356, "indent": 0, "parameters": [">图片快捷操作 : 批量图片[4,5,6,7,8,9] : 修改单属性 : 透明度[255] : 时间[30]"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 355, "indent": 0, "parameters": ["for (let i = 0; i <= 6; i++) {"]}, {"code": 655, "indent": 0, "parameters": ["    ImageManager.reservePicture(\"game_itazura/alt_sister_normal_hand\" + i);"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 655, "indent": 0, "parameters": ["this.setWaitMode('image')"]}, {"code": 355, "indent": 0, "parameters": ["QJ.MPMZ.Shoot({"]}, {"code": 655, "indent": 0, "parameters": ["         img:\"null1\",groupName: ['ImoutoSoloPlay'],"]}, {"code": 655, "indent": 0, "parameters": ["         existData: [          "]}, {"code": 655, "indent": 0, "parameters": ["         ],"]}, {"code": 655, "indent": 0, "parameters": ["         moveF:["]}, {"code": 655, "indent": 0, "parameters": ["           [4,0,QJ.MPMZ.tl._imoutoUtilImoutoSoloPlay]"]}, {"code": 655, "indent": 0, "parameters": ["         ]"]}, {"code": 655, "indent": 0, "parameters": ["});"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(8);"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE sis_room_achievement_05"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(9);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(10);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(11);"]}, {"code": 121, "indent": 0, "parameters": [500, 500, 0]}, {"code": 355, "indent": 0, "parameters": ["AudioManager.fadeOutBgsByLine(3, 1); "]}, {"code": 245, "indent": 0, "parameters": [{"name": "The-Little-Witch_s-Home-8bit", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["AudioManager.fadeInBgsByLine(4, 2); "]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "明るい感じで終わりや完成を表現", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(12);"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["QJ.MPMZ.deleteProjectile('ImoutoSoloPlay');"]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 355, "indent": 0, "parameters": ["$gameSystem.add_day(1);"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem.set_hour(7)"]}, {"code": 117, "indent": 0, "parameters": [8]}, {"code": 122, "indent": 0, "parameters": [17, 17, 1, 0, 35]}, {"code": 122, "indent": 0, "parameters": [20, 20, 0, 0, 85]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 246, "indent": 0, "parameters": [2]}, {"code": 122, "indent": 0, "parameters": [13, 13, 0, 0, 2]}, {"code": 201, "indent": 0, "parameters": [0, 21, 5, 4, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 2, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["夜晚发现妹妹熬夜打游戏"]}, {"code": 121, "indent": 0, "parameters": [497, 497, 0]}, {"code": 231, "indent": 0, "parameters": [1, "sister_room_night2_fine", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [2, "sister_room_night2_fine_gameScene", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [4, "sister_room_night2_fine_gameConsloe", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [7, "sis_chibi_game_night", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 313, "indent": 0, "parameters": [0, 2, 0, 26]}, {"code": 117, "indent": 0, "parameters": [26]}, {"code": 231, "indent": 0, "parameters": [16, "mio_tachie_kao_shy3", 0, 0, 200, 1600, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [20, "mio_tachie_ahoge", 0, 0, 500, 1600, 100, 100, 255, 0]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(13);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(14);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(15);"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE sis_room_achievement_22"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(16);"]}, {"code": 241, "indent": 0, "parameters": [{"name": "PerituneMaterial_NoWay3_loop", "volume": 40, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["AudioManager.fadeInBgm(4)"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(17);"]}, {"code": 355, "indent": 0, "parameters": ["var pic_ids = $gameNumberArray.value(41);"]}, {"code": 655, "indent": 0, "parameters": ["pic_ids.forEach(function(pic_id) {"]}, {"code": 655, "indent": 0, "parameters": ["    var picture = $gameScreen.picture(pic_id);"]}, {"code": 655, "indent": 0, "parameters": ["var data = {\t\t\t\t\t\t\t\t\"type\":\"smoothMove\","]}, {"code": 655, "indent": 0, "parameters": ["\"valueX\":0,"]}, {"code": 655, "indent": 0, "parameters": ["\"valueY\":-1450,"]}, {"code": 655, "indent": 0, "parameters": ["\"time\":30,"]}, {"code": 655, "indent": 0, "parameters": ["\"cur_time\":0,\"cur_speedX\":0,\"cur_speedY\":0,}"]}, {"code": 655, "indent": 0, "parameters": ["    if (picture) { picture._drill_PSh_commandChangeTank.push(data);}});"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 250, "indent": 0, "parameters": [{"name": "037myuu_YumeSE_FukidashiHatena01", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 108, "indent": 0, "parameters": ["解锁新睡姿"]}, {"code": 355, "indent": 0, "parameters": ["$gameNumberArray.value(50).push(1)"]}, {"code": 231, "indent": 0, "parameters": [4, "sister_room_night2_fine_gameConsloe_noLigjht", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": [">图片快捷操作 : 批量图片[2,7] : 修改单属性 : 透明度[0] : 时间[30]"]}, {"code": 122, "indent": 0, "parameters": [204, 204, 0, 4, "\"害怕\""]}, {"code": 117, "indent": 0, "parameters": [35]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE sis_room_achievement_23"]}, {"code": 355, "indent": 0, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["let index = 18;"]}, {"code": 655, "indent": 0, "parameters": ["let mapId = $gameMap.mapId();"]}, {"code": 655, "indent": 0, "parameters": ["let key   = `MapEventDialogue${mapId}`;"]}, {"code": 655, "indent": 0, "parameters": ["let textArray = window[key][eid][String(index)];"]}, {"code": 655, "indent": 0, "parameters": ["var template = \"\\\\{\\\\dDCCE[文本[%TEXT%]:预设[11]]\\\\}\";"]}, {"code": 655, "indent": 0, "parameters": ["textArray = textArray.map(t => template.replace(\"%TEXT%\", t));"]}, {"code": 655, "indent": 0, "parameters": ["textArray[0] += \"\\\\w[180]\\\\^\";"]}, {"code": 655, "indent": 0, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 246, "indent": 0, "parameters": [1]}, {"code": 250, "indent": 0, "parameters": [{"name": "瞬間移動・姿を消す・ゆっくり・シュピン", "volume": 45, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["var pic_ids = $gameNumberArray.value(41);"]}, {"code": 655, "indent": 0, "parameters": ["pic_ids.forEach(function(pic_id) {"]}, {"code": 655, "indent": 0, "parameters": ["    var picture = $gameScreen.picture(pic_id);"]}, {"code": 655, "indent": 0, "parameters": ["    if (picture) { "]}, {"code": 655, "indent": 0, "parameters": ["picture.drill_PFOE_playHidingMoveDisappear( 40,0,1600 );"]}, {"code": 655, "indent": 0, "parameters": ["}});"]}, {"code": 230, "indent": 0, "parameters": [50]}, {"code": 250, "indent": 0, "parameters": [{"name": "ゴソッ衣擦れ", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 0, "parameters": [7, "sis_chibi_sleep2_night", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 356, "indent": 0, "parameters": [">图片快捷操作 : 图片[7] : 修改单属性 : 透明度[255] : 时间[30]"]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 108, "indent": 0, "parameters": ["初始化计数器"]}, {"code": 355, "indent": 0, "parameters": ["this.count = 0"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["………………"]}, {"code": 401, "indent": 0, "parameters": ["………………"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(19);"]}, {"code": 118, "indent": 0, "parameters": ["选项"]}, {"code": 108, "indent": 0, "parameters": ["修正选项组图标"]}, {"code": 355, "indent": 0, "parameters": ["$gameSystem._drill_DCB_curStyle = 25;"]}, {"code": 655, "indent": 0, "parameters": ["let style = [];"]}, {"code": 655, "indent": 0, "parameters": ["style.push(\"eventButtons_touch2\");"]}, {"code": 655, "indent": 0, "parameters": ["DrillUp.g_DCB_data[24].btn_src = style;"]}, {"code": 108, "indent": 0, "parameters": ["选项赋予名称"]}, {"code": 355, "indent": 0, "parameters": ["const idx   = 21;"]}, {"code": 655, "indent": 0, "parameters": ["const eid   = String(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["const mapId = $gameMap.mapId();"]}, {"code": 655, "indent": 0, "parameters": ["const key   = `MapEventDialogue${mapId}`;"]}, {"code": 655, "indent": 0, "parameters": ["const dialogueTable = window[key] || {};"]}, {"code": 655, "indent": 0, "parameters": ["const textArray     = dialogueTable[eid]?.[String(idx)];"]}, {"code": 655, "indent": 0, "parameters": ["const option1 = textArray[0];"]}, {"code": 655, "indent": 0, "parameters": ["$gameStrings.setValue(6,option1);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(20);"]}, {"code": 102, "indent": 0, "parameters": [["\\str[6]"], -1, -1, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\str[6]"]}, {"code": 111, "indent": 1, "parameters": [12, "this.count === 0"]}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(22);"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "037myuu_YumeSE_FukidashiHatena01", "volume": 50, "pitch": 80, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 250, "indent": 1, "parameters": [{"name": "014myuu_YumeSE_SystemBuzzer03", "volume": 55, "pitch": 80, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": [">持续动作 : 图片[7] : 左右震动 : 持续时间[20] : 周期[10] : 震动幅度[2]"]}, {"code": 111, "indent": 1, "parameters": [12, "this.count === 0"]}, {"code": 356, "indent": 2, "parameters": ["SV_PLAY_VOICE sis_room_achievement_24"]}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(23);"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [12, "this.count === 1"]}, {"code": 356, "indent": 3, "parameters": ["SV_PLAY_VOICE sis_room_achievement_25"]}, {"code": 355, "indent": 3, "parameters": ["this.showMapEventDialogue(24);"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 356, "indent": 3, "parameters": ["SV_PLAY_VOICE sis_room_achievement_26"]}, {"code": 355, "indent": 3, "parameters": ["this.showMapEventDialogue(25);"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["this.count++"]}, {"code": 111, "indent": 1, "parameters": [12, "this.count >= 3"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(26);"]}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(27);"]}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(28);"]}, {"code": 117, "indent": 2, "parameters": [9]}, {"code": 115, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 119, "indent": 1, "parameters": ["选项"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 7}, null, null, null, null, null, null, null, {"id": 42, "name": "每日结束", "note": "<重置独立开关>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["消除其他正在执行的事件"]}, {"code": 355, "indent": 0, "parameters": ["chahuiUtil.abortEventById.call(this, -1)"]}, {"code": 108, "indent": 0, "parameters": ["重新计算睡眠时间"]}, {"code": 355, "indent": 0, "parameters": ["QJ.MPMZ.tl._imoutoUtilCalculateFinalTargetMinutes()"]}, {"code": 355, "indent": 0, "parameters": ["$gameSelfSwitches.setValue([$gameMap.mapId(), 6, 'A'], false);"]}, {"code": 655, "indent": 0, "parameters": ["$gameSelfSwitches.setValue([$gameMap.mapId(), 6, 'B'], false);"]}, {"code": 121, "indent": 0, "parameters": [14, 14, 1]}, {"code": 121, "indent": 0, "parameters": [26, 26, 1]}, {"code": 121, "indent": 0, "parameters": [45, 45, 1]}, {"code": 121, "indent": 0, "parameters": [46, 46, 1]}, {"code": 121, "indent": 0, "parameters": [47, 47, 1]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 108, "indent": 0, "parameters": ["呆毛防范"]}, {"code": 355, "indent": 0, "parameters": ["if( $gameScreen.picture(20) ) {"]}, {"code": 655, "indent": 0, "parameters": [" $gameScreen.picture(20).drill_PDr_setCanDrag( false )"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["喝茶动作防范"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameScreen.picture(30)"]}, {"code": 356, "indent": 1, "parameters": [">图片快捷操作 : 图片[30] : 修改单属性 : 透明度[0] : 时间[45]"]}, {"code": 230, "indent": 1, "parameters": [45]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 111, "indent": 0, "parameters": [12, "$gameScreen.picture(5) && $gameScreen.picture(5)._opacity > 1"]}, {"code": 356, "indent": 1, "parameters": [">图片快捷操作 : 批量图片[2,5] : 修改单属性 : 透明度[0] : 时间[45]"]}, {"code": 230, "indent": 1, "parameters": [90]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "$gameScreen.picture(16) && $gameScreen.picture(16)._opacity > 250"]}, {"code": 119, "indent": 1, "parameters": ["跳过演出"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [31]}, {"code": 117, "indent": 0, "parameters": [26]}, {"code": 111, "indent": 0, "parameters": [1, 20, 0, 30, 2]}, {"code": 231, "indent": 1, "parameters": [16, "mio_tachie_kao_duqi", 0, 0, 1000, 1600, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 231, "indent": 1, "parameters": [16, "mio_tachie_kao_-A-", 0, 0, 1000, 1600, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["var pic_ids = $gameNumberArray.value(41);"]}, {"code": 655, "indent": 0, "parameters": ["pic_ids.forEach(function(pic_id) {"]}, {"code": 655, "indent": 0, "parameters": ["    var picture = $gameScreen.picture(pic_id);"]}, {"code": 655, "indent": 0, "parameters": ["var data = {  \"type\":\"smoothMove\","]}, {"code": 655, "indent": 0, "parameters": ["\"valueX\":0,"]}, {"code": 655, "indent": 0, "parameters": ["\"valueY\":-1450,"]}, {"code": 655, "indent": 0, "parameters": ["\"time\":90,"]}, {"code": 655, "indent": 0, "parameters": ["\"cur_time\":0,\"cur_speedX\":0,\"cur_speedY\":0,}"]}, {"code": 655, "indent": 0, "parameters": ["    if (picture) { picture._drill_PSh_commandChangeTank.push(data);}});"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 118, "indent": 0, "parameters": ["跳过演出"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 108, "indent": 0, "parameters": ["防范玩家死档"]}, {"code": 355, "indent": 0, "parameters": ["$gameSystem._drill_COI_map_mouse = true"]}, {"code": 108, "indent": 0, "parameters": ["妹妹心情很差时"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameVariables.value(20) <= 30 || $gameSwitches.value(102)"]}, {"code": 355, "indent": 1, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.event(eid).steupCEQJ(2)"]}, {"code": 230, "indent": 1, "parameters": [90]}, {"code": 123, "indent": 1, "parameters": ["A", 1]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["修正选项组图标"]}, {"code": 355, "indent": 0, "parameters": ["$gameSystem._drill_DCB_curStyle = 25"]}, {"code": 355, "indent": 0, "parameters": ["let style = [\"eventButtons_sleep2\",\"eventButtons_returns\"];"]}, {"code": 655, "indent": 0, "parameters": ["DrillUp.g_DCB_data[24].btn_src = style;"]}, {"code": 108, "indent": 0, "parameters": ["选项赋予名称"]}, {"code": 355, "indent": 0, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["let index = 2;"]}, {"code": 655, "indent": 0, "parameters": ["let option1 = window.MapEventDialogue4[eid][String(index)][0];"]}, {"code": 655, "indent": 0, "parameters": ["let option2 = window.MapEventDialogue4[eid][String(index)][1];"]}, {"code": 655, "indent": 0, "parameters": [" $gameStrings.setValue(6,option1);"]}, {"code": 655, "indent": 0, "parameters": [" $gameStrings.setValue(7,option2);"]}, {"code": 231, "indent": 0, "parameters": [16, "mio_tachie_kao_-A-", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE sis_room_sleep_05"]}, {"code": 355, "indent": 0, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["let index = 0;"]}, {"code": 655, "indent": 0, "parameters": ["let textArray = window.MapEventDialogue4[eid][String(index)];"]}, {"code": 655, "indent": 0, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 355, "indent": 0, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["let index = 1;"]}, {"code": 655, "indent": 0, "parameters": ["let textArray = window.MapEventDialogue4[eid][String(index)];"]}, {"code": 655, "indent": 0, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 102, "indent": 0, "parameters": [["\\str[6]", "\\c[15]\\str[7]"], 1, -1, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\str[6]"]}, {"code": 355, "indent": 1, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 1, "parameters": ["let index = 3;"]}, {"code": 655, "indent": 1, "parameters": ["let textArray = window.MapEventDialogue4[eid][String(index)];"]}, {"code": 655, "indent": 1, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 119, "indent": 1, "parameters": ["一人睡觉"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\c[15]\\str[7]"]}, {"code": 355, "indent": 1, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 1, "parameters": ["let index = 4;"]}, {"code": 655, "indent": 1, "parameters": ["let textArray = window.MapEventDialogue4[eid][String(index)];"]}, {"code": 655, "indent": 1, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE sis_room_sleep_06"]}, {"code": 355, "indent": 1, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 1, "parameters": ["let index = 5;"]}, {"code": 655, "indent": 1, "parameters": ["let textArray = window.MapEventDialogue4[eid][String(index)];"]}, {"code": 655, "indent": 1, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 355, "indent": 1, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 1, "parameters": ["let index = 6;"]}, {"code": 655, "indent": 1, "parameters": ["let textArray = window.MapEventDialogue4[eid][String(index)];"]}, {"code": 655, "indent": 1, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 355, "indent": 1, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 1, "parameters": ["let index = 7;"]}, {"code": 655, "indent": 1, "parameters": ["let textArray = window.MapEventDialogue4[eid][String(index)];"]}, {"code": 655, "indent": 1, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 111, "indent": 1, "parameters": [1, 15, 0, 9, 1]}, {"code": 119, "indent": 2, "parameters": ["一人睡觉"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 118, "indent": 2, "parameters": ["一人睡觉"]}, {"code": 355, "indent": 2, "parameters": ["var pic_ids = $gameNumberArray.value(41);"]}, {"code": 655, "indent": 2, "parameters": ["pic_ids.forEach(function(pic_id) {"]}, {"code": 655, "indent": 2, "parameters": ["    var picture = $gameScreen.picture(pic_id);"]}, {"code": 655, "indent": 2, "parameters": ["    if (picture) { "]}, {"code": 655, "indent": 2, "parameters": ["picture.drill_PFOE_playHidingMoveDisappear( 45,90,720 );"]}, {"code": 655, "indent": 2, "parameters": ["}});"]}, {"code": 230, "indent": 2, "parameters": [45]}, {"code": 250, "indent": 2, "parameters": [{"name": "着替え１、シャツを着る極短、装備変更", "volume": 90, "pitch": 70, "pan": 0}]}, {"code": 231, "indent": 2, "parameters": [10, "sis_chibi_sleep1_night_fine", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 2, "parameters": [10, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, true]}, {"code": 355, "indent": 2, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 2, "parameters": ["let index = 8;"]}, {"code": 655, "indent": 2, "parameters": ["let textArray = window.MapEventDialogue4[eid][String(index)];"]}, {"code": 655, "indent": 2, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 355, "indent": 2, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 2, "parameters": ["let index = 9;"]}, {"code": 655, "indent": 2, "parameters": ["let textArray = window.MapEventDialogue4[eid][String(index)];"]}, {"code": 655, "indent": 2, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 242, "indent": 2, "parameters": [2]}, {"code": 246, "indent": 2, "parameters": [2]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.event(eid).steupCEQJ(4)"]}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 4, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["妹妹生气时的结束事件"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 231, "indent": 0, "parameters": [16, "mio_tachie_kao_duqi", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE sis_room_sleep_16"]}, {"code": 355, "indent": 0, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["let index = 10;"]}, {"code": 655, "indent": 0, "parameters": ["let textArray = window.MapEventDialogue4[eid][String(index)];"]}, {"code": 655, "indent": 0, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 355, "indent": 0, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["let index = 11;"]}, {"code": 655, "indent": 0, "parameters": ["let textArray = window.MapEventDialogue4[eid][String(index)];"]}, {"code": 655, "indent": 0, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 231, "indent": 0, "parameters": [16, "mio_tachie_kao_nununu", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 355, "indent": 0, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.event(eid).steupCEQJ(3)"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE sis_room_sleep_17"]}, {"code": 355, "indent": 0, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["let index = 12;"]}, {"code": 655, "indent": 0, "parameters": ["let textArray = window.MapEventDialogue4[eid][String(index)];"]}, {"code": 655, "indent": 0, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 121, "indent": 0, "parameters": [102, 102, 1]}, {"code": 122, "indent": 0, "parameters": [13, 13, 0, 0, 4]}, {"code": 201, "indent": 0, "parameters": [0, 25, 4, 4, 0, 2]}, {"code": 121, "indent": 0, "parameters": [15, 15, 1]}, {"code": 115, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 4, "pattern": 1, "characterIndex": 1}, "list": [{"code": 355, "indent": 0, "parameters": ["var pic_ids = $gameNumberArray.value(41);"]}, {"code": 655, "indent": 0, "parameters": ["pic_ids.forEach(function(pic_id) {"]}, {"code": 655, "indent": 0, "parameters": ["    var picture = $gameScreen.picture(pic_id);"]}, {"code": 655, "indent": 0, "parameters": ["var data = {\t\t\t\t\t\t\t\t\"type\":\"smoothMove\","]}, {"code": 655, "indent": 0, "parameters": ["\"valueX\":120,"]}, {"code": 655, "indent": 0, "parameters": ["\"valueY\":0,"]}, {"code": 655, "indent": 0, "parameters": ["\"time\":60,"]}, {"code": 655, "indent": 0, "parameters": ["\"cur_time\":0,\"cur_speedX\":0,\"cur_speedY\":0,}"]}, {"code": 655, "indent": 0, "parameters": ["    if (picture) { picture._drill_PSh_commandChangeTank.push(data);}});"]}, {"code": 230, "indent": 0, "parameters": [80]}, {"code": 355, "indent": 0, "parameters": ["var pic_ids = $gameNumberArray.value(41);"]}, {"code": 655, "indent": 0, "parameters": ["pic_ids.forEach(function(pic_id) {"]}, {"code": 655, "indent": 0, "parameters": ["    var picture = $gameScreen.picture(pic_id);"]}, {"code": 655, "indent": 0, "parameters": ["var data = {\t\t\t\t\t\t\t\t\"type\":\"smoothMove\","]}, {"code": 655, "indent": 0, "parameters": ["\"valueX\":120,"]}, {"code": 655, "indent": 0, "parameters": ["\"valueY\":0,"]}, {"code": 655, "indent": 0, "parameters": ["\"time\":60,"]}, {"code": 655, "indent": 0, "parameters": ["\"cur_time\":0,\"cur_speedX\":0,\"cur_speedY\":0,}"]}, {"code": 655, "indent": 0, "parameters": ["    if (picture) { picture._drill_PSh_commandChangeTank.push(data);}});"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 122, "indent": 0, "parameters": [13, 13, 0, 0, 1]}, {"code": 201, "indent": 0, "parameters": [0, 21, 4, 5, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 1, "y": 10}, null, {"id": 44, "name": "喝茶事件", "note": "<重置独立开关>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["由小人触发的情况"]}, {"code": 111, "indent": 0, "parameters": [12, "!$gameActors.actor(2).isStateAffected(27)"]}, {"code": 117, "indent": 1, "parameters": [16]}, {"code": 117, "indent": 1, "parameters": [31]}, {"code": 313, "indent": 1, "parameters": [0, 2, 0, 27]}, {"code": 356, "indent": 1, "parameters": [">图片快捷操作 : 批量图片[2,5] : 修改单属性 : 透明度[0] : 时间[45]"]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 117, "indent": 1, "parameters": [26]}, {"code": 111, "indent": 1, "parameters": [12, "$gameSystem.hour() >= 21"]}, {"code": 231, "indent": 2, "parameters": [16, "mio_tachie_kao_-A-", 0, 0, 1000, 1600, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 231, "indent": 2, "parameters": [16, "mio_tachie_kao_mu2", 0, 0, 1000, 1600, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.steupCEQJ(38,eid)"]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [8, 197]}, {"code": 111, "indent": 1, "parameters": [12, "$gameSelfVariables.value([1, 2, 'healing']) === 0"]}, {"code": 355, "indent": 2, "parameters": ["let id = this._eventId;"]}, {"code": 655, "indent": 2, "parameters": ["$gameMap.event(id).steupCEQJ(2)"]}, {"code": 115, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(0);"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameParty.customItemTypeCount('jihanki') == 0"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["…………"]}, {"code": 401, "indent": 1, "parameters": ["…………"]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(1);"]}, {"code": 355, "indent": 1, "parameters": ["$gameMap.event(18).steupCEQJ(1)"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["let pic = $gameScreen.picture(1);"]}, {"code": 655, "indent": 0, "parameters": ["if (pic) {"]}, {"code": 655, "indent": 0, "parameters": ["var id = \"blur1\" ;"]}, {"code": 655, "indent": 0, "parameters": ["var filterTarget = 5001;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.createFilter(id, \"blur\", filterTarget);"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.setFilter( id ,[0]);"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.moveFilter(id, [3], 60);"]}, {"code": 655, "indent": 0, "parameters": ["   }"]}, {"code": 108, "indent": 0, "parameters": ["部署监听子弹"]}, {"code": 355, "indent": 0, "parameters": ["QJ.MPMZ.Shoot({"]}, {"code": 655, "indent": 0, "parameters": ["   img:\"null1\","]}, {"code": 655, "indent": 0, "parameters": ["   groupName: ['SelectDrink'],"]}, {"code": 655, "indent": 0, "parameters": ["   existData: [ ],"]}, {"code": 655, "indent": 0, "parameters": ["   moveF:["]}, {"code": 655, "indent": 0, "parameters": ["     [60,20,QJ.MPMZ.tl._imoutoUtilSelectDrinkResponse]"]}, {"code": 655, "indent": 0, "parameters": ["   ]"]}, {"code": 655, "indent": 0, "parameters": ["});\t"]}, {"code": 121, "indent": 0, "parameters": [30, 30, 0]}, {"code": 356, "indent": 0, "parameters": ["SetSelectItemType jihanki"]}, {"code": 104, "indent": 0, "parameters": [90, 1]}, {"code": 355, "indent": 0, "parameters": ["var id = \"blur1\" ;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.moveFilter(id, [0], 60);"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.eraseFilterAfterMove(id);"]}, {"code": 111, "indent": 0, "parameters": [1, 90, 0, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(3);"]}, {"code": 355, "indent": 1, "parameters": ["$gameMap.event(18).steupCEQJ(1)"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["var itemId; "]}, {"code": 655, "indent": 0, "parameters": ["var item;"]}, {"code": 655, "indent": 0, "parameters": ["itemId = $gameVariables.value(90);"]}, {"code": 655, "indent": 0, "parameters": ["item = $dataItems[itemId];"]}, {"code": 655, "indent": 0, "parameters": ["this.count = itemId;"]}, {"code": 655, "indent": 0, "parameters": ["$gameParty.gainItem(item, -1);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(4);"]}, {"code": 111, "indent": 0, "parameters": [1, 20, 0, 30, 2]}, {"code": 231, "indent": 1, "parameters": [16, "mio_tachie_kao_duqi", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE sis_room_ocha9"]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(5);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "this.count === 23 || this.count === 25"]}, {"code": 231, "indent": 2, "parameters": [16, "mio_tachie_kao_happy", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 122, "indent": 2, "parameters": [204, 204, 0, 4, "\"上下震动\""]}, {"code": 117, "indent": 2, "parameters": [35]}, {"code": 356, "indent": 2, "parameters": ["SV_PLAY_VOICE sis_room_ocha5"]}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(6);"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [12, "this.count === 26"]}, {"code": 231, "indent": 3, "parameters": [16, "mio_tachie_kao_smile", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 3, "parameters": ["SV_PLAY_VOICE sis_room_ocha7"]}, {"code": 355, "indent": 3, "parameters": ["this.showMapEventDialogue(7);"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [12, "this.count === 27"]}, {"code": 231, "indent": 4, "parameters": [16, "mio_tachie_kao_=ω=", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 4, "parameters": ["SV_PLAY_VOICE sis_room_ocha6"]}, {"code": 355, "indent": 4, "parameters": ["this.showMapEventDialogue(8);"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 111, "indent": 4, "parameters": [12, "this.count === 24"]}, {"code": 231, "indent": 5, "parameters": [16, "mio_tachie_kao_nununu3", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 122, "indent": 5, "parameters": [204, 204, 0, 4, "\"左右震动\""]}, {"code": 117, "indent": 5, "parameters": [35]}, {"code": 356, "indent": 5, "parameters": ["SV_PLAY_VOICE sis_room_ocha8"]}, {"code": 355, "indent": 5, "parameters": ["this.showMapEventDialogue(9);"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["end"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["var pic_ids = $gameNumberArray.value(41);"]}, {"code": 655, "indent": 0, "parameters": ["pic_ids.forEach(function(pic_id) {"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.erasePicture(pic_id);"]}, {"code": 655, "indent": 0, "parameters": ["});"]}, {"code": 230, "indent": 0, "parameters": [4]}, {"code": 108, "indent": 0, "parameters": ["确认视频路径"]}, {"code": 355, "indent": 0, "parameters": ["let vName = \"sis_room_ocha\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.setVideoPictureName(vName, true, false);"]}, {"code": 108, "indent": 0, "parameters": ["绑定视频"]}, {"code": 355, "indent": 0, "parameters": ["let pid = 30;"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPicture(pid, '', 0, 260, 300, 100, 100, 255, 0);"]}, {"code": 655, "indent": 0, "parameters": ["const pic = $gameScreen.picture(pid);"]}, {"code": 655, "indent": 0, "parameters": ["if (pic) {"]}, {"code": 655, "indent": 0, "parameters": ["pic.set<PERSON><PERSON>o<PERSON>(false);"]}, {"code": 655, "indent": 0, "parameters": ["pic.set<PERSON><PERSON>o<PERSON>(true);"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["const pic = $gameScreen.picture(30);"]}, {"code": 655, "indent": 0, "parameters": ["if (pic) {"]}, {"code": 655, "indent": 0, "parameters": ["pic.set<PERSON>ideo<PERSON>(false);"]}, {"code": 655, "indent": 0, "parameters": ["pic.set<PERSON><PERSON><PERSON>(true);"]}, {"code": 655, "indent": 0, "parameters": ["this._waitMode = 'videoPicture';"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 313, "indent": 0, "parameters": [0, 2, 1, 40]}, {"code": 111, "indent": 0, "parameters": [12, "this.count === 23 || this.count === 25"]}, {"code": 122, "indent": 1, "parameters": [17, 17, 1, 2, 6, 12]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [17, 17, 1, 2, 6, 12]}, {"code": 111, "indent": 0, "parameters": [1, 20, 0, 40, 2]}, {"code": 122, "indent": 1, "parameters": [20, 20, 1, 0, 25]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 122, "indent": 1, "parameters": [20, 20, 0, 2, 75, 90]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 313, "indent": 0, "parameters": [0, 2, 0, 30]}, {"code": 117, "indent": 0, "parameters": [12]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(17);"]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 122, "indent": 0, "parameters": [14, 14, 0, 0, 15]}, {"code": 117, "indent": 0, "parameters": [3]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 235, "indent": 0, "parameters": [30]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 355, "indent": 0, "parameters": ["$gameMap.event(1).steupCEQJ(1)"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(32);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(33);"]}, {"code": 231, "indent": 0, "parameters": [16, "mio_tachie_kao_OAO", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE sis_room_ocha10"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(34);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(35);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(36);"]}, {"code": 231, "indent": 0, "parameters": [16, "mio_tachie_kao_smile2", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE sis_room_ocha11"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(37);"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["…………"]}, {"code": 401, "indent": 0, "parameters": ["…………"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(38);"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE sis_room_ocha12"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(39);"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE sis_room_ocha13"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(40);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(41);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(42);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(43);"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["var pic_ids = $gameNumberArray.value(41);"]}, {"code": 655, "indent": 0, "parameters": ["pic_ids.forEach(function(pic_id) {"]}, {"code": 655, "indent": 0, "parameters": ["  if($gameScreen.picture( pic_id )) {"]}, {"code": 655, "indent": 0, "parameters": ["  $gameScreen.picture(pic_id)._opacity = 0;"]}, {"code": 655, "indent": 0, "parameters": ["  }"]}, {"code": 655, "indent": 0, "parameters": ["});"]}, {"code": 230, "indent": 0, "parameters": [4]}, {"code": 108, "indent": 0, "parameters": ["确认视频路径"]}, {"code": 355, "indent": 0, "parameters": ["let vName = \"sis_room_ocha\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.setVideoPictureName(vName, true, false);"]}, {"code": 108, "indent": 0, "parameters": ["绑定视频"]}, {"code": 355, "indent": 0, "parameters": ["let pid = 30;"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPicture(pid, '', 0, 260, 300, 100, 100, 255, 0);"]}, {"code": 655, "indent": 0, "parameters": ["const pic = $gameScreen.picture(pid);"]}, {"code": 655, "indent": 0, "parameters": ["if (pic) {"]}, {"code": 655, "indent": 0, "parameters": ["pic.set<PERSON><PERSON>o<PERSON>(false);"]}, {"code": 655, "indent": 0, "parameters": ["pic.set<PERSON><PERSON>o<PERSON>(true);"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["const pic = $gameScreen.picture(30);"]}, {"code": 655, "indent": 0, "parameters": ["if (pic) {"]}, {"code": 655, "indent": 0, "parameters": ["pic.set<PERSON>ideo<PERSON>(false);"]}, {"code": 655, "indent": 0, "parameters": ["pic.set<PERSON><PERSON><PERSON>(true);"]}, {"code": 655, "indent": 0, "parameters": ["this._waitMode = 'videoPicture';"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 235, "indent": 0, "parameters": [30]}, {"code": 355, "indent": 0, "parameters": ["var pic_ids = $gameNumberArray.value(41);"]}, {"code": 655, "indent": 0, "parameters": ["pic_ids.forEach(function(pic_id) {"]}, {"code": 655, "indent": 0, "parameters": ["  if($gameScreen.picture( pic_id )) {"]}, {"code": 655, "indent": 0, "parameters": ["  $gameScreen.picture(pic_id)._opacity = 255;"]}, {"code": 655, "indent": 0, "parameters": ["  }"]}, {"code": 655, "indent": 0, "parameters": ["});"]}, {"code": 231, "indent": 0, "parameters": [16, "mio_tachie_kao_-A-", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 126, "indent": 0, "parameters": [197, 1, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(44);"]}, {"code": 231, "indent": 0, "parameters": [16, "mio_tachie_kao_=ω=", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE sis_room_ocha14"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(45);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(46);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(47);"]}, {"code": 231, "indent": 0, "parameters": [16, "mio_tachie_kao_happy", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE sis_room_ocha15"]}, {"code": 122, "indent": 0, "parameters": [204, 204, 0, 4, "\"上下震动\""]}, {"code": 117, "indent": 0, "parameters": [35]}, {"code": 122, "indent": 0, "parameters": [20, 20, 0, 2, 75, 90]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(47);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(48);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(49);"]}, {"code": 122, "indent": 0, "parameters": [14, 14, 0, 0, 15]}, {"code": 117, "indent": 0, "parameters": [3]}, {"code": 108, "indent": 0, "parameters": ["记录治疗次数"]}, {"code": 355, "indent": 0, "parameters": ["let healing = $gameSelfVariables.value([1, 2, 'healing']);"]}, {"code": 655, "indent": 0, "parameters": ["healing += 1;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSelfVariables.setValue([1, 2, 'healing'], healing);"]}, {"code": 111, "indent": 0, "parameters": [1, 1, 0, 0, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["在给妹妹喝下药之后，缠绕她的诅咒终于被解除。"]}, {"code": 401, "indent": 1, "parameters": ["从那天起，两人过上了安稳平静的生活——不再有恐惧与危险。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["\\c[2]你已抵达故事的终点……"]}, {"code": 401, "indent": 1, "parameters": ["但冒险还没有结束——     \\w[60]"]}, {"code": 401, "indent": 1, "parameters": ["自由游玩模式现已解锁！ "]}, {"code": 401, "indent": 1, "parameters": ["尽情探索、回顾，享受你自己的节奏吧。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 1, 0, 1, 0]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["妹に薬を飲ませたことで、"]}, {"code": 401, "indent": 2, "parameters": ["彼女を苦しめていた呪いはついに解かれた。"]}, {"code": 401, "indent": 2, "parameters": ["あの日から、二人は恐れも危険もない穏やかな日々を"]}, {"code": 401, "indent": 2, "parameters": ["過ごすようになった。"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["\\c[2]物語はここで一段落……"]}, {"code": 401, "indent": 2, "parameters": ["でも、冒険はまだ終わらない——     \\w[60]"]}, {"code": 401, "indent": 2, "parameters": ["フリープレイモードが開放されました！"]}, {"code": 401, "indent": 2, "parameters": ["自由に探索して、振り返って、自分のペースで世界を楽しんでください。"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["After giving <PERSON><PERSON><PERSON> the cure, "]}, {"code": 401, "indent": 2, "parameters": ["the curse that had haunted her was dispelled."]}, {"code": 401, "indent": 2, "parameters": ["From that day forward, the two of them began a quiet, "]}, {"code": 401, "indent": 2, "parameters": ["peaceful life together—free from fear and danger."]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["\\c[2]You’ve reached the end of the story..."]}, {"code": 401, "indent": 2, "parameters": ["But the adventure isn’t over just yet—"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["\\c[2]Free Play Mode has now been unlocked! "]}, {"code": 401, "indent": 2, "parameters": ["Explore, revisit, and enjoy the world at your own pace."]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["var pic_ids = $gameNumberArray.value(41);"]}, {"code": 655, "indent": 0, "parameters": ["pic_ids.forEach(function(pic_id) {"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.erasePicture(pic_id);"]}, {"code": 655, "indent": 0, "parameters": ["});"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 355, "indent": 0, "parameters": ["$gameMap.event(1).steupCEQJ(1)"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 2, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["第一次触发喝茶事件"]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.initPictureArray();"]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$gameScreen.picture(5) && $gameScreen.picture(5).name().includes(\"dozingOff\") && $gameScreen.picture(5)._opacity > 250"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 230, "indent": 2, "parameters": [10]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["var hasItem = false;"]}, {"code": 655, "indent": 0, "parameters": ["for (var i = 23; i <= 27; i++) {"]}, {"code": 655, "indent": 0, "parameters": ["    var item = $dataItems[i];"]}, {"code": 655, "indent": 0, "parameters": ["    if ($gameParty.numItems(item) >= 1) {"]}, {"code": 655, "indent": 0, "parameters": ["        hasItem = true;"]}, {"code": 655, "indent": 0, "parameters": ["        break;"]}, {"code": 655, "indent": 0, "parameters": ["    }"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 655, "indent": 0, "parameters": ["if (!hasItem) {"]}, {"code": 655, "indent": 0, "parameters": ["    $gameMap.event(1).steupCEQJ(1);"]}, {"code": 655, "indent": 0, "parameters": ["    $gameSelfSwitches.setValue([$gameMap.mapId(), 44, 'A'], false);"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.setPictureRemoveCommon(5)"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameMap.getGroupBulletListQJ('imoutoUtil').length > 0) {"]}, {"code": 655, "indent": 0, "parameters": ["let bulletList = $gameMap.getGroupBulletListQJ('imoutoUtil');"]}, {"code": 655, "indent": 0, "parameters": ["bulletList.forEach(bid => {"]}, {"code": 655, "indent": 0, "parameters": ["    let bullet = $gameMap._mapBulletsQJ[bid];"]}, {"code": 655, "indent": 0, "parameters": ["    if (bullet) {"]}, {"code": 655, "indent": 0, "parameters": ["        bullet.setDead({ t: ['Time', 0] });"]}, {"code": 655, "indent": 0, "parameters": ["         }"]}, {"code": 655, "indent": 0, "parameters": ["    });"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 356, "indent": 0, "parameters": [">图片快捷操作 : 批量图片[2,5] : 修改单属性 : 透明度[0] : 时间[45]"]}, {"code": 117, "indent": 0, "parameters": [31]}, {"code": 117, "indent": 0, "parameters": [26]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 231, "indent": 0, "parameters": [16, "mio_tachie_kao_-A-", 0, 0, 1000, 1600, 100, 100, 255, 0]}, {"code": 355, "indent": 0, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.steupCEQJ(38,eid)"]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE sis_room_ocha1"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(18);"]}, {"code": 355, "indent": 0, "parameters": ["var itemId; "]}, {"code": 655, "indent": 0, "parameters": ["var item;"]}, {"code": 655, "indent": 0, "parameters": ["    do {"]}, {"code": 655, "indent": 0, "parameters": ["        itemId = Math.randomInt(6) + 23;"]}, {"code": 655, "indent": 0, "parameters": ["        item = $dataItems[itemId];"]}, {"code": 655, "indent": 0, "parameters": ["    } while ($gameParty.numItems(item) < 1);"]}, {"code": 655, "indent": 0, "parameters": ["$gameParty.gainItem(item, -1); "]}, {"code": 655, "indent": 0, "parameters": ["this.count = itemId;"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.setValue(90, itemId);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(19);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(20);"]}, {"code": 231, "indent": 0, "parameters": [16, "mio_tachie_kao_OAO", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE sis_room_ocha2"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(21);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(22);"]}, {"code": 231, "indent": 0, "parameters": [16, "mio_tachie_kao_=ω=", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE sis_room_ocha3"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(23);"]}, {"code": 111, "indent": 0, "parameters": [2, "C", 0]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(24);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(25);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [2, "D", 0]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(26);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE sis_room_ocha4"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(27);"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["var pic_ids = $gameNumberArray.value(41);"]}, {"code": 655, "indent": 0, "parameters": ["pic_ids.forEach(function(pic_id) {"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.erasePicture(pic_id);"]}, {"code": 655, "indent": 0, "parameters": ["});"]}, {"code": 230, "indent": 0, "parameters": [4]}, {"code": 108, "indent": 0, "parameters": ["确认视频路径"]}, {"code": 355, "indent": 0, "parameters": ["let vName = \"sis_room_ocha\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.setVideoPictureName(vName, true, false);"]}, {"code": 108, "indent": 0, "parameters": ["绑定视频"]}, {"code": 355, "indent": 0, "parameters": ["let pid = 30;"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPicture(pid, '', 0, 260, 300, 100, 100, 255, 0);"]}, {"code": 230, "indent": 0, "parameters": [2]}, {"code": 355, "indent": 0, "parameters": ["const pid = 30;"]}, {"code": 655, "indent": 0, "parameters": ["const pic = $gameScreen.picture(pid);"]}, {"code": 655, "indent": 0, "parameters": ["if (pic) {"]}, {"code": 655, "indent": 0, "parameters": ["pic.set<PERSON><PERSON>o<PERSON>(false);"]}, {"code": 655, "indent": 0, "parameters": ["pic.set<PERSON><PERSON>o<PERSON>(true);"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.setPictureVideoCurrentTime(30,0.01)"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(28);"]}, {"code": 355, "indent": 0, "parameters": ["const pic = $gameScreen.picture(30);"]}, {"code": 655, "indent": 0, "parameters": ["if (pic) {"]}, {"code": 655, "indent": 0, "parameters": ["pic.set<PERSON>ideo<PERSON>(false);"]}, {"code": 655, "indent": 0, "parameters": ["pic.set<PERSON><PERSON><PERSON>(true);"]}, {"code": 655, "indent": 0, "parameters": ["this._waitMode = 'videoPicture';"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 122, "indent": 0, "parameters": [17, 17, 1, 2, 6, 12]}, {"code": 111, "indent": 0, "parameters": [1, 20, 0, 40, 2]}, {"code": 122, "indent": 1, "parameters": [20, 20, 1, 0, 25]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 122, "indent": 1, "parameters": [20, 20, 0, 2, 75, 90]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 313, "indent": 0, "parameters": [0, 2, 0, 30]}, {"code": 117, "indent": 0, "parameters": [12]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(29);"]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(30);"]}, {"code": 250, "indent": 0, "parameters": [{"name": "キラーン（ひらめき・アイテム発見・獲得）", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 121, "indent": 0, "parameters": [493, 493, 0]}, {"code": 355, "indent": 0, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["let index = 31;"]}, {"code": 655, "indent": 0, "parameters": ["let textArray = window.MapEventDialogue4[eid][String(index)];"]}, {"code": 655, "indent": 0, "parameters": ["var template = \"\\\\fs[36]\\\\c[2]\\\\>%TEXT%\";"]}, {"code": 655, "indent": 0, "parameters": ["textArray = textArray.map(t => template.replace(\"%TEXT%\", t));"]}, {"code": 655, "indent": 0, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 122, "indent": 0, "parameters": [14, 14, 0, 0, 15]}, {"code": 117, "indent": 0, "parameters": [3]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 235, "indent": 0, "parameters": [30]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 355, "indent": 0, "parameters": ["$gameMap.event(1).steupCEQJ(1)"]}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [12, "!this.selectedId"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 115, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["没做完，鸽着"]}, {"code": 111, "indent": 0, "parameters": [12, "this.selectedId === 24"]}, {"code": 231, "indent": 1, "parameters": [16, "mio_tachie_kao_=ω=", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 1, "parameters": [">地图临时漂浮文字 : 简单临时对象 : 位置[1300,150] : 文本[\\dDCOG[11:2:2:2]胡萝卜牛奶…] : 样式[2] : 弹道[9] : 持续时间[180]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "this.selectedId === 25"]}, {"code": 231, "indent": 1, "parameters": [16, "mio_tachie_kao_=ω=", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 1, "parameters": [">地图临时漂浮文字 : 简单临时对象 : 位置[1300,150] : 文本[\\dDCOG[11:2:2:2]草莓牛奶…] : 样式[2] : 弹道[9] : 持续时间[180]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "this.selectedId === 26"]}, {"code": 231, "indent": 1, "parameters": [16, "mio_tachie_kao_=ω=", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 1, "parameters": [">地图临时漂浮文字 : 简单临时对象 : 位置[1300,150] : 文本[\\dDCOG[11:2:2:2]蜜瓜牛奶…] : 样式[2] : 弹道[9] : 持续时间[180]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 1, "y": 1}, null, null, null, null, null, null, null, null, null, null, null]}