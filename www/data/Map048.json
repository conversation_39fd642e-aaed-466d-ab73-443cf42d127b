{"autoplayBgm": false, "autoplayBgs": false, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "", "pan": 0, "pitch": 100, "volume": 50}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 71, "note": "<深渊>\n<All Allow Region: 249,250>", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 18, "width": 49, "data": [5984, 5984, 5984, 6008, 1572, 2448, 2432, 2432, 2432, 2456, 1552, 2860, 1573, 1574, 1565, 1589, 2880, 2864, 2864, 2864, 2864, 2864, 2864, 2868, 2902, 2860, 1590, 1591, 1567, 2860, 1554, 2832, 2840, 3984, 3968, 3968, 3968, 3968, 3992, 2832, 2816, 2816, 2816, 2840, 1572, 2448, 2432, 2432, 2432, 5984, 5984, 5984, 6008, 1572, 2448, 2432, 2432, 2432, 2456, 1560, 1584, 1561, 1563, 1573, 1557, 2904, 2872, 2864, 2864, 2864, 2864, 2868, 2902, 1590, 1591, 1567, 1574, 1575, 1564, 1580, 2834, 2854, 3984, 3968, 3968, 3968, 3968, 3992, 2832, 2816, 2816, 2816, 2840, 1572, 2448, 2432, 2432, 2432, 5984, 5984, 5984, 6008, 1554, 2448, 2432, 2432, 2432, 2456, 1568, 1569, 1569, 1560, 1563, 1557, 2858, 2904, 2872, 2864, 2868, 2892, 2902, 2862, 1559, 1574, 1575, 1564, 1579, 1562, 1575, 2860, 1654, 3969, 3968, 3968, 3968, 3968, 3992, 2832, 2816, 2820, 2844, 2854, 1554, 2448, 2432, 2432, 2432, 5984, 5984, 5984, 6008, 1572, 2448, 2432, 2432, 2432, 2456, 1568, 1569, 1569, 1568, 1571, 1557, 2833, 2852, 2904, 2892, 2902, 2954, 2862, 1590, 1583, 1564, 1585, 1562, 1577, 1575, 2858, 4002, 3969, 3968, 3968, 3968, 3968, 3968, 3992, 2832, 2816, 2840, 1564, 1585, 1562, 2448, 2432, 2432, 2432, 5984, 5984, 5984, 6008, 1554, 2448, 2432, 2432, 2432, 2456, 1568, 1569, 1594, 1568, 1552, 1557, 2856, 2854, 2946, 2932, 2933, 2951, 1590, 1583, 1575, 1554, 1577, 1575, 2851, 2849, 2855, 1644, 3968, 3968, 3968, 3968, 3968, 3972, 1663, 2832, 2820, 2854, 1554, 1569, 1570, 2448, 2432, 2432, 2432, 5984, 5984, 5984, 5986, 6020, 2472, 2440, 2432, 2432, 2456, 1568, 1569, 1569, 1568, 1571, 1565, 1589, 2955, 2941, 2940, 2950, 2862, 1559, 1575, 1564, 1580, 2850, 2837, 2855, 1654, 3988, 3969, 3968, 3968, 3968, 3968, 3972, 1663, 2859, 2845, 2841, 1564, 1580, 1554, 1570, 2448, 2432, 2432, 2432, 5984, 5984, 5984, 5984, 5986, 6020, 2472, 2440, 2432, 2434, 2452, 2452, 2468, 1568, 1552, 1573, 1557, 2859, 2861, 1590, 1566, 1566, 1567, 1564, 1580, 1575, 2834, 2854, 1654, 3969, 3968, 3968, 3968, 3968, 3968, 3968, 1645, 2858, 2898, 2900, 2848, 1572, 1570, 1554, 1570, 2448, 2432, 2432, 2432, 5984, 5984, 5984, 5984, 5984, 5986, 6020, 2472, 2460, 2440, 2432, 2432, 2434, 2468, 1552, 2858, 1565, 1566, 1566, 1567, 1574, 1574, 1575, 1554, 1575, 2851, 2855, 4002, 3969, 3968, 3968, 3968, 3968, 3968, 3968, 3972, 1663, 2860, 2882, 2902, 2860, 1554, 1578, 1572, 2466, 2433, 2432, 2432, 2432, 5984, 5984, 5984, 5984, 5984, 5984, 5986, 6020, 1572, 2448, 2432, 2432, 2432, 2456, 1571, 2848, 1573, 1577, 1577, 1578, 2859, 2838, 2852, 1554, 2851, 2855, 1654, 3969, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3992, 2858, 2898, 2890, 2858, 1564, 1580, 2858, 1572, 2448, 2432, 2432, 2432, 2432, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 6008, 1572, 2448, 2432, 2432, 2432, 2456, 1571, 2835, 2861, 2898, 2884, 2884, 2900, 2856, 1564, 1580, 2848, 1654, 3969, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3972, 1663, 2860, 2882, 2902, 2860, 1572, 1570, 1564, 1580, 2448, 2432, 2432, 2432, 2432, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 6008, 1556, 2472, 2440, 2432, 2432, 2456, 1571, 2860, 2898, 2865, 2864, 2864, 2888, 1564, 1580, 1575, 2848, 3984, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3972, 4006, 2858, 2898, 2890, 2858, 1564, 1580, 1578, 1554, 1570, 2448, 2432, 2432, 2432, 2432, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5986, 6020, 1554, 2448, 2432, 2432, 2456, 1552, 2898, 2865, 2864, 2864, 2868, 2902, 1554, 1575, 2851, 2855, 1644, 3968, 3968, 3968, 3968, 3968, 3972, 1647, 1663, 2850, 2842, 2904, 2902, 2860, 1554, 1570, 1564, 1580, 2466, 2433, 2432, 2432, 2432, 2432, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 6008, 1554, 2448, 2432, 2432, 2456, 1552, 2880, 2864, 2864, 2864, 2888, 2858, 1554, 2850, 2842, 1654, 3969, 3968, 3968, 3968, 3968, 3972, 4006, 2850, 2836, 2817, 2818, 2836, 2852, 1564, 1580, 1578, 1572, 1570, 2448, 2432, 2432, 2432, 2432, 2432, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 6008, 1572, 2448, 2432, 2432, 2456, 1571, 2904, 2872, 2864, 2868, 2902, 2860, 1554, 2834, 2854, 3984, 3968, 3968, 3968, 3968, 3968, 1645, 2850, 2817, 2816, 2816, 2816, 2820, 2854, 1572, 1570, 1564, 1580, 2466, 2433, 2432, 2432, 2432, 2432, 2432, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 6008, 1572, 2448, 2432, 2432, 2456, 1571, 2862, 2904, 2892, 2902, 2858, 1564, 1580, 2848, 4002, 3969, 3968, 3968, 3968, 3968, 3972, 1663, 2832, 2816, 2820, 2844, 2844, 2854, 1564, 1580, 1578, 1554, 1570, 2448, 2432, 2432, 2432, 2432, 2432, 2432, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 6008, 1554, 2448, 2432, 2432, 2456, 1560, 1584, 1563, 2859, 2849, 2855, 1554, 1575, 2848, 1644, 3968, 3968, 3968, 3968, 3968, 3992, 2850, 2817, 2816, 2840, 1564, 1579, 1603, 1562, 1570, 1564, 1580, 2466, 2433, 2432, 2432, 2432, 2432, 2432, 2432, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 6008, 1572, 2448, 2432, 2432, 2456, 1552, 1577, 1560, 1543, 1584, 1584, 1562, 2850, 2842, 3984, 3968, 3968, 3968, 3968, 3972, 1663, 2832, 2820, 2844, 2854, 1554, 1569, 1611, 1570, 1578, 1572, 1570, 2448, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 6008, 1572, 2448, 2432, 2432, 2456, 1560, 1563, 1576, 1597, 1577, 1577, 1575, 2832, 2840, 3984, 3968, 3968, 3968, 3968, 3992, 2850, 2817, 2840, 2898, 2900, 1572, 1577, 1619, 1578, 1564, 1580, 2466, 2433, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 5984, 5984, 5984, 5984, 5984, 5988, 6012, 6012, 6022, 1554, 2448, 2432, 2432, 2456, 1568, 1552, 2858, 1551, 2850, 2836, 2836, 2821, 2854, 1644, 3968, 3968, 3968, 3968, 1645, 2832, 2816, 2840, 2880, 2888, 1554, 2851, 2849, 2861, 1554, 1570, 2448, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 5984, 5984, 5984, 5984, 5988, 6022, 3992, 2850, 2852, 1554, 2448, 2432, 2432, 2456, 1568, 1571, 2833, 2836, 2817, 2820, 2844, 2854, 1654, 3969, 3968, 3968, 3968, 3968, 3992, 2834, 2844, 2854, 2882, 2902, 1554, 3236, 1564, 1579, 1562, 2466, 2433, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 5984, 5984, 5988, 6012, 6022, 3968, 1645, 2856, 2841, 1554, 2448, 2432, 2432, 2456, 1568, 1571, 2832, 2816, 2820, 2854, 1654, 3988, 3969, 3968, 3968, 3968, 3968, 3968, 1645, 2848, 2898, 2884, 2890, 2858, 1572, 3224, 1572, 1569, 1570, 2448, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 6012, 6012, 6022, 3968, 3968, 3968, 3970, 4004, 2860, 1572, 2448, 2432, 2432, 2434, 2468, 1552, 2832, 2816, 2840, 1654, 3969, 3968, 3968, 3968, 3968, 3968, 3968, 3972, 1663, 2848, 2904, 2872, 2888, 2848, 1554, 3224, 1554, 2466, 2452, 2433, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2836, 2852, 1662, 3976, 3968, 3968, 3968, 3970, 1655, 1556, 2472, 2440, 2432, 2432, 2456, 1571, 2834, 2844, 2854, 1644, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 1645, 2850, 2819, 2852, 2904, 2902, 2848, 1554, 3238, 1572, 2448, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2816, 2818, 2852, 3984, 3968, 3968, 3968, 3968, 1645, 2858, 1554, 2448, 2432, 2432, 2456, 1552, 2860, 1654, 3988, 3969, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3992, 2832, 2816, 2818, 2836, 2836, 2842, 1572, 2860, 1554, 2056, 2460, 2440, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2844, 2844, 2841, 1644, 3968, 3968, 3968, 3968, 3992, 2860, 1554, 2448, 2432, 2432, 2456, 1552, 4002, 3969, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 1645, 2832, 2816, 2816, 2816, 2816, 2840, 1572, 1603, 1562, 2064, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 3233, 3245, 2848, 1662, 3996, 3976, 3968, 3968, 3970, 4004, 1554, 2448, 2432, 2432, 2456, 1552, 4008, 3976, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 1645, 2832, 2816, 2816, 2816, 2816, 2840, 1572, 1611, 1570, 2064, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2849, 2849, 2847, 2849, 2861, 1644, 3968, 3968, 3972, 1663, 1554, 2448, 2432, 2432, 2456, 1552, 2858, 1662, 3976, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3992, 2832, 2820, 2844, 2844, 2824, 2840, 1554, 1619, 1578, 2064, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 6004, 6004, 6004, 6004, 6004, 6004, 6020, 3968, 1645, 2858, 1554, 2448, 2432, 2432, 2456, 1552, 2833, 2852, 4008, 1647, 3976, 3968, 3968, 3968, 3968, 3968, 3968, 1645, 2834, 2854, 2898, 2900, 2832, 2840, 1572, 2851, 2861, 2064, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 5984, 5984, 5984, 5984, 5984, 5984, 5986, 6020, 3992, 2860, 1572, 2448, 2432, 2432, 2456, 1552, 2856, 2826, 2836, 2852, 1662, 3976, 3968, 3968, 3968, 3968, 3972, 1663, 2848, 2898, 2865, 2888, 2834, 2854, 1554, 2848, 3242, 2064, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5986, 6004, 6020, 1554, 2448, 2432, 2432, 2456, 1560, 1563, 2856, 2824, 2818, 2852, 3984, 3968, 3968, 3968, 3968, 1645, 2851, 2855, 2880, 2864, 2888, 2848, 1564, 1580, 2848, 3232, 2064, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 6008, 1554, 2448, 2432, 2432, 2456, 1568, 1560, 1563, 2832, 2816, 2840, 4008, 3976, 3968, 3968, 3968, 3992, 2848, 2898, 2865, 2864, 2888, 2848, 1554, 1570, 2860, 3244, 2064, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 6008, 1572, 2448, 2432, 2432, 2456, 1568, 1568, 1552, 2832, 2816, 2818, 2852, 1644, 3968, 3968, 3968, 3992, 2848, 2880, 2864, 2864, 2888, 2848, 1572, 1570, 3242, 2082, 2049, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 6008, 1572, 2448, 2432, 2432, 2456, 1659, 1568, 1571, 2832, 2816, 2816, 2840, 3984, 3968, 3968, 3968, 1645, 2848, 2880, 2864, 2868, 2902, 2848, 1572, 1570, 3232, 2064, 2048, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 6008, 1572, 2448, 2432, 2432, 2434, 2468, 1659, 1571, 2832, 2816, 2820, 2854, 1644, 3968, 3968, 3968, 1645, 2848, 2880, 2864, 2888, 2851, 2855, 1554, 1578, 3244, 2064, 2048, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5986, 6020, 2472, 2440, 2432, 2432, 2434, 2468, 1571, 2832, 2816, 2840, 1654, 3969, 3968, 3968, 3968, 3992, 2848, 2880, 2864, 2888, 2848, 1564, 1580, 2899, 2909, 2064, 2048, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 6008, 1554, 2448, 2432, 2432, 2432, 2456, 1552, 2856, 2824, 2840, 3984, 3968, 3968, 3968, 3968, 3992, 2848, 2904, 2892, 2902, 2848, 1572, 1570, 2896, 2858, 2064, 2048, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 6008, 1572, 2448, 2432, 2432, 2432, 2456, 1560, 1563, 2856, 2854, 1644, 3968, 3968, 3968, 3968, 1645, 2833, 2836, 2836, 2836, 2842, 1554, 1570, 2908, 2860, 2064, 2048, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 6008, 1554, 2448, 2432, 2432, 2432, 2456, 1568, 1560, 1561, 1563, 4008, 3996, 3996, 3996, 3996, 4006, 2856, 2844, 2844, 2844, 2841, 1554, 1570, 2068, 2068, 2049, 2048, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 6008, 1572, 2448, 2432, 2432, 2432, 2456, 1568, 1568, 1569, 1560, 1584, 1584, 1584, 1584, 1584, 1584, 1584, 1561, 1561, 1563, 2860, 1572, 2474, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 6008, 1572, 2448, 2432, 2432, 2432, 2456, 1659, 1568, 1569, 1568, 1569, 1569, 1569, 1569, 1569, 1569, 1569, 1569, 1569, 1560, 1584, 1562, 2449, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 6008, 1572, 2448, 2432, 2432, 2432, 2434, 2468, 1659, 1660, 1568, 1569, 1569, 1569, 1569, 1569, 1569, 1569, 1651, 1652, 1568, 1569, 1570, 2448, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5988, 6012, 6022, 1556, 2472, 2440, 2432, 2432, 2432, 2434, 2452, 2468, 1568, 1594, 1569, 1569, 1569, 1569, 1569, 1569, 1653, 1569, 1659, 1660, 1661, 2448, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 5984, 5984, 5984, 5984, 5984, 5984, 5988, 6012, 6022, 2912, 2914, 2948, 1572, 2448, 2432, 2432, 2432, 2432, 2432, 2434, 2452, 2452, 2452, 2452, 2452, 2452, 2452, 2452, 2452, 2468, 1568, 1569, 1570, 2448, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 5984, 5988, 6012, 6012, 6012, 6012, 6022, 2912, 2912, 2912, 2912, 2936, 1556, 2472, 2440, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2434, 2452, 2452, 2452, 2433, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 6012, 6022, 2912, 2912, 2912, 2912, 2912, 2912, 2912, 2912, 2912, 2914, 2948, 1556, 2472, 2440, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2912, 2912, 2912, 2912, 2912, 2912, 2912, 2912, 2912, 2912, 2912, 2912, 2914, 2948, 1556, 2472, 2440, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2940, 2940, 2940, 2940, 2940, 2940, 2940, 2920, 2920, 2912, 2912, 2912, 2912, 2914, 2948, 1556, 2472, 2460, 2440, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2884, 2884, 2900, 2859, 2838, 2852, 3674, 1572, 2952, 2940, 2940, 2940, 2940, 2920, 2914, 2932, 2948, 1556, 2472, 2460, 2460, 2460, 2460, 2460, 2460, 2460, 2460, 2460, 2460, 2460, 2440, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2868, 2892, 2874, 2900, 2832, 2840, 3676, 1554, 2862, 2898, 2884, 2900, 1556, 2952, 2940, 2940, 2950, 2833, 2836, 2836, 2836, 2852, 4002, 3988, 3988, 3988, 4004, 2850, 2836, 1556, 2472, 2460, 2460, 2440, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2888, 2858, 2882, 2902, 2856, 2854, 1564, 1580, 2898, 2865, 2868, 2894, 2909, 2862, 1556, 2859, 2838, 2817, 2816, 2816, 2820, 2854, 3984, 3968, 3968, 3968, 1645, 2832, 2816, 2818, 2837, 2849, 1556, 2472, 2460, 2460, 2440, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2888, 2860, 2908, 2858, 1564, 1579, 1562, 1575, 2880, 2868, 2902, 2850, 2852, 2907, 2901, 1556, 2856, 2824, 2816, 2820, 2854, 1654, 3969, 3968, 3968, 3968, 3992, 2832, 2816, 2816, 2840, 2898, 2884, 2900, 2862, 1572, 2448, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2866, 2900, 2859, 2855, 1554, 1574, 1575, 2858, 2904, 2902, 2859, 2825, 2818, 2852, 2881, 2900, 1572, 2832, 2816, 2840, 1654, 3969, 3968, 3968, 3968, 3968, 1645, 2832, 2816, 2816, 2840, 2904, 2872, 2866, 2900, 1572, 2448, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2864, 2888, 1564, 1543, 1562, 2828, 1572, 1539, 1540, 1538, 1538, 1541, 2824, 2840, 2880, 2888, 1572, 2832, 2816, 2840, 3984, 3968, 3968, 3968, 3968, 3972, 1663, 2832, 2816, 2816, 2818, 2852, 2880, 2864, 2888, 1572, 2448, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2864, 2888, 1554, 1595, 1570, 2850, 2854, 1539, 1538, 1539, 1538, 1539, 2832, 2840, 2882, 2902, 1554, 2832, 2820, 2854, 1644, 3968, 3968, 3968, 3968, 3992, 2850, 2817, 2816, 2816, 2816, 2840, 2880, 2868, 2902, 1554, 2448, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2868, 2902, 1554, 1595, 1575, 2832, 2840, 1539, 1538, 1538, 1538, 1539, 2832, 2840, 2908, 2862, 1554, 2832, 2840, 1654, 3969, 3968, 3968, 3968, 3972, 1663, 2832, 2816, 2816, 2816, 2816, 2840, 2904, 2902, 2862, 1572, 2448, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2902, 2858, 1554, 1551, 2850, 2817, 2840, 1538, 1539, 1538, 1538, 1538, 2832, 2818, 2852, 1564, 1580, 2832, 2840, 3984, 3968, 3968, 3968, 3968, 1645, 2850, 2817, 2816, 2816, 2816, 2816, 2818, 2837, 2861, 1564, 1580, 2448, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2836, 2842, 1572, 2850, 2817, 2816, 2840, 1539, 1538, 1538, 1539, 1539, 2832, 2820, 2854, 1572, 1575, 2832, 2840, 1644, 3968, 3968, 3968, 3968, 3992, 2832, 2816, 2816, 2816, 2816, 2816, 2820, 2854, 1564, 1580, 1570, 2448, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2816, 2840, 1572, 2834, 2844, 2824, 2818, 2852, 3240, 3212, 3238, 2850, 2817, 2840, 1564, 1580, 2850, 2817, 2840, 3984, 3968, 3968, 3968, 3968, 3992, 2832, 2816, 2816, 2816, 2816, 2816, 2840, 1564, 1580, 1570, 1570, 2448, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2816, 2840, 1554, 2860, 2954, 2856, 2824, 2818, 2852, 3232, 2859, 2825, 2820, 2854, 1554, 2850, 2817, 2816, 2840, 3984, 3968, 3968, 3968, 3968, 1645, 2832, 2816, 2816, 2816, 2816, 2816, 2840, 1554, 1570, 1570, 1570, 2448, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2816, 2840, 1572, 2932, 2915, 2948, 2832, 2816, 2840, 3241, 3237, 2856, 2854, 1564, 1580, 2832, 2816, 2816, 2840, 3984, 3968, 3968, 3968, 3968, 1645, 2832, 2816, 2816, 2816, 2816, 2816, 2840, 1572, 1570, 1570, 2466, 2433, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2844, 2841, 1572, 2912, 2912, 2936, 2856, 2824, 2822, 2861, 3241, 3245, 1564, 1580, 1578, 2832, 2816, 2820, 2854, 1644, 3968, 3968, 3968, 3968, 3992, 2832, 2816, 2820, 2844, 2844, 2828, 2854, 1572, 1570, 2466, 2433, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2900, 2848, 1554, 2912, 2912, 2914, 2948, 2856, 2841, 1564, 1585, 1603, 1562, 1575, 2850, 2817, 2816, 2840, 1654, 3969, 3968, 3968, 3968, 3968, 3992, 2832, 2820, 2854, 2898, 2900, 2848, 1564, 1580, 2466, 2433, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2888, 2860, 1556, 2940, 2920, 2912, 2914, 2948, 2860, 1572, 1574, 1619, 1575, 2859, 2845, 2844, 2844, 2854, 3984, 3968, 3968, 3968, 3968, 3968, 1645, 2832, 2840, 2898, 2865, 2888, 2848, 1572, 1570, 2448, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2866, 2900, 2850, 2852, 2952, 2920, 2912, 2914, 2948, 1554, 2858, 3234, 3220, 3220, 3220, 3220, 3220, 3236, 1644, 3968, 3968, 3968, 3968, 3968, 3992, 2834, 2854, 2880, 2864, 2888, 2848, 1554, 1570, 2448, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2864, 2888, 2832, 2818, 2852, 1556, 2940, 2920, 2936, 1572, 2860, 3240, 3228, 3208, 1545, 3200, 3204, 3238, 3984, 3968, 3968, 3968, 3968, 3972, 1663, 2860, 2898, 2865, 2864, 2888, 2848, 1572, 1570, 2448, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2864, 2888, 2832, 2816, 2818, 2852, 1553, 2952, 2937, 1572, 2994, 2980, 2996, 3240, 3228, 3228, 3238, 1654, 3969, 3968, 3968, 3968, 3968, 1645, 2858, 2898, 2865, 2864, 2864, 2888, 2848, 1572, 2466, 2433, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2864, 2888, 2856, 2844, 2844, 2826, 2836, 2852, 1554, 1580, 2976, 2960, 2962, 2980, 2980, 2996, 2858, 3984, 3968, 3968, 3968, 3968, 3968, 3992, 2848, 2880, 2864, 2864, 2864, 2888, 2848, 1554, 2448, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2864, 2866, 2884, 2884, 2900, 2832, 2816, 2840, 1554, 1575, 3000, 2988, 2968, 2960, 2964, 2998, 2848, 3984, 3968, 3968, 3968, 3968, 3968, 1645, 2848, 2904, 2892, 2872, 2868, 2902, 2848, 1572, 2448, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2864, 2864, 2864, 2864, 2888, 2856, 2844, 2841, 1572, 2851, 2849, 2861, 3000, 2988, 2998, 2850, 2842, 1644, 3968, 3968, 3968, 3968, 3968, 3992, 2833, 2836, 2852, 2904, 2902, 2850, 2842, 1556, 2472, 2440, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2864, 2864, 2864, 2864, 2866, 2884, 2900, 2848, 1554, 2848, 2907, 2909, 2850, 2836, 2836, 2817, 2840, 3984, 3968, 3968, 3968, 3968, 3968, 3992, 2832, 2816, 2818, 2836, 2836, 2817, 2818, 2816, 1554, 2448, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2864, 2864, 2864, 2864, 2864, 2864, 2888, 2848, 1572, 2833, 2836, 2836, 2817, 2816, 2816, 2816, 2840, 3984, 3968, 3968, 3968, 3968, 3968, 3992, 2832, 2816, 2816, 2816, 2816, 2816, 2818, 2852, 1554, 2448, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 2432, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3120, 3104, 3104, 3104, 3104, 3108, 3142, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4272, 4256, 4280, 0, 0, 3480, 3448, 3440, 3440, 3464, 3952, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3144, 3132, 3112, 3104, 3108, 3142, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4272, 4256, 4280, 0, 0, 0, 3456, 3444, 3468, 3478, 3952, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3144, 3132, 3142, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4272, 4256, 4280, 0, 0, 0, 3480, 3478, 3955, 3953, 3959, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4290, 4257, 4256, 4280, 0, 0, 0, 0, 0, 3952, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3906, 3908, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4272, 4256, 4260, 4294, 0, 0, 0, 0, 3955, 3959, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3912, 3910, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4290, 4257, 4260, 4294, 0, 0, 0, 3858, 3860, 3952, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4290, 4257, 4256, 4280, 0, 0, 0, 3858, 3829, 3862, 3952, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4290, 4257, 4256, 4260, 4294, 0, 0, 0, 3840, 3848, 3955, 3959, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4290, 4257, 4256, 4260, 4294, 0, 0, 0, 3858, 3829, 3862, 3952, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3426, 3412, 3412, 3428, 0, 0, 0, 0, 0, 0, 0, 4290, 4257, 4256, 4260, 4294, 0, 0, 0, 3858, 3825, 3848, 3955, 3959, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3426, 3393, 3392, 3392, 3416, 0, 0, 0, 0, 0, 0, 4290, 4257, 4256, 4260, 4294, 0, 0, 0, 0, 3864, 3852, 3862, 3952, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3426, 3393, 3392, 3392, 3396, 3430, 0, 0, 0, 0, 0, 0, 4272, 4256, 4260, 4294, 0, 0, 0, 0, 0, 0, 0, 3955, 3959, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3408, 3392, 3392, 3392, 3416, 0, 0, 0, 0, 0, 0, 4290, 4257, 4260, 4294, 0, 0, 0, 0, 0, 0, 0, 0, 3952, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3432, 3400, 3392, 3396, 3430, 0, 0, 0, 0, 0, 4290, 4257, 4256, 4280, 0, 0, 0, 0, 0, 0, 0, 0, 3955, 3959, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3432, 3420, 3430, 0, 0, 0, 0, 0, 0, 4272, 4256, 4260, 4294, 0, 0, 0, 0, 0, 3955, 3965, 0, 3964, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4290, 4257, 4256, 4280, 0, 0, 0, 0, 0, 0, 3952, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4272, 4256, 4256, 4280, 0, 0, 0, 0, 0, 0, 3952, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4272, 4256, 4260, 4294, 0, 0, 0, 0, 0, 0, 3952, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4272, 4256, 4280, 0, 0, 0, 0, 0, 0, 0, 3952, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4272, 4256, 4280, 0, 0, 0, 0, 0, 0, 0, 3952, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4272, 4256, 4280, 0, 0, 0, 0, 0, 0, 0, 3952, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4290, 4257, 4256, 4280, 0, 0, 0, 0, 0, 0, 0, 3952, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4290, 4257, 4256, 4256, 4280, 0, 0, 0, 0, 0, 0, 0, 3952, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4290, 4277, 4285, 4264, 4256, 4260, 4294, 0, 0, 0, 0, 0, 0, 0, 3952, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4290, 4257, 4280, 0, 4296, 4264, 4280, 0, 0, 0, 0, 0, 0, 0, 0, 3952, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4299, 4285, 4264, 4258, 4292, 0, 4272, 4280, 0, 0, 0, 0, 0, 0, 0, 0, 3952, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4296, 4284, 4266, 4276, 4257, 4280, 0, 0, 0, 0, 0, 0, 0, 0, 3952, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4272, 4256, 4256, 4280, 0, 0, 0, 0, 0, 0, 0, 0, 3952, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4272, 4256, 4260, 4294, 0, 0, 0, 0, 0, 0, 0, 3955, 3959, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3963, 3957, 0, 0, 0, 0, 4296, 4264, 4280, 0, 0, 0, 0, 0, 0, 0, 0, 3952, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3952, 0, 0, 0, 3054, 0, 4272, 4258, 4292, 0, 0, 0, 0, 0, 0, 0, 3952, 0, 3091, 3101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3952, 0, 0, 0, 0, 0, 4272, 4256, 4280, 0, 0, 0, 0, 0, 0, 0, 3952, 0, 3100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3952, 0, 0, 0, 0, 0, 4272, 4256, 4280, 0, 0, 0, 0, 0, 0, 0, 3952, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3952, 0, 0, 0, 0, 0, 4272, 4256, 4280, 0, 0, 0, 0, 0, 0, 3955, 3959, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3952, 0, 0, 0, 0, 0, 4272, 4256, 4280, 0, 0, 0, 0, 0, 0, 3952, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3961, 3957, 0, 0, 0, 0, 4272, 4256, 4280, 0, 0, 0, 0, 0, 0, 3952, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3961, 3953, 3957, 0, 0, 4272, 4256, 4280, 0, 0, 0, 0, 0, 0, 3952, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3961, 3965, 0, 4296, 4284, 4294, 0, 3963, 3953, 3953, 3957, 0, 3952, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3961, 3953, 3959, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3188, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4290, 4276, 4292, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3176, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4272, 4256, 4280, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3176, 0, 0, 0, 0, 0, 0, 0, 3579, 3569, 3569, 3569, 3573, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4272, 4256, 4280, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3962, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3154, 3188, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3577, 3573, 0, 0, 0, 0, 0, 0, 0, 0, 4290, 4257, 4260, 4294, 0, 0, 3474, 3460, 3476, 0, 0, 0, 0, 3952, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3152, 3176, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3568, 0, 0, 0, 0, 0, 0, 0, 4290, 4257, 4256, 4280, 0, 0, 0, 3456, 3440, 3442, 3476, 0, 0, 0, 3952, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3152, 3176, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3568, 0, 0, 0, 0, 0, 0, 0, 4272, 4256, 4260, 4294, 0, 0, 0, 3456, 3440, 3440, 3464, 0, 0, 0, 3952, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3156, 3190, 0, 0, 0, 3578, 0, 0, 0, 0, 0, 0, 0, 3568, 0, 0, 0, 0, 0, 0, 0, 4272, 4256, 4280, 0, 0, 0, 0, 3456, 3440, 3444, 3478, 0, 0, 3955, 3959, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3190, 0, 0, 0, 0, 3568, 0, 0, 0, 0, 0, 0, 0, 3568, 0, 0, 0, 0, 0, 0, 0, 4272, 4256, 4280, 0, 0, 0, 3474, 3441, 3440, 3464, 0, 0, 3955, 3959, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3568, 0, 0, 0, 0, 0, 0, 0, 3568, 0, 0, 0, 0, 0, 0, 4290, 4257, 4256, 4280, 0, 0, 3474, 3441, 3440, 3444, 3478, 0, 3955, 3959, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3577, 3573, 0, 0, 0, 0, 0, 3571, 3575, 0, 0, 0, 0, 0, 0, 4272, 4256, 4256, 4280, 0, 0, 3456, 3440, 3440, 3464, 0, 0, 3952, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3577, 3581, 0, 0, 0, 3579, 3575, 0, 0, 0, 3138, 3140, 0, 0, 4272, 4256, 4256, 4280, 0, 0, 3456, 3440, 3444, 3478, 0, 0, 3952, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3914, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3138, 3105, 3128, 0, 0, 4272, 4256, 4256, 4280, 0, 0, 3456, 3440, 3464, 0, 0, 0, 3952, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3889, 3908, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3120, 3108, 3142, 0, 0, 4272, 4256, 4256, 4280, 0, 0, 3480, 3468, 3478, 0, 0, 3955, 3959, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3888, 3874, 3908, 0, 0, 0, 0, 0, 0, 0, 0, 3138, 3105, 3128, 0, 0, 0, 4272, 4256, 4260, 4294, 0, 0, 0, 0, 0, 0, 0, 3952, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3912, 3900, 3882, 3908, 0, 0, 0, 0, 0, 0, 0, 3120, 3108, 3142, 0, 0, 4290, 4257, 4256, 4280, 0, 0, 0, 0, 0, 0, 0, 0, 3952, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3912, 3910, 0, 0, 0, 0, 0, 0, 0, 3144, 3142, 0, 0, 0, 4272, 4256, 4256, 4280, 0, 0, 0, 0, 0, 0, 0, 0, 3952, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4272, 4256, 4260, 4294, 0, 0, 0, 0, 0, 0, 0, 0, 3952, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4272, 4256, 4280, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3952, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3172, 3188, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4272, 4256, 4280, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3952, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3152, 3154, 3188, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4272, 4256, 4280, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3952, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3152, 3152, 3154, 3188, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4272, 4256, 4280, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3952, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3152, 3152, 3152, 3154, 3188, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4272, 4256, 4280, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3964, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3152, 3152, 3152, 3152, 3176, 0, 0, 0, 0, 3963, 3953, 3953, 3953, 3953, 3953, 3965, 0, 0, 0, 4272, 4256, 4280, 0, 0, 0, 3963, 3953, 3953, 3953, 3953, 3953, 3953, 3965, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 0, 0, 0, 0, 172, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 49, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 173, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 181, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 22, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 173, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 181, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 173, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 181, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12, 0, 173, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 181, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 152, 0, 0, 0, 0, 173, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 160, 0, 0, 0, 0, 181, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 256, 256, 256, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 173, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 256, 272, 272, 272, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 181, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 256, 256, 272, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 172, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 256, 256, 256, 272, 272, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 180, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 272, 272, 272, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 568, 569, 569, 569, 569, 570, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 75, 11, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 51, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 173, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 181, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 63, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 256, 257, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 172, 172, 172, 172, 172, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 256, 256, 274, 273, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 180, 180, 180, 180, 180, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 257, 256, 256, 257, 256, 272, 274, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 256, 256, 273, 274, 274, 273, 274, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 316, 316, 317, 280, 281, 282, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 52, 49, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 49, 49, 49, 49, 49, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 49, 49, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 49, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 81, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 90, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 173, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 173, 181, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 173, 181, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 181, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 282, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 282, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 173, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 181, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 301, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 296, 297, 298, 0, 0, 304, 305, 306, 1, 0, 0, 0, 178, 0, 59, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 301, 0, 0, 0, 0, 0, 0, 169, 0, 0, 0, 0, 0, 312, 313, 314, 0, 0, 0, 9, 0, 0, 0, 0, 0, 67, 0, 59, 0, 0, 0, 0, 0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 301, 0, 0, 0, 0, 0, 0, 177, 0, 0, 0, 0, 0, 0, 0, 38, 39, 0, 0, 0, 0, 0, 0, 67, 0, 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 301, 0, 0, 0, 0, 0, 0, 177, 0, 0, 0, 0, 38, 39, 0, 46, 47, 0, 17, 0, 0, 0, 0, 0, 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 311, 285, 0, 0, 0, 0, 0, 177, 0, 0, 0, 0, 46, 47, 0, 0, 0, 0, 0, 0, 0, 67, 0, 0, 67, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 287, 285, 0, 0, 0, 0, 177, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 0, 0, 37, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 65, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 287, 285, 0, 0, 171, 177, 172, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 178, 0, 0, 67, 45, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 287, 285, 0, 179, 180, 180, 171, 0, 6, 0, 0, 0, 0, 59, 37, 0, 67, 0, 67, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 59, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 293, 0, 0, 0, 0, 179, 0, 6, 0, 0, 0, 0, 67, 45, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 178, 0, 59, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 322, 0, 0, 0, 0, 0, 0, 9, 0, 0, 0, 0, 202, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 311, 285, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 210, 0, 0, 0, 37, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 293, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 218, 0, 0, 67, 45, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 59, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 301, 0, 0, 0, 0, 0, 208, 0, 0, 0, 210, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 67, 0, 59, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 301, 51, 0, 0, 0, 0, 216, 0, 0, 0, 37, 5, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 59, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 301, 0, 0, 0, 0, 0, 3, 0, 0, 218, 45, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 0, 59, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 301, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 59, 0, 59, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 322, 0, 0, 0, 0, 56, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 67, 0, 59, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 301, 0, 0, 0, 0, 0, 21, 0, 0, 155, 0, 0, 67, 37, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 59, 0, 68, 67, 0, 59, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 301, 0, 0, 0, 0, 0, 0, 0, 0, 163, 1, 0, 0, 45, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 178, 75, 76, 12, 0, 59, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 295, 308, 336, 309, 0, 0, 0, 0, 0, 0, 0, 0, 38, 39, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 59, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 295, 327, 316, 344, 317, 0, 0, 0, 0, 0, 0, 1, 0, 46, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 59, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 295, 308, 327, 317, 0, 1, 0, 0, 0, 0, 0, 171, 0, 0, 9, 0, 0, 0, 27, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 59, 173, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 336, 337, 339, 327, 316, 317, 11, 0, 0, 0, 50, 0, 0, 0, 179, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 59, 181, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 344, 345, 347, 317, 0, 0, 0, 0, 0, 0, 552, 553, 553, 553, 553, 554, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 18, 0, 178, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 560, 561, 561, 561, 561, 562, 563, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 18, 0, 178, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 283, 320, 284, 284, 285, 0, 0, 0, 568, 569, 569, 569, 569, 570, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 18, 0, 178, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 284, 284, 286, 352, 353, 354, 287, 285, 0, 0, 576, 577, 577, 577, 577, 578, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 8, 0, 0, 178, 68, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 350, 361, 351, 0, 287, 285, 1, 584, 585, 585, 585, 585, 586, 587, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 178, 76, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 287, 320, 285, 0, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 293, 0, 0, 0, 0, 0, 0, 0, 0, 0, 22, 0, 0, 0, 0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 178, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 322, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 301, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 301, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 311, 285, 0, 0, 0, 171, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 293, 0, 0, 0, 179, 171, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 59, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 301, 0, 0, 0, 0, 179, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 311, 285, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 203, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 351, 354, 335, 0, 0, 0, 0, 0, 0, 0, 0, 0, 516, 0, 0, 0, 518, 0, 0, 0, 0, 19, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 361, 362, 335, 0, 0, 0, 0, 0, 0, 0, 0, 0, 524, 525, 525, 525, 526, 0, 0, 0, 0, 0, 0, 59, 67, 0, 195, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 361, 362, 335, 0, 0, 0, 0, 0, 0, 174, 0, 0, 532, 533, 533, 533, 534, 0, 174, 0, 0, 0, 0, 59, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 359, 370, 335, 0, 0, 0, 0, 171, 0, 0, 0, 0, 532, 533, 533, 533, 534, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 295, 343, 0, 0, 0, 0, 179, 171, 172, 0, 0, 532, 533, 533, 533, 534, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 295, 308, 327, 64, 0, 0, 0, 0, 0, 179, 180, 171, 172, 532, 533, 533, 533, 534, 172, 172, 172, 0, 0, 0, 0, 203, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 295, 336, 327, 316, 317, 0, 50, 0, 0, 0, 0, 0, 0, 179, 180, 532, 533, 533, 533, 534, 180, 180, 180, 171, 172, 173, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 295, 337, 338, 339, 308, 327, 344, 317, 0, 0, 0, 0, 50, 0, 0, 0, 0, 0, 0, 0, 532, 533, 533, 533, 534, 0, 0, 0, 179, 180, 181, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 308, 308, 327, 345, 346, 347, 316, 317, 0, 0, 0, 0, 0, 0, 0, 50, 0, 0, 0, 0, 0, 0, 532, 533, 533, 533, 534, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 49, 49, 52, 49, 49, 49, 49, 50, 0, 0, 0, 0, 0, 0, 0, 2, 49, 50, 0, 0, 0, 0, 532, 533, 533, 533, 534, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 38, 39, 0, 0, 0, 0, 0, 33, 34, 49, 49, 50, 0, 0, 0, 0, 0, 49, 52, 49, 49, 540, 541, 541, 541, 542, 49, 52, 50, 38, 39, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 46, 47, 0, 0, 0, 0, 0, 178, 0, 0, 0, 0, 52, 50, 0, 0, 0, 0, 38, 39, 0, 0, 0, 0, 0, 0, 0, 0, 0, 46, 47, 38, 39, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 38, 39, 50, 0, 0, 0, 46, 47, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 46, 47, 49, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 46, 47, 0, 50, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 1, 0, 178, 107, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 51, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 38, 39, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 178, 0, 0, 0, 0, 0, 0, 0, 0, 38, 39, 0, 0, 38, 39, 0, 0, 0, 0, 0, 0, 0, 0, 0, 38, 39, 0, 0, 46, 47, 0, 51, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 170, 0, 98, 90, 67, 708, 709, 710, 711, 36, 0, 46, 47, 0, 0, 46, 47, 0, 0, 0, 0, 0, 0, 0, 7, 0, 46, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 36, 0, 716, 717, 0, 719, 36, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 7, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 59, 1, 0, 36, 0, 724, 725, 726, 727, 36, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 38, 39, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 59, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 38, 39, 47, 37, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 59, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 6, 0, 46, 47, 0, 45, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 59, 0, 1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 66, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 0, 38, 39, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 59, 4, 0, 38, 39, 0, 0, 0, 0, 0, 0, 0, 67, 0, 5, 0, 0, 0, 0, 0, 0, 0, 6, 0, 46, 47, 0, 0, 0, 0, 0, 0, 59, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 46, 47, 0, 0, 0, 0, 0, 0, 59, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 59, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 282, 0, 0, 0, 0, 0, 0, 0, 38, 39, 0, 0, 0, 59, 67, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 59, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 290, 3, 0, 50, 0, 0, 0, 0, 46, 47, 0, 0, 68, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 59, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 298, 0, 0, 0, 49, 50, 0, 4, 0, 0, 0, 75, 76, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 306, 0, 0, 0, 0, 0, 50, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 0, 0, 0, 0, 49, 50, 0, 59, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 59, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 59, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 59, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 59, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 178, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 9, 0, 0, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 281, 282, 0, 0, 0, 0, 0, 3, 0, 178, 0, 0, 0, 1, 0, 0, 640, 0, 0, 0, 0, 0, 0, 0, 640, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 289, 290, 0, 0, 0, 0, 0, 0, 0, 178, 0, 0, 0, 0, 0, 0, 648, 0, 0, 0, 0, 0, 0, 0, 648, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13, 12, 12, 12, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 15, 15, 15, 15, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 15, 15, 15, 15, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 3, 3, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 8, 8, 8, 8, 8, 249, 249, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 8, 8, 8, 8, 255, 255, 255, 255, 255, 8, 8, 8, 8, 8, 249, 249, 249, 249, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 255, 255, 255, 255, 255, 8, 8, 8, 8, 8, 0, 0, 0, 249, 249, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 255, 255, 255, 255, 255, 8, 8, 8, 8, 8, 0, 0, 0, 0, 249, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 255, 255, 255, 255, 255, 8, 8, 8, 8, 8, 0, 0, 0, 0, 249, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 255, 255, 255, 255, 255, 0, 8, 8, 8, 8, 0, 0, 0, 0, 249, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 118, 0, 0, 0, 0, 5, 5, 0, 0, 0, 0, 8, 8, 8, 8, 255, 255, 255, 255, 255, 0, 0, 8, 8, 8, 0, 0, 0, 0, 249, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 0, 0, 0, 0, 8, 8, 8, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 8, 8, 8, 8, 0, 249, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 0, 0, 0, 0, 0, 8, 8, 8, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 8, 8, 8, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 0, 0, 0, 0, 8, 8, 8, 8, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 8, 8, 8, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 8, 8, 8, 8, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 8, 8, 8, 8, 0, 0, 0, 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 8, 8, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 118, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 8, 8, 8, 8, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 8, 8, 8, 8, 0, 249, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 8, 8, 8, 8, 0, 249, 249, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 8, 8, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 8, 8, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 8, 8, 8, 8, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 8, 8, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 8, 8, 8, 8, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 0, 0, 0, 0, 15, 0, 15, 0, 0, 0, 8, 8, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 8, 8, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 118, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 8, 8, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 8, 8, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 8, 8, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 8, 8, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 8, 8, 8, 8, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 8, 8, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 8, 8, 8, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 8, 8, 8, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 8, 8, 8, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 8, 8, 8, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 8, 8, 8, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 8, 8, 8, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 8, 8, 8, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 5, 0, 8, 8, 8, 8, 8, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 5, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 0, 0, 0, 0, 0, 8, 8, 8, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 0, 0, 8, 8, 8, 8, 8, 8, 8, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 0, 0, 8, 8, 8, 8, 8, 8, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 8, 8, 8, 8, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 30, 30, 30, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 30, 30, 30, 5, 5, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 0, 0, 249, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 0, 0, 250, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 0, 0, 250, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 0, 0, 250, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 0, 0, 249, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 0, 0, 249, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 0, 0, 249, 249, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 0, 0, 249, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 1, 0, 0, 249, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 0, 0, 249, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 0, 0, 249, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 0, 0, 0, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 0, 0, 0, 0, 0, 5, 5, 5, 0, 0, 0, 15, 15, 5, 5, 15, 15, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 0, 0, 0, 0, 0, 0, 5, 5, 0, 0, 0, 15, 15, 15, 15, 15, 15, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 0, 118, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8], "events": [null, {"id": 1, "name": "EV001", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 51, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 9, "y": 66}, {"id": 2, "name": "EV002", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 67, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 8, "y": 50}, {"id": 3, "name": "EV003", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 0, "characterIndex": 3}, "list": [{"code": 356, "indent": 0, "parameters": ["PB_BGS_全停止"]}, {"code": 121, "indent": 0, "parameters": [3, 3, 0]}, {"code": 121, "indent": 0, "parameters": [14, 14, 1]}, {"code": 117, "indent": 0, "parameters": [79]}, {"code": 356, "indent": 0, "parameters": [">允许操作玩家移动 : 开启"]}, {"code": 117, "indent": 0, "parameters": [6]}, {"code": 117, "indent": 0, "parameters": [100]}, {"code": 117, "indent": 0, "parameters": [99]}, {"code": 355, "indent": 0, "parameters": ["let id = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.event(id).steupCEQJ(2)"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "$gameScreen.zoomScale() == 1"]}, {"code": 356, "indent": 1, "parameters": ["d<PERSON><PERSON><PERSON> 2 60 player"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["particle set splash_walk walk:player:8 def below"]}, {"code": 356, "indent": 0, "parameters": ["particle set dust_walk walk:player:0 def below"]}, {"code": 356, "indent": 0, "parameters": ["particle set grass_walk walk:player:5 def below"]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["雨天重置的创造物"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameSelfVariables.value([$gameMap.mapId(), 1, 'rainyCreation']) > 0"]}, {"code": 355, "indent": 1, "parameters": ["QJ.SE.spawnRegionNum(1,106,[15],2,true);"]}, {"code": 108, "indent": 1, "parameters": ["鱼"]}, {"code": 355, "indent": 1, "parameters": ["let num = 3 + Math.randomInt(6);"]}, {"code": 655, "indent": 1, "parameters": ["if ($gameVariables.value(60) == 2) {"]}, {"code": 655, "indent": 1, "parameters": ["    num *= 2;"]}, {"code": 655, "indent": 1, "parameters": ["    num  = Math.max(num, 10);"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 655, "indent": 1, "parameters": ["QJ.SE.spawnRegionNum(1,9,[8],num,true);"]}, {"code": 108, "indent": 1, "parameters": ["生成蔬菜"]}, {"code": 355, "indent": 1, "parameters": ["let type = \"vege\";"]}, {"code": 655, "indent": 1, "parameters": ["let times = 6;"]}, {"code": 655, "indent": 1, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.steupCEQJ(75,eid,{creationType:type,creationQuantity:times})"]}, {"code": 108, "indent": 1, "parameters": ["生成树"]}, {"code": 355, "indent": 1, "parameters": ["let type = \"tree\";"]}, {"code": 655, "indent": 1, "parameters": ["let times = 18;"]}, {"code": 655, "indent": 1, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.steupCEQJ(75,eid,{creationType:type,creationQuantity:times})"]}, {"code": 108, "indent": 1, "parameters": ["生成采集物"]}, {"code": 355, "indent": 1, "parameters": ["$gameSelfSwitches.setValue([$gameMap.mapId(), 12, 'A'], false);"]}, {"code": 108, "indent": 1, "parameters": ["清除标记"]}, {"code": 355, "indent": 1, "parameters": ["$gameSelfVariables.setValue([$gameMap.mapId(), 1, 'rainyCreation'], 0);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["日常重置的创造物"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameSelfVariables.value([$gameMap.mapId(), 1, 'day']) < $gameSystem.day()"]}, {"code": 108, "indent": 1, "parameters": ["更新标记天数"]}, {"code": 355, "indent": 1, "parameters": ["let day = $gameSystem.day();"]}, {"code": 655, "indent": 1, "parameters": ["$gameSelfVariables.setValue([$gameMap.mapId(), 1, 'day'], day)"]}, {"code": 108, "indent": 1, "parameters": ["敌人"]}, {"code": 111, "indent": 1, "parameters": [12, "Math.random() > 0.5"]}, {"code": 355, "indent": 2, "parameters": ["let num = 4 + Math.randomInt(5);"]}, {"code": 655, "indent": 2, "parameters": ["QJ.SE.spawnRegionNum(1,87,[0],num,true);"]}, {"code": 655, "indent": 2, "parameters": ["//草食史莱姆"]}, {"code": 355, "indent": 2, "parameters": ["let num = 0 + Math.randomInt(2);"]}, {"code": 655, "indent": 2, "parameters": ["if (num > 0) {"]}, {"code": 655, "indent": 2, "parameters": ["QJ.SE.spawnRegionNum(1,90,[0,8],num,true);"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 655, "indent": 2, "parameters": ["//急冻史莱姆"]}, {"code": 355, "indent": 2, "parameters": ["let num = 0 + Math.randomInt(2);"]}, {"code": 655, "indent": 2, "parameters": ["if (num > 0) {"]}, {"code": 655, "indent": 2, "parameters": ["QJ.SE.spawnRegionNum(1,91,[0,8],num,true);"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 655, "indent": 2, "parameters": ["//哔哩哔哩史莱姆"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["let num = Math.randomInt(4) + 2; "]}, {"code": 655, "indent": 2, "parameters": ["for (let i = 0; i < num; i++) {"]}, {"code": 655, "indent": 2, "parameters": ["let type = 66 + Math.randomInt(4);"]}, {"code": 655, "indent": 2, "parameters": ["    QJ.SE.spawnRegionNum(1,type,[0],1,true);"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["let num = 1 + Math.randomInt(3);"]}, {"code": 655, "indent": 1, "parameters": ["QJ.SE.spawnRegionNum(1,70,[0],num,true);"]}, {"code": 655, "indent": 1, "parameters": ["//香蕉猫"]}, {"code": 355, "indent": 1, "parameters": ["let num = 1 + Math.randomInt(3);"]}, {"code": 655, "indent": 1, "parameters": ["QJ.SE.spawnRegionNum(1,71,[0],num,true);"]}, {"code": 655, "indent": 1, "parameters": ["//苹果猫"]}, {"code": 355, "indent": 1, "parameters": ["let num = 0 + Math.randomInt(4);"]}, {"code": 655, "indent": 1, "parameters": ["if (num > 0) {"]}, {"code": 655, "indent": 1, "parameters": ["QJ.SE.spawnRegionNum(1,113,[0],num,true);"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 655, "indent": 1, "parameters": ["//角兔"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$gameMap.drill_COET_getEventsByTag_direct(\"需监听\").length > 0"]}, {"code": 355, "indent": 2, "parameters": ["let monster = $gameMap.drill_COET_getEventsByTag_direct(\"需监听\");  "]}, {"code": 655, "indent": 2, "parameters": [" monster.forEach(function(target) {"]}, {"code": 655, "indent": 2, "parameters": ["let EID = target._eventId;"]}, {"code": 655, "indent": 2, "parameters": ["QJ.MPMZ.tl.ex_enemyAutomaticHitReaction(EID)"]}, {"code": 655, "indent": 2, "parameters": ["    });"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 4, "y": 32}, {"id": 4, "name": "EV004", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<width: 1>"]}, {"code": 408, "indent": 0, "parameters": ["<height: 5>"]}, {"code": 408, "indent": 0, "parameters": ["<offsetX: 0>"]}, {"code": 408, "indent": 0, "parameters": ["<offsetY: 0>"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Move", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 121, "indent": 0, "parameters": [40, 40, 0]}, {"code": 201, "indent": 0, "parameters": [0, 49, 46, 10, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 2, "walkAnime": true}], "x": 0, "y": 24}, {"id": 5, "name": "EV005", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 6, "pattern": 1, "characterIndex": 5}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": false}], "x": 0, "y": 25}, {"id": 6, "name": "从遗迹之森坠落", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[8]"]}, {"code": 356, "indent": 0, "parameters": [">对话框皮肤 : 只对话框 : 修改样式 : 样式[9]"]}, {"code": 355, "indent": 0, "parameters": ["let event = $gamePlayer;"]}, {"code": 655, "indent": 0, "parameters": ["let x = event.screenX() * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 0, "parameters": ["let y = (event.screenY() - 100) * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['x_value'] = x ;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['y_value'] = y ;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['height_rowCount'] = 2;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['height_value'] = 64 * 2;"]}, {"code": 250, "indent": 0, "parameters": [{"name": "水面に浮かび上がる", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": [">行走图动画序列 : 玩家 : 播放简单状态元集合 : 集合[虚弱]"]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 213, "indent": 0, "parameters": [-1, 6, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["…………"]}, {"code": 401, "indent": 0, "parameters": ["…………"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["唔啊啊…好危险……"]}, {"code": 401, "indent": 0, "parameters": ["不小心从河边摔下来、差点淹死了——"]}, {"code": 356, "indent": 0, "parameters": [">行走图动画序列 : 玩家 : 全标签播放 : 开启"]}, {"code": 356, "indent": 0, "parameters": [">独立开关 : 事件[7] : A : 开启"]}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 37, "y": 35}, {"id": 7, "name": "橡胶鸭", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "$rubberDuck", "direction": 6, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[12,12]"]}, {"code": 108, "indent": 0, "parameters": ["=>持续动作 : 空中飘浮 : 飘浮高度[12] : 周期[120] : 幅度[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[8]"]}, {"code": 356, "indent": 0, "parameters": [">对话框皮肤 : 只对话框 : 修改样式 : 样式[9]"]}, {"code": 355, "indent": 0, "parameters": ["let event = $gamePlayer;"]}, {"code": 655, "indent": 0, "parameters": ["let x = event.screenX() * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 0, "parameters": ["let y = (event.screenY() - 100) * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['x_value'] = x ;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['y_value'] = y ;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['height_rowCount'] = 2;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['height_value'] = 64 * 2;"]}, {"code": 108, "indent": 0, "parameters": ["选项框适配"]}, {"code": 355, "indent": 0, "parameters": ["$gameSystem._drill_DCB_enable = true;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DCB_curStyle = 30;"]}, {"code": 655, "indent": 0, "parameters": ["DrillUp.g_DCB_data[29].x = $gamePlayer.screenX() + 64;"]}, {"code": 655, "indent": 0, "parameters": ["DrillUp.g_DCB_data[29].x *= $gameScreen.zoomScale();"]}, {"code": 655, "indent": 0, "parameters": ["DrillUp.g_DCB_data[29].y = $gamePlayer.screenY() - 24;"]}, {"code": 655, "indent": 0, "parameters": ["DrillUp.g_DCB_data[29].y *= $gameScreen.zoomScale();"]}, {"code": 108, "indent": 0, "parameters": ["选项赋予名称"]}, {"code": 355, "indent": 0, "parameters": ["let eid = \"rubberDuck\";"]}, {"code": 655, "indent": 0, "parameters": ["let index = 1;"]}, {"code": 655, "indent": 0, "parameters": ["let option1 = window.mapCommonEventDialogue[eid][String(index)][0];"]}, {"code": 655, "indent": 0, "parameters": ["let option2 = window.mapCommonEventDialogue[eid][String(index)][1];"]}, {"code": 655, "indent": 0, "parameters": [" $gameStrings.setValue(6,option1);"]}, {"code": 655, "indent": 0, "parameters": [" $gameStrings.setValue(7,option2);"]}, {"code": 355, "indent": 0, "parameters": ["let type = \"rubberDuck\";"]}, {"code": 655, "indent": 0, "parameters": ["let index = 0;"]}, {"code": 655, "indent": 0, "parameters": ["this.showCommonEventDialogue(type,String(index))"]}, {"code": 102, "indent": 0, "parameters": [["\\str[6]", "\\str[7]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\str[6]"]}, {"code": 355, "indent": 1, "parameters": ["let type = \"rubberDuck\";"]}, {"code": 655, "indent": 1, "parameters": ["let index = 2;"]}, {"code": 655, "indent": 1, "parameters": ["this.showCommonEventDialogue(type,String(index))"]}, {"code": 111, "indent": 1, "parameters": [2, "A", 0]}, {"code": 355, "indent": 2, "parameters": ["let type = \"rubberDuck\";"]}, {"code": 655, "indent": 2, "parameters": ["let index = 3;"]}, {"code": 655, "indent": 2, "parameters": ["this.showCommonEventDialogue(type,String(index))"]}, {"code": 108, "indent": 2, "parameters": ["为浴室事件进行标记"]}, {"code": 355, "indent": 2, "parameters": ["var mapId = 6; "]}, {"code": 655, "indent": 2, "parameters": ["var eventId = 1; "]}, {"code": 655, "indent": 2, "parameters": ["var switchKey = [mapId, eventId, 'A']; "]}, {"code": 655, "indent": 2, "parameters": ["$gameSelfSwitches.setValue(switchKey, true);"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "水面に浮かび上がる", "volume": 80, "pitch": 90, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 355, "indent": 1, "parameters": ["var seNames = [\"間違った答えのゴムのきしみ\", \"びっくりチキン小 速い\", \"コミカルラバーキーキー\"];"]}, {"code": 655, "indent": 1, "parameters": ["var randomSeName = seNames[Math.floor(Math.random() * seNames.length)];"]}, {"code": 655, "indent": 1, "parameters": ["var randomPitch = Math.randomInt(40) + 81;"]}, {"code": 655, "indent": 1, "parameters": ["var se = { name: randomSeName, volume: 70, pitch: randomPitch, pan: 0 };"]}, {"code": 655, "indent": 1, "parameters": ["AudioManager.playSe(se);"]}, {"code": 126, "indent": 1, "parameters": [18, 0, 0, 1]}, {"code": 205, "indent": 1, "parameters": [0, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 355, "indent": 1, "parameters": ["let type = \"rubberDuck\";"]}, {"code": 655, "indent": 1, "parameters": ["let index = 4;"]}, {"code": 655, "indent": 1, "parameters": ["this.showCommonEventDialogue(type,String(index))"]}, {"code": 123, "indent": 1, "parameters": ["D", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\str[7]"]}, {"code": 355, "indent": 1, "parameters": ["let type = \"rubberDuck\";"]}, {"code": 655, "indent": 1, "parameters": ["let index = 5;"]}, {"code": 655, "indent": 1, "parameters": ["this.showCommonEventDialogue(type,String(index))"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 36, "y": 31}, {"id": 8, "name": "EV008", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 51, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 8, "y": 47}, {"id": 9, "name": "街灯", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!fsm_Flame04", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": false}], "x": 17, "y": 23}, {"id": 10, "name": "街灯", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!fsm_Flame04", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": false}], "x": 9, "y": 23}, {"id": 11, "name": "街灯", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!fsm_Flame04", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": false}], "x": 9, "y": 27}, {"id": 12, "name": "采集点", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "キラーン（ひらめき・アイテム発見・獲得）", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 127, "indent": 0, "parameters": [6, 0, 0, 1, false]}, {"code": 355, "indent": 0, "parameters": ["dingk.Loot.directlyAcquireDrops(21,false)"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 9, "y": 53}, {"id": 13, "name": "街灯", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!fsm_Flame04", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": false}], "x": 21, "y": 36}, {"id": 14, "name": "街灯", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!fsm_Flame04", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": false}], "x": 27, "y": 36}, {"id": 15, "name": "EV015", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<width: 7>"]}, {"code": 408, "indent": 0, "parameters": ["<height: 1>"]}, {"code": 408, "indent": 0, "parameters": ["<offsetX: 0>"]}, {"code": 408, "indent": 0, "parameters": ["<offsetY: 0>"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameMap.getGroupBulletListQJ('systemWarn').length > 0"]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "Magic1", "volume": 65, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 1, "parameters": ["    QJ.MPMZ.Shoot({"]}, {"code": 655, "indent": 1, "parameters": ["        img:\"systemWarn[4,8]\","]}, {"code": 655, "indent": 1, "parameters": ["        position:[['Map',18],['Map',70]],"]}, {"code": 655, "indent": 1, "parameters": ["        initialRotation:['S',0],imgRotation:['F'],"]}, {"code": 655, "indent": 1, "parameters": ["        moveType:['S',0],groupName:['systemWarn'],"]}, {"code": 655, "indent": 1, "parameters": ["        opacity:'0|0~20/1~999999|1',"]}, {"code": 655, "indent": 1, "parameters": ["        existData:["]}, {"code": 655, "indent": 1, "parameters": ["          {t:['Time',180],d:[1,30,0]}"]}, {"code": 655, "indent": 1, "parameters": ["        ],"]}, {"code": 655, "indent": 1, "parameters": ["        z:\"W\""]}, {"code": 655, "indent": 1, "parameters": ["    });"]}, {"code": 355, "indent": 1, "parameters": ["    QJ.MPMZ.Shoot({"]}, {"code": 655, "indent": 1, "parameters": ["        img:\"systemWarn[4,8]\","]}, {"code": 655, "indent": 1, "parameters": ["        position:[['Map',20],['Map',70]],"]}, {"code": 655, "indent": 1, "parameters": ["        initialRotation:['S',0],imgRotation:['F'],"]}, {"code": 655, "indent": 1, "parameters": ["        moveType:['S',0],groupName:['systemWarn'],"]}, {"code": 655, "indent": 1, "parameters": ["        opacity:'0|0~20/1~999999|1',"]}, {"code": 655, "indent": 1, "parameters": ["        existData:["]}, {"code": 655, "indent": 1, "parameters": ["          {t:['Time',180],d:[1,30,0]}"]}, {"code": 655, "indent": 1, "parameters": ["        ],"]}, {"code": 655, "indent": 1, "parameters": ["        z:\"W\""]}, {"code": 655, "indent": 1, "parameters": ["    });"]}, {"code": 355, "indent": 1, "parameters": ["    QJ.MPMZ.Shoot({"]}, {"code": 655, "indent": 1, "parameters": ["        img:\"systemWarn[4,8]\","]}, {"code": 655, "indent": 1, "parameters": ["        position:[['Map',22],['Map',70]],"]}, {"code": 655, "indent": 1, "parameters": ["        initialRotation:['S',0],imgRotation:['F'],"]}, {"code": 655, "indent": 1, "parameters": ["        moveType:['S',0],groupName:['systemWarn'],"]}, {"code": 655, "indent": 1, "parameters": ["        opacity:'0|0~20/1~999999|1',"]}, {"code": 655, "indent": 1, "parameters": ["        existData:["]}, {"code": 655, "indent": 1, "parameters": ["          {t:['Time',180],d:[1,30,0]}"]}, {"code": 655, "indent": 1, "parameters": ["        ],"]}, {"code": 655, "indent": 1, "parameters": ["        z:\"W\""]}, {"code": 655, "indent": 1, "parameters": ["    });"]}, {"code": 230, "indent": 1, "parameters": [4]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 17, "y": 70}, {"id": 16, "name": "街灯", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!fsm_Flame04", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": false}], "x": 32, "y": 11}, {"id": 17, "name": "街灯", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!fsm_Flame04", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": false}], "x": 27, "y": 8}, {"id": 18, "name": "街灯", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!fsm_Flame04", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": false}], "x": 32, "y": 2}, {"id": 19, "name": "街灯", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!fsm_Flame04", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": false}], "x": 17, "y": 27}, {"id": 20, "name": "街灯", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!fsm_Flame04", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": false}], "x": 38, "y": 2}, {"id": 21, "name": "街灯", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!fsm_Flame04", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": false}], "x": 27, "y": 47}, {"id": 22, "name": "街灯", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!fsm_Flame04", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": false}], "x": 21, "y": 47}, {"id": 23, "name": "街灯", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!fsm_Flame04", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": false}], "x": 18, "y": 61}, {"id": 24, "name": "街灯", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!fsm_Flame04", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": false}], "x": 24, "y": 65}, {"id": 25, "name": "街灯", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!fsm_Flame04", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": false}], "x": 12, "y": 56}, {"id": 26, "name": "街灯", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!fsm_Flame04", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": false}], "x": 6, "y": 56}, {"id": 27, "name": "EV027", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<width: 8>"]}, {"code": 408, "indent": 0, "parameters": ["<height: 1>"]}, {"code": 408, "indent": 0, "parameters": ["<offsetX: 0>"]}, {"code": 408, "indent": 0, "parameters": ["<offsetY: 0>"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameMap.getGroupBulletListQJ('systemWarn').length > 0"]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "Magic1", "volume": 65, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 1, "parameters": ["    QJ.MPMZ.Shoot({"]}, {"code": 655, "indent": 1, "parameters": ["        img:\"systemWarn[4,8]\","]}, {"code": 655, "indent": 1, "parameters": ["        position:[['Map',32],['Map',0]],"]}, {"code": 655, "indent": 1, "parameters": ["        initialRotation:['S',0],imgRotation:['F'],"]}, {"code": 655, "indent": 1, "parameters": ["        moveType:['S',0],groupName:['systemWarn'],"]}, {"code": 655, "indent": 1, "parameters": ["        opacity:'0|0~20/1~999999|1',"]}, {"code": 655, "indent": 1, "parameters": ["        existData:["]}, {"code": 655, "indent": 1, "parameters": ["          {t:['Time',180],d:[1,30,0]}"]}, {"code": 655, "indent": 1, "parameters": ["        ],"]}, {"code": 655, "indent": 1, "parameters": ["        z:\"E\""]}, {"code": 655, "indent": 1, "parameters": ["    });"]}, {"code": 355, "indent": 1, "parameters": ["    QJ.MPMZ.Shoot({"]}, {"code": 655, "indent": 1, "parameters": ["        img:\"systemWarn[4,8]\","]}, {"code": 655, "indent": 1, "parameters": ["        position:[['Map',34],['Map',0]],"]}, {"code": 655, "indent": 1, "parameters": ["        initialRotation:['S',0],imgRotation:['F'],"]}, {"code": 655, "indent": 1, "parameters": ["        moveType:['S',0],groupName:['systemWarn'],"]}, {"code": 655, "indent": 1, "parameters": ["        opacity:'0|0~20/1~999999|1',"]}, {"code": 655, "indent": 1, "parameters": ["        existData:["]}, {"code": 655, "indent": 1, "parameters": ["          {t:['Time',180],d:[1,30,0]}"]}, {"code": 655, "indent": 1, "parameters": ["        ],"]}, {"code": 655, "indent": 1, "parameters": ["        z:\"E\""]}, {"code": 655, "indent": 1, "parameters": ["    });"]}, {"code": 355, "indent": 1, "parameters": ["    QJ.MPMZ.Shoot({"]}, {"code": 655, "indent": 1, "parameters": ["        img:\"systemWarn[4,8]\","]}, {"code": 655, "indent": 1, "parameters": ["        position:[['Map',36],['Map',0]],"]}, {"code": 655, "indent": 1, "parameters": ["        initialRotation:['S',0],imgRotation:['F'],"]}, {"code": 655, "indent": 1, "parameters": ["        moveType:['S',0],groupName:['systemWarn'],"]}, {"code": 655, "indent": 1, "parameters": ["        opacity:'0|0~20/1~999999|1',"]}, {"code": 655, "indent": 1, "parameters": ["        existData:["]}, {"code": 655, "indent": 1, "parameters": ["          {t:['Time',180],d:[1,30,0]}"]}, {"code": 655, "indent": 1, "parameters": ["        ],"]}, {"code": 655, "indent": 1, "parameters": ["        z:\"E\""]}, {"code": 655, "indent": 1, "parameters": ["    });"]}, {"code": 355, "indent": 1, "parameters": ["    QJ.MPMZ.Shoot({"]}, {"code": 655, "indent": 1, "parameters": ["        img:\"systemWarn[4,8]\","]}, {"code": 655, "indent": 1, "parameters": ["        position:[['Map',38],['Map',0]],"]}, {"code": 655, "indent": 1, "parameters": ["        initialRotation:['S',0],imgRotation:['F'],"]}, {"code": 655, "indent": 1, "parameters": ["        moveType:['S',0],groupName:['systemWarn'],"]}, {"code": 655, "indent": 1, "parameters": ["        opacity:'0|0~20/1~999999|1',"]}, {"code": 655, "indent": 1, "parameters": ["        existData:["]}, {"code": 655, "indent": 1, "parameters": ["          {t:['Time',180],d:[1,30,0]}"]}, {"code": 655, "indent": 1, "parameters": ["        ],"]}, {"code": 655, "indent": 1, "parameters": ["        z:\"E\""]}, {"code": 655, "indent": 1, "parameters": ["    });"]}, {"code": 230, "indent": 1, "parameters": [4]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 31, "y": 0}, {"id": 28, "name": "EV028", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 717, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 9, "y": 54}, {"id": 29, "name": "EV029", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 718, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 10, "y": 54}, {"id": 30, "name": "EV030", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 716, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 8, "y": 54}, {"id": 31, "name": "EV031", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 231, "indent": 0, "parameters": [9, "black", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 232, "indent": 0, "parameters": [9, 0, 0, 0, 0, 0, 100, 100, 100, 0, 30, false]}, {"code": 355, "indent": 0, "parameters": ["QJ.MPMZ.tl.displayOniichanDefeatCount()"]}, {"code": 230, "indent": 0, "parameters": [240]}, {"code": 232, "indent": 0, "parameters": [9, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, true]}, {"code": 235, "indent": 0, "parameters": [9]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 9, "y": 55}, {"id": 32, "name": "EV032", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<width: 2>"]}, {"code": 408, "indent": 0, "parameters": ["<height: 1>"]}, {"code": 408, "indent": 0, "parameters": ["<offsetX: 0>"]}, {"code": 408, "indent": 0, "parameters": ["<offsetY: 0>"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Move", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 121, "indent": 0, "parameters": [40, 40, 0]}, {"code": 201, "indent": 0, "parameters": [0, 47, 72, 33, 8, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 2, "walkAnime": true}], "x": 10, "y": 0}, null, null, null]}