[null, {"id": 1, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 2, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 3, "atypeId": 1, "description": "", "etypeId": 2, "traits": [], "iconIndex": 530, "name": "奇怪的发饰", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 4, "atypeId": 1, "description": "", "etypeId": 2, "traits": [], "iconIndex": 530, "name": "逆流者的证明", "note": "<颜色:14>\n\n", "params": [0, 0, 0, 0, 0, 0, 0, 50], "price": 0}, {"id": 5, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 6, "atypeId": 1, "description": "", "etypeId": 2, "traits": [{"code": 23, "dataId": 3, "value": 1.25}, {"code": 21, "dataId": 0, "value": 1.1}, {"code": 22, "dataId": 7, "value": 0.01}], "iconIndex": 576, "name": "不幸的诞生", "note": "<颜色:14>\n", "params": [35, 0, 0, 0, 0, 0, 0, 0], "price": 4000}, {"id": 7, "atypeId": 1, "description": "", "etypeId": 2, "traits": [{"code": 23, "dataId": 3, "value": 1.2}, {"code": 21, "dataId": 0, "value": 1.02}], "iconIndex": 560, "name": "受诅胎体", "note": "<Loot Pool: Uncommon>\n<颜色:16>\n\n", "params": [25, 0, 0, 0, 0, 0, 0, 0], "price": 400}, {"id": 8, "atypeId": 1, "description": "", "etypeId": 2, "traits": [{"code": 21, "dataId": 4, "value": 1.04}, {"code": 22, "dataId": 4, "value": 0.1}], "iconIndex": 606, "name": "魔力晶簇", "note": "<Loot Pool: Uncommon>\n<颜色:16>\n\n", "params": [0, 0, 0, 0, 20, 10, 0, 0], "price": 2000}, {"id": 9, "atypeId": 1, "description": "", "etypeId": 2, "traits": [{"code": 21, "dataId": 4, "value": 1.1}, {"code": 22, "dataId": 4, "value": 0.15}], "iconIndex": 622, "name": "深邃魔力晶簇", "note": "<颜色:14>\n\n", "params": [0, 0, 0, 0, 35, 20, 0, 0], "price": 6000}, {"id": 10, "atypeId": 1, "description": "", "etypeId": 2, "traits": [{"code": 21, "dataId": 4, "value": 1.5}, {"code": 23, "dataId": 7, "value": 1.5}, {"code": 14, "dataId": 11, "value": 1}], "iconIndex": 539, "name": "精神凝聚头环", "note": "<Loot Pool: Epic>\n<颜色:14>\n", "params": [0, 0, 0, 0, 1, 20, 0, 0], "price": 10000}, {"id": 11, "atypeId": 1, "description": "", "etypeId": 2, "traits": [{"code": 21, "dataId": 4, "value": 1.1}, {"code": 43, "dataId": 45, "value": 1}], "iconIndex": 571, "name": "魔力汲取手套", "note": "<Loot Pool: Epic>\n<颜色:14>\n\n", "params": [0, 0, 0, 1, 10, 20, 0, 0], "price": 10000}, {"id": 12, "atypeId": 1, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 6, "value": 0.1}, {"code": 21, "dataId": 2, "value": 1.05}], "iconIndex": 647, "name": "借来的猫爪", "note": "<Loot Pool: Uncommon>\n<颜色:16>\n", "params": [0, 0, 5, 0, 0, 0, 0, 0], "price": 600}, {"id": 13, "atypeId": 1, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 6, "value": 0.1}, {"code": 22, "dataId": 1, "value": 0.15}, {"code": 43, "dataId": 28, "value": 1}], "iconIndex": 663, "name": "借来的猫足", "note": "<Loot Pool: Uncommon>\n<颜色:16>\n\n<Custom On Equip Eval>\nuser.gainSkillMasteryLevel(28, 1);\nuser.gainSkillMasteryLevel(39, 3);\n</Custom On Equip Eval>\n\n<Custom On Remove Equip Eval>\nuser.gainSkillMasteryLevel(28, -1);\nuser.gainSkillMasteryLevel(39, -3);\n</Custom On Remove Equip Eval>", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 600}, {"id": 14, "atypeId": 1, "description": "", "etypeId": 2, "traits": [{"code": 21, "dataId": 0, "value": 1}], "iconIndex": 204, "name": "芳草心", "note": "<Loot Pool: Uncommon>\n<Loot Pool: Common>\n<颜色:16>\n\n<Custom On Equip Eval>\nuser.gainSkillMasteryLevel(35, 1);\nlet value = user.skillMasteryLevel(35);\nvalue *= 15;\nuser.addMaxHp(value);\n</Custom On Equip Eval>\n\n<Custom On Remove Equip Eval>\nlet value = user.skillMasteryLevel(35);\nvalue *= 15;\nuser.addMaxHp(-value);\nuser.gainSkillMasteryLevel(35, -1);\n</Custom On Remove Equip Eval>", "params": [0, 0, 0, 1, 0, 10, 0, 5], "price": 300}, {"id": 15, "atypeId": 1, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 631, "name": "猫猫玩偶服", "note": "<Loot Pool: Uncommon>\n<Loot Pool: Common>\n<颜色:16>\n\n<Custom On Equip Eval>\nuser._weaponAmountBonus += 10;\nuser._armorAmountBonus += 20;\n</Custom On Equip Eval>\n\n<Custom On Remove Equip Eval>\nuser._weaponAmountBonus -= 10;\nuser._armorAmountBonus -= 20;\n</Custom On Remove Equip Eval>", "params": [0, 0, 0, 3, 0, 25, 0, 0], "price": 500}, {"id": 16, "atypeId": 1, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": -0.05}, {"code": 43, "dataId": 89, "value": 1}], "iconIndex": 616, "name": "穿刺钉盔", "note": "<Loot Pool: Uncommon>\n<Loot Pool: Rare>\n<颜色:16>\n\n<Custom On Equip Eval>\nuser.gainSkillMasteryLevel(89, 2);\n</Custom On Equip Eval>\n\n<Custom On Remove Equip Eval>\nuser.gainSkillMasteryLevel(89, -2);\n</Custom On Remove Equip Eval>", "params": [0, 0, 4, 2, 0, 0, 0, 0], "price": 3000}, {"id": 17, "atypeId": 1, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": -0.15}, {"code": 43, "dataId": 89, "value": 1}], "iconIndex": 632, "name": "穿刺钉甲", "note": "<Loot Pool: Uncommon>\n<Loot Pool: Rare>\n<颜色:16>\n\n<Custom On Equip Eval>\nuser.gainSkillMasteryLevel(89, 3);\n</Custom On Equip Eval>\n\n<Custom On Remove Equip Eval>\nuser.gainSkillMasteryLevel(89, -3);\n</Custom On Remove Equip Eval>", "params": [0, 0, 2, 6, 0, 0, 0, 0], "price": 3000}, {"id": 18, "atypeId": 1, "description": "", "etypeId": 2, "traits": [{"code": 43, "dataId": 90, "value": 1}], "iconIndex": 648, "name": "穿刺臂铠", "note": "<Loot Pool: Uncommon>\n<Loot Pool: Rare>\n<颜色:16>\n\n<Custom On Equip Eval>\nuser.gainSkillMasteryLevel(90, 5);\n</Custom On Equip Eval>\n\n<Custom On Remove Equip Eval>\nuser.gainSkillMasteryLevel(90, -5);\n</Custom On Remove Equip Eval>", "params": [0, 0, 5, 1, 0, 0, 0, 0], "price": 3000}, {"id": 19, "atypeId": 1, "description": "", "etypeId": 2, "traits": [{"code": 43, "dataId": 91, "value": 1}], "iconIndex": 664, "name": "穿刺钉鞋", "note": "<Loot Pool: Uncommon>\n<Loot Pool: Rare>\n<颜色:16>\n\n<Custom On Equip Eval>\nuser.gainSkillMasteryLevel(91, 6);\n</Custom On Equip Eval>\n\n<Custom On Remove Equip Eval>\nuser.gainSkillMasteryLevel(91, -6);\n</Custom On Remove Equip Eval>", "params": [0, 0, 2, 2, 0, 0, 0, 0], "price": 3000}, {"id": 20, "atypeId": 1, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": -0.05}], "iconIndex": 617, "name": "骑士头盔", "note": "<Loot Pool: Uncommon>\n<Loot Pool: Rare>\n<颜色:16>\n\n<Custom On Equip Eval>\nuser.gainSkillMasteryLevel(117, 1);\n</Custom On Equip Eval>\n\n<Custom On Remove Equip Eval>\nuser.gainSkillMasteryLevel(117, -1);\n</Custom On Remove Equip Eval>\n", "params": [0, 0, 0, 5, 0, 2, 0, 0], "price": 2000}, {"id": 21, "atypeId": 1, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": -0.15}], "iconIndex": 633, "name": "骑士铠甲", "note": "<Loot Pool: Uncommon>\n<Loot Pool: Rare>\n<颜色:16>\n\n<Custom On Equip Eval>\nuser.gainSkillMasteryLevel(118, 1);\n</Custom On Equip Eval>\n\n<Custom On Remove Equip Eval>\nuser.gainSkillMasteryLevel(118, -1);\n</Custom On Remove Equip Eval>\n", "params": [0, 0, 0, 10, 0, 4, 0, 0], "price": 2000}, {"id": 22, "atypeId": 1, "description": "", "etypeId": 2, "traits": [], "iconIndex": 649, "name": "骑士护臂", "note": "<Loot Pool: Uncommon>\n<Loot Pool: Rare>\n<颜色:16>\n\n<Custom On Equip Eval>\nuser.gainSkillMasteryLevel(120, 1);\n</Custom On Equip Eval>\n\n<Custom On Remove Equip Eval>\nuser.gainSkillMasteryLevel(120, -1);\n</Custom On Remove Equip Eval>\n", "params": [0, 0, 2, 3, 0, 0, 0, 0], "price": 2000}, {"id": 23, "atypeId": 1, "description": "", "etypeId": 2, "traits": [], "iconIndex": 665, "name": "骑士胫甲", "note": "<Loot Pool: Uncommon>\n<Loot Pool: Rare>\n<颜色:16>\n\n<Custom On Equip Eval>\nuser.gainSkillMasteryLevel(119, 1);\n</Custom On Equip Eval>\n\n<Custom On Remove Equip Eval>\nuser.gainSkillMasteryLevel(119, -1);\n</Custom On Remove Equip Eval>\n", "params": [0, 0, 0, 2, 0, 2, 0, 0], "price": 2000}, {"id": 24, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 25, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 26, "atypeId": 1, "description": "", "etypeId": 2, "traits": [{"code": 21, "dataId": 0, "value": 1.04}], "iconIndex": 282, "name": "火鸟的羽毛", "note": "<Loot Pool: Rare>\n<颜色:14>\n\n<Custom On Equip Eval>\n$gameVariables.setValue(133, $gameVariables.value(133) + 1);\n</Custom On Equip Eval>\n\n<Custom On Remove Equip Eval>\n$gameVariables.setValue(133, $gameVariables.value(133) - 1);\n</Custom On Remove Equip Eval>", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 27, "atypeId": 1, "description": "", "etypeId": 2, "traits": [{"code": 43, "dataId": 53, "value": 1}], "iconIndex": 668, "name": "厕纸", "note": "<Loot Pool: Common>\n\n<Custom On Equip Eval>\nuser.gainSkillMasteryLevel(53, 1);\n</Custom On Equip Eval>\n\n<Custom On Remove Equip Eval>\nuser.gainSkillMasteryLevel(53, -1)\n</Custom On Remove Equip Eval>", "params": [0, 0, 0, 1, 0, 0, 0, 0], "price": 0}, {"id": 28, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 29, "atypeId": 1, "description": "", "etypeId": 2, "traits": [{"code": 43, "dataId": 37, "value": 1}], "iconIndex": 542, "name": "调皮的杰克", "note": "<Loot Pool: Epic>\n<Loot Pool: Rare>\n<颜色:15>\n\n<Custom On Equip Eval>\nuser.gainSkillMasteryLevel(37, 1);\n</Custom On Equip Eval>\n\n<Custom On Remove Equip Eval>\nuser.gainSkillMasteryLevel(37, -1)\n</Custom On Remove Equip Eval>", "params": [0, 0, 0, 0, 8, 8, 0, 0], "price": 3000}, {"id": 30, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 31, "atypeId": 1, "description": "", "etypeId": 2, "traits": [{"code": 43, "dataId": 36, "value": 1}], "iconIndex": 507, "name": "夜雾幻影", "note": "<颜色:15>\n\n<Custom On Equip Eval>\nuser.gainSkillMasteryLevel(36, 1);\n</Custom On Equip Eval>\n\n<Custom On Remove Equip Eval>\nuser.gainSkillMasteryLevel(36, -1)\n</Custom On Remove Equip Eval>\n\n", "params": [0, 0, 0, 2, 0, 2, 0, -5], "price": 3000}, {"id": 32, "atypeId": 1, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}, {"code": 22, "dataId": 6, "value": 0.15}, {"code": 43, "dataId": 99, "value": 1}], "iconIndex": 643, "name": "猫爪手套", "note": "<Loot Pool: Epic>\n<颜色:15>\n\n<Custom On Equip Eval>\nuser.gainSkillMasteryLevel(99, 1);\n</Custom On Equip Eval>\n\n<Custom On Remove Equip Eval>\nuser.gainSkillMasteryLevel(99, -1);\n</Custom On Remove Equip Eval>", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 5000}, {"id": 33, "atypeId": 1, "description": "", "etypeId": 2, "traits": [{"code": 43, "dataId": 52, "value": 1}], "iconIndex": 644, "name": "苹果头套", "note": "<Loot Pool: Rare>\n<颜色:16>\n\n<Custom On Equip Eval>\nuser.gainSkillMasteryLevel(52, 1);\n</Custom On Equip Eval>\n\n<Custom On Remove Equip Eval>\nuser.gainSkillMasteryLevel(52, -1)\n</Custom On Remove Equip Eval>", "params": [0, 0, 0, 0, 0, 0, 0, 10], "price": 500}, {"id": 34, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 35, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 36, "atypeId": 1, "description": "", "etypeId": 2, "traits": [{"code": 43, "dataId": 40, "value": 1}], "iconIndex": 232, "name": "招财猫", "note": "<Loot Pool: Rare>\n<颜色:15>\n\n<Custom On Equip Eval>\nif ($gameMap.drill_COET_getEventsByTag_direct(\"招财猫\").length == 0) {\nvar XX = $gamePlayer._x;\nvar YY = $gamePlayer._y;\n$gameMap.spawnEventQJ(1,118,XX,YY,false);\n}\n</Custom On Equip Eval>", "params": [0, 0, 0, 0, 0, 10, 0, 50], "price": 0}, {"id": 37, "atypeId": 1, "description": "", "etypeId": 2, "traits": [{"code": 43, "dataId": 32, "value": 1}], "iconIndex": 230, "name": "猪猪存钱罐", "note": "<needRefresh>\n\n<Custom On Equip Eval>\nvar XX = $gamePlayer._x;\nvar YY = $gamePlayer._y;\n$gameMap.spawnEventQJ(1,111,XX,YY,false);\n</Custom On Equip Eval>", "params": [0, 0, 0, 0, 0, 0, 0, 40], "price": 0}, {"id": 38, "atypeId": 1, "description": "", "etypeId": 2, "traits": [{"code": 43, "dataId": 32, "value": 1}], "iconIndex": 246, "name": "贪欲存钱罐", "note": "<颜色:13>\n<needRefresh>\n\n<Custom On Equip Eval>\nlet XX = $gamePlayer._x;\nlet YY = $gamePlayer._y;\nlet eid = $gameMap.spawnEventQJ(1, 112, XX, YY, false); \nlet event = $gameMap.event(eid);\n if (event) {\nevent._needSE = true;\nlet condition = DrillUp.g_COFA_condition_list[6];\nlet validPositions = $gameMap.drill_COFA_getShapePointsWithCondition(\nMath.floor(XX), Math.floor(YY), \"圆形区域\", 3, condition\n   );\nif (validPositions.length > 0) {\nlet pos = validPositions[Math.floor(Math.random() * validPositions.length)];\nevent.locate(pos.x, pos.y);\n }\n}\n</Custom On Equip Eval>", "params": [0, 0, 0, 0, 0, 0, 0, 100], "price": 0}, {"id": 39, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 40, "atypeId": 1, "description": "", "etypeId": 2, "traits": [{"code": 43, "dataId": 15, "value": 1}], "iconIndex": 584, "name": "女仆制服", "note": "<Loot Pool: Rare>\n<颜色:16>\n<Passive State: 15>\n\n<Custom On Equip Eval>\nQJ.MPMZ.deleteProjectile('attackMonitoring');\n$gamePlayer.setStealthMode(true);\n$gameParty.leader()._characterName = \"$player_maid\";\n$gamePlayer.refresh();\n</Custom On Equip Eval>\n\n<Custom On Remove Equip Eval>\n$gamePlayer.setStealthMode(false);\n$gameParty.leader()._characterName = \"$player\";\n$gamePlayer.refresh();\n</Custom On Remove Equip Eval>", "params": [0, 0, 0, 4, 0, 4, 0, 30], "price": 4000}, {"id": 41, "atypeId": 1, "description": "", "etypeId": 2, "traits": [{"code": 43, "dataId": 80, "value": 1}], "iconIndex": 568, "name": "萌萌兔耳", "note": "<Loot Pool: Rare>\n<颜色:16>\n\n<Custom On Equip Eval>\nuser.gainSkillMasteryLevel(80, 1);\n</Custom On Equip Eval>\n\n<Custom On Remove Equip Eval>\nuser.gainSkillMasteryLevel(80, -1)\n</Custom On Remove Equip Eval>", "params": [0, 0, 0, 1, 0, 0, 0, 60], "price": 0}, {"id": 42, "atypeId": 1, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}, {"code": 43, "dataId": 34, "value": 1}], "iconIndex": 639, "name": "炸弹魔之魂", "note": "<Loot Pool: Epic>\n<Loot Pool: Rare>\n<颜色:15>\n\n<Custom On Equip Eval>\nuser.gainSkillMasteryLevel(34, 1)\n</Custom On Equip Eval>\n\n<Custom On Remove Equip Eval>\nuser.gainSkillMasteryLevel(34, -1)\n</Custom On Remove Equip Eval>", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 100}, {"id": 43, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 44, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 45, "atypeId": 1, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 365, "name": "毒蛇之拥", "note": "<Loot Pool: Rare>\n<颜色:15>\n\n<Custom On Equip Eval>\n$gameNumberArray.value(22).push(\"serpentEmbrace\");\nlet bulletTypes = $gameNumberArray.value(22);\nQJ.MPMZ.tl.ex_orbitingBulletInitialization(bulletTypes);\n</Custom On Equip Eval>\n\n<Custom On Remove Equip Eval>\n$gameNumberArray.value(22).splice($gameNumberArray.value(22).indexOf(\"serpentEmbrace\"), 1);\nlet bulletTypes = $gameNumberArray.value(22);\nQJ.MPMZ.tl.ex_orbitingBulletInitialization(bulletTypes);\n</Custom On Remove Equip Eval>", "params": [0, 0, 1, 0, 8, 0, 0, 0], "price": 0}, {"id": 46, "atypeId": 1, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 464, "name": "愤怒的拳头", "note": "<Loot Pool: Uncommon>\n<颜色:16>\n\n<Custom On Equip Eval>\n$gameNumberArray.value(22).push(\"FacePunchGloves\");\nlet bulletTypes = $gameNumberArray.value(22);\nQJ.MPMZ.tl.ex_orbitingBulletInitialization(bulletTypes);\n</Custom On Equip Eval>\n\n<Custom On Remove Equip Eval>\n$gameNumberArray.value(22).splice($gameNumberArray.value(22).indexOf(\"FacePunchGloves\"), 1);\nlet bulletTypes = $gameNumberArray.value(22);\nQJ.MPMZ.tl.ex_orbitingBulletInitialization(bulletTypes);\n</Custom On Remove Equip Eval>", "params": [0, 0, 1, 0, 8, 0, 0, 0], "price": 0}, {"id": 47, "atypeId": 1, "description": "", "etypeId": 2, "traits": [{"code": 43, "dataId": 88, "value": 1}], "iconIndex": 654, "name": "谢里斯的诅咒之手", "note": "<Loot Pool: Rare>\n<颜色:15>\n\n<Custom On Equip Eval>\nQJ.MPMZ.tl.ex_XerisesCursedHand()\n</Custom On Equip Eval>\n\n<Custom On Remove Equip Eval>\nQJ.MPMZ.tl.ex_XerisesCursedHand()\n</Custom On Remove Equip Eval>", "params": [0, 0, 0, 0, 0, 18, 0, -5], "price": 400}, {"id": 48, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 49, "atypeId": 1, "description": "", "etypeId": 2, "traits": [{"code": 43, "dataId": 42, "value": 1}, {"code": 13, "dataId": 5, "value": 0.75}, {"code": 13, "dataId": 6, "value": 0.75}, {"code": 13, "dataId": 7, "value": 0.75}, {"code": 13, "dataId": 9, "value": 0.75}, {"code": 13, "dataId": 8, "value": 0.75}, {"code": 21, "dataId": 0, "value": 1.05}], "iconIndex": 580, "name": "废弃", "note": "\n<颜色:16>\n\n<Custom On Equip Eval>\nuser.gainSkillMasteryLevel(42, 1)\n</Custom On Equip Eval>\n\n<Custom On Remove Equip Eval>\nuser.gainSkillMasteryLevel(42, -1)\n</Custom On Remove Equip Eval>", "params": [0, 0, 0, 2, 0, 0, 0, 0], "price": 2000}, {"id": 50, "atypeId": 1, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 1327, "name": "炸薯条", "note": "<Durability: 10>\n<Loot Pool: Uncommon>\n\n<Custom On Equip Eval>\n$gameSwitches.setValue(223, true);\nvar data = {\n'type': \"公共事件\",\n'pipeType': \"并行\",\n'commonEventId': 291,\n'callBack_str': \"\",};\n$gameMap.drill_LCT_addPipeEvent( data );\n</Custom On Equip Eval>\n\n<Custom On Remove Equip Eval>\n$gameSwitches.setValue(223, false);\n</Custom On Remove Equip Eval>", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 51, "atypeId": 1, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 6, "value": 0.05}, {"code": 43, "dataId": 41, "value": 1}, {"code": 21, "dataId": 4, "value": 1.04}], "iconIndex": 646, "name": "废弃", "note": "<颜色:15>\n\n<Custom On Equip Eval>\nuser.gainSkillMasteryLevel(41, 1)\n</Custom On Equip Eval>\n\n<Custom On Remove Equip Eval>\nuser.gainSkillMasteryLevel(41, -1)\n</Custom On Remove Equip Eval>", "params": [0, 0, 0, 0, 10, 0, 0, -5], "price": 0}, {"id": 52, "atypeId": 1, "description": "", "etypeId": 2, "traits": [{"code": 43, "dataId": 39, "value": 1}], "iconIndex": 667, "name": "废弃", "note": "<eva Rate: 110%>\n<eva Flat: +10%>\n<颜色:16>\n\n<Custom On Equip Eval>\nuser.gainSkillMasteryLevel(39, 1)\n</Custom On Equip Eval>\n\n<Custom On Remove Equip Eval>\nuser.gainSkillMasteryLevel(39, -1)\n</Custom On Remove Equip Eval>", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 2000}, {"id": 53, "atypeId": 1, "description": "", "etypeId": 2, "traits": [{"code": 23, "dataId": 8, "value": 0.5}], "iconIndex": 203, "name": "倒吊人的骨戒", "note": "<Loot Pool: Uncommon>\n<颜色:16>\n", "params": [20, 0, 0, 2, 0, 15, 0, 0], "price": 1200}, {"id": 54, "atypeId": 1, "description": "", "etypeId": 2, "traits": [], "iconIndex": 564, "name": "未满的黄金圣杯", "note": "<Loot Pool: Legendary>\n<Passive State: 105>\n<颜色:13>\n\n\n", "params": [0, 0, 0, 0, 0, 0, 0, 100], "price": 50000}, {"id": 55, "atypeId": 1, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}, {"code": 43, "dataId": 33, "value": 1}], "iconIndex": 613, "name": "纵火狂的防烫手套", "note": "<Loot Pool: Uncommon>\n<颜色:16>\n\n<Custom On Equip Eval>\nuser.gainSkillMasteryLevel(33, 30);\n</Custom On Equip Eval>\n\n<Custom On Remove Equip Eval>\nuser.gainSkillMasteryLevel(33, -30)\n</Custom On Remove Equip Eval>", "params": [0, 0, 4, 0, 0, 8, 0, 4], "price": 500}, {"id": 56, "atypeId": 1, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}, {"code": 43, "dataId": 61, "value": 1}, {"code": 43, "dataId": 15, "value": 1}], "iconIndex": 612, "name": "圣少女的怜悯", "note": "<颜色:14>\n\n<Custom On Equip Eval>\nuser.gainSkillMasteryLevel(61, 1);\n$gameVariables.setValue(25, -10);\n</Custom On Equip Eval>\n\n<Custom On Remove Equip Eval>\nuser.gainSkillMasteryLevel(61, -1)\n</Custom On Remove Equip Eval>", "params": [0, 0, 0, 2, 20, 40, 0, 0], "price": 10000}, {"id": 57, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 58, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 59, "atypeId": 1, "description": "", "etypeId": 2, "traits": [{"code": 43, "dataId": 31, "value": 1}], "iconIndex": 228, "name": "红髓玉吊坠", "note": "<颜色:14>\n<Passive State: 117>\n\n<Custom On Equip Eval>\nuser.gainSkillMasteryLevel(31, 24)\n</Custom On Equip Eval>\n\n<Custom On Remove Equip Eval>\nuser.gainSkillMasteryLevel(31, -24)\n</Custom On Remove Equip Eval>", "params": [0, 0, 0, 0, 20, 45, 0, 0], "price": 15000}, {"id": 60, "atypeId": 1, "description": "", "etypeId": 2, "traits": [{"code": 21, "dataId": 4, "value": 1.04}], "iconIndex": 614, "name": "堕落法师的头环", "note": "<颜色:15>\n\n<Custom On Equip Eval>\nuser.gainSkillMasteryLevel(41, 1)\n</Custom On Equip Eval>\n\n<Custom On Remove Equip Eval>\nuser.gainSkillMasteryLevel(41, -1)\n</Custom On Remove Equip Eval>", "params": [0, 0, 0, 0, 30, 25, 0, -5], "price": 1000}, {"id": 61, "atypeId": 1, "description": "", "etypeId": 2, "traits": [{"code": 21, "dataId": 4, "value": 1.04}], "iconIndex": 630, "name": "堕落法师的披风", "note": "<颜色:15>\n\n<Custom On Equip Eval>\nuser.gainSkillMasteryLevel(41, 1)\n</Custom On Equip Eval>\n\n<Custom On Remove Equip Eval>\nuser.gainSkillMasteryLevel(41, -1)\n</Custom On Remove Equip Eval>", "params": [0, 0, 0, 1, 10, 35, 0, -5], "price": 1000}, {"id": 62, "atypeId": 1, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 6, "value": 0.05}, {"code": 43, "dataId": 41, "value": 1}, {"code": 21, "dataId": 4, "value": 1.04}], "iconIndex": 646, "name": "堕落法师的手套", "note": "<Loot Pool: Rare>\n<颜色:15>\n\n<Custom On Equip Eval>\nuser.gainSkillMasteryLevel(41, 1)\n</Custom On Equip Eval>\n\n<Custom On Remove Equip Eval>\nuser.gainSkillMasteryLevel(41, -1)\n</Custom On Remove Equip Eval>", "params": [0, 0, 0, 0, 10, 20, 0, -5], "price": 1000}, {"id": 63, "atypeId": 1, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0.15}, {"code": 21, "dataId": 4, "value": 1.04}, {"code": 43, "dataId": 92, "value": 1}], "iconIndex": 662, "name": "堕落法师的轻履", "note": "<Loot Pool: Rare>\n<颜色:15>\n\n<Custom On Equip Eval>\nuser.gainSkillMasteryLevel(41, 1)\n</Custom On Equip Eval>\n\n<Custom On Remove Equip Eval>\nuser.gainSkillMasteryLevel(41, -1)\n</Custom On Remove Equip Eval>", "params": [0, 0, 0, 2, 10, 20, 0, -5], "price": 1000}, {"id": 64, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 65, "atypeId": 1, "description": "", "etypeId": 2, "traits": [{"code": 43, "dataId": 42, "value": 1}, {"code": 13, "dataId": 5, "value": 0.75}, {"code": 13, "dataId": 6, "value": 0.75}, {"code": 13, "dataId": 7, "value": 0.75}, {"code": 13, "dataId": 9, "value": 0.75}, {"code": 13, "dataId": 8, "value": 0.75}, {"code": 21, "dataId": 0, "value": 1.05}], "iconIndex": 619, "name": "毅力头巾", "note": "<Loot Pool: Uncommon>\n<颜色:16>\n\n<Custom On Equip Eval>\nuser.gainSkillMasteryLevel(42, 1)\n</Custom On Equip Eval>\n\n<Custom On Remove Equip Eval>\nuser.gainSkillMasteryLevel(42, -1)\n</Custom On Remove Equip Eval>", "params": [0, 0, 0, 2, 0, 0, 0, 0], "price": 2000}, {"id": 66, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 635, "name": "修行服", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 67, "atypeId": 1, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 6, "value": 0.1}, {"code": 43, "dataId": 30, "value": 1}], "iconIndex": 651, "name": "武术家护腕", "note": "<atk Flat: +2>\n<Loot Pool: Uncommon>\n<颜色:16>\n\n<Custom On Equip Eval>\nuser.gainSkillMasteryLevel(30, 12)\n</Custom On Equip Eval>\n\n<Custom On Remove Equip Eval>\nuser.gainSkillMasteryLevel(30, -12)\n</Custom On Remove Equip Eval>", "params": [0, 0, 1, 0, 0, 0, 0, 0], "price": 500}, {"id": 68, "atypeId": 1, "description": "", "etypeId": 2, "traits": [{"code": 43, "dataId": 39, "value": 1}], "iconIndex": 667, "name": "武术家之鞋", "note": "<eva Rate: 110%>\n<eva Flat: +10%>\n<Loot Pool: Uncommon>\n<颜色:16>\n\n<Custom On Equip Eval>\nuser.gainSkillMasteryLevel(39, 1)\n</Custom On Equip Eval>\n\n<Custom On Remove Equip Eval>\nuser.gainSkillMasteryLevel(39, -1)\n</Custom On Remove Equip Eval>", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 2000}, {"id": 69, "atypeId": 1, "description": "", "etypeId": 2, "traits": [{"code": 53, "dataId": 1, "value": 1}, {"code": 43, "dataId": 69, "value": 1}], "iconIndex": 577, "name": "破颜拳套", "note": "<atk Flat: +3>\n<Loot Pool: Uncommon>\n<颜色:16>\n\n<Custom On Equip Eval>\nif ($gameParty.leader().equips()[0] && $gameParty.leader().equips()[0].baseItemId !== 4) {\n$gameParty.leader().changeEquipById(1, 4);\n$gameMap.steupCEQJ(100,1,{equipChange:true,equipIndex:-1});\n}\nuser.gainSkillMasteryLevel(69, 1)\n</Custom On Equip Eval>\n\n<Custom On Remove Equip Eval>\nuser.gainSkillMasteryLevel(69, -1)\n</Custom On Remove Equip Eval>", "params": [0, 0, 3, 1, 0, 0, 0, 0], "price": 600}, {"id": 70, "atypeId": 1, "description": "", "etypeId": 2, "traits": [{"code": 23, "dataId": 3, "value": 0}, {"code": 14, "dataId": 1, "value": 1}], "iconIndex": 660, "name": "稻草人的心", "note": "<颜色:14>", "params": [0, 0, 0, 0, 0, 30, 0, 0], "price": 0}, {"id": 71, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 72, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 73, "atypeId": 1, "description": "", "etypeId": 2, "traits": [{"code": 13, "dataId": 9, "value": 0.2}, {"code": 13, "dataId": 8, "value": 0.5}], "iconIndex": 174, "name": "熔岩戒指", "note": "<颜色:16>", "params": [0, 0, 0, 2, 15, 60, 0, 0], "price": 1500}, {"id": 74, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 75, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 76, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 77, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 78, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 79, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 80, "atypeId": 1, "description": "", "etypeId": 2, "traits": [{"code": 21, "dataId": 0, "value": 1.05}, {"code": 21, "dataId": 2, "value": 1.05}, {"code": 21, "dataId": 3, "value": 1.05}, {"code": 21, "dataId": 4, "value": 1.05}, {"code": 21, "dataId": 5, "value": 1.05}, {"code": 21, "dataId": 6, "value": 1.05}, {"code": 21, "dataId": 7, "value": 1.05}, {"code": 43, "dataId": 101, "value": 1}], "iconIndex": 171, "name": "王权征令", "note": "<颜色:13>\n<Loot Pool: Legendary>\n\n<Custom On Equip Eval>\nuser.gainSkillMasteryLevel(101, 1);\nQJ.MPMZ.deleteProjectile('servant',{a:['S',\"this._needJS=true\"]});\nQJ.MPMZ.tl.ServantResetAndRegeneration();\t\n</Custom On Equip Eval>\n\n<Custom On Remove Equip Eval>\nuser.gainSkillMasteryLevel(101, -1);\nQJ.MPMZ.deleteProjectile('servant',{a:['S',\"this._needJS=true\"]});\nQJ.MPMZ.tl.ServantResetAndRegeneration();\n</Custom On Remove Equip Eval>", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 30000}, {"id": 81, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 82, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 83, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 84, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 85, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 86, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 87, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 88, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 89, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 90, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 91, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 92, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 93, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 94, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 95, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 96, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 97, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 98, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 99, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 100, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 101, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 102, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 103, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 104, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 105, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 106, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 107, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 108, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 109, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 110, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 111, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 112, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 113, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 114, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 115, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 116, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 117, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 118, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 119, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 120, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 121, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 122, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 123, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 124, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 125, "atypeId": 1, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 524, "name": "炎天娃娃", "note": "<Loot Pool: Common>\n<Loot Pool: Uncommon>", "params": [0, 0, 0, 0, 0, 10, 0, 10], "price": 100}, {"id": 126, "atypeId": 1, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 521, "name": "晴天娃娃", "note": "<Loot Pool: Common>\n<Loot Pool: Uncommon>\n", "params": [0, 0, 0, 0, 0, 10, 0, 10], "price": 100}, {"id": 127, "atypeId": 1, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 523, "name": "阴天娃娃", "note": "<Loot Pool: Common>\n<Loot Pool: Uncommon>\n", "params": [0, 0, 0, 0, 0, 10, 0, 10], "price": 100}, {"id": 128, "atypeId": 1, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 522, "name": "雨天娃娃", "note": "<Loot Pool: Common>\n<Loot Pool: Uncommon>", "params": [0, 0, 0, 0, 0, 10, 0, 10], "price": 100}, {"id": 129, "atypeId": 1, "description": "", "etypeId": 2, "traits": [{"code": 21, "dataId": 0, "value": 1.1}, {"code": 21, "dataId": 2, "value": 1.1}, {"code": 21, "dataId": 4, "value": 1.1}, {"code": 21, "dataId": 6, "value": 1.1}, {"code": 21, "dataId": 7, "value": 1.1}], "iconIndex": 529, "name": "少女的笑容", "note": "<颜色:14>\n\n\n\n", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 130, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 131, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 132, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 133, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 134, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 135, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 136, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 137, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 138, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 139, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 140, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 141, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 142, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 143, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 144, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 145, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 146, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 147, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 148, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 149, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 150, "atypeId": 2, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 531, "name": "特典T恤", "note": "<颜色:15>\n", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 151, "atypeId": 2, "description": "", "etypeId": 2, "traits": [], "iconIndex": 513, "name": "妹妹的外套", "note": "", "params": [0, 0, 0, 0, 0, 5, 0, 0], "price": 0}, {"id": 152, "atypeId": 2, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 481, "name": "妹妹的睡衣", "note": "<颜色:16>\n", "params": [0, 0, 0, 0, 0, 15, 0, 0], "price": 0}, {"id": 153, "atypeId": 2, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 482, "name": "妹妹的短裤", "note": "<颜色:16>\n", "params": [0, 0, 0, 0, 0, 10, 0, 0], "price": 0}, {"id": 154, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 155, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 156, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 157, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 158, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 159, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 160, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 161, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 162, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 163, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 164, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 165, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 166, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 167, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 168, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 169, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 170, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 171, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 172, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 173, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 174, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 175, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 176, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 177, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 178, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 179, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 180, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 181, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 182, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 183, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 184, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 185, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 186, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 187, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 188, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 189, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 190, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 191, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 192, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 193, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 194, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 195, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 196, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 197, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 198, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 199, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 200, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 201, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 202, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 203, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 204, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 205, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 206, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 207, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 208, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 209, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 210, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 211, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 212, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 213, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 214, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 215, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 216, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 217, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 218, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 219, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 220, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 221, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 222, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 223, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 224, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 225, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 226, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 227, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 228, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 229, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 230, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 231, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 232, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 233, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 234, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 235, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 236, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 237, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 238, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 239, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 240, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 241, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 242, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 243, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 244, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 245, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 246, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 247, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 248, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 249, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 250, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 251, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 252, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 253, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 254, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 255, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 256, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 257, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 258, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 259, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 260, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 261, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 262, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 263, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 264, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 265, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 266, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 267, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 268, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 269, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 270, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 271, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 272, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 273, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 274, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 275, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 276, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 277, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 278, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 279, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 280, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 281, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 282, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 283, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 284, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 285, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 286, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 287, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 288, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 289, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 290, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 291, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 292, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 293, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 294, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 295, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 296, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 297, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 298, "atypeId": 0, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}], "iconIndex": 0, "name": "", "note": "", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 0}, {"id": 299, "atypeId": 1, "description": "", "etypeId": 2, "traits": [{"code": 22, "dataId": 1, "value": 0}, {"code": 43, "dataId": 15, "value": 1}], "iconIndex": 584, "name": "绝对领域", "note": "<Custom On Equip Eval>\nuser._weaponAmountBonus += 44;\nuser._armorAmountBonus += 99;\n</Custom On Equip Eval>\n\n<Custom On Remove Equip Eval>\nuser._weaponAmountBonus -= 44;\nuser._armorAmountBonus -= 99;\n</Custom On Remove Equip Eval>", "params": [0, 0, 0, 0, 0, 69, 0, 69], "price": 99999}, {"id": 300, "atypeId": 1, "description": "", "etypeId": 2, "traits": [{"code": 43, "dataId": 61, "value": 1}], "iconIndex": 188, "name": "爱神荡漾", "note": "<颜色:13>\n\n<Custom On Equip Eval>\nuser.gainSkillMasteryLevel(61, 15);\n</Custom On Equip Eval>\n\n<Custom On Remove Equip Eval>\nuser.gainSkillMasteryLevel(61, 15)\n</Custom On Remove Equip Eval>", "params": [0, 0, 0, 0, 0, 0, 0, 0], "price": 99999}]