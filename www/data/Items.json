[null, {"id": 1, "animationId": 0, "consumable": false, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 88, "itypeId": 1, "name": "茶点心券", "note": "<颜色:14>", "occasion": 0, "price": 11111, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 2, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 3, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 21, "dataId": 58, "value1": 0.25, "value2": 0}], "hitType": 0, "iconIndex": 1345, "itypeId": 2, "name": "垃圾", "note": "<Loot Pool: Junk1>\n<Loot Pool: Common>\n<Ingredients: Junk>\n<黑暗料理>\n", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 4, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 68, "itypeId": 2, "name": "10元", "note": "", "occasion": 0, "price": 10, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 5, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 69, "itypeId": 2, "name": "50元", "note": "", "occasion": 0, "price": 50, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 6, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 70, "itypeId": 2, "name": "100元", "note": "<颜色:16>", "occasion": 0, "price": 100, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 7, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 71, "itypeId": 2, "name": "500元", "note": "<颜色:16>", "occasion": 0, "price": 500, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 8, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 72, "itypeId": 2, "name": "1000元", "note": "<颜色:15>", "occasion": 0, "price": 1000, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 9, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 73, "itypeId": 2, "name": "5000元", "note": "<颜色:15>", "occasion": 0, "price": 5000, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 10, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 74, "itypeId": 2, "name": "10000元", "note": "<颜色:14>", "occasion": 0, "price": 10000, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 11, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 76, "itypeId": 1, "name": "50000元", "note": "<颜色:14>", "occasion": 0, "price": 50000, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 12, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 77, "itypeId": 2, "name": "100000元", "note": "<颜色:13>", "occasion": 0, "price": 100000, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 13, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 14, "animationId": 0, "consumable": false, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 44, "dataId": 11, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 897, "itypeId": 1, "name": "魔导映写器", "note": "<颜色:15>\n", "occasion": 0, "price": 5000, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 15, "animationId": 0, "consumable": false, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 900, "itypeId": 3, "name": "JoyStation V", "note": "<Loot Pool: chahuiGoods>\n<颜色:13>\n", "occasion": 0, "price": 5000, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 16, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 198, "itypeId": 3, "name": "暗影之魂DLC", "note": "<颜色:15>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 17, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 512, "itypeId": 2, "name": "妹妹的呆毛", "note": "<Loot Pool: <PERSON>og<PERSON>>\n<Ingredients: <PERSON>oge>\n", "occasion": 0, "price": 1000, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 18, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 899, "itypeId": 3, "name": "橡胶鸭", "note": "", "occasion": 0, "price": 1, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 19, "animationId": 0, "consumable": false, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 898, "itypeId": 3, "name": "电风扇", "note": "<颜色:16>", "occasion": 3, "price": 1500, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 20, "animationId": 44, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 44, "dataId": 320, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 64, "itypeId": 1, "name": "天上之石碎片", "note": "<颜色:1>", "occasion": 0, "price": 10000, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 21, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 22, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 23, "animationId": 176, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "100", "type": 0, "variance": 0}, "description": "", "effects": [{"code": 44, "dataId": 311, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 113, "itypeId": 1, "name": "好喝的牛奶", "note": "<itemType:jihanki>\n<useableItem>\n<Info Text Top>\n\\fr\\fs[18]•\\c[110]\\fi\\「广告赞助商：逆流茶会」\n\\fr\\fs[18]•从奇妙的自动贩售机里拿到的饮料。\n味道且不提，为什么会在深渊里发现自动贩售机啊…\n\\fs[18]•\\{\\i[113]\\}体力恢复\\c[29]120\\c[0]点、并获得\\c[31]真美味！！\\c[0]状态。\n</Info Text Top>", "occasion": 0, "price": 100, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 24, "animationId": 176, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 44, "dataId": 311, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 98, "itypeId": 1, "name": "胡萝卜牛奶", "note": "<Remove 2 State Category: CanBeDeleted>\n<itemType:jihanki>\n\n<useableItem>\n<Info Text Top>\n\\fr\\fs[18]•\\c[110]\\fi\\「广告赞助商：逆流茶会」\n\\fr\\fs[18]•从奇妙的自动贩售机里拿到的饮料。\n味道且不提，为什么会在深渊里发现自动贩售机啊…\n\\fs[18]•\\{\\i[98]\\}体力恢复\\c[29]75\\c[0]点、并随机消除一种异常状态。\n</Info Text Top>\n\n\n", "occasion": 0, "price": 100, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 25, "animationId": 176, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "222", "type": 3, "variance": 20}, "description": "", "effects": [{"code": 44, "dataId": 311, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 97, "itypeId": 1, "name": "草莓味牛奶", "note": "<useableItem>\n<itemType:jihanki>\n<Info Text Top>\n\\fr\\fs[18]•\\c[110]\\fi\\「广告赞助商：逆流茶会」\n\\fr\\fs[18]•从奇妙的自动贩售机里拿到的饮料。\n味道且不提，为什么会在深渊里发现自动贩售机啊…\n\\fs[18]•\\{\\i[97]\\}体力恢复\\c[29]75\\c[0]点、并在一定时间内提升\\c[17]50%\\c[0]攻击力。\n</Info Text Top>\n", "occasion": 0, "price": 100, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 26, "animationId": 176, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "100", "type": 3, "variance": 0}, "description": "", "effects": [{"code": 44, "dataId": 311, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 96, "itypeId": 1, "name": "蜜瓜味牛奶", "note": "<useableItem>\n<itemType:jihanki>\n<Info Text Top>\n\\fr\\fs[18]•\\c[110]\\fi\\「广告赞助商：逆流茶会」\n\\fr\\fs[18]•从奇妙的自动贩售机里拿到的饮料。\n味道且不提，为什么会在深渊里发现自动贩售机啊…\n\\fs[18]•\\{\\i[96]\\}体力恢复\\c[29]75\\c[0]点、并在一定时间内提升\\c[17]50%\\c[0]魔力。\n</Info Text Top>\n", "occasion": 0, "price": 100, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 27, "animationId": 176, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 44, "dataId": 311, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 99, "itypeId": 1, "name": "香蕉味牛奶", "note": "<useableItem>\n<itemType:jihanki>\n<Info Text Top>\n\\fr\\fs[18]•\\c[110]\\fi\\「广告赞助商：逆流茶会」\n\\fr\\fs[18]•从奇妙的自动贩售机里拿到的饮料。\n味道且不提，为什么会在深渊里发现自动贩售机啊…\n\\fs[18]•\\{\\i[99]\\}体力恢复\\c[29]75\\c[0]点、并在一定时间内提升移动速度。\n</Info Text Top>\n", "occasion": 0, "price": 100, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 28, "animationId": 176, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 44, "dataId": 311, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 112, "itypeId": 1, "name": "巧克力牛奶", "note": "<useableItem>\n<itemType:jihanki>\n<Info Text Top>\n\\fr\\fs[18]•\\c[110]\\fi\\「広告スポンサー：逆流茶会」\n\\fr\\fs[18]•奇妙な自動販売機から手に入れた飲み物。\n味はともかく、なぜアビスに自動販売機があるのだろうか…\n\\fs[18]•\\{\\i[112]\\}\\c[10]78\\c[0]の体力を回復し、プレイヤーに\\c[31]霸体\\c[0]を与える.\n</Info Text Top>\n", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 29, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 30, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 31, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 32, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 33, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 34, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 35, "animationId": 0, "consumable": false, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 116, "itypeId": 1, "name": "大瓶逆流可乐", "note": "<颜色:16>\n<Info Text Top>\n\\fr\\fs[18]•\\c[110]\\fi\\「广告赞助商：逆流茶会」\n\\fr\\fs[18]•从奇妙的自动贩售机里拿到的饮料。\n味道且不提，为什么会在深渊里发现自动贩售机啊…\n\\fs[18]•\\{\\i[116]\\}剧烈摇晃时可以引发强烈的爆炸\n</Info Text Top>\n", "occasion": 3, "price": 500, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 36, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 37, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 38, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 39, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 40, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 161, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 41, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 162, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 42, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 163, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 43, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 44, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 44, "dataId": 310, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 1092, "itypeId": 1, "name": "卢恩符文Raido", "note": "<useableItem>\n<Loot Pool: Rare>\n<Loot Pool: Rune>\n<颜色:14>\n<Info Text Top>\n\\fr•\\c[110]\\fi\\「骑乘之力」\n\\fr\\fs[18]•刻印了魔法符文的石头，被设计成了就算是\n不懂魔法的新手也能简单发动效果的形式。\n\\fs[18]•\\{\\i[1764]\\}投掷后制作出点击便能交换位置的标记\n</Info Text Top>", "occasion": 0, "price": 1500, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 45, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 44, "dataId": 310, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 1106, "itypeId": 1, "name": "卢恩符文Ehwaz", "note": "<useableItem>\n<Loot Pool: Rare>\n<Loot Pool: Rune>\n<颜色:14>\n<Info Text Top>\n\\fr•\\c[110]\\fi\\「改变之力」\n\\fr\\fs[18]•刻印了魔法符文的石头，被设计成了就算是\n不懂魔法的新手也能简单发动效果的形式。\n\\fs[18]•\\{\\i[1778]\\}投掷后会与触碰到的目标交换位置\n</Info Text Top>", "occasion": 0, "price": 1500, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 46, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 44, "dataId": 312, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 1120, "itypeId": 1, "name": "愚者", "note": "<useableItem>\n<Loot Pool: Rare>\n<Loot Pool: Tarot>\n<颜色:14>\n<Info Text Top>\n\\fr\\fs[18]•\\c[110]\\fi\\「愚蠢冒险者的无尽好奇心」\n\\fr\\fs[18]•施加了术式的魔法卡片。被设计成了就算是\n不懂魔法的新手也能简单发动效果的形式。\n\\fs[18]•\\{\\i[1852]\\}使用时、强制转移到随机地图。\n</Info Text Top>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 47, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 44, "dataId": 312, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 1121, "itypeId": 1, "name": "魔术师", "note": "<useableItem>\n\n<颜色:14>\n<Info Text Top>\n\\fr\\fs[18]•\\c[110]\\fi\\「炼金术师的杰作」\n\\fr\\fs[18]•施加了术式的魔法卡片。被设计成了就算是\n不懂魔法的新手也能简单发动效果的形式。\n\\fs[18]•\\{\\i[1852]\\}使用时、召唤出随机数量的元素从者\n</Info Text Top>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 48, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 44, "dataId": 312, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 1122, "itypeId": 1, "name": "女祭司", "note": "<useableItem>\n<Loot Pool: Rare>\n<Loot Pool: Tarot>\n<颜色:14>\n<Info Text Top>\n\\fr\\fs[18]•\\c[110]\\fi\\「愿你能获得她的宠爱……」\n\\fr\\fs[18]•施加了术式的魔法卡片。被设计成了就算是\n不懂魔法的新手也能简单发动效果的形式。\n\\fs[18]•\\{\\i[1852]\\}使用时、召唤出可以前往\\c[8]堕落教堂\\c[0]的门\n</Info Text Top>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 49, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 44, "dataId": 312, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 1123, "itypeId": 1, "name": "女帝", "note": "<useableItem>\n\n<颜色:14>\n<Info Text Top>\n\\fr\\fs[18]•\\c[110]\\fi\\「一顾倾人城，再顾倾人国」\n\\fr\\fs[18]•施加了术式的魔法卡片。被设计成了就算是\n不懂魔法的新手也能简单发动效果的形式。\n\\fs[18]•\\{\\i[1852]\\}使用时、赋予玩家持续7秒的\\c[31]倾国倾城\\c[0]状态\n</Info Text Top>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 50, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 44, "dataId": 312, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 1124, "itypeId": 1, "name": "皇帝", "note": "<useableItem>\n<Loot Pool: Rare>\n<Loot Pool: Tarot>\n<颜色:14>\n<Info Text Top>\n\\fr\\fs[18]•\\c[110]\\fi\\「龙之威严、不可侵犯」\n\\fr\\fs[18]•施加了术式的魔法卡片。被设计成了就算是\n不懂魔法的新手也能简单发动效果的形式。\n\\fs[18]•\\{\\i[1852]\\}使用时、对画面内所有敌人使用\\c[27]巨龙咆哮\\c[0]使其眩晕。\n</Info Text Top>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 51, "animationId": 190, "consumable": false, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 44, "dataId": 312, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 1125, "itypeId": 1, "name": "教皇", "note": "<useableItem>\n\n<颜色:14>\n<Info Text Top>\n\\fr\\fs[18]•\\c[110]\\fi\\「此刻正是觉醒之时」\n\\fr\\fs[18]施加了术式的魔法卡片。被设计成了就算是\n不懂魔法的新手也能简单发动效果的形式。\n\\fs[18]•\\{\\i[1852]\\}使用时、赋予玩家\\c[31]心眼\\c[0]和\\c[31]贤者模式\\c[0]状态\n</Info Text Top>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 52, "animationId": 0, "consumable": false, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 44, "dataId": 312, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 1126, "itypeId": 1, "name": "恋人", "note": "\n<颜色:14>\n<Info Text Top>\n\\fr\\fs[18]•\\c[110]\\fi\\「魔女的残香」\n\\fr\\fs[18]•施加了术式的魔法卡片。被设计成了就算是\n不懂魔法的新手也能简单发动效果的形式。\n\\fs[18]•\\{\\i[1852]\\}使用时、一段时间内魔物生成效率提升并得到强化。\n</Info Text Top>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 53, "animationId": 0, "consumable": false, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 44, "dataId": 312, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 1127, "itypeId": 1, "name": "战车", "note": "<useableItem>\n<Loot Pool: Rare>\n<Loot Pool: Tarot>\n<颜色:14>\n<Info Text Top>\n\\fr\\fs[18]•\\c[110]\\fi\\「随叫随到！地狱特快！」\n\\fr\\fs[18]•施加了术式的魔法卡片。被设计成了就算是\n不懂魔法的新手也能简单发动效果的形式。\n\\fs[18]•\\{\\i[1852]\\}使用时、出现一辆茶会配送车撞向玩家。\n</Info Text Top>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 54, "animationId": 0, "consumable": false, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 44, "dataId": 312, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 1128, "itypeId": 1, "name": "力", "note": "<useableItem>\n<Loot Pool: Rare>\n<Loot Pool: Tarot>\n<颜色:14>\n<Info Text Top>\n\\fr\\fs[18]•\\c[110]\\fi\\「解放巨神兵之力」\n\\fr\\fs[18]•施加了术式的魔法卡片。被设计成了就算是\n不懂魔法的新手也能简单发动效果的形式。\n\\fs[18]•\\{\\i[1852]\\}使用时，近战武器暂时巨大化并提升伤害。\n</Info Text Top>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 55, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 44, "dataId": 312, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 1129, "itypeId": 1, "name": "隐者", "note": "\n\n<颜色:14>\n<Info Text Top>\n\\fr\\fs[18]•\\c[110]\\fi\\「揭开这个世界的秘密」\n\\fr\\fs[18]•施加了术式的魔法卡片。被设计成了就算是\n不懂魔法的新手也能简单发动效果的形式。\n\\fs[18]•\\{\\i[1852]\\}使用时、一段时间内标记特殊的单位或地点\n</Info Text Top>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 56, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 44, "dataId": 312, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 1130, "itypeId": 1, "name": "命运之轮", "note": "<useableItem>\n<Loot Pool: Rare>\n<Loot Pool: Tarot>\n<颜色:14>\n<Info Text Top>\n\\fr\\fs[18]•\\c[110]\\fi\\「赌上灵魂的命运轮盘」\n\\fr\\fs[18]•施加了术式的魔法卡片。被设计成了就算是\n不懂魔法的新手也能简单发动效果的形式。\n\\fs[18]•\\{\\i[1852]\\}使用时、获得随机增益或减益效果\n</Info Text Top>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 57, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 44, "dataId": 312, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 1131, "itypeId": 1, "name": "正义", "note": "<useableItem>\n<Loot Pool: Rare>\n<Loot Pool: Tarot>\n<颜色:14>\n<Info Text Top>\n\\fr\\fs[18]•\\c[110]\\fi\\「尘归尘土归土」\n\\fr\\fs[18]•施加了术式的魔法卡片。被设计成了就算是\n不懂魔法的新手也能简单发动效果的形式。\n\\fs[18]•\\{\\i[1852]\\}使用时，消除玩家身上所有的状态\n</Info Text Top>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 58, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 44, "dataId": 312, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 1132, "itypeId": 1, "name": "倒吊人", "note": "\n\n<颜色:14>\n<Info Text Top>\n\\fr\\fs[18]•\\c[110]\\fi\\「太空坠落了…」\n\\fr\\fs[18]•施加了术式的魔法卡片。被设计成了就算是\n不懂魔法的新手也能简单发动效果的形式。\n\\fs[18]•\\{\\i[1852]\\}使用时、引发重力颠倒而坠落空中\n</Info Text Top>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 59, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 44, "dataId": 312, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 1133, "itypeId": 1, "name": "死神", "note": "<useableItem>\n<Loot Pool: Rare>\n<Loot Pool: Tarot>\n<颜色:14>\n", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 60, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 44, "dataId": 312, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 1134, "itypeId": 1, "name": "节制", "note": "<useableItem>\n<Loot Pool: Rare>\n<Loot Pool: Tarot>\n<颜色:14>\n<Info Text Top>\n\\fr\\fs[18]•\\c[110]\\fi\\「深渊的潘多拉魔盒」\n\\fr\\fs[18]•施加了术式的魔法卡片。被设计成了就算是\n不懂魔法的新手也能简单发动效果的形式。\n\\fs[18]•\\{\\i[1852]\\}使用时、在玩家附近位置召唤出奇怪的扭蛋机。\n</Info Text Top>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 61, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 44, "dataId": 312, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 1135, "itypeId": 1, "name": "恶魔", "note": "\n\n<颜色:14>\n<Info Text Top>\n\\fr\\fs[18]•\\c[110]\\fi\\「永不熄灭的诅咒之炎」\n\\fr\\fs[18]•施加了术式的魔法卡片。被设计成了就算是\n不懂魔法的新手也能简单发动效果的形式。\n\\fs[18]•\\{\\i[1852]\\}使用时、在指定地点燃起\\c[27]地獄火\\c[0]\n</Info Text Top>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 62, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "ザ・デビル", "effects": [{"code": 44, "dataId": 312, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 1136, "itypeId": 1, "name": "塔", "note": "<useableItem>\n\n<颜色:14>\n<Info Text Top>\n\\fr\\fs[18]•\\c[110]\\fi\\「深渊狂宴的邀请函」\n\\fr\\fs[18]•施加了术式的魔法卡片。被设计成了就算是\n不懂魔法的新手也能简单发动效果的形式。\n\\fs[18]•\\{\\i[1852]\\}使用时、召唤出\\c[113]深渊方尖碑\\c[0]\n</Info Text Top>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 63, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 44, "dataId": 312, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 1137, "itypeId": 1, "name": "星", "note": "<useableItem>\n<Loot Pool: Rare>\n<Loot Pool: Tarot>\n<颜色:14>\n<Info Text Top>\n\\fr\\fs[18]•\\c[110]\\fi\\「星乃归乡之道标」\n\\fr\\fs[18]•施加了术式的魔法卡片。被设计成了就算是\n不懂魔法的新手也能简单发动效果的形式。\n\\fs[18]•\\{\\i[1852]\\}使用时、在玩家附近位置召唤出可以回家的\\c[18]星之门\\c[0]\n</Info Text Top>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 64, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 44, "dataId": 312, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 1138, "itypeId": 1, "name": "月", "note": "<useableItem>\n\n<颜色:14>\n<Info Text Top>\n\\fr\\fs[18]•\\c[110]\\fi\\「背负变若的诅咒」\n\\fr\\fs[18]•施加了术式的魔法卡片。被设计成了就算是\n不懂魔法的新手也能简单发动效果的形式。\n\\fs[18]•\\{\\i[1852]\\}使用时、对玩家赋予\\c[31]「变若之力」\\c[0]状态。\n</Info Text Top>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 65, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 44, "dataId": 312, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 1139, "itypeId": 1, "name": "日", "note": "<useableItem>\n<Loot Pool: Rare>\n<Loot Pool: Tarot>\n<颜色:14>\n<Info Text Top>\n\\fr\\fs[18]•\\c[110]\\fi\\「浴火重生」\n\\fr\\fs[18]•施加了术式的魔法卡片。被设计成了就算是\n不懂魔法的新手也能简单发动效果的形式。\n\\fs[18]•\\{\\i[1852]\\}使用时，获得\\c[2]火鸟的祝福\\c[0]\n</Info Text Top>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 66, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 44, "dataId": 312, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 1140, "itypeId": 1, "name": "审判", "note": "<useableItem>\n\n<颜色:14>\n<Info Text Top>\n\\fr\\fs[18]•\\c[110]\\fi\\「细数你这家伙的罪恶吧！」\n\\fr\\fs[18]•施加了术式的魔法卡片。被设计成了就算是\n不懂魔法的新手也能简单发动效果的形式。\n\\fs[18]•\\{\\i[1852]\\}使用时、朝指定方向发射\\c[31]变成其他生物\\c[0]的魔法矢。\n</Info Text Top>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 67, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 44, "dataId": 312, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 1141, "itypeId": 1, "name": "世界", "note": "<useableItem>\n<Loot Pool: Rare>\n<Loot Pool: Tarot>\n<颜色:14>\n<Info Text Top>\n\\fr\\fs[18]•\\c[110]\\fi\\「邀请你前往那个神秘的箱庭世界…」\n\\fr\\fs[18]•施加了术式的魔法卡片。被设计成了就算是\n不懂魔法的新手也能简单发动效果的形式。\n\\fs[18]•\\{\\i[1845]\\}使用后重置掉落物相关事件的倒计时\n</Info Text Top>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 68, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 69, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 70, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 44, "dataId": 310, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 912, "itypeId": 1, "name": "杰克爆弹", "note": "<useableItem>\n<Loot Pool: Common>\n<Loot Pool: Uncommon>\n<Info Text Top>\n\\fr\\fs[18]•\\c[110]\\fi\\「给我看这边啊…！」\n\\fr\\fs[18]•充满了狂热火元素的炼金炸弹，因为极不稳定的性质\n只是拿在手上也会马上被引爆。\n•\\c[2]在炎热环境中会瞬间被引爆！\n</Info Text Top>", "occasion": 0, "price": 22, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 71, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 44, "dataId": 310, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 913, "itypeId": 1, "name": "冰霜杰克爆弹", "note": "<useableItem>\n<Loot Pool: Uncommon>\n<颜色:16>\n<Info Text Top>\n\\fr\\fs[18]•\\c[110]\\fi\\「给我看这边啊…！」\n\\fr\\fs[18]•充满了严寒冰元素的炼金炸弹，因为极不稳定的性质\n只是拿在手上也会马上被引爆。\n</Info Text Top>", "occasion": 0, "price": 22, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 72, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 44, "dataId": 310, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 914, "itypeId": 1, "name": "猛毒杰克爆弹", "note": "<useableItem>\n<Loot Pool: Uncommon>\n<颜色:16>\n<Info Text Top>\n\\fr\\fs[18]•\\c[110]\\fi\\「给我看这边啊…！」\n\\fr\\fs[18]•充满了剧毒的炼金炸弹，因为极不稳定的性质\n只是拿在手上也会马上被引爆。\n</Info Text Top>", "occasion": 0, "price": 22, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 73, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 74, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 22, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 75, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 44, "dataId": 310, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 197, "itypeId": 1, "name": "封存的时计", "note": "<useableItem>\n<Loot Pool: Epic>\n<颜色:15>\n<Info Text Top>\n\\fr\\fs[18]•一种特殊的魔法道具，内部封印着强力的时间术式。\n只要将其打碎，便会瞬间释放出效果，令包括时间在内的一切事物\n在一定范围内停滞数秒。\n</Info Text Top>", "occasion": 0, "price": 2000, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 76, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 77, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 78, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 79, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 80, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 81, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 82, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 83, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 84, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 85, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 86, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 11, "dataId": 0, "value1": 0.25, "value2": 45}], "hitType": 0, "iconIndex": 1266, "itypeId": 2, "name": "肉排片", "note": "<Loot Pool: Meat>\n<颜色:16>\n<Ingredients: Meat,Meat>\n<Info Text Top>\n\\fr\\fs[18]•从野兽身上狩取得到的肉片、\n\\fs[18]•富含蛋白质，简单烤一下就会很香！\n</Info Text Top>", "occasion": 0, "price": 5000, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 87, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 88, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 11, "dataId": 0, "value1": 0.15, "value2": 30}], "hitType": 0, "iconIndex": 1280, "itypeId": 2, "name": "鸡腿肉", "note": "<Loot Pool: Meat>\n<Ingredients: Chicken>\n<颜色:16>\n<Info Text Top>\n\\fr\\fs[18]•从各种各样的鸟类身上狩取的腿肉。\n\\fs[18]•肉质肥美，非常适合肉类菜肴。\n</Info Text Top>", "occasion": 0, "price": 2000, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 89, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 11, "dataId": 0, "value1": 0.25, "value2": 45}], "hitType": 0, "iconIndex": 1265, "itypeId": 2, "name": "大块兽肉", "note": "<Loot Pool: Meat>\n\n<Ingredients: Meat,Meat>\n\n<颜色:16>\n<Info Text Top>\n\\fr\\fs[18]•从魔物身上狩猎得到的肉。可以用来制作美味的肉料理。\n魔物吃人、人吃魔物，这是自然而然的事情。\n</Info Text Top>", "occasion": 0, "price": 3000, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 90, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 11, "dataId": 0, "value1": 0.15, "value2": 30}], "hitType": 0, "iconIndex": 1268, "itypeId": 2, "name": "小块兽肉", "note": "<Loot Pool: Meat>\n<Ingredients: Meat>\n<颜色:16>\n<Info Text Top>\n\\fr\\fs[18]•从魔物身上狩猎得到的肉。可以用来制作美味的肉料理。\n魔物吃人、人吃魔物，这是自然而然的事情。\n</Info Text Top>", "occasion": 0, "price": 1000, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 91, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 92, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 93, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 94, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 95, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 96, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 97, "animationId": 0, "consumable": false, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 11, "dataId": 0, "value1": 0, "value2": 4}], "hitType": 0, "iconIndex": 1310, "itypeId": 2, "name": "水怪的粘液", "note": "<Loot Pool: Junk2>\n<颜色:17>\n<Ingredients: <PERSON><PERSON>,<PERSON><PERSON>>\n<Info Text Top>\n\\fr\\fs[18]•水怪分泌的粘液，是炼金术中常用的材料\n\\fs[18]•有时也会作为润滑剂被使用\n</Info Text Top>\n", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 98, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 11, "dataId": 0, "value1": 0, "value2": 15}], "hitType": 0, "iconIndex": 1197, "itypeId": 2, "name": "走路菇", "note": "<Loot Pool: Junk2>\n\n<Ingredients: Mushroom>\n\n<颜色:16>\n<Info Text Top>\n\\fr\\fs[18]•从走路菇魔物身上摘下的蘑菇，已经不会动弹了\n\\fs[18]•切开后和普通的蘑菇看起来没什么两样\n</Info Text Top>\n", "occasion": 0, "price": 100, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 99, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 100, "animationId": 0, "consumable": false, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 11, "dataId": 0, "value1": 0, "value2": 2}], "hitType": 0, "iconIndex": 1310, "itypeId": 2, "name": "史莱姆的粘液", "note": "<Loot Pool: Junk2>\n<颜色:17>\n<Ingredients: Slime>\n<Info Text Top>\n\\fr\\fs[18]•史莱姆分泌的粘液，是炼金术中常用的材料\n\\fs[18]•有时也会作为润滑剂被使用\n</Info Text Top>\n", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 101, "animationId": 0, "consumable": false, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 11, "dataId": 0, "value1": 0, "value2": 15}], "hitType": 0, "iconIndex": 1187, "itypeId": 2, "name": "苹果", "note": "<Loot Pool: Fruit>\n<颜色:17>\n<Ingredients: Apple,Fruit,Red>\n<Info Text Top>\n\\fr\\fs[18]•新鲜采摘的水果，充满了清凉感\n\\fs[18]•直接吃也有稍微回复体力的效果。\n</Info Text Top>\n", "occasion": 0, "price": 300, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 102, "animationId": 0, "consumable": false, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 11, "dataId": 0, "value1": 0.05, "value2": 20}], "hitType": 0, "iconIndex": 1236, "itypeId": 2, "name": "咖喱块", "note": "<颜色:16>\n<Loot Pool: Flavoring>\n\n<Ingredients: CurryBlock>\n\n<Info Text Top>\n\\fr\\fs[18]•用异国的香辛料浓缩制成的辣味咖喱块\n\\fs[18]•和米饭的相性非常不错、也可以放入各种各样的食材\n</Info Text Top>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 103, "animationId": 0, "consumable": false, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 11, "dataId": 0, "value1": 0.05, "value2": 25}, {"code": 21, "dataId": 41, "value1": 0.05, "value2": 0}], "hitType": 0, "iconIndex": 1235, "itypeId": 2, "name": "白米饭", "note": "<Loot Pool: Rice>\n<Ingredients: Rice>\n<颜色:17>\n<Info Text Top>\n\\fr\\fs[18]•虽然很朴素，但是令人感到安心的白米饭\n\\fs[18]•可以直接吃，但也适合加入各种食材。\n</Info Text Top>", "occasion": 0, "price": 300, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 104, "animationId": 0, "consumable": false, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 11, "dataId": 0, "value1": 0, "value2": 15}], "hitType": 0, "iconIndex": 1193, "itypeId": 2, "name": "胡萝卜", "note": "<Loot Pool: Vegetable>\n<颜色:17>\n<Ingredients: Carrot,Vegetable,Red>\n<Info Text Top>\n\\fr\\fs[18]•常见的蔬菜，但在深渊中也能采集到就挺奇怪的\n\\fs[18]•要吃的话得好好料理一下\n</Info Text Top>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 105, "animationId": 0, "consumable": false, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 11, "dataId": 0, "value1": 0, "value2": 25}], "hitType": 0, "iconIndex": 1192, "itypeId": 2, "name": "土豆", "note": "<Loot Pool: Vegetable>\n<颜色:17>\n<Ingredients: Potato,Vegetable>\n<Info Text Top>\n\\fr\\fs[18]•常见的蔬菜，但在深渊中也能采集到就挺奇怪的\n\\fs[18]•要吃的话得好好料理一下\n</Info Text Top>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 106, "animationId": 0, "consumable": false, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 11, "dataId": 0, "value1": 0, "value2": 10}, {"code": 21, "dataId": 40, "value1": 0.3, "value2": 0}], "hitType": 0, "iconIndex": 1191, "itypeId": 2, "name": "辣椒", "note": "<Loot Pool: Hot>\n<颜色:17>\n<Ingredients: <PERSON>lli,Vegetable,Red>\n<Info Text Top>\n\\fr\\fs[18]•常见的蔬菜，但在深渊中也能采集到就挺奇怪的\n\\fs[18]•要吃的话得好好料理一下\n</Info Text Top>", "occasion": 0, "price": 0, "repeats": 1, "scope": 8, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 107, "animationId": 0, "consumable": false, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 11, "dataId": 0, "value1": 0, "value2": 15}], "hitType": 0, "iconIndex": 1184, "itypeId": 2, "name": "番茄", "note": "<Loot Pool: Vegetable>\n<颜色:17>\n<Ingredients: Tom<PERSON>,Vegetable,Red>\n<Info Text Top>\n\\fr\\fs[18]•常见的蔬菜，但在深渊中也能采集到就挺奇怪的\n\\fs[18]•要吃的话得好好料理一下\n</Info Text Top>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 108, "animationId": 0, "consumable": false, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 11, "dataId": 0, "value1": 0, "value2": 15}], "hitType": 0, "iconIndex": 1185, "itypeId": 2, "name": "香蕉", "note": "<Loot Pool: Fruit>\n<颜色:17>\n<Ingredients: Banana,Fruit>\n<Info Text Top>\n\\fr\\fs[18]•新鲜采摘的水果，充满了清凉感\n\\fs[18]•直接吃也有稍微回复体力的效果。\n</Info Text Top>\n", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 109, "animationId": 0, "consumable": false, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 11, "dataId": 0, "value1": 0, "value2": 15}], "hitType": 0, "iconIndex": 1186, "itypeId": 2, "name": "草莓", "note": "<颜色:17>\n<Loot Pool: Fruit>\n<Ingredients: Strawberry,Fruit,Red>\n<Info Text Top>\n\\fr\\fs[18]•新鲜采摘的水果，充满了清凉感\n\\fs[18]•直接吃也有稍微回复体力的效果。\n</Info Text Top>\n", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 110, "animationId": 0, "consumable": false, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 11, "dataId": 0, "value1": 0, "value2": 15}], "hitType": 0, "iconIndex": 1202, "itypeId": 2, "name": "葡萄", "note": "<Loot Pool: Fruit>\n<Ingredients: Fruit>\n<颜色:17>\n<Info Text Top>\n\\fr\\fs[18]•新鲜采摘的水果，充满了清凉感\n\\fs[18]•直接吃也有稍微回复体力的效果。\n</Info Text Top>\n", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 111, "animationId": 0, "consumable": false, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 11, "dataId": 0, "value1": 0, "value2": 15}], "hitType": 0, "iconIndex": 1203, "itypeId": 2, "name": "菠萝", "note": "<Ingredients: Pineapple,Fruit>\n<Loot Pool: Fruit>\n<颜色:17>\n<Info Text Top>\n\\fr\\fs[18]•新鲜采摘的水果，充满了清凉感\n\\fs[18]•直接吃也有稍微回复体力的效果。\n</Info Text Top>\n", "occasion": 0, "price": 0, "repeats": 1, "scope": 8, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 112, "animationId": 0, "consumable": false, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 11, "dataId": 0, "value1": 0, "value2": 15}], "hitType": 0, "iconIndex": 1204, "itypeId": 2, "name": "西瓜", "note": "<Ingredients: Watermelon,Fruit>\n<Loot Pool: Fruit>\n<颜色:17>\n<Info Text Top>\n\\fr\\fs[18]•新鲜采摘的水果，充满了清凉感\n\\fs[18]•直接吃也有稍微回复体力的效果。\n</Info Text Top>\n", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 113, "animationId": 0, "consumable": false, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 11, "dataId": 0, "value1": 0.05, "value2": 25}, {"code": 21, "dataId": 41, "value1": 0.05, "value2": 0}], "hitType": 0, "iconIndex": 1234, "itypeId": 2, "name": "意大利面", "note": "<Ingredients: Pasta>\n<Loot Pool: Flavoring>\n<颜色:17>\n<Info Text Top>\n\\fr\\fs[18]•柔韧有弹性的面条、什么料理都适合放进去\n\\fs[18]•浇上酱汁就可以很美味\n</Info Text Top>\n", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 114, "animationId": 0, "consumable": false, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 11, "dataId": 0, "value1": 0, "value2": 15}], "hitType": 0, "iconIndex": 1253, "itypeId": 2, "name": "番茄酱", "note": "<Loot Pool: Flavoring>\n<颜色:16>\n<Ingredients: Tomato,Vegetable,Red>\n<Info Text Top>\n\\fr\\fs[18]•用番茄制成的酱汁、酸酸甜甜的。\n\\fs[18]•但还是不去思考为什么怪物会掉落这个了…\n</Info Text Top>\n", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 115, "animationId": 0, "consumable": false, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 11, "dataId": 0, "value1": 0.05, "value2": 25}], "hitType": 0, "iconIndex": 1249, "itypeId": 2, "name": "小麦粉", "note": "<Loot Pool: Rice>\n<Ingredients: Flour>\n<颜色:17>\n<Info Text Top>\n\\fr\\fs[18]•磨碎小麦制成的面粉，可用于多种料理。\n</Info Text Top>\n", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 116, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 11, "dataId": 0, "value1": 0, "value2": 10}], "hitType": 0, "iconIndex": 1228, "itypeId": 2, "name": "平菇", "note": "<Loot Pool: Vegetable>\n<Ingredients: Mushroom>\n<颜色:17>\n<Info Text Top>\n\\fr\\fs[18]•在外面经常见到的普通蘑菇。\n然而，在深渊中能发现它却很不寻常……\n</Info Text Top>\n", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 117, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 11, "dataId": 0, "value1": 0, "value2": 10}], "hitType": 0, "iconIndex": 1229, "itypeId": 2, "name": "香菇", "note": "<Loot Pool: Vegetable>\n<Ingredients: Mushroom>\n<颜色:17>\n<Info Text Top>\n\\fr\\fs[18]•在外面经常见到的普通蘑菇。\n然而，在深渊中能发现它却很不寻常……\n</Info Text Top>\n", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 118, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 11, "dataId": 0, "value1": 0, "value2": 10}], "hitType": 0, "iconIndex": 1230, "itypeId": 2, "name": "蟹味菇", "note": "<Loot Pool: Vegetable>\n<Ingredients: Mushroom>\n<颜色:17>\n<Info Text Top>\n\\fr\\fs[18]•在外面经常见到的普通蘑菇。\n然而，在深渊中能发现它却很不寻常……\n</Info Text Top>\n", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 119, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 21, "dataId": 58, "value1": 0.66, "value2": 0}], "hitType": 0, "iconIndex": 1198, "itypeId": 2, "name": "紫丁香蘑", "note": "<Loot Pool: Vegetable>\n<Ingredients: Mushroom,Poison>\n<颜色:16>\n<Info Text Top>\n\\fr\\fs[18]•在外面经常见到的普通蘑菇。\n然而，在深渊中能发现它却很不寻常……\n</Info Text Top>\n", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 120, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 21, "dataId": 58, "value1": 0.88, "value2": 0}], "hitType": 0, "iconIndex": 1215, "itypeId": 2, "name": "月夜茸", "note": "<Loot Pool: Vegetable>\n<Ingredients: Mushroom,Poison>\n<颜色:16>\n<Info Text Top>\n\\fr\\fs[18]•在外面经常见到的普通蘑菇。\n然而，在深渊中能发现它却很不寻常……\n</Info Text Top>\n", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 121, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 11, "dataId": 0, "value1": 0, "value2": 30}], "hitType": 0, "iconIndex": 1213, "itypeId": 2, "name": "长裙竹荪", "note": "<Loot Pool: Vegetable>\n<Ingredients: Mushroom>\n<颜色:15>\n<Info Text Top>\n\\fr\\fs[18]•在外面也不经常见到的高级蘑菇。\n然而，在深渊中能发现它却很不寻常……\n</Info Text Top>\n", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 122, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 123, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 124, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 125, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 126, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 127, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 11, "dataId": 0, "value1": 0.1, "value2": 20}], "hitType": 0, "iconIndex": 1312, "itypeId": 2, "name": "章鱼触手", "note": "<Ingredients: Octopus>\n<Info Text Top>\n\\fr\\fs[18]•从海里捞到的断掉的章鱼触须，烤一下就会很香。\n\\fs[18]•也可以和其他食材一起烹调\n</Info Text Top>\n", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 128, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 129, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 130, "animationId": 0, "consumable": false, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 11, "dataId": 0, "value1": 0.1, "value2": 20}], "hitType": 0, "iconIndex": 1296, "itypeId": 2, "name": "鲷鱼", "note": "<Loot Pool: Fish>\n<Ingredients: Fish>\n<Info Text Top>\n\\fr\\fs[18]•从海里捕获的新鲜鱼类，可以做成刺身\n\\fs[18]•也可以和其他食材一起烹调\n</Info Text Top>\n", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 131, "animationId": 0, "consumable": false, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 11, "dataId": 0, "value1": 0.1, "value2": 20}], "hitType": 0, "iconIndex": 1297, "itypeId": 2, "name": "金枪鱼", "note": "<Loot Pool: Fish>\n<Ingredients: Fish,Maguro>\n<Info Text Top>\n\\fr\\fs[18]•从海里捕获的新鲜鱼类，可以做成刺身\n\\fs[18]•也可以和其他食材一起烹调\n</Info Text Top>\n", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 132, "animationId": 0, "consumable": false, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 11, "dataId": 0, "value1": 0.1, "value2": 20}], "hitType": 0, "iconIndex": 1298, "itypeId": 2, "name": "条石鲷", "note": "<Loot Pool: Fish>\n<Ingredients: Fish>\n<Info Text Top>\n\\fr\\fs[18]•从海里捕获的新鲜鱼类，可以做成刺身\n\\fs[18]•也可以和其他食材一起烹调\n</Info Text Top>\n", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 133, "animationId": 0, "consumable": false, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 11, "dataId": 0, "value1": 0.1, "value2": 20}], "hitType": 0, "iconIndex": 1299, "itypeId": 2, "name": "石斑鱼", "note": "<Loot Pool: Fish>\n<Ingredients: Fish>\n<Info Text Top>\n\\fr\\fs[18]•从海里捕获的新鲜鱼类，可以做成刺身\n\\fs[18]•也可以和其他食材一起烹调\n</Info Text Top>\n", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 134, "animationId": 0, "consumable": false, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 11, "dataId": 0, "value1": 0.1, "value2": 20}], "hitType": 0, "iconIndex": 1300, "itypeId": 2, "name": "秋刀鱼", "note": "<Loot Pool: Fish>\n<Ingredients: Fish>\n<Info Text Top>\n\\fr\\fs[18]•从海里捕获的新鲜鱼类，可以做成刺身\n\\fs[18]•也可以和其他食材一起烹调\n</Info Text Top>\n", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 135, "animationId": 0, "consumable": false, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 11, "dataId": 0, "value1": 0.1, "value2": 20}], "hitType": 0, "iconIndex": 1314, "itypeId": 2, "name": "甜虾", "note": "<Loot Pool: Seafood>\n<Ingredients: Shrimp,Seafood>\n<Info Text Top>\n\\fr\\fs[18]•从海里捕获的新鲜虾类，可以做成刺身\n\\fs[18]•也可以和其他食材一起烹调\n</Info Text Top>\n", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 136, "animationId": 0, "consumable": false, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 11, "dataId": 0, "value1": 0.2, "value2": 35}], "hitType": 0, "iconIndex": 1315, "itypeId": 2, "name": "大龙虾", "note": "<Loot Pool: Seafood>\n<Ingredients: Lobster,Seafood>\n<Info Text Top>\n\\fr\\fs[18]•从海里捕获的新鲜龙虾，要小心被夹到手\n也可以和其他食材一起烹调\n</Info Text Top>\n", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 137, "animationId": 0, "consumable": false, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 11, "dataId": 0, "value1": 0.1, "value2": 35}], "hitType": 0, "iconIndex": 1316, "itypeId": 2, "name": "毛蟹", "note": "<Loot Pool: Seafood>\n<Ingredients: Crab,Seafood>\n<Info Text Top>\n\\fr\\fs[18]•从海里捕获的新鲜螃蟹，要小心被夹到手\n也可以和其他食材一起烹调\n</Info Text Top>\n", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 138, "animationId": 0, "consumable": false, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 11, "dataId": 0, "value1": 0.3, "value2": 35}], "hitType": 0, "iconIndex": 1317, "itypeId": 2, "name": "椰子蟹", "note": "<Loot Pool: Seafood>\n<Ingredients: Crab,Seafood>\n<Info Text Top>\n\\fr\\fs[18]•岸上捕获的新鲜螃蟹，要小心被夹到手\n也可以和其他食材一起烹调\n</Info Text Top>\n", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 139, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 140, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 141, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 142, "animationId": 0, "consumable": false, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 11, "dataId": 0, "value1": 0.1, "value2": 20}], "hitType": 0, "iconIndex": 1329, "itypeId": 2, "name": "鲑鱼", "note": "<Loot Pool: Fish>\n<Ingredients: Fish>\n<Info Text Top>\n\\fr\\fs[18]•从海里捕获的新鲜虾类，可以做成刺身\n\\fs[18]•也可以和其他食材一起烹调\n</Info Text Top>\n", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 143, "animationId": 0, "consumable": false, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 11, "dataId": 0, "value1": 0.2, "value2": 45}], "hitType": 0, "iconIndex": 1330, "itypeId": 2, "name": "帝王鲑", "note": "<颜色:15>\n<Loot Pool: Fish>\n<Ingredients: Fish>\n<Info Text Top>\n\\fr\\fs[18]•从海里捕获的新鲜虾类，可以做成刺身\n\\fs[18]•也可以和其他食材一起烹调\n</Info Text Top>\n", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 144, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 145, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 146, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 147, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 148, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 149, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 150, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 151, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 152, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 153, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 11, "dataId": 0, "value1": 0.05, "value2": 10}], "hitType": 0, "iconIndex": 1287, "itypeId": 2, "name": "很大的蛋", "note": "<Ingredients: Egg>\n<Info Text Top>\n\\fr\\fs[18]•从深渊里带回来的巨大的蛋。到底是什么生物的蛋呢\n并没有深入思考这个问题，反正都是蛋嘛\n</Info Text Top>\n", "occasion": 0, "price": 600, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 154, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 155, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 156, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 157, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 158, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 159, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 160, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 161, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 162, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 163, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 164, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 165, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 166, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 167, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 168, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 169, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 170, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 1326, "itypeId": 1, "name": "炸薯条", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 171, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 172, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 173, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 11, "dataId": 0, "value1": 0.05, "value2": 0}], "hitType": 0, "iconIndex": 1241, "itypeId": 2, "name": "香蕉妖精", "note": "<Loot Pool: Junk1>\n<Ingredients: Banana,Fruit>\n<Info Text Top>\n\\fr\\fs[18]•……这到底是什么玩意？\n</Info Text Top>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 174, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 1242, "itypeId": 2, "name": "天妇罗妖精", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 175, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 11, "dataId": 0, "value1": 0.05, "value2": 0}], "hitType": 0, "iconIndex": 1243, "itypeId": 2, "name": "苹果妖精", "note": "<Loot Pool: Junk1>\n<Ingredients: Apple,Fruit,Red>\n<Info Text Top>\n\\fr\\fs[18]•……这到底是什么玩意？\n</Info Text Top>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 176, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 177, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 178, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 179, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 180, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 181, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 182, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 183, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 184, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 185, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 186, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 187, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 188, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 189, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 190, "animationId": 0, "consumable": false, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 44, "dataId": 308, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 192, "itypeId": 1, "name": "百合同人本", "note": "<颜色:14>", "occasion": 0, "price": 5000, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 191, "animationId": 0, "consumable": false, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 44, "dataId": 308, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 193, "itypeId": 1, "name": "萌萌同人本", "note": "<颜色:14>", "occasion": 0, "price": 5000, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 192, "animationId": 0, "consumable": false, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 44, "dataId": 308, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 194, "itypeId": 1, "name": "猫娘同人本", "note": "<颜色:14>", "occasion": 0, "price": 5000, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 193, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 194, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 195, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 196, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 197, "animationId": 0, "consumable": false, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 532, "itypeId": 3, "name": "红髓液", "note": "<颜色:1>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 198, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 531, "itypeId": 3, "name": "妹妹的T恤", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 199, "animationId": 0, "consumable": false, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 528, "itypeId": 1, "name": "妹妹的\"什么都行\"券", "note": "<颜色:13>\n<Info Text Top>\n\\fr\\fs[18]•\\c[110]\\fi\\「奖励券上有很多涂涂改改的痕迹…」\n\\fr\\fs[18]•为了表扬一直以来非常努力的哥哥、妹妹亲手画的奖励券。\n凭着这张券就能随意向妹妹撒娇、不管是多么任性的请求都会被同意。\n但就算没有这个，妹妹也一直都想多为哥哥做些什么——\n</Info Text Top>", "occasion": 3, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 200, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 201, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 202, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 203, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 204, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 205, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 206, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 207, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 208, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 209, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 210, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 211, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 212, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 213, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 214, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 215, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 216, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 217, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 218, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 219, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 220, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 221, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 222, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 223, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 224, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 225, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 226, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 227, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 228, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 229, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 230, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 231, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 232, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 233, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 234, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 235, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 236, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 237, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 238, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 239, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 1377, "itypeId": 1, "name": "史莱姆果冻", "note": "<完美料理>\n<史莱姆料理>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 240, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 1361, "itypeId": 3, "name": "咖喱饭", "note": "<颜色:16>\n<完美料理>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 241, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "苹果派", "note": "<完美料理>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 242, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 1362, "itypeId": 3, "name": "烤苹果", "note": "<完美料理>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 243, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 3, "name": "炒贝类", "note": "<颜色:16>\n<完美料理>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 244, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 1380, "itypeId": 3, "name": "煎鱼", "note": "<颜色:16>\n<完美料理>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 245, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 1525, "itypeId": 3, "name": "炒蘑菇", "note": "<颜色:16>\n<完美料理>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 246, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 1379, "itypeId": 1, "name": "烤串", "note": "<完美料理>\n<肉料理>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 247, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 1360, "itypeId": 3, "name": "饭团", "note": "<完美料理>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 248, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 3, "name": "肉排咖喱饭", "note": "<完美料理>\n<肉料理>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 249, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 1382, "itypeId": 1, "name": "烤肉块", "note": "<完美料理>\n<肉料理>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 250, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 1378, "itypeId": 1, "name": "肉汤", "note": "<完美料理>\n<肉料理>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 251, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "牛排肉", "note": "<完美料理>\n<肉料理>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 252, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "牛肉饭团", "note": "<完美料理>\n<肉料理>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 253, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 1377, "itypeId": 1, "name": "奇怪的史莱姆果冻", "note": "<完美料理>\n<史莱姆料理>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 254, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 1377, "itypeId": 1, "name": "奇怪的史莱姆果冻", "note": "<黑暗料理>\n<史莱姆料理>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 255, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 1377, "itypeId": 1, "name": "奇怪的史莱姆果冻", "note": "<完美料理>\n<史莱姆料理>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 256, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 1377, "itypeId": 1, "name": "奇怪的史莱姆果冻", "note": "<完美料理>\n<史莱姆料理>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 257, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 1363, "itypeId": 1, "name": "烤香蕉", "note": "<完美料理>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 258, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 1364, "itypeId": 1, "name": "罗宋汤", "note": "<完美料理>\n<肉料理>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 259, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 1381, "itypeId": 1, "name": "烤章鱼脚", "note": "<完美料理>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 260, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "蔬菜沙拉", "note": "<完美料理>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 261, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "土豆泥", "note": "<完美料理>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 262, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 1367, "itypeId": 1, "name": "萝卜菇片汤", "note": "<完美料理>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 263, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "意面", "note": "<完美料理>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 264, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "番茄意面", "note": "<完美料理>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 265, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "海鲜意面", "note": "<完美料理>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 266, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "鸡蛋咖喱饭", "note": "<完美料理>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 267, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "蛋包饭", "note": "<完美料理>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 268, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "番茄蛋包饭", "note": "<完美料理>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 269, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "海鲜炖饭", "note": "<完美料理>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 270, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "菠萝饮", "note": "<完美料理>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 271, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "冲绳杂炒", "note": "<完美料理>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 272, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "炸鸡桶", "note": "<完美料理>\n<肉料理>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 273, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "炸鸡腿", "note": "<完美料理>\n<肉料理>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 274, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "天妇罗饭团", "note": "<完美料理>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 275, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "焗龙虾", "note": "<完美料理>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 276, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "煮螃蟹", "note": "<完美料理>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 277, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "金枪鱼头", "note": "<完美料理>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 278, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "章鱼丸子", "note": "<完美料理>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 279, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "汉堡肉", "note": "<完美料理>\n<肉料理>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 280, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "辣土豆泥", "note": "<完美料理>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 281, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "辣椒水", "note": "<黑暗料理>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 282, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "水怪软糖", "note": "<完美料理>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 283, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "水怪刨冰", "note": "<完美料理>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 284, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "水怪软糖（红）", "note": "<完美料理>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 285, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "煎薄饼", "note": "<完美料理>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 286, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "草莓煎薄饼", "note": "<完美料理>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 287, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 1366, "itypeId": 1, "name": "番茄汤", "note": "<完美料理>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 288, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "草莓", "note": "<完美料理>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 289, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 290, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 4, "name": "————恶魔交易————", "note": "", "occasion": 3, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 291, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 1760, "itypeId": 1, "name": "フェフ", "note": "<颜色:14>\n<商品描述:ゲーム会社ソーニーが開発した最新のゲーム機>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 292, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 1761, "itypeId": 1, "name": "ウルズ", "note": "<颜色:14>\n<商品描述:ゲーム会社ソーニーが開発した最新のゲーム機>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 293, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 1762, "itypeId": 1, "name": "ソリサズ", "note": "<颜色:14>\n<商品描述:ゲーム会社ソーニーが開発した最新のゲーム機>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 294, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 1763, "itypeId": 1, "name": "アンスズ", "note": "<颜色:14>\n<商品描述:ゲーム会社ソーニーが開発した最新のゲーム機>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 295, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 1764, "itypeId": 1, "name": "ライド", "note": "<颜色:14>\n<商品描述:ゲーム会社ソーニーが開発した最新のゲーム機>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 296, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 297, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 298, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 299, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 300, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 301, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 302, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 303, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 304, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 305, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 306, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 307, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 308, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 309, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 310, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "————材料类————", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 311, "animationId": 0, "consumable": false, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 605, "itypeId": 1, "name": "蓝辉石", "note": "<颜色:16>\n<Loot Pool: Common>\n<Info Text Top>\n\\fr\\fs[18]•散发着迷人光芒的蓝色宝石，虽然没有什么实用价值，\n但在某个角落，或许有人愿意为它支付高昂的价格……又或者另有所图？\n</Info Text Top>\n", "occasion": 3, "price": 2500, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 312, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 44, "dataId": 309, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 607, "itypeId": 1, "name": "魔力小凝胶", "note": "<Loot Pool: Junk2>\n<Loot Pool: Common>\n<颜色:16>\n<Info Text Top>\n\\fr\\fs[18]•纯粹由纯净的魔力凝固而成的胶状物质。 \n可以食用，但根据魔法素养的差异，感受到的味道似乎区别很大？\n最常见的用途还是作为构筑魔法的耗材而使用\n</Info Text Top>\n", "occasion": 0, "price": 500, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 313, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 90, "itypeId": 1, "name": "福袋？", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 314, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 315, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 316, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 317, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 318, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 319, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 320, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 321, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 322, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 323, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 324, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 325, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 326, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 327, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 328, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 329, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 330, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 331, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 332, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 333, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 334, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 335, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 336, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 337, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 338, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 339, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 340, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 341, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 342, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 343, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 344, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 345, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 346, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 347, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 348, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 349, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 350, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 351, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 352, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 353, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 354, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 355, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 356, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 357, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 358, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 359, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 360, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 361, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 362, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 363, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 364, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 365, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 366, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 367, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 368, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 369, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 370, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 371, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 372, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 373, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 374, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 375, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 376, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 377, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 378, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 379, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 380, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 381, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 382, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 383, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 384, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 385, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 386, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 387, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 388, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 389, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 390, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 391, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 392, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 393, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 394, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 395, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 396, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 397, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 398, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 399, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "——特殊概念——", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 400, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 3, "name": "晴天", "note": "<Loot Pool: sunnyDay>\n", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 401, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 3, "name": "阴天", "note": "<Loot Pool: cloudyDay>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 402, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 3, "name": "雨天", "note": "<Loot Pool: rainyDay>", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 403, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 3, "name": "炎热天", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 404, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 3, "name": "妹妹的约定:不要受伤", "note": "", "occasion": 3, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 405, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 3, "name": "妹妹的约定:肉料理", "note": "", "occasion": 3, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 406, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 407, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 408, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 409, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 410, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 411, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 412, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 413, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 414, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 415, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 416, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 417, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 418, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 419, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 420, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 421, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 422, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 423, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 424, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 425, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 426, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 427, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 428, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 429, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 430, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 431, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 432, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 433, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 434, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 435, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 436, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 437, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 438, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 439, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 440, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 441, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 442, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 443, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 444, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 445, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 446, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 447, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 448, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 449, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 450, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 451, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 452, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 453, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 454, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 455, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 456, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 457, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 458, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 459, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 460, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 461, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 462, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 463, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 464, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 465, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 466, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 467, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 468, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 469, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 470, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 471, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 472, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 473, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 474, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 475, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 476, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 477, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 478, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 479, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 480, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 481, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 482, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 483, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 484, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 485, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 486, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 487, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 488, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 489, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 490, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 491, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 492, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 493, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 494, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 495, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 496, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 497, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 498, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 499, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}, {"id": 500, "animationId": 0, "consumable": true, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "itypeId": 1, "name": "", "note": "", "occasion": 0, "price": 0, "repeats": 1, "scope": 7, "speed": 0, "successRate": 100, "tpGain": 0}]