{"autoplayBgm": false, "autoplayBgs": false, "battleback1Name": "Abyss", "battleback2Name": "gray bar", "bgm": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 8, "note": "", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": true, "tilesetId": 1, "width": 10, "data": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "EV001", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 2, "characterIndex": 1}, "list": [{"code": 356, "indent": 0, "parameters": [">外发光效果 : 固定对话框外发光 : 颜色[11] : 厚度[2] : 偏移[2,2]"]}, {"code": 231, "indent": 0, "parameters": [1, "esfan_011_1440", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 355, "indent": 0, "parameters": ["var id = \"noise1\";"]}, {"code": 655, "indent": 0, "parameters": ["var filterTarget = 0;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.createFilter(id, \"noise\", filterTarget);"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.setFilter(id, [0.1]);"]}, {"code": 231, "indent": 0, "parameters": [30, "", 0, 0, 0, 0, 100, 100, 185, 2]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[30] : 创建动画序列 : 动画序列[5]"]}, {"code": 356, "indent": 0, "parameters": [">图片快捷操作 : 图片[1] : 修改单属性 : 位置Y[-360] : 时间[3600]"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 241, "indent": 0, "parameters": [{"name": "m-art_<PERSON>", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["AudioManager.fadeInBgm(3)"]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.startFadeIn(360);"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(0);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(1);"]}, {"code": 231, "indent": 0, "parameters": [90, "black", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 0, "parameters": [90, 0, 0, 0, 0, 0, 100, 100, 125, 0, 120, false]}, {"code": 355, "indent": 0, "parameters": ["let lang = ConfigManager.language;  if (lang > 2) lang = 2;"]}, {"code": 655, "indent": 0, "parameters": ["let imgName = 'map_name/abyss_introduction' + lang; "]}, {"code": 655, "indent": 0, "parameters": ["       QJ.MPMZ.Shoot({"]}, {"code": 655, "indent": 0, "parameters": ["            img: img<PERSON>ame,onScreen: true,"]}, {"code": 655, "indent": 0, "parameters": ["            position: [['S', 941], ['S', 500]],groupName:['abyss'],"]}, {"code": 655, "indent": 0, "parameters": ["            initialRotation: ['S', 0],imgRotation: ['F'],"]}, {"code": 655, "indent": 0, "parameters": ["            opacity: '0|0~120/1~9999/1',z: \"A\","]}, {"code": 655, "indent": 0, "parameters": ["            onScreen: true,moveType: ['S', 0],"]}, {"code": 655, "indent": 0, "parameters": ["            existData: [ {t:['Time',180],d:[0,60]} ],"]}, {"code": 655, "indent": 0, "parameters": ["        });"]}, {"code": 230, "indent": 0, "parameters": [180]}, {"code": 232, "indent": 0, "parameters": [90, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, true]}, {"code": 355, "indent": 0, "parameters": ["var IMG = 'char (1)';"]}, {"code": 655, "indent": 0, "parameters": ["var path = \"characters\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(15, path, IMG, 0, 600, 1300, 100, 100, 0, 0)"]}, {"code": 356, "indent": 0, "parameters": [">图片快捷操作 : 图片[15] : 修改单属性 : 位置Y[-1300] : 时间[1500]"]}, {"code": 356, "indent": 0, "parameters": [">图片快捷操作 : 图片[15] : 修改单属性 : 透明度[125] : 时间[500]"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(2);"]}, {"code": 355, "indent": 0, "parameters": ["var IMG = 'char (6)';"]}, {"code": 655, "indent": 0, "parameters": ["var path = \"characters\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(10, path, IMG, 0, -600, 0, 100, 100, 0, 0)"]}, {"code": 356, "indent": 0, "parameters": [">图片快捷操作 : 图片[10] : 修改单属性 : 位置X[2000] : 时间[1500]"]}, {"code": 356, "indent": 0, "parameters": [">图片快捷操作 : 图片[10] : 修改单属性 : 透明度[125] : 时间[500]"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(3);"]}, {"code": 355, "indent": 0, "parameters": ["var IMG = 'char (5)';"]}, {"code": 655, "indent": 0, "parameters": ["var path = \"characters\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(11, path, IMG, 0, 1200, 800, 100, 100, 0, 0)"]}, {"code": 356, "indent": 0, "parameters": [">图片快捷操作 : 图片[11] : 修改单属性 : 位置X[-400] : 时间[1500]"]}, {"code": 356, "indent": 0, "parameters": [">图片快捷操作 : 图片[11] : 修改单属性 : 位置Y[-1400] : 时间[1500]"]}, {"code": 356, "indent": 0, "parameters": [">图片快捷操作 : 图片[11] : 修改单属性 : 透明度[125] : 时间[500]"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(4);"]}, {"code": 355, "indent": 0, "parameters": ["var IMG = 'char (4)';"]}, {"code": 655, "indent": 0, "parameters": ["var path = \"characters\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(12, path, IMG, 0, 1800, 0, 100, 100, 0, 0)"]}, {"code": 356, "indent": 0, "parameters": [">图片快捷操作 : 图片[12] : 修改单属性 : 位置X[-1000] : 时间[1500]"]}, {"code": 356, "indent": 0, "parameters": [">图片快捷操作 : 图片[12] : 修改单属性 : 透明度[125] : 时间[500]"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(5);"]}, {"code": 355, "indent": 0, "parameters": ["var IMG = 'char (3)';"]}, {"code": 655, "indent": 0, "parameters": ["var path = \"characters\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(14, path, IMG, 0, -600, -1100, 100, 100, 0, 0)"]}, {"code": 356, "indent": 0, "parameters": [">图片快捷操作 : 图片[14] : 修改单属性 : 位置X[1800] : 时间[1500]"]}, {"code": 356, "indent": 0, "parameters": [">图片快捷操作 : 图片[14] : 修改单属性 : 位置Y[1100] : 时间[1500]"]}, {"code": 356, "indent": 0, "parameters": [">图片快捷操作 : 图片[14] : 修改单属性 : 透明度[125] : 时间[500]"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(6);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(7);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(8);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(9);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(10);"]}, {"code": 242, "indent": 0, "parameters": [2]}, {"code": 355, "indent": 0, "parameters": ["var id = \"noise1\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.moveFilter(id, [0], 30);"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.eraseFilterAfterMove(id);"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 108, "indent": 0, "parameters": ["清理背包里异物"]}, {"code": 355, "indent": 0, "parameters": ["var armorIdsToRemove = [152,153,154,155,156,157,158,159];"]}, {"code": 655, "indent": 0, "parameters": ["var armors = $gameParty.allItems().filter(function(item) {"]}, {"code": 655, "indent": 0, "parameters": ["    return item && DataManager.isArmor(item) && armorIdsToRemove.includes(item.baseItemId);"]}, {"code": 655, "indent": 0, "parameters": ["});"]}, {"code": 655, "indent": 0, "parameters": ["armors.forEach(function(armor) {"]}, {"code": 655, "indent": 0, "parameters": ["    $gameParty.loseItem(armor, 1);"]}, {"code": 655, "indent": 0, "parameters": ["});"]}, {"code": 355, "indent": 0, "parameters": ["const maxPictures = 90; "]}, {"code": 655, "indent": 0, "parameters": ["for (let pictureId = 2; pictureId <= maxPictures; pictureId++) {"]}, {"code": 655, "indent": 0, "parameters": ["    let picture = $gameScreen.picture(pictureId);"]}, {"code": 655, "indent": 0, "parameters": ["    if (picture) {"]}, {"code": 655, "indent": 0, "parameters": ["        picture.drill_PCE_stopEffect();"]}, {"code": 655, "indent": 0, "parameters": ["        $gameScreen.erasePicture(pictureId);"]}, {"code": 655, "indent": 0, "parameters": ["        $gameScreen.setPictureRemoveCommon(pictureId);"]}, {"code": 655, "indent": 0, "parameters": ["    }"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 201, "indent": 0, "parameters": [0, 18, 8, 46, 8, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 1, "y": 6}, {"id": 2, "name": "场景初始化", "note": "<resetSelfSwitch>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 0, "characterIndex": 3}, "list": [{"code": 108, "indent": 0, "parameters": ["加载多语言地图对话模板"]}, {"code": 355, "indent": 0, "parameters": ["const mapId = $gameMap.mapId();"]}, {"code": 655, "indent": 0, "parameters": ["const key = 'MapEventDialogue' + mapId; "]}, {"code": 655, "indent": 0, "parameters": ["if (!window[key]) { "]}, {"code": 655, "indent": 0, "parameters": ["chahuiUtil.loadMapEventDialogue();"]}, {"code": 655, "indent": 0, "parameters": ["this.wait(15);"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["补充监听器"]}, {"code": 355, "indent": 0, "parameters": ["QJ.MPMZ.tl._imoutoUtilCheckInitialization()"]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 121, "indent": 0, "parameters": [31, 31, 1]}, {"code": 111, "indent": 0, "parameters": [12, "chahuiUtil.checkScriptExecutability()"]}, {"code": 355, "indent": 1, "parameters": ["let code = $gameStrings.value(20);"]}, {"code": 655, "indent": 1, "parameters": ["eval(code);"]}, {"code": 655, "indent": 1, "parameters": ["$gameStrings.setValue(20, \"\");"]}, {"code": 119, "indent": 1, "parameters": ["end"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 13, 0, 0, 3]}, {"code": 355, "indent": 1, "parameters": ["let ID = $gameVariables.value(13);"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.event(ID).start()"]}, {"code": 122, "indent": 1, "parameters": [13, 13, 0, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameMap.event(1).start()"]}, {"code": 122, "indent": 1, "parameters": [13, 13, 0, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["end"]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 7}, {"id": 3, "name": "EV003", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 4, "pattern": 1, "characterIndex": 1}, "list": [{"code": 355, "indent": 0, "parameters": ["$gameScreen.startFadeOut(4);"]}, {"code": 230, "indent": 0, "parameters": [4]}, {"code": 231, "indent": 0, "parameters": [1, "esfan_011_1440", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 355, "indent": 0, "parameters": ["let lang = $gameVariables.value(1);"]}, {"code": 655, "indent": 0, "parameters": ["let IMG = \"abyss_introduction\" + lang;"]}, {"code": 655, "indent": 0, "parameters": ["let path = \"map_name\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(2, path, IMG, 1, 960, 540, 100, 100, 0, 0)"]}, {"code": 356, "indent": 0, "parameters": [">图片快捷操作 : 图片[1] : 修改单属性 : 位置Y[-50] : 时间[250]"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.startFadeIn(150);"]}, {"code": 355, "indent": 0, "parameters": ["$gameSystem.add_minute(8)"]}, {"code": 117, "indent": 0, "parameters": [8]}, {"code": 108, "indent": 0, "parameters": ["清理背包里异物"]}, {"code": 355, "indent": 0, "parameters": ["var armorIdsToRemove = [152,153,154,155,156,157,158,159];"]}, {"code": 655, "indent": 0, "parameters": ["var armors = $gameParty.allItems().filter(function(item) {"]}, {"code": 655, "indent": 0, "parameters": ["    return item && DataManager.isArmor(item) && armorIdsToRemove.includes(item.baseItemId);"]}, {"code": 655, "indent": 0, "parameters": ["});"]}, {"code": 655, "indent": 0, "parameters": ["armors.forEach(function(armor) {"]}, {"code": 655, "indent": 0, "parameters": ["    $gameParty.loseItem(armor, 1);"]}, {"code": 655, "indent": 0, "parameters": ["});"]}, {"code": 230, "indent": 0, "parameters": [50]}, {"code": 356, "indent": 0, "parameters": [">图片快捷操作 : 图片[2] : 修改单属性 : 透明度[255] : 时间[60]"]}, {"code": 355, "indent": 0, "parameters": ["$gameSystem.add_minute(8)"]}, {"code": 117, "indent": 0, "parameters": [8]}, {"code": 230, "indent": 0, "parameters": [50]}, {"code": 355, "indent": 0, "parameters": ["$gameSystem.add_minute(8)"]}, {"code": 117, "indent": 0, "parameters": [8]}, {"code": 230, "indent": 0, "parameters": [50]}, {"code": 355, "indent": 0, "parameters": ["$gameSystem.add_minute(8)"]}, {"code": 117, "indent": 0, "parameters": [8]}, {"code": 230, "indent": 0, "parameters": [50]}, {"code": 355, "indent": 0, "parameters": ["$gameSystem.add_minute(8)"]}, {"code": 117, "indent": 0, "parameters": [8]}, {"code": 356, "indent": 0, "parameters": ["SaveNow"]}, {"code": 355, "indent": 0, "parameters": ["$gamePlayer.startOpacity(20, 255)"]}, {"code": 230, "indent": 0, "parameters": [50]}, {"code": 121, "indent": 0, "parameters": [5, 5, 0]}, {"code": 201, "indent": 0, "parameters": [0, 18, 8, 46, 8, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 2, "y": 7}, {"id": 4, "name": "哥哥的梦境", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 241, "indent": 0, "parameters": [{"name": "m-art_Rest", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["AudioManager.fadeInBgm(3)"]}, {"code": 231, "indent": 0, "parameters": [1, "magician'sRoom4", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 355, "indent": 0, "parameters": ["let pic = $gameScreen.picture(1);"]}, {"code": 655, "indent": 0, "parameters": ["if (pic) {"]}, {"code": 655, "indent": 0, "parameters": ["var id = \"blur1\";"]}, {"code": 655, "indent": 0, "parameters": ["var filterTarget = 5001;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.createFilter(id, \"blur\", filterTarget);"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.setFilter( id ,[2]);"]}, {"code": 655, "indent": 0, "parameters": ["   }"]}, {"code": 231, "indent": 0, "parameters": [15, "", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[15] : 创建动画序列 : 动画序列[5]"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(0);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(1);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(2);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(3);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(4);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(5);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(6);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(7);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(8);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(9);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(10);"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["…………"]}, {"code": 401, "indent": 0, "parameters": ["…………"]}, {"code": 122, "indent": 0, "parameters": [13, 13, 0, 0, 8]}, {"code": 201, "indent": 0, "parameters": [0, 4, 6, 5, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 9, "y": 7}]}