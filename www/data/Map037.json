{"autoplayBgm": false, "autoplayBgs": false, "battleback1Name": "Abyss", "battleback2Name": "gray bar", "bgm": {"name": "PerituneMaterial_Lost_place_loop", "pan": 0, "pitch": 100, "volume": 90}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 999, "height": 77, "note": "<深渊>\n<All Restrict Region: 254,255>\n<All Allow Region: 200,250>\n<Event Restrict Region: 8>", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": true, "tilesetId": 15, "width": 100, "data": [5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2387, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5892, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5896, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2387, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 5928, 5916, 5916, 5916, 5896, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2387, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6275, 6274, 6274, 6274, 5928, 5896, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5892, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5896, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2387, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2850, 2836, 2836, 2836, 2852, 2090, 2862, 6281, 6280, 6280, 6280, 6275, 5928, 5916, 5916, 5916, 5916, 5916, 5896, 5888, 5888, 5888, 5888, 5888, 5912, 6162, 6148, 6148, 6148, 6148, 6148, 6148, 6148, 6148, 6148, 6148, 6148, 6148, 6148, 6148, 6148, 6148, 6148, 6148, 6148, 6148, 6148, 6148, 6148, 6148, 6164, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2387, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2832, 2816, 2816, 2816, 2840, 2065, 2084, 2859, 2838, 2836, 2852, 6281, 6275, 6274, 6274, 6274, 6274, 6274, 5928, 5916, 5916, 5916, 5896, 5888, 5912, 6144, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6152, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2387, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2832, 2816, 2816, 2816, 2840, 2064, 2050, 2084, 2832, 2816, 2818, 2852, 6281, 6280, 6280, 6280, 6280, 6280, 6275, 6274, 6274, 6274, 5904, 5888, 5912, 6144, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6152, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2387, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2832, 2816, 2816, 2816, 2840, 2064, 2048, 2072, 2832, 2816, 2816, 2818, 2836, 2836, 2836, 2836, 2836, 2852, 6281, 6280, 6280, 6280, 5904, 5888, 5912, 6144, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6152, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2387, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2832, 2816, 2816, 2816, 2840, 2064, 2048, 2072, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2836, 2836, 2836, 2852, 5904, 5888, 5912, 6144, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6152, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2387, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2832, 2816, 2816, 2816, 2840, 2064, 2048, 2072, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2844, 2854, 5904, 5888, 5912, 6168, 6156, 6156, 6156, 6156, 6156, 6156, 6156, 6156, 6156, 6156, 6156, 6156, 6156, 6156, 6156, 6156, 6156, 6156, 6156, 6156, 6156, 6156, 6156, 6156, 6166, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2387, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2832, 2816, 2816, 2816, 2840, 2064, 2048, 2072, 2834, 2844, 2844, 2844, 2844, 2824, 2816, 2816, 2816, 2816, 2820, 2854, 5922, 5908, 5889, 5888, 5890, 5909, 5921, 5921, 5921, 5921, 5921, 5921, 5910, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5889, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2387, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2856, 2844, 2824, 2820, 2854, 2088, 2076, 2086, 2860, 5922, 5908, 5908, 5924, 2856, 2844, 2844, 2844, 2844, 2854, 5922, 5889, 5888, 5888, 5888, 5888, 5912, 6274, 6274, 6274, 6274, 6274, 6274, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5892, 5916, 5916, 5916, 5916, 5916, 5896, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2387, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5890, 5908, 5924, 2856, 2854, 5922, 5908, 5908, 5908, 5908, 5889, 5888, 5888, 5912, 6378, 6382, 5931, 5933, 6379, 6378, 5928, 5916, 5896, 5888, 5888, 5888, 5912, 6280, 6280, 6280, 6280, 6280, 6280, 5904, 5888, 5888, 5888, 5888, 5888, 5892, 5916, 5916, 5916, 5916, 5916, 5916, 5926, 6370, 6370, 6370, 6370, 6370, 5928, 5896, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2387, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5890, 5908, 5908, 5889, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2850, 2836, 2836, 2836, 2836, 2852, 6275, 6274, 5928, 5916, 5916, 5916, 5926, 4002, 3988, 3988, 3988, 3988, 4004, 5928, 5916, 5916, 5916, 5916, 5916, 5926, 6370, 6370, 6370, 6370, 6370, 6370, 6374, 6376, 6376, 6376, 6376, 6376, 6371, 5928, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5900, 5926, 2387, 5928, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2832, 2816, 2816, 2816, 2816, 2840, 6281, 6280, 6275, 6274, 6274, 6274, 6278, 4008, 3996, 3996, 3996, 3996, 4006, 6275, 6274, 6274, 6274, 6274, 6274, 6278, 6376, 6376, 6376, 6376, 6376, 6376, 6380, 2907, 2909, 4010, 2907, 2909, 6377, 6371, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 5932, 6374, 2387, 6371, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 6374, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2832, 2816, 2816, 2816, 2816, 2818, 2836, 2852, 6281, 6280, 6280, 6280, 6284, 2091, 2081, 2081, 2081, 2081, 2093, 6281, 6280, 6280, 6280, 6280, 6280, 6284, 2850, 2836, 2852, 2899, 2897, 2897, 2909, 2090, 4002, 3971, 4004, 2094, 4010, 6377, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 6375, 6380, 2675, 6377, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 6380, 5888, 5892, 5916, 5916, 5896, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2817, 2820, 2854, 2908, 2082, 2068, 2069, 2087, 3984, 3968, 3970, 3988, 3971, 3988, 3988, 4004, 2910, 4002, 4004, 2090, 4014, 6381, 4014, 2065, 2068, 2068, 2068, 2068, 2068, 2068, 2068, 2068, 2068, 2068, 2068, 2068, 2068, 2068, 2068, 2068, 2068, 2068, 2068, 2068, 2068, 2068, 2068, 2068, 2068, 2068, 2068, 2068, 2068, 2068, 2068, 2068, 2068, 2068, 2068, 5888, 5912, 6274, 6278, 5928, 5916, 5900, 5916, 5916, 5896, 5888, 5888, 5888, 5888, 5912, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2906, 2082, 2049, 2048, 2072, 4011, 3997, 3996, 3976, 3968, 3968, 3968, 3968, 3970, 3988, 3969, 3992, 2065, 2068, 2068, 2068, 2049, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 5888, 5912, 6280, 6284, 3234, 3236, 5932, 6275, 6274, 5928, 5900, 5916, 5896, 5888, 5912, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2844, 2844, 2844, 2844, 2844, 2824, 2816, 2816, 2816, 2816, 2820, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2824, 2840, 2896, 2064, 2048, 2048, 2050, 2068, 2068, 2084, 4008, 3980, 3996, 3996, 3976, 3968, 3968, 3968, 3992, 2088, 2076, 2076, 2076, 2076, 2076, 2076, 2076, 2076, 2076, 2076, 2076, 2076, 2076, 2076, 2076, 2076, 2076, 2076, 2076, 2076, 2076, 2076, 2076, 2076, 2076, 2076, 2076, 2076, 2076, 2076, 2076, 2076, 2076, 2076, 2076, 2076, 2076, 2076, 2076, 5892, 5926, 3234, 3220, 3201, 3224, 6287, 6281, 6280, 6275, 5932, 6278, 5904, 5892, 5926, 2856, 2844, 2844, 2844, 2844, 2844, 2844, 2854, 5931, 5921, 5921, 5921, 5933, 2856, 2844, 2824, 2820, 2844, 2854, 5931, 5921, 5921, 5921, 5921, 5921, 5933, 2856, 2854, 2908, 2088, 2076, 2076, 2076, 2076, 2076, 2078, 2093, 4012, 5922, 5924, 4008, 3976, 3968, 3972, 4006, 5922, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5926, 6278, 3240, 3228, 3228, 3230, 3233, 3222, 3236, 6281, 6279, 6280, 5904, 5912, 6163, 6161, 6161, 6161, 6161, 6161, 6161, 6161, 6161, 6161, 6161, 6161, 6161, 6161, 6173, 5930, 2832, 2840, 5930, 6162, 6148, 6148, 6148, 6148, 6148, 6148, 6148, 6148, 6148, 6148, 6148, 6148, 6148, 6148, 6148, 6148, 6148, 6164, 5922, 5889, 5894, 5933, 3984, 3968, 3992, 5931, 5897, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 6279, 6284, 2082, 2068, 2068, 2068, 2084, 3216, 3202, 3236, 6285, 3242, 5904, 5912, 6160, 6018, 6004, 6004, 6004, 6004, 6004, 6004, 6004, 6004, 6004, 6004, 6004, 6004, 6020, 5920, 2832, 2840, 5920, 6144, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6152, 5904, 5888, 5912, 6278, 3984, 3968, 3992, 6275, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 6285, 3246, 2064, 2048, 2048, 2048, 2072, 3216, 3200, 3202, 3220, 3226, 5904, 5912, 6160, 6000, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 6008, 5920, 2832, 2840, 5920, 6144, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6152, 5904, 5888, 5912, 6284, 3984, 3968, 3992, 6281, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 2081, 2081, 2077, 2076, 2076, 2076, 2086, 3240, 3228, 3228, 3228, 3238, 5904, 5912, 6160, 6000, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 6008, 5920, 2832, 2840, 5920, 6144, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6128, 6152, 5904, 5888, 5912, 4002, 3969, 3968, 3970, 4004, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5889, 5912, 6172, 6024, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6012, 6022, 5932, 2832, 2840, 5932, 6168, 6156, 6156, 6156, 6156, 6156, 6156, 6156, 6156, 6156, 6156, 6156, 6156, 6156, 6156, 6156, 6156, 6156, 6166, 5904, 5888, 5912, 3984, 3968, 3968, 3968, 3992, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5892, 5916, 5916, 5916, 5916, 5916, 5916, 5918, 5911, 5921, 5921, 5921, 5921, 5921, 5921, 5921, 5921, 5921, 5921, 5921, 5921, 5921, 5933, 6278, 2832, 2840, 6275, 5931, 5921, 5921, 5921, 5921, 5921, 5921, 5921, 5921, 5921, 5921, 5921, 5921, 5921, 5910, 5908, 5908, 5908, 5908, 5889, 5888, 5912, 4008, 3976, 3968, 3972, 4006, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 5920, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 6278, 6284, 2832, 2840, 6281, 6275, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 5928, 5896, 5888, 5888, 5888, 5888, 5888, 5894, 5933, 3984, 3968, 3992, 5931, 5897, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 5932, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6284, 2850, 2817, 2818, 2852, 6281, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6275, 5904, 5888, 5888, 5892, 5916, 5916, 5926, 6278, 3984, 3968, 3992, 6275, 5928, 5896, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2850, 2836, 2836, 2836, 2836, 2836, 2852, 6287, 2850, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2817, 2816, 2816, 2818, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2852, 6281, 5904, 5888, 5892, 5926, 6274, 6274, 6278, 6284, 3984, 3968, 3992, 6281, 6275, 5928, 5896, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2832, 2816, 2816, 2816, 2816, 2816, 2818, 2836, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2910, 5904, 5892, 5926, 6278, 6280, 6280, 6284, 4002, 3969, 3968, 3970, 4004, 6281, 6275, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2856, 2844, 2844, 2824, 2820, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2824, 2816, 2816, 2820, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2828, 2854, 5922, 5889, 5912, 6278, 6284, 4002, 3988, 3989, 3997, 3996, 3996, 3996, 3998, 4005, 6281, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5890, 5908, 5908, 5924, 2856, 2854, 5922, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5924, 2856, 2824, 2820, 2854, 5922, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5924, 2848, 5931, 5897, 5888, 5912, 6284, 4002, 3969, 3968, 3992, 2850, 2836, 2836, 2836, 2852, 3985, 4004, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5890, 5908, 5908, 5889, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5890, 5924, 2856, 2854, 5922, 5889, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2848, 6275, 5904, 5888, 5912, 4002, 3973, 3996, 3996, 4006, 2832, 2816, 2816, 2816, 2840, 3984, 3992, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5890, 5908, 5908, 5889, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5892, 5926, 2848, 6281, 5904, 5888, 5912, 3986, 4006, 2850, 2836, 2836, 2817, 2816, 2816, 2816, 2840, 3984, 3992, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 6278, 2833, 2852, 5928, 5916, 5926, 4012, 2850, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 3984, 3992, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5892, 5916, 5916, 5916, 5916, 5916, 5896, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 6284, 2832, 2840, 6275, 6274, 6278, 2850, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 3984, 3992, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5892, 5926, 6274, 6274, 6274, 6274, 6274, 5928, 5896, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2850, 2817, 2840, 6281, 6280, 6284, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 3984, 3992, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5892, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5896, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5892, 5916, 5916, 5916, 5896, 5892, 5916, 5916, 5916, 5913, 6278, 6272, 6272, 6272, 6272, 6272, 6275, 5904, 5888, 5888, 5888, 5892, 5916, 5916, 5896, 5892, 5916, 5916, 5896, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2856, 2844, 2846, 2849, 2849, 2849, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 3984, 3992, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5892, 5926, 6322, 6322, 6322, 6322, 6322, 6322, 6322, 6322, 6322, 6322, 6322, 5928, 5916, 5916, 5916, 5916, 5916, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 6274, 6274, 6278, 5928, 5926, 6275, 6274, 6274, 5920, 6276, 6280, 6280, 6280, 6280, 6280, 6273, 5904, 5888, 5892, 5916, 5926, 6274, 6278, 5928, 5926, 6275, 6274, 5928, 5916, 5896, 5888, 5888, 5888, 5888, 5888, 5888, 5890, 5908, 5908, 5908, 5908, 5908, 5924, 5934, 5934, 2816, 2844, 2844, 2844, 2844, 2844, 2844, 2854, 3986, 4006, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 6326, 6328, 6328, 6328, 6328, 6328, 6328, 6328, 6328, 6328, 6328, 6328, 6323, 6322, 6322, 6322, 6322, 6326, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 6280, 6280, 6284, 2898, 2900, 6281, 6280, 6280, 5920, 6284, 4002, 3988, 3988, 3988, 4004, 6281, 5904, 5888, 5912, 6274, 6278, 6280, 6284, 2850, 2852, 6281, 6280, 6275, 6274, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 4001, 4001, 4001, 4001, 4001, 4001, 4001, 4001, 4001, 4001, 4007, 5922, 5889, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 6332, 2082, 2068, 2068, 2068, 2068, 2068, 2068, 2068, 2068, 2068, 2084, 6329, 6328, 6328, 6328, 6328, 6332, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2898, 2884, 2884, 2865, 2866, 2884, 2884, 2900, 5920, 4002, 3969, 3968, 3968, 3968, 3970, 4004, 5928, 5916, 5926, 6280, 6284, 2850, 2836, 2817, 2818, 2836, 2852, 6281, 6280, 5928, 5896, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5890, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5889, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2082, 2049, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2050, 2068, 2068, 2068, 2068, 2068, 2068, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2904, 2892, 2892, 2872, 2868, 2892, 2892, 2902, 5920, 3984, 3968, 3968, 3968, 3968, 3968, 3992, 6275, 6274, 6278, 2850, 2836, 2821, 2844, 2844, 2844, 2844, 2826, 2836, 2852, 6275, 5928, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5896, 5912, 2064, 2052, 2076, 2076, 2076, 2076, 2076, 2076, 2076, 2076, 2076, 2076, 2076, 2076, 2076, 2076, 2076, 2076, 5888, 5888, 5888, 5888, 5888, 5888, 5890, 5908, 5908, 5924, 2904, 2902, 5922, 5908, 5908, 5914, 3984, 3968, 3968, 3968, 3968, 3968, 3992, 6281, 6280, 6284, 2832, 2820, 2854, 2082, 2068, 2068, 2084, 2856, 2824, 2840, 6281, 6275, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 5904, 5912, 2064, 2072, 5922, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5890, 5908, 5908, 5889, 5888, 5888, 5912, 3984, 3968, 3968, 3968, 3968, 3968, 3970, 4004, 2090, 2850, 2817, 2840, 2082, 2053, 2076, 2076, 2058, 2084, 2832, 2818, 2852, 6281, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 5904, 5912, 2289, 2290, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 3984, 3968, 3968, 3968, 3968, 3968, 3968, 3992, 2080, 2832, 2816, 2840, 2064, 2072, 2898, 2900, 2064, 2072, 2832, 2816, 2818, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2852, 5904, 5912, 2385, 2386, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 3986, 3996, 3976, 3968, 3968, 3968, 3972, 4006, 2092, 2856, 2824, 2840, 2064, 2072, 2904, 2902, 2064, 2072, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 5904, 5912, 2385, 2386, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 4012, 2910, 4008, 3980, 3996, 3996, 4006, 5922, 5908, 5924, 2832, 2840, 2088, 2058, 2068, 2068, 2053, 2086, 2832, 2820, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2854, 5904, 5912, 2385, 2386, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5892, 5916, 5896, 5892, 5916, 5896, 5888, 5890, 5908, 5908, 5924, 4000, 5922, 5908, 5908, 5889, 5888, 5912, 2832, 2818, 2852, 2088, 2076, 2076, 2086, 2850, 2817, 2840, 5922, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5889, 5912, 2385, 2386, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5892, 5916, 5926, 6326, 5928, 5926, 6323, 5928, 5916, 5916, 5916, 5916, 5926, 4000, 5928, 5896, 5888, 5888, 5888, 5912, 2856, 2844, 2826, 2836, 2836, 2836, 2836, 2821, 2844, 2854, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5892, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5926, 2385, 2386, 5928, 5916, 5916, 5916, 5916, 5916, 5916, 5896, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 6322, 6326, 6332, 2898, 2900, 6329, 6323, 6322, 6322, 6322, 6322, 6326, 4000, 6323, 5928, 5896, 5888, 5888, 5890, 5908, 5924, 2856, 2844, 2824, 2820, 2844, 2854, 5922, 5908, 5889, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 6322, 6322, 6322, 6322, 6322, 6322, 6322, 6322, 6322, 6326, 2673, 2674, 6323, 6322, 6322, 6322, 6322, 6322, 6322, 5928, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 6328, 6332, 2898, 2865, 2866, 2900, 6329, 6328, 6328, 6328, 6328, 6332, 4012, 6329, 6323, 5904, 5888, 5888, 5888, 5888, 5890, 5908, 5924, 2856, 2854, 5922, 5908, 5889, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5892, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5926, 6328, 6328, 6328, 6328, 6328, 6328, 6328, 6328, 6328, 6332, 2088, 2086, 6329, 6328, 6328, 6328, 6328, 6328, 6328, 6323, 6322, 6322, 6322, 6322, 6322, 6322, 6322, 6326, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2898, 2884, 2865, 2864, 2864, 2866, 2900, 2094, 2906, 2094, 2906, 2094, 2898, 2900, 6329, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5890, 5908, 5908, 5889, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 6322, 6322, 6322, 6322, 6322, 6322, 6322, 6322, 6322, 6322, 6322, 6322, 6322, 6322, 6322, 6322, 6326, 2899, 2897, 2897, 2897, 2897, 2897, 2897, 2897, 2897, 2897, 2897, 2897, 2897, 2897, 2897, 2897, 2897, 2897, 2909, 6329, 6328, 6328, 6328, 6328, 6328, 6328, 6328, 6332, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2880, 2864, 2864, 2864, 2864, 2864, 2866, 2884, 2867, 2884, 2867, 2884, 2865, 2866, 2900, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 6328, 6328, 6328, 6328, 6328, 6328, 6328, 6328, 6328, 6328, 6328, 6328, 6328, 6328, 6328, 6328, 6332, 2896, 2083, 2081, 2081, 2081, 2081, 2081, 2081, 2081, 2081, 2081, 2081, 2081, 2081, 2081, 2081, 2081, 2081, 2081, 2081, 2081, 2081, 2081, 2070, 2068, 2069, 2081, 2081, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2880, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2888, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5892, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5926, 2899, 2897, 2897, 2897, 2897, 2897, 2897, 2897, 2897, 2897, 2897, 2897, 2897, 2897, 2897, 2897, 2897, 2903, 2080, 5922, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5909, 5921, 5921, 5921, 5910, 5908, 5908, 5908, 5908, 5908, 5908, 5924, 2088, 2060, 2086, 5922, 5908, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2880, 2864, 2864, 2864, 2864, 2864, 2864, 2868, 2892, 2872, 2864, 2864, 2864, 2868, 2902, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 6322, 6322, 6322, 6322, 6322, 6322, 6322, 6322, 6326, 2896, 2083, 2081, 2081, 2081, 2071, 2081, 2081, 2081, 2071, 2081, 2081, 2081, 2081, 2081, 2081, 2081, 2081, 2087, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 6322, 6322, 6322, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5890, 5924, 2080, 5922, 5889, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2904, 2892, 2892, 2892, 2892, 2892, 2892, 2902, 5930, 2904, 2892, 2892, 2892, 2902, 5922, 5889, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 6328, 6328, 6328, 6328, 6328, 6328, 6328, 6328, 6332, 2896, 2080, 5922, 5908, 5924, 2092, 2898, 2884, 2900, 2092, 5922, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5889, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 6320, 6320, 6320, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2080, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5890, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5891, 5908, 5908, 5908, 5908, 5908, 5889, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2907, 2887, 2897, 2897, 2897, 2897, 2897, 2897, 2897, 2903, 2080, 5904, 5888, 5912, 2898, 2865, 2864, 2866, 2900, 5904, 5888, 5888, 5888, 5888, 5892, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5896, 5888, 5888, 5888, 5912, 6328, 6328, 6328, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2080, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5890, 5924, 2896, 5930, 2091, 2081, 2081, 2071, 2081, 2081, 2081, 2087, 5904, 5888, 5912, 2880, 2864, 2864, 2864, 2888, 5904, 5888, 5888, 5888, 5892, 5926, 6326, 6322, 6322, 6322, 6322, 6322, 6322, 6323, 5928, 5896, 5888, 5892, 5926, 2091, 2081, 2093, 5928, 5896, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2080, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2896, 5905, 5908, 5908, 5924, 2080, 5922, 5908, 5908, 5908, 5889, 5888, 5912, 2904, 2892, 2876, 2892, 2902, 5904, 5888, 5888, 5892, 5926, 6326, 6324, 6324, 6328, 6321, 6324, 6328, 6321, 6321, 6322, 5928, 5896, 5912, 6322, 6322, 6322, 6322, 6322, 5928, 5896, 5888, 5888, 5888, 5888, 5888, 5912, 2080, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5892, 5916, 5916, 5916, 5896, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5892, 5916, 5896, 5892, 5916, 5896, 5888, 5888, 5892, 5916, 5916, 5916, 5896, 5888, 5888, 5888, 5888, 5888, 5912, 2896, 5904, 5892, 5916, 5926, 2080, 5928, 5916, 5896, 5888, 5888, 5888, 5894, 5921, 5933, 2896, 5931, 5921, 5897, 5888, 5892, 5926, 6326, 6324, 6324, 6332, 2906, 6329, 6332, 2906, 6329, 6321, 6320, 6322, 5928, 5913, 6320, 6320, 6320, 6320, 6320, 6323, 5928, 5916, 5916, 5916, 5916, 5916, 5926, 2080, 5928, 5916, 5916, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5892, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5926, 2898, 2900, 6323, 5928, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5926, 6326, 5928, 5926, 6323, 5928, 5916, 5916, 5926, 6322, 6322, 6322, 5928, 5916, 5916, 5916, 5916, 5916, 5926, 2896, 5904, 5912, 6322, 6326, 2080, 6323, 6322, 5904, 5892, 5916, 5916, 5926, 6322, 6326, 2896, 6323, 6322, 5928, 5916, 5926, 6327, 6324, 6324, 6332, 2898, 2867, 2884, 2884, 2867, 2900, 6329, 6320, 6320, 6326, 5932, 6320, 6320, 6320, 6320, 6320, 6321, 6323, 6322, 6322, 6322, 6322, 6322, 6326, 2291, 6323, 6322, 6326, 5888, 5888, 5888, 5888, 5888, 5888, 5892, 5926, 6322, 6322, 6322, 6322, 6322, 6322, 6322, 6322, 6326, 2880, 2888, 6329, 5928, 6322, 6322, 6322, 6322, 6322, 6322, 6326, 5928, 6332, 2898, 2900, 6329, 5928, 6322, 6322, 6326, 6328, 6328, 6328, 6323, 6322, 6322, 6322, 6322, 6322, 6326, 2896, 5928, 5926, 6328, 6332, 2080, 6329, 6328, 5928, 5926, 6322, 6322, 6326, 6328, 6332, 2896, 6329, 6328, 6323, 6322, 6322, 6325, 6324, 6332, 2898, 2865, 2864, 2864, 2864, 2864, 2888, 5930, 6328, 6320, 6324, 6326, 6328, 6328, 6328, 6328, 6328, 6321, 6321, 6320, 6320, 6320, 6320, 6320, 6324, 2387, 6321, 6320, 6324, 5916, 5916, 5916, 5916, 5916, 5916, 5926, 6326, 6328, 6328, 6328, 6328, 6328, 6328, 6328, 6328, 6332, 2880, 2866, 2900, 6329, 6328, 6328, 6328, 6328, 6328, 6328, 6332, 6335, 2898, 2865, 2866, 2900, 6335, 6328, 6328, 6332, 4050, 4036, 4052, 6329, 6328, 6328, 6328, 6328, 6328, 6332, 2896, 6323, 5928, 2091, 2071, 2079, 2071, 2093, 6323, 6326, 6328, 6328, 6332, 2898, 2884, 2867, 2884, 2900, 6329, 6328, 6328, 6325, 6332, 2898, 2865, 2864, 2864, 2864, 2864, 2864, 2888, 5905, 5924, 6328, 6324, 6332, 2899, 2909, 2090, 2907, 2901, 6329, 6321, 6320, 6320, 6320, 6320, 6320, 6324, 2387, 6321, 6320, 6324, 6323, 6322, 6322, 6322, 6322, 6322, 6326, 6332, 2090, 2910, 2090, 2910, 2090, 2910, 2090, 2910, 2090, 2880, 2864, 2866, 2884, 2884, 2884, 2884, 2884, 2884, 2884, 2884, 2884, 2865, 2864, 2864, 2866, 2884, 2884, 2900, 4050, 4017, 4016, 4018, 4036, 4036, 4052, 2898, 2884, 2884, 2884, 2890, 6329, 6335, 2906, 2080, 2910, 2080, 2906, 6329, 6332, 2898, 2884, 2884, 2865, 2864, 2864, 2864, 2866, 2884, 2884, 2900, 6333, 2898, 2869, 2892, 2892, 2892, 2892, 2892, 2892, 2902, 5904, 5890, 5924, 6332, 2899, 2903, 2082, 2051, 2084, 2883, 2909, 6329, 6328, 6328, 6328, 6328, 6328, 6332, 2675, 6329, 6328, 6332, 6329, 6328, 6328, 6328, 6328, 6328, 6332, 2910, 2065, 2068, 2051, 2068, 2051, 2068, 2051, 2068, 2074, 2880, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2888, 4032, 4016, 4016, 4016, 4016, 4016, 4040, 2880, 2864, 2864, 2864, 2866, 2884, 2884, 2890, 2089, 2071, 2087, 2881, 2884, 2884, 2869, 2892, 2872, 2864, 2864, 2864, 2864, 2864, 2868, 2892, 2874, 2884, 2869, 2902, 5922, 5908, 5908, 5908, 5908, 5908, 5908, 5889, 5888, 5890, 5924, 2908, 2082, 2049, 2048, 2072, 2908, 2083, 2081, 2085, 2898, 2885, 2909, 2082, 2069, 2079, 2070, 2084, 2907, 2068, 2068, 2068, 2068, 2068, 2068, 2068, 2068, 2049, 2048, 2052, 2076, 2056, 2048, 2048, 2052, 2086, 2880, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2888, 4032, 4016, 4016, 4016, 4016, 4016, 4040, 2880, 2864, 2864, 2864, 2864, 2864, 2864, 2866, 2900, 2080, 2898, 2865, 2864, 2864, 2864, 6335, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 6335, 2864, 2868, 2902, 5922, 5889, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2082, 2049, 2048, 2048, 2050, 2068, 2074, 2910, 2080, 2882, 2902, 2082, 2049, 2072, 2910, 2064, 2050, 2068, 2076, 2076, 2076, 2076, 2076, 2076, 2076, 2076, 2076, 2076, 2086, 2906, 2088, 2076, 2076, 2086, 2898, 2865, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2888, 4032, 4016, 4016, 4016, 4016, 4016, 4040, 2880, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2888, 2080, 2880, 2864, 2864, 2864, 2864, 2884, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2884, 2868, 2902, 5922, 5889, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2064, 2048, 2052, 2076, 2056, 2048, 2054, 2081, 2087, 2896, 2082, 2049, 2052, 2078, 2081, 2077, 2056, 2048, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5924, 2898, 2884, 2867, 2884, 2884, 2884, 2884, 2865, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2888, 4032, 4016, 4016, 4016, 4016, 4016, 4040, 2880, 2864, 2864, 2864, 2868, 2892, 2892, 2892, 2889, 2080, 2882, 2892, 2892, 2892, 2872, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2864, 2888, 5922, 5889, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2088, 2076, 2086, 2906, 2088, 2076, 2086, 2907, 2897, 2903, 2088, 2056, 2072, 2898, 2884, 2900, 2064, 2048, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2904, 2892, 2892, 2892, 2892, 2892, 2892, 2872, 2864, 2864, 2868, 2892, 2892, 2892, 2892, 2892, 2892, 2892, 2892, 2892, 2872, 2864, 2864, 2868, 2892, 2892, 2902, 4056, 4044, 4044, 4044, 4044, 4044, 4054, 2904, 2892, 2892, 2892, 2902, 5922, 5908, 5924, 2908, 2080, 2908, 5922, 5908, 5924, 2904, 2892, 2892, 2892, 2892, 2892, 2892, 2892, 2892, 2892, 2902, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2907, 2897, 2897, 2895, 2897, 2897, 2909, 5922, 5908, 5908, 5924, 2064, 2072, 2880, 2864, 2888, 2064, 2048, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2091, 2081, 2081, 2081, 2081, 2081, 2093, 2904, 2892, 2892, 2902, 5922, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5924, 2904, 2892, 2892, 2902, 5922, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5889, 5888, 5894, 5933, 2080, 5931, 5897, 5888, 5890, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5889, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5890, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5889, 5888, 5888, 5912, 2064, 2072, 2904, 2892, 2902, 2064, 2048, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5890, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5889, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5890, 5908, 5908, 5908, 5908, 5889, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 6326, 2080, 6323, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2064, 2050, 2068, 2068, 2068, 2049, 2048, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 6332, 2080, 6329, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2064, 2048, 2048, 2048, 2048, 2048, 2048, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2906, 2080, 2906, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2064, 2052, 2076, 2076, 2076, 2056, 2048, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2908, 2080, 2908, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2064, 2072, 2898, 2884, 2900, 2064, 2048, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5894, 5933, 2080, 5931, 5897, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2064, 2072, 2880, 2864, 2888, 2064, 2048, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 6327, 2080, 6327, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2064, 2072, 2904, 2892, 2902, 2064, 2048, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 6333, 2080, 6333, 5904, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5888, 5912, 2064, 2050, 2068, 2068, 2068, 2049, 2048, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3530, 3146, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3522, 3514, 3145, 3149, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3522, 3489, 3490, 3524, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3504, 3492, 3516, 3518, 3533, 0, 0, 0, 3534, 3147, 3137, 3149, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3528, 3526, 0, 0, 0, 0, 0, 0, 0, 3531, 3510, 3524, 0, 0, 3531, 3533, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3534, 0, 0, 0, 0, 0, 0, 3528, 3518, 3533, 0, 0, 3150, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3534, 0, 0, 3150, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3150, 0, 3534, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3534, 3150, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3043, 3053, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3146, 3534, 0, 3530, 0, 0, 0, 0, 0, 0, 0, 0, 3051, 3047, 0, 3054, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3050, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3148, 0, 3522, 3514, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3531, 3510, 3524, 0, 0, 0, 0, 0, 0, 3040, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3528, 3526, 0, 3531, 3510, 3524, 0, 3150, 0, 0, 3522, 3524, 0, 3530, 0, 0, 3138, 3140, 0, 3522, 3524, 0, 0, 3528, 3526, 0, 0, 0, 0, 0, 0, 3052, 0, 3578, 0, 0, 0, 0, 3054, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3150, 0, 3534, 0, 3528, 3526, 0, 3534, 0, 0, 3528, 3513, 0, 3532, 0, 0, 3144, 3142, 3522, 3489, 3490, 3524, 3147, 3149, 0, 0, 0, 0, 0, 3051, 3053, 0, 3570, 3562, 0, 0, 3054, 0, 0, 0, 3050, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3529, 3525, 0, 0, 0, 0, 3531, 3517, 3516, 3516, 3518, 3510, 3524, 0, 0, 0, 0, 0, 0, 0, 0, 3576, 3561, 0, 0, 0, 0, 0, 0, 3052, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3582, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3534, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3532, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3528, 3526, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3580, 0, 0, 0, 3054, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3582, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3150, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3050, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2139, 2141, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3530, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3040, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2139, 2141, 0, 2238, 0, 0, 3579, 3573, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3505, 3524, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3042, 3015, 3053, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2238, 0, 0, 0, 0, 3580, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3504, 3512, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3026, 3046, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3506, 3526, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3052, 0, 0, 3054, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3532, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3054, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3150, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3186, 3172, 3173, 3185, 3185, 3185, 3197, 0, 0, 0, 0, 0, 0, 0, 0, 3531, 3510, 3508, 3508, 3509, 3521, 3533, 0, 0, 0, 3534, 0, 3530, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3050, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3168, 3156, 3190, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3523, 3533, 0, 0, 3528, 3496, 3488, 3512, 3146, 0, 3534, 0, 0, 0, 3531, 3519, 3533, 0, 0, 0, 0, 0, 3150, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3052, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3192, 3190, 0, 0, 0, 3195, 3197, 0, 0, 0, 0, 3531, 3527, 0, 0, 0, 0, 3528, 3516, 3526, 3145, 3149, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3186, 3172, 3172, 3172, 3172, 3172, 3172, 3172, 3188, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3168, 3152, 3152, 3152, 3152, 3152, 3152, 3152, 3176, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3186, 3153, 3152, 3152, 3152, 3152, 3152, 3152, 3152, 3154, 3188, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3186, 3153, 3152, 3152, 3152, 3152, 3152, 3152, 3152, 3152, 3152, 3176, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3530, 0, 0, 0, 0, 3168, 3152, 3152, 3152, 3152, 3152, 3152, 3152, 3152, 3152, 3152, 3176, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3532, 0, 0, 0, 0, 3168, 3152, 3152, 3152, 3152, 3152, 3152, 3152, 3152, 3152, 3152, 3176, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3530, 0, 0, 0, 3168, 3152, 3152, 3152, 3152, 3152, 3152, 3152, 3152, 3152, 3152, 3176, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3531, 3527, 0, 0, 0, 3152, 3152, 3152, 3152, 3152, 3152, 3152, 3152, 3152, 3152, 3152, 3176, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3152, 3152, 3152, 3152, 3152, 3152, 3152, 3152, 3156, 3190, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3570, 3572, 3050, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3180, 3180, 3180, 3180, 3180, 3180, 3180, 3180, 3180, 3180, 3190, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3570, 3537, 3560, 3052, 3582, 0, 3582, 0, 0, 0, 0, 0, 0, 3530, 0, 3534, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3552, 3540, 3574, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3523, 3519, 3533, 0, 3150, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3576, 3574, 3051, 3030, 3044, 0, 0, 0, 0, 0, 0, 0, 3532, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3026, 3046, 0, 0, 0, 0, 0, 0, 3530, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3578, 0, 0, 3052, 0, 3051, 3041, 3053, 0, 3531, 3510, 3514, 0, 0, 0, 0, 0, 0, 3150, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3531, 3533, 0, 0, 0, 0, 3186, 3188, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3553, 3556, 3556, 3572, 0, 3582, 0, 0, 0, 0, 3528, 3526, 0, 0, 0, 0, 0, 0, 0, 3150, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3168, 3176, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3576, 3564, 3564, 3550, 3581, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3192, 3190, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3568, 0, 0, 0, 0, 0, 0, 0, 3534, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3568, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3568, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3534, 3150, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3146, 0, 0, 0, 0, 0, 0, 0, 3568, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3150, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3570, 3572, 0, 3148, 0, 0, 0, 0, 0, 0, 0, 3553, 3572, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3579, 3545, 3560, 0, 0, 3138, 3124, 3140, 3582, 0, 3579, 3558, 3537, 3538, 3572, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3576, 3566, 3581, 0, 3144, 3132, 3134, 3126, 3124, 3140, 3576, 3544, 3536, 3560, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3186, 3172, 3172, 3172, 3172, 3188, 3582, 3144, 3132, 3134, 3149, 3576, 3564, 3574, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3192, 3180, 3180, 3180, 3180, 3190, 0, 0, 0, 0, 3582, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3582, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3570, 3572, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3582, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3147, 3126, 3140, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3582, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3552, 3560, 0, 0, 0, 0, 0, 3138, 3124, 3140, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3146, 3578, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3144, 3142, 0, 3150, 0, 0, 0, 0, 0, 0, 3150, 0, 3147, 3141, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3150, 0, 0, 0, 0, 0, 3582, 0, 0, 0, 0, 3579, 3549, 3574, 0, 0, 0, 0, 3147, 3133, 3116, 3142, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3148, 3553, 3557, 3581, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3582, 0, 0, 0, 0, 0, 0, 0, 3150, 0, 0, 0, 3136, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3150, 3582, 0, 3570, 3557, 3581, 0, 0, 0, 0, 0, 3582, 0, 3580, 0, 0, 3578, 0, 0, 3102, 0, 3148, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3579, 3545, 3560, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3150, 0, 0, 0, 0, 3148, 0, 0, 0, 0, 3582, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3578, 0, 0, 3576, 3574, 0, 0, 3147, 3137, 3149, 3578, 3147, 3137, 3149, 0, 3570, 3539, 3572, 0, 0, 0, 0, 3139, 3149, 0, 0, 0, 0, 3570, 3572, 0, 0, 0, 0, 0, 0, 0, 0, 3579, 3581, 0, 0, 0, 3578, 0, 0, 3576, 3574, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3580, 3138, 3124, 3140, 0, 0, 0, 0, 0, 3570, 3562, 0, 0, 0, 3570, 3537, 3536, 3560, 0, 3098, 0, 0, 3148, 0, 0, 0, 0, 0, 3552, 3542, 3581, 0, 0, 0, 0, 0, 3582, 0, 0, 0, 0, 0, 0, 3580, 0, 0, 0, 0, 3582, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3582, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3582, 0, 0, 0, 0, 3144, 3132, 3142, 0, 3579, 3581, 0, 3570, 3537, 3560, 0, 3579, 3569, 3545, 3536, 3536, 3560, 0, 3075, 3101, 3150, 0, 0, 0, 0, 0, 0, 3576, 3574, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3582, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3146, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3579, 3581, 0, 3147, 3137, 3149, 3582, 0, 0, 0, 0, 0, 0, 0, 3576, 3564, 3574, 0, 0, 0, 3552, 3540, 3564, 3574, 0, 3100, 0, 3099, 3101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3147, 3137, 3143, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3579, 3565, 3574, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 184, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 26, 26, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 18, 0, 0, 0, 18, 0, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 192, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 95, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 103, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 111, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 228, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 236, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 216, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 18, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 153, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 48, 0, 48, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 48, 0, 0, 48, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 48, 48, 48, 0, 0, 0, 48, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16, 0, 48, 0, 0, 0, 0, 0, 48, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16, 0, 0, 0, 48, 0, 0, 0, 0, 48, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16, 0, 0, 16, 0, 0, 0, 48, 48, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 48, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 156, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 164, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 213, 0, 0, 0, 0, 213, 0, 0, 0, 0, 213, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 221, 0, 0, 0, 0, 221, 0, 0, 0, 0, 221, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 0, 0, 0, 42, 0, 0, 0, 42, 0, 0, 0, 42, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 0, 0, 0, 50, 0, 0, 0, 50, 0, 0, 0, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 28, 0, 28, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 164, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 165, 0, 164, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 18, 0, 18, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 234, 0, 234, 0, 12, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 202, 0, 202, 0, 202, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 194, 0, 186, 0, 194, 0, 0, 234, 4, 234, 0, 234, 0, 0, 0, 186, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 217, 0, 0, 0, 0, 0, 0, 0, 217, 0, 0, 0, 0, 217, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 234, 0, 234, 0, 234, 0, 0, 0, 234, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 51, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 217, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 172, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 194, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 18, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 205, 0, 0, 0, 0, 0, 0, 206, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 217, 0, 0, 0, 0, 0, 0, 0, 217, 0, 0, 0, 205, 0, 0, 0, 0, 0, 0, 0, 0, 206, 0, 0, 0, 18, 0, 0, 0, 18, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 18, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 205, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 206, 0, 0, 18, 0, 0, 0, 18, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 18, 0, 0, 0, 0, 0, 0, 0, 18, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 205, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 206, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 18, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 155, 183, 0, 183, 2, 183, 154, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 163, 191, 12, 191, 10, 191, 162, 184, 184, 5, 152, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 171, 0, 0, 0, 0, 0, 170, 192, 192, 192, 160, 152, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 168, 160, 42, 183, 0, 0, 183, 152, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 168, 50, 191, 0, 0, 191, 160, 24, 0, 0, 152, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 41, 41, 41, 0, 0, 0, 0, 0, 0, 0, 0, 0, 168, 0, 0, 0, 160, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 49, 49, 49, 0, 0, 0, 0, 0, 0, 0, 0, 34, 218, 0, 0, 0, 168, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 226, 0, 0, 0, 179, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 18, 18, 18, 0, 0, 179, 177, 180, 0, 0, 0, 0, 234, 0, 176, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 180, 33, 0, 0, 0, 26, 26, 26, 176, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 155, 6, 0, 0, 58, 154, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 163, 14, 0, 0, 0, 162, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 58, 42, 0, 42, 58, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 17, 0, 0, 0, 0, 17, 6, 154, 0, 0, 0, 0, 0, 171, 0, 0, 0, 54, 170, 0, 0, 0, 0, 0, 0, 0, 155, 183, 0, 183, 0, 183, 24, 19, 50, 0, 50, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14, 162, 4, 184, 184, 184, 24, 0, 0, 0, 0, 0, 0, 24, 184, 14, 184, 0, 184, 24, 163, 191, 0, 191, 0, 191, 0, 27, 18, 0, 18, 28, 30, 5, 128, 130, 115, 128, 130, 0, 82, 0, 83, 0, 0, 128, 130, 0, 128, 130, 0, 0, 128, 130, 0, 128, 130, 0, 0, 128, 130, 0, 128, 130, 0, 0, 128, 130, 0, 128, 130, 0, 0, 128, 130, 0, 128, 130, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 170, 43, 192, 192, 192, 0, 0, 44, 0, 0, 44, 0, 2, 192, 0, 11, 0, 192, 0, 171, 0, 0, 18, 18, 18, 18, 0, 0, 0, 0, 0, 38, 0, 136, 138, 123, 136, 138, 6, 90, 42, 91, 0, 0, 136, 138, 0, 136, 138, 0, 0, 136, 138, 0, 136, 138, 0, 0, 136, 138, 0, 136, 138, 0, 0, 136, 138, 0, 136, 138, 0, 0, 136, 138, 0, 136, 138, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 218, 0, 0, 0, 0, 0, 51, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 18, 18, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 148, 57, 0, 14, 98, 50, 99, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 155, 24, 1, 1, 212, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 226, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 18, 0, 0, 0, 0, 0, 0, 0, 0, 57, 0, 0, 0, 0, 0, 65, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 163, 10, 8, 8, 212, 58, 0, 0, 0, 0, 0, 0, 0, 0, 0, 234, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 18, 0, 0, 0, 0, 0, 0, 0, 0, 65, 0, 0, 0, 0, 0, 0, 0, 0, 179, 177, 180, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 220, 0, 0, 154, 0, 155, 0, 0, 0, 178, 0, 0, 0, 0, 0, 0, 176, 0, 0, 0, 0, 0, 178, 0, 0, 0, 33, 176, 0, 0, 0, 0, 0, 0, 0, 178, 0, 18, 0, 0, 0, 0, 0, 0, 0, 0, 176, 0, 0, 178, 0, 0, 0, 176, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 155, 0, 0, 0, 0, 228, 0, 0, 162, 42, 163, 0, 0, 0, 0, 177, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 106, 106, 106, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 163, 0, 0, 0, 0, 0, 0, 0, 170, 50, 171, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 153, 114, 0, 114, 152, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 171, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 161, 122, 0, 122, 160, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 21, 22, 0, 0, 179, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 169, 0, 0, 0, 168, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 155, 0, 0, 154, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 180, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 153, 184, 24, 184, 24, 184, 152, 0, 153, 0, 152, 153, 0, 6, 152, 153, 4, 0, 152, 153, 24, 0, 163, 0, 0, 162, 0, 24, 152, 153, 6, 0, 152, 153, 0, 0, 152, 153, 184, 152, 0, 0, 0, 0, 0, 0, 0, 0, 0, 106, 106, 106, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 161, 192, 0, 192, 34, 192, 160, 218, 161, 0, 160, 161, 0, 14, 160, 161, 0, 0, 160, 161, 6, 0, 171, 0, 0, 170, 2, 0, 160, 161, 14, 0, 160, 161, 0, 0, 160, 161, 192, 160, 4, 0, 0, 0, 0, 0, 0, 0, 153, 114, 0, 114, 152, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 169, 0, 0, 0, 0, 0, 168, 226, 169, 0, 168, 169, 0, 0, 168, 169, 0, 0, 168, 169, 14, 0, 0, 0, 0, 0, 0, 0, 168, 169, 0, 0, 168, 169, 0, 0, 168, 169, 18, 168, 0, 0, 0, 0, 0, 0, 24, 0, 161, 122, 0, 122, 160, 12, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 234, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16, 0, 0, 0, 153, 0, 0, 11, 169, 0, 0, 0, 170, 0, 154, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 180, 0, 0, 0, 0, 0, 0, 0, 0, 0, 179, 180, 0, 0, 179, 180, 0, 0, 179, 180, 0, 0, 0, 0, 0, 0, 0, 0, 179, 180, 0, 0, 179, 180, 0, 33, 179, 180, 0, 179, 0, 0, 0, 4, 161, 76, 76, 76, 76, 40, 40, 40, 76, 0, 162, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 178, 0, 0, 176, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 161, 0, 76, 76, 76, 40, 40, 48, 40, 76, 76, 170, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 152, 0, 0, 0, 169, 0, 76, 40, 40, 40, 48, 76, 40, 40, 76, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 160, 0, 0, 0, 0, 76, 76, 40, 76, 76, 76, 76, 48, 40, 76, 76, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 153, 0, 168, 0, 0, 0, 76, 76, 76, 40, 40, 76, 76, 76, 76, 40, 76, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 161, 0, 0, 0, 24, 4, 181, 76, 76, 48, 40, 40, 40, 40, 40, 40, 76, 76, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 9, 0, 0, 0, 0, 0, 155, 34, 24, 0, 154, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 169, 0, 0, 34, 0, 0, 181, 76, 76, 76, 48, 48, 48, 40, 40, 48, 76, 76, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 9, 0, 0, 0, 0, 24, 163, 0, 0, 0, 162, 24, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 180, 0, 0, 0, 0, 0, 76, 76, 76, 76, 40, 40, 40, 40, 48, 76, 76, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 153, 185, 24, 9, 9, 24, 185, 152, 0, 4, 163, 29, 0, 30, 162, 0, 0, 0, 0, 0, 0, 153, 24, 1, 1, 4, 152, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 76, 76, 48, 48, 48, 48, 76, 76, 0, 76, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 161, 193, 19, 8, 8, 19, 193, 160, 0, 0, 171, 37, 54, 38, 170, 0, 0, 0, 0, 153, 4, 161, 0, 8, 8, 0, 160, 6, 152, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 76, 76, 76, 76, 76, 76, 76, 76, 76, 76, 179, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 169, 0, 27, 0, 0, 27, 0, 168, 0, 0, 21, 22, 0, 0, 66, 0, 0, 0, 0, 161, 0, 169, 0, 0, 0, 0, 168, 14, 160, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 180, 0, 0, 0, 18, 0, 0, 179, 0, 17, 0, 0, 0, 0, 0, 0, 0, 24, 0, 169, 0, 0, 0, 0, 0, 0, 0, 0, 168, 152, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 9, 1, 9, 9, 1, 9, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 160, 34, 185, 185, 185, 185, 185, 152, 153, 185, 185, 185, 185, 185, 152, 153, 185, 185, 185, 185, 185, 152, 153, 185, 185, 185, 185, 185, 152, 153, 185, 185, 185, 185, 185, 0, 152, 153, 185, 185, 185, 185, 185, 152, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 168, 0, 193, 193, 193, 193, 193, 160, 161, 193, 193, 193, 193, 193, 160, 161, 193, 193, 193, 193, 193, 160, 161, 193, 193, 193, 193, 193, 160, 161, 193, 193, 193, 193, 193, 0, 160, 161, 193, 193, 193, 193, 193, 160, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 9, 0, 0, 0, 0, 0, 34, 0, 0, 0, 0, 0, 0, 41, 0, 0, 0, 0, 0, 18, 18, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 168, 169, 0, 0, 0, 0, 0, 168, 169, 0, 0, 0, 0, 0, 168, 169, 0, 0, 0, 0, 0, 168, 169, 0, 0, 0, 0, 0, 0, 168, 169, 0, 0, 0, 0, 0, 168, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 21, 22, 0, 49, 0, 0, 0, 0, 0, 18, 18, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 9, 0, 0, 0, 0, 180, 0, 0, 0, 0, 0, 179, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 179, 177, 177, 177, 177, 177, 177, 180, 179, 177, 177, 177, 177, 177, 180, 179, 177, 177, 177, 177, 177, 180, 179, 177, 177, 177, 177, 177, 180, 179, 177, 177, 177, 177, 177, 177, 180, 179, 177, 177, 177, 177, 177, 177, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 157, 9, 1, 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 180, 0, 0, 0, 0, 0, 0, 0, 0, 179, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 157, 2, 165, 8, 8, 14, 0, 186, 0, 186, 0, 186, 0, 158, 0, 0, 0, 0, 0, 0, 0, 178, 0, 0, 0, 0, 176, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 157, 0, 24, 221, 0, 0, 0, 0, 221, 24, 0, 0, 0, 221, 6, 0, 24, 0, 156, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 165, 10, 173, 18, 0, 172, 0, 194, 0, 194, 34, 194, 0, 166, 156, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 165, 0, 0, 229, 0, 0, 0, 0, 229, 0, 0, 0, 0, 229, 14, 0, 0, 0, 164, 0, 0, 0, 0, 0, 188, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 173, 21, 22, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 174, 164, 0, 0, 0, 0, 0, 0, 0, 0, 9, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 157, 221, 6, 0, 24, 221, 0, 188, 0, 221, 24, 0, 0, 221, 13, 0, 0, 0, 0, 0, 237, 0, 0, 0, 0, 237, 0, 0, 0, 0, 237, 0, 0, 21, 22, 172, 0, 0, 0, 0, 0, 196, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 18, 0, 0, 0, 0, 0, 0, 33, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 165, 229, 14, 0, 0, 229, 0, 196, 0, 229, 0, 0, 0, 229, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 29, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 173, 237, 0, 0, 0, 237, 0, 0, 0, 237, 0, 0, 0, 237, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 180, 0, 179, 0, 0, 0, 0, 0, 0, 0, 0, 0, 37, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 157, 24, 0, 0, 0, 0, 188, 24, 0, 0, 0, 179, 177, 180, 0, 19, 0, 19, 0, 179, 177, 180, 0, 0, 0, 0, 0, 179, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 157, 0, 156, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 178, 0, 0, 0, 0, 0, 0, 176, 0, 177, 0, 0, 177, 177, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 165, 0, 0, 0, 0, 0, 196, 11, 0, 0, 0, 0, 0, 0, 20, 27, 0, 27, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 165, 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 173, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 28, 0, 0, 0, 28, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11, 0, 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 179, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 244, 188, 209, 0, 0, 209, 188, 252, 0, 0, 0, 0, 0, 173, 18, 172, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 180, 0, 0, 0, 179, 0, 0, 0, 0, 0, 244, 0, 236, 217, 0, 12, 217, 236, 0, 252, 0, 0, 0, 186, 23, 186, 23, 186, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 244, 24, 0, 196, 225, 0, 0, 225, 196, 0, 24, 252, 0, 0, 194, 15, 194, 31, 194, 158, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 158, 0, 0, 0, 0, 0, 0, 0, 0, 0, 157, 1, 9, 156, 0, 0, 0, 0, 157, 188, 156, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 24, 58, 0, 58, 24, 0, 0, 0, 0, 0, 157, 34, 0, 58, 156, 0, 0, 0, 243, 0, 0, 189, 0, 233, 0, 33, 233, 0, 190, 0, 6, 253, 0, 194, 31, 194, 23, 194, 166, 0, 186, 0, 186, 0, 186, 0, 0, 0, 186, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 186, 24, 186, 0, 186, 24, 186, 0, 0, 0, 166, 219, 0, 0, 0, 24, 0, 0, 0, 219, 165, 8, 8, 164, 219, 0, 24, 0, 165, 196, 164, 0, 24, 0, 0, 0, 6, 24, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 157, 0, 24, 165, 0, 0, 0, 164, 24, 6, 156, 0, 12, 189, 197, 0, 0, 0, 0, 18, 0, 198, 190, 14, 0, 157, 29, 23, 194, 31, 30, 166, 0, 194, 24, 194, 24, 194, 0, 0, 0, 194, 0, 0, 0, 0, 0, 0, 0, 0, 157, 0, 194, 0, 194, 0, 194, 0, 194, 0, 0, 0, 174, 227, 0, 12, 0, 0, 0, 0, 0, 227, 173, 0, 0, 172, 227, 0, 43, 34, 0, 0, 172, 0, 29, 0, 0, 0, 14, 0, 0, 0, 219, 0, 0, 19, 0, 0, 42, 0, 165, 0, 30, 173, 0, 0, 0, 172, 0, 14, 164, 0, 189, 197, 197, 18, 0, 0, 0, 0, 0, 198, 198, 190, 5, 165, 37, 39, 18, 39, 38, 166, 0, 194, 0, 194, 12, 194, 0, 0, 0, 194, 0, 0, 183, 0, 183, 0, 183, 0, 165, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 235, 33, 21, 22, 0, 0, 21, 22, 235, 0, 0, 0, 0, 235, 18, 43, 0, 0, 0, 0, 0, 37, 0, 18, 18, 0, 17, 0, 0, 227, 0, 0, 27, 0, 0, 50, 0, 173, 0, 38, 0, 0, 0, 0, 0, 0, 0, 33, 189, 197, 197, 197, 0, 0, 0, 0, 0, 0, 198, 198, 198, 190, 173, 0, 18, 18, 18, 0, 174, 34, 194, 0, 194, 0, 194, 0, 0, 0, 202, 0, 0, 191, 5, 191, 0, 191, 0, 173, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 18, 51, 0, 0, 0, 0, 0, 0, 0, 18, 18, 0, 0, 0, 0, 235, 0, 0, 0, 0, 0, 0, 0, 0, 219, 0, 0, 0, 0, 0, 0, 0, 219, 0, 197, 197, 197, 176, 0, 0, 0, 0, 0, 0, 178, 198, 198, 198, 0, 18, 18, 18, 18, 66, 18, 18, 18, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 18, 18, 0, 0, 0, 0, 0, 0, 0, 18, 18, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 227, 0, 0, 0, 0, 0, 0, 0, 227, 0, 197, 197, 176, 0, 0, 0, 0, 0, 0, 0, 0, 178, 198, 198, 18, 43, 18, 18, 18, 43, 18, 7, 18, 0, 0, 35, 35, 35, 18, 0, 0, 0, 0, 0, 179, 177, 177, 177, 177, 177, 180, 0, 0, 18, 0, 0, 0, 0, 27, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 30, 0, 0, 0, 0, 18, 18, 0, 0, 0, 0, 0, 0, 0, 43, 18, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 235, 0, 0, 0, 209, 0, 0, 0, 235, 0, 197, 176, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 178, 198, 18, 51, 18, 18, 18, 51, 18, 18, 18, 0, 0, 0, 0, 0, 44, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 38, 0, 0, 0, 0, 18, 43, 0, 0, 0, 0, 0, 0, 0, 51, 18, 0, 0, 0, 0, 0, 0, 0, 0, 0, 33, 0, 0, 0, 0, 0, 0, 0, 217, 0, 0, 30, 0, 0, 176, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 178, 18, 18, 18, 66, 18, 18, 18, 0, 0, 176, 178, 0, 0, 18, 18, 18, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 18, 51, 0, 0, 33, 0, 0, 0, 0, 18, 18, 0, 0, 176, 0, 0, 0, 180, 0, 179, 0, 0, 0, 178, 0, 0, 0, 0, 225, 0, 0, 38, 0, 176, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 178, 34, 0, 0, 0, 33, 176, 0, 0, 0, 0, 0, 0, 18, 18, 18, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 28, 0, 0, 176, 0, 0, 0, 0, 0, 0, 0, 0, 0, 178, 0, 0, 176, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 18, 18, 18, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 157, 0, 156, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 165, 0, 164, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 41, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 173, 0, 172, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 49, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 180, 0, 179, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 18, 18, 18, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 18, 18, 18, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 157, 0, 156, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 18, 18, 18, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 165, 0, 164, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 214, 255, 214, 255, 214, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 5, 0, 0, 8, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 5, 5, 0, 0, 8, 8, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 5, 5, 5, 5, 0, 8, 8, 8, 0, 0, 0, 0, 255, 214, 255, 255, 214, 255, 215, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 5, 5, 5, 5, 5, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 5, 5, 0, 0, 0, 8, 8, 8, 0, 5, 5, 5, 0, 0, 5, 5, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 5, 0, 8, 8, 8, 0, 0, 5, 5, 5, 0, 0, 0, 0, 255, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 8, 8, 8, 255, 255, 255, 255, 255, 0, 0, 0, 0, 5, 0, 255, 255, 255, 255, 255, 255, 255, 255, 254, 7, 7, 254, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 254, 254, 7, 7, 254, 254, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 254, 254, 254, 254, 254, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 5, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 5, 5, 7, 7, 7, 7, 255, 255, 255, 255, 255, 255, 255, 255, 254, 254, 254, 254, 254, 215, 254, 254, 254, 254, 254, 254, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 5, 0, 5, 255, 254, 254, 254, 254, 254, 215, 5, 7, 7, 5, 7, 5, 215, 254, 254, 254, 254, 254, 215, 254, 214, 254, 214, 254, 214, 254, 0, 0, 0, 0, 0, 254, 254, 254, 254, 254, 254, 254, 254, 255, 255, 255, 8, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 255, 255, 255, 255, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 5, 5, 0, 0, 254, 254, 254, 254, 254, 8, 7, 8, 8, 7, 8, 254, 254, 254, 254, 254, 254, 254, 5, 5, 5, 0, 0, 0, 0, 8, 5, 0, 5, 8, 0, 254, 254, 254, 214, 254, 254, 254, 254, 254, 254, 8, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 255, 255, 255, 255, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 5, 5, 0, 5, 254, 5, 0, 0, 0, 7, 5, 5, 7, 5, 7, 0, 0, 0, 0, 5, 5, 0, 0, 5, 0, 0, 8, 8, 8, 8, 5, 0, 5, 5, 5, 5, 5, 5, 0, 0, 5, 8, 254, 254, 254, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 255, 255, 254, 215, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 255, 0, 0, 0, 5, 0, 5, 5, 0, 5, 0, 0, 5, 5, 0, 5, 0, 0, 0, 0, 5, 5, 5, 5, 0, 0, 0, 8, 8, 8, 8, 200, 0, 5, 5, 0, 5, 5, 0, 5, 5, 5, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 255, 255, 254, 255, 0, 0, 255, 254, 254, 255, 255, 255, 255, 255, 255, 0, 0, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 200, 8, 8, 8, 8, 8, 8, 8, 5, 5, 5, 5, 5, 5, 5, 5, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 255, 255, 0, 0, 0, 5, 255, 254, 254, 254, 255, 254, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 0, 0, 5, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 5, 0, 8, 8, 8, 8, 8, 8, 8, 8, 5, 240, 240, 0, 0, 5, 5, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 200, 0, 0, 255, 0, 0, 254, 254, 254, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 5, 0, 5, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 254, 254, 8, 8, 8, 8, 8, 0, 0, 254, 254, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 5, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 254, 0, 0, 0, 254, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 254, 0, 8, 8, 8, 8, 8, 200, 0, 5, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 5, 5, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 254, 0, 0, 0, 254, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 8, 8, 8, 8, 8, 8, 8, 0, 0, 5, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 5, 5, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 5, 0, 0, 5, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 5, 5, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 5, 0, 5, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 5, 0, 254, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 5, 5, 5, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 254, 254, 215, 254, 215, 254, 254, 255, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 215, 254, 254, 0, 0, 254, 254, 215, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 255, 255, 255, 255, 255, 255, 255, 255, 255, 5, 0, 5, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 254, 254, 254, 254, 254, 254, 254, 255, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 0, 0, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 255, 255, 255, 255, 255, 255, 255, 254, 5, 5, 5, 254, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 250, 250, 250, 250, 250, 250, 250, 254, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 0, 5, 0, 0, 0, 5, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 254, 255, 255, 255, 255, 254, 254, 254, 254, 5, 0, 5, 254, 254, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 250, 250, 250, 0, 0, 0, 0, 254, 0, 0, 0, 0, 5, 5, 0, 0, 5, 5, 5, 5, 0, 0, 5, 0, 0, 0, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 250, 255, 255, 255, 254, 254, 254, 254, 0, 5, 0, 5, 0, 254, 254, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 250, 250, 0, 0, 0, 250, 250, 0, 0, 0, 0, 5, 5, 0, 0, 0, 0, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 254, 254, 250, 250, 250, 250, 0, 0, 0, 250, 250, 254, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 255, 255, 255, 255, 254, 5, 250, 250, 250, 0, 0, 250, 0, 250, 250, 254, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 254, 255, 255, 255, 5, 250, 250, 0, 0, 0, 250, 250, 0, 0, 250, 250, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 254, 255, 255, 255, 250, 250, 250, 0, 250, 250, 250, 250, 250, 0, 250, 250, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 254, 5, 0, 255, 255, 255, 250, 250, 250, 0, 0, 250, 250, 250, 250, 0, 250, 250, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 254, 5, 0, 254, 215, 254, 250, 250, 250, 250, 0, 0, 0, 0, 0, 0, 250, 250, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 255, 255, 255, 255, 255, 254, 254, 215, 254, 254, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 5, 254, 254, 254, 250, 250, 250, 250, 250, 250, 250, 0, 0, 250, 250, 250, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 255, 255, 255, 255, 215, 254, 254, 254, 254, 254, 215, 255, 255, 255, 255, 255, 255, 255, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 5, 5, 0, 0, 0, 250, 250, 250, 250, 0, 0, 0, 0, 250, 250, 250, 250, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 254, 254, 215, 0, 0, 215, 254, 254, 255, 254, 254, 254, 0, 254, 254, 254, 255, 255, 255, 255, 255, 254, 215, 0, 0, 254, 254, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 254, 214, 254, 0, 0, 254, 214, 254, 255, 254, 5, 0, 0, 0, 0, 254, 255, 255, 255, 254, 254, 254, 254, 0, 0, 254, 254, 254, 254, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 250, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 0, 255, 5, 0, 0, 0, 5, 0, 5, 255, 255, 255, 254, 254, 0, 5, 0, 5, 0, 0, 254, 254, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 0, 255, 0, 5, 5, 5, 5, 5, 5, 254, 215, 254, 0, 0, 5, 5, 5, 0, 0, 0, 0, 0, 254, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 254, 254, 254, 0, 0, 5, 8, 8, 8, 8, 0, 0, 0, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 255, 255, 8, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 255, 255, 255, 255, 5, 5, 5, 0, 0, 5, 5, 5, 8, 0, 0, 5, 8, 8, 8, 8, 8, 8, 0, 0, 0, 254, 214, 214, 214, 214, 214, 254, 254, 214, 214, 214, 214, 214, 254, 254, 214, 214, 214, 214, 214, 254, 254, 214, 214, 214, 214, 214, 254, 254, 214, 214, 214, 214, 214, 254, 254, 254, 214, 214, 214, 214, 214, 254, 255, 255, 8, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 255, 255, 255, 255, 5, 0, 5, 0, 5, 0, 0, 0, 0, 5, 5, 5, 8, 8, 254, 254, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 0, 0, 0, 0, 250, 250, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 8, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 255, 255, 255, 255, 5, 5, 5, 5, 5, 0, 0, 5, 8, 0, 5, 5, 8, 8, 254, 254, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 250, 250, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 8, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 255, 255, 255, 255, 5, 5, 5, 5, 5, 5, 5, 255, 255, 255, 0, 0, 8, 8, 8, 8, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 250, 250, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 8, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 255, 255, 255, 255, 255, 255, 255, 5, 255, 255, 255, 255, 255, 255, 0, 5, 0, 8, 8, 8, 8, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 8, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 254, 0, 0, 254, 255, 255, 255, 255, 255, 255, 5, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 8, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 254, 254, 254, 0, 0, 254, 254, 254, 254, 254, 254, 254, 5, 254, 255, 255, 255, 255, 255, 255, 255, 0, 0, 5, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 254, 254, 215, 254, 254, 254, 254, 254, 254, 215, 8, 8, 254, 254, 254, 254, 215, 254, 254, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 254, 254, 0, 0, 0, 0, 254, 254, 254, 254, 254, 254, 5, 254, 254, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 8, 8, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 8, 0, 8, 0, 8, 5, 0, 254, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 254, 254, 254, 254, 215, 254, 254, 254, 254, 254, 215, 254, 254, 254, 254, 254, 254, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 254, 254, 254, 254, 254, 254, 254, 254, 254, 255, 255, 255, 255, 255, 255, 255, 5, 5, 5, 0, 0, 0, 0, 0, 5, 0, 5, 0, 5, 5, 5, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 255, 255, 255, 255, 255, 255, 255, 0, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 8, 8, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 250, 250, 250, 250, 250, 250, 5, 0, 0, 0, 0, 5, 5, 5, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 254, 215, 254, 254, 254, 254, 254, 215, 254, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 254, 254, 254, 255, 255, 255, 255, 255, 255, 255, 255, 255, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 250, 250, 250, 250, 250, 250, 0, 255, 255, 0, 5, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 254, 254, 254, 254, 254, 254, 254, 254, 254, 0, 8, 255, 255, 255, 8, 0, 0, 0, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 254, 254, 254, 255, 255, 255, 255, 255, 255, 255, 255, 255, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 255, 255, 255, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 254, 0, 254, 255, 255, 255, 255, 255, 255, 255, 255, 255, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 255, 8, 8, 8, 8, 8, 8, 8, 8, 255, 255, 255, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 254, 254, 254, 254, 254, 254, 254, 254, 255, 255, 255, 255, 255, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 255, 255, 255, 255, 8, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 5, 0, 255, 255, 255, 255, 255, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 255, 255, 255, 254, 0, 254, 0, 254, 255, 255, 255, 255, 255, 255, 255, 255, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 255, 255, 255, 255, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 255, 255, 255, 255, 255, 255, 254, 215, 254, 254, 254, 254, 254, 254, 254, 254, 215, 254, 255, 255, 254, 0, 254, 0, 254, 254, 255, 255, 255, 255, 255, 255, 255, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 254, 0, 0, 254, 255, 255, 255, 255, 254, 254, 254, 255, 255, 255, 255, 255, 255, 255, 0, 255, 255, 215, 254, 8, 254, 215, 255, 255, 255, 255, 255, 254, 254, 0, 254, 254, 255, 255, 255, 254, 254, 254, 0, 0, 0, 0, 0, 0, 0, 0, 254, 254, 254, 255, 254, 0, 254, 0, 254, 254, 254, 254, 254, 254, 254, 254, 254, 8, 254, 254, 254, 255, 255, 255, 255, 255, 255, 255, 255, 254, 254, 215, 254, 254, 254, 215, 254, 254, 0, 0, 254, 254, 254, 254, 254, 215, 254, 254, 254, 254, 254, 5, 5, 254, 255, 254, 215, 254, 254, 254, 254, 254, 215, 254, 254, 254, 254, 215, 0, 255, 255, 254, 254, 8, 254, 254, 255, 255, 254, 254, 215, 254, 254, 0, 254, 254, 215, 254, 254, 254, 254, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 254, 254, 254, 0, 0, 254, 0, 0, 254, 254, 254, 215, 254, 215, 254, 254, 8, 254, 254, 254, 255, 255, 255, 255, 255, 255, 255, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 0, 5, 0, 254, 254, 254, 254, 254, 254, 254, 254, 254, 0, 5, 5, 0, 254, 254, 254, 254, 5, 5, 5, 254, 254, 254, 254, 254, 254, 254, 0, 254, 255, 8, 8, 8, 8, 8, 254, 254, 254, 254, 254, 0, 0, 0, 5, 0, 254, 254, 254, 254, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 254, 254, 0, 0, 0, 0, 0, 254, 254, 254, 254, 254, 254, 254, 254, 8, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 254, 8, 254, 8, 254, 8, 254, 8, 254, 8, 0, 0, 0, 254, 254, 254, 254, 5, 0, 0, 0, 254, 5, 5, 5, 0, 254, 0, 0, 5, 5, 5, 5, 5, 0, 5, 0, 0, 0, 0, 0, 254, 254, 0, 8, 0, 8, 0, 254, 254, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 254, 254, 254, 254, 254, 254, 8, 254, 254, 254, 254, 214, 254, 214, 254, 214, 254, 254, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 5, 0, 5, 5, 5, 0, 0, 0, 0, 0, 5, 0, 5, 0, 0, 5, 0, 0, 0, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 254, 0, 8, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 8, 8, 8, 8, 8, 254, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 5, 0, 0, 5, 5, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 5, 0, 0, 5, 5, 5, 5, 5, 5, 5, 0, 0, 5, 5, 0, 0, 0, 0, 0, 1, 0, 0, 5, 5, 0, 255, 0, 5, 0, 0, 5, 5, 0, 255, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 0, 8, 8, 8, 8, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 0, 0, 0, 5, 5, 5, 0, 0, 0, 5, 5, 5, 5, 5, 5, 0, 0, 5, 5, 5, 0, 0, 0, 0, 1, 5, 0, 0, 0, 0, 255, 0, 5, 0, 0, 0, 0, 5, 255, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 8, 8, 8, 8, 0, 8, 8, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 0, 5, 5, 5, 0, 0, 5, 5, 5, 5, 0, 0, 0, 0, 5, 5, 5, 5, 5, 0, 0, 5, 5, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 8, 0, 0, 0, 8, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 5, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 0, 0, 0, 5, 5, 5, 0, 0, 0, 5, 0, 0, 5, 5, 5, 0, 0, 0, 0, 0, 255, 255, 255, 0, 1, 0, 255, 255, 255, 0, 0, 0, 0, 0, 255, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 8, 8, 0, 0, 0, 8, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 8, 8, 8, 8, 8, 8, 8, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 5, 5, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 8, 8, 0, 0, 0, 8, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 254, 8, 254, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 8, 8, 8, 8, 8, 8, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 254, 8, 254, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 8, 8, 8, 0, 8, 8, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 254, 8, 254, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 8, 8, 8, 8, 8, 8, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 254, 8, 254, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 8, 8, 0, 0, 0, 8, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 8, 8, 0, 0, 0, 8, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 254, 8, 254, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 8, 8, 0, 0, 0, 8, 8, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 254, 8, 254, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 8, 8, 8, 8, 8, 8, 8], "events": [null, {"id": 1, "name": "迷宫物件生成", "note": "<NoOptimize>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 0, "characterIndex": 3}, "list": [{"code": 108, "indent": 0, "parameters": ["恢复玩家自动朝向"]}, {"code": 355, "indent": 0, "parameters": ["ctb.useTurnPlayer = true"]}, {"code": 121, "indent": 0, "parameters": [3, 3, 0]}, {"code": 121, "indent": 0, "parameters": [14, 14, 1]}, {"code": 356, "indent": 0, "parameters": [">允许操作玩家移动 : 开启"]}, {"code": 356, "indent": 0, "parameters": ["particle set splash_walk1 walk:player:8 def below"]}, {"code": 356, "indent": 0, "parameters": ["particle set ripple_walk walk:player:8 def below"]}, {"code": 356, "indent": 0, "parameters": ["particle set dust_walk walk:player:0 def below"]}, {"code": 356, "indent": 0, "parameters": ["particle set grass_walk walk:player:5 def below"]}, {"code": 356, "indent": 0, "parameters": ["particle set ripple_walk walk:player:2 "]}, {"code": 108, "indent": 0, "parameters": ["灯光、坑洞预加载"]}, {"code": 355, "indent": 0, "parameters": ["QJ.MPMZ.tl.ex_LabyrinthScenePreload()"]}, {"code": 356, "indent": 0, "parameters": [">显现动作 : 玩家 : 移动显现 : 时间[30] : 方向角度[270] : 移动距离[100]"]}, {"code": 355, "indent": 0, "parameters": ["$gamePlayer.mppHidPasZ = 300"]}, {"code": 108, "indent": 0, "parameters": ["创造物管理"]}, {"code": 355, "indent": 0, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.event(eid).steupCEQJ(2)"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 117, "indent": 0, "parameters": [100]}, {"code": 241, "indent": 0, "parameters": [{"name": "PerituneMaterial_Lost_place_loop", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["AudioManager.fadeInBgm(2)"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameScreen.zoomScale() == 1"]}, {"code": 356, "indent": 1, "parameters": ["d<PERSON><PERSON><PERSON> 2 30 player"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 222, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 6, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["雨天重置的创造物"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameSelfVariables.value([$gameMap.mapId(), 1, 'rainyCreation']) > 0"]}, {"code": 108, "indent": 1, "parameters": ["生成石中剑"]}, {"code": 355, "indent": 1, "parameters": ["$gameMap.spawnEventQJ(1,85,32,13,true);"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.spawnEventQJ(1,85,19,39,true);"]}, {"code": 108, "indent": 1, "parameters": ["重置采集物"]}, {"code": 355, "indent": 1, "parameters": ["var events = $gameMap.drill_COET_getEventsByTag_direct(\"宝箱\");"]}, {"code": 655, "indent": 1, "parameters": ["events.forEach(function(event) {"]}, {"code": 655, "indent": 1, "parameters": ["    var eventId = event.eventId();"]}, {"code": 655, "indent": 1, "parameters": ["    var key1 = [$gameMap.mapId(), eventId, 'A'];"]}, {"code": 655, "indent": 1, "parameters": ["    var key2 = [$gameMap.mapId(), eventId, 'D'];"]}, {"code": 655, "indent": 1, "parameters": ["    $gameSelfSwitches.setValue(key1, false);"]}, {"code": 655, "indent": 1, "parameters": ["    $gameSelfSwitches.setValue(key2, false);"]}, {"code": 655, "indent": 1, "parameters": ["});"]}, {"code": 108, "indent": 1, "parameters": ["清除标记"]}, {"code": 355, "indent": 1, "parameters": ["$gameSelfVariables.setValue([$gameMap.mapId(), 1, 'rainyCreation'], 0);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["日常重置的创造物"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameSelfVariables.value([$gameMap.mapId(), 1, 'day']) < $gameSystem.day()"]}, {"code": 111, "indent": 1, "parameters": [12, "$gameMap.drill_COET_getEventsByTag_direct(\"魔物\").length > 40"]}, {"code": 115, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["更新标记天数"]}, {"code": 355, "indent": 1, "parameters": ["let day = $gameSystem.day();"]}, {"code": 655, "indent": 1, "parameters": ["$gameSelfVariables.setValue([$gameMap.mapId(), 1, 'day'], day)"]}, {"code": 108, "indent": 1, "parameters": ["鱼"]}, {"code": 355, "indent": 1, "parameters": ["let num = 2 + Math.randomInt(3);"]}, {"code": 655, "indent": 1, "parameters": ["if ($gameVariables.value(60) == 2) {"]}, {"code": 655, "indent": 1, "parameters": ["    num *= 2;"]}, {"code": 655, "indent": 1, "parameters": ["    num  = Math.max(num, 4);"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 655, "indent": 1, "parameters": ["QJ.SE.spawnRegionNum(1,9,[8],num,true);"]}, {"code": 108, "indent": 1, "parameters": ["蘑菇"]}, {"code": 355, "indent": 1, "parameters": ["let num = 5 + Math.randomInt(10);"]}, {"code": 655, "indent": 1, "parameters": ["QJ.SE.spawnRegionNum(1,49,[5],num,true);"]}, {"code": 108, "indent": 1, "parameters": ["罐子 "]}, {"code": 355, "indent": 1, "parameters": ["let num = 10 + Math.randomInt(15);"]}, {"code": 655, "indent": 1, "parameters": ["QJ.SE.spawnRegionNum(1,48,[0],num,true);"]}, {"code": 108, "indent": 1, "parameters": ["花"]}, {"code": 355, "indent": 1, "parameters": ["let num = 2 + Math.randomInt(5);"]}, {"code": 655, "indent": 1, "parameters": ["QJ.SE.spawnRegionNum(1,50,[5],num,true);"]}, {"code": 108, "indent": 1, "parameters": ["敌人"]}, {"code": 355, "indent": 1, "parameters": ["let num = 8 + Math.randomInt(12);"]}, {"code": 655, "indent": 1, "parameters": ["QJ.SE.spawnRegionNum(1,43,[0],num,true);"]}, {"code": 655, "indent": 1, "parameters": ["//行走菇"]}, {"code": 355, "indent": 1, "parameters": ["let num = 4 + Math.randomInt(4);"]}, {"code": 655, "indent": 1, "parameters": ["QJ.SE.spawnRegionNum(1,73,[8],num,true);"]}, {"code": 655, "indent": 1, "parameters": ["//水怪"]}, {"code": 355, "indent": 1, "parameters": ["let num = 5 + Math.randomInt(5);"]}, {"code": 655, "indent": 1, "parameters": ["QJ.SE.spawnRegionNum(1,51,[0],num,true);"]}, {"code": 655, "indent": 1, "parameters": ["//兽人"]}, {"code": 355, "indent": 1, "parameters": ["let num = 2 + Math.randomInt(2);"]}, {"code": 655, "indent": 1, "parameters": ["QJ.SE.spawnRegionNum(1,65,[0],num,true);"]}, {"code": 655, "indent": 1, "parameters": ["//怪鸟"]}, {"code": 355, "indent": 1, "parameters": ["let num = 0 + Math.randomInt(3);"]}, {"code": 655, "indent": 1, "parameters": ["if (num > 0) {"]}, {"code": 655, "indent": 1, "parameters": ["QJ.SE.spawnRegionNum(1,77,[0],num,true);"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 655, "indent": 1, "parameters": ["//恶魔猫"]}, {"code": 355, "indent": 1, "parameters": ["let num = 0 + Math.randomInt(3);"]}, {"code": 655, "indent": 1, "parameters": ["if (num > 0) {"]}, {"code": 655, "indent": 1, "parameters": ["QJ.SE.spawnRegionNum(1,91,[0],num,true);"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 655, "indent": 1, "parameters": ["//哔哩哔哩史莱姆"]}, {"code": 355, "indent": 1, "parameters": ["let num = 0 + Math.randomInt(3);"]}, {"code": 655, "indent": 1, "parameters": ["if (num > 0) {"]}, {"code": 655, "indent": 1, "parameters": ["QJ.SE.spawnRegionNum(1,88,[0],num,true);"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 655, "indent": 1, "parameters": ["//杰克史莱姆"]}, {"code": 355, "indent": 1, "parameters": ["let num = 1 + Math.randomInt(3);"]}, {"code": 655, "indent": 1, "parameters": ["if (num > 0) {"]}, {"code": 655, "indent": 1, "parameters": ["QJ.SE.spawnRegionNum(1,90,[8],num,true);"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 655, "indent": 1, "parameters": ["//急冻史莱姆"]}, {"code": 355, "indent": 1, "parameters": ["let num = 0 + Math.randomInt(4);"]}, {"code": 655, "indent": 1, "parameters": ["if (num > 0) {"]}, {"code": 655, "indent": 1, "parameters": ["QJ.SE.spawnRegionNum(1,24,[0],num,true);"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 655, "indent": 1, "parameters": ["//女仆兔"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$gameMap.drill_COET_getEventsByTag_direct(\"需监听\").length > 0"]}, {"code": 355, "indent": 2, "parameters": ["let monster = $gameMap.drill_COET_getEventsByTag_direct(\"需监听\");  "]}, {"code": 655, "indent": 2, "parameters": [" monster.forEach(function(target) {"]}, {"code": 655, "indent": 2, "parameters": ["let EID = target._eventId;"]}, {"code": 655, "indent": 2, "parameters": ["QJ.MPMZ.tl.ex_enemyConditionCheck(EID)"]}, {"code": 655, "indent": 2, "parameters": ["    });"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 37, "y": 4}, {"id": 2, "name": "door2", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!ruins_dungeon_door", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["=>标签核心 : 添加标签 : 机关桥"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!ruins_dungeon_door", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 16, "indent": null}, {"code": 15, "parameters": [6], "indent": null}, {"code": 17, "indent": null}, {"code": 15, "parameters": [6], "indent": null}, {"code": 18, "indent": null}, {"code": 15, "parameters": [6], "indent": null}, {"code": 19, "indent": null}, {"code": 15, "parameters": [6], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [6], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [6], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [6], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [6], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 123, "indent": 0, "parameters": ["B", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!ruins_dungeon_door", "direction": 8, "pattern": 1, "characterIndex": 6}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}], "x": 57, "y": 22}, null, null, null, null, null, null, null, null, {"id": 11, "name": "EV011", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!ruins_dungeon_door", "direction": 2, "pattern": 1, "characterIndex": 5}, "list": [{"code": 108, "indent": 0, "parameters": ["=>标签核心 : 添加标签 : 机关桥"]}, {"code": 111, "indent": 0, "parameters": [6, -1, 8]}, {"code": 356, "indent": 1, "parameters": [">对话框变形器 : 切换形状 : 变形样式[8]"]}, {"code": 356, "indent": 1, "parameters": [">对话框皮肤 : 只对话框 : 修改样式 : 样式[9]"]}, {"code": 355, "indent": 1, "parameters": ["let event = $gamePlayer;"]}, {"code": 655, "indent": 1, "parameters": ["let x = event.screenX() * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 1, "parameters": ["let y = (event.screenY() - 128) * $gameScreen.zoomScale();"]}, {"code": 655, "indent": 1, "parameters": ["$gameSystem._drill_DOp_curStyle['x_value'] = x ;"]}, {"code": 655, "indent": 1, "parameters": ["$gameSystem._drill_DOp_curStyle['y_value'] = y ;"]}, {"code": 655, "indent": 1, "parameters": ["$gameSystem._drill_DOp_curStyle['height_rowCount'] = 2;"]}, {"code": 655, "indent": 1, "parameters": ["$gameSystem._drill_DOp_curStyle['height_value'] = 48 * 2;"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["被铁栅栏紧禁关住的门。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["去其他地方找找看也许能"]}, {"code": 401, "indent": 1, "parameters": ["发现打开它的机关…"]}, {"code": 356, "indent": 1, "parameters": [">对话框皮肤 : 只对话框 : 修改样式 : 样式[3]"]}, {"code": 356, "indent": 1, "parameters": [">对话框变形器 : 还原默认形状样式"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!ruins_dungeon_door", "direction": 8, "pattern": 1, "characterIndex": 5}, "list": [{"code": 355, "indent": 0, "parameters": ["Zzy.TWF.ToTheWorld(true) "]}, {"code": 242, "indent": 0, "parameters": [2]}, {"code": 356, "indent": 0, "parameters": [">消失动作 : 玩家 : 移动消失 : 时间[60] : 方向角度[270] : 移动距离[40]"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Move1", "volume": 65, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 201, "indent": 0, "parameters": [0, 28, 29, 12, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": false}], "x": 85, "y": 56}, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, {"id": 35, "name": "凹み", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!ruins_dungeon_door", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>重力开关 : 独立开关[A] : 绑定持续触发"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 9, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!ruins_dungeon_door", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Switch3", "volume": 65, "pitch": 80, "pan": 0}]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$gameMap.getGroupBulletListQJ('Trap').length === 0"]}, {"code": 355, "indent": 2, "parameters": ["let target = this._eventId;"]}, {"code": 655, "indent": 2, "parameters": ["QJ.MPMZ.tl.ex_flameSpewingStatueTrap(target)"]}, {"code": 250, "indent": 2, "parameters": [{"name": "Fire1", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 230, "indent": 2, "parameters": [11]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [19]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": false}], "x": 52, "y": 66}, null, {"id": 37, "name": "EV037", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "$ruins_dungeon_object2", "direction": 4, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<width: 1.0>"]}, {"code": 408, "indent": 0, "parameters": ["<height: 1.0>"]}, {"code": 408, "indent": 0, "parameters": ["<offsetX: 0>"]}, {"code": 408, "indent": 0, "parameters": ["<offsetY: +0.5>"]}, {"code": 108, "indent": 0, "parameters": ["=>行走图锁定帧 : 锁定"]}, {"code": 355, "indent": 0, "parameters": ["let ID = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.event(ID)._oldrealX = $gameMap.event(ID)._realX;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.event(ID)._oldrealY = $gameMap.event(ID)._realY;"]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$gamePlayer.calcDistance(37) < 45"]}, {"code": 355, "indent": 2, "parameters": ["let ID = this._eventId;"]}, {"code": 655, "indent": 2, "parameters": ["let difX = $gameMap.event(ID)._realX - $gameMap.event(ID)._oldrealX;"]}, {"code": 655, "indent": 2, "parameters": ["let difY = $gameMap.event(ID)._realY - $gameMap.event(ID)._oldrealY;"]}, {"code": 655, "indent": 2, "parameters": ["if (difX !== 0 || difY !== 0){"]}, {"code": 655, "indent": 2, "parameters": ["$gamePlayer._realX += difX;$gamePlayer._realY += difY;"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 655, "indent": 2, "parameters": ["$gameMap.event(ID)._oldrealX = $gameMap.event(ID)._realX;"]}, {"code": 655, "indent": 2, "parameters": ["$gameMap.event(ID)._oldrealY = $gameMap.event(ID)._realY;"]}, {"code": 121, "indent": 2, "parameters": [188, 188, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["let ID = this._eventId;"]}, {"code": 655, "indent": 2, "parameters": ["$gameMap.event(ID)._oldrealX = $gameMap.event(ID)._realX;"]}, {"code": 655, "indent": 2, "parameters": ["$gameMap.event(ID)._oldrealY = $gameMap.event(ID)._realY;"]}, {"code": 121, "indent": 2, "parameters": [188, 188, 1]}, {"code": 230, "indent": 2, "parameters": [10]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 45, "parameters": ["$gameMap.event(this._eventId).moveToTarget(53,37.7)"], "indent": null}, {"code": 15, "parameters": [15], "indent": null}, {"code": 45, "parameters": ["$gameMap.event(this._eventId).moveToTarget(50,37.7)"], "indent": null}, {"code": 15, "parameters": [15], "indent": null}, {"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 0, "stepAnime": false, "through": true, "trigger": 4, "walkAnime": false}], "x": 53, "y": 37}, null, null, null, {"id": 41, "name": "EV041", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<width: 2.5>"]}, {"code": 408, "indent": 0, "parameters": ["<height: 6>"]}, {"code": 355, "indent": 0, "parameters": ["this.count = 0"]}, {"code": 313, "indent": 0, "parameters": [0, 1, 0, 66]}, {"code": 355, "indent": 0, "parameters": ["let XX = $gamePlayer._realX;"]}, {"code": 655, "indent": 0, "parameters": ["$gamePlayer.moveToTarget(XX,50)"]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "!$gamePlayer.isMoved()"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 355, "indent": 1, "parameters": ["this.count++"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "this.count >= 10"]}, {"code": 212, "indent": 1, "parameters": [-1, 182, false]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 313, "indent": 0, "parameters": [0, 1, 1, 66]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 82, "y": 44}, null, null, null, null, null, {"id": 47, "name": "EV047", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<width: 1>"]}, {"code": 408, "indent": 0, "parameters": ["<height: 16>"]}, {"code": 355, "indent": 0, "parameters": ["this.count = 0;"]}, {"code": 655, "indent": 0, "parameters": ["$gamePlayer._through = true;"]}, {"code": 313, "indent": 0, "parameters": [0, 1, 0, 66]}, {"code": 355, "indent": 0, "parameters": ["let xx = $gamePlayer.centerRealX();"]}, {"code": 655, "indent": 0, "parameters": ["$gamePlayer.moveToTarget(xx,16)"]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "!$gamePlayer.isMoved()"]}, {"code": 355, "indent": 2, "parameters": ["$gamePlayer._through = false;"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 355, "indent": 1, "parameters": ["this.count++"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "this.count >= 10"]}, {"code": 212, "indent": 1, "parameters": [-1, 182, false]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 313, "indent": 0, "parameters": [0, 1, 1, 66]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 64, "y": 0}, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, {"id": 65, "name": "隐藏门", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!ruins_dungeon_door", "direction": 8, "pattern": 1, "characterIndex": 5}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Move", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 201, "indent": 0, "parameters": [0, 47, 48, 19, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": false}], "x": 8, "y": 19}, {"id": 66, "name": "宝箱", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!fsm_chest01", "direction": 2, "pattern": 0, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["=>标签核心 : 添加标签 : 宝箱"]}, {"code": 108, "indent": 0, "parameters": ["<Group:\"object\">"]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 36, "indent": null}, {"code": 16, "indent": null}, {"code": 15, "parameters": [4], "indent": null}, {"code": 17, "indent": null}, {"code": 15, "parameters": [4], "indent": null}, {"code": 18, "indent": null}, {"code": 15, "parameters": [4], "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [4]}, {"code": 250, "indent": 0, "parameters": [{"name": "UI系　かっこいい装備音　", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 126, "indent": 0, "parameters": [53, 0, 0, 1]}, {"code": 127, "indent": 0, "parameters": [15, 0, 0, 1, false]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!fsm_chest01", "direction": 8, "pattern": 1, "characterIndex": 1}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 7, "y": 55}, {"id": 67, "name": "EV067", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!fsm_chest01", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>标签核心 : 添加标签 : 宝箱"]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 36, "indent": null}, {"code": 16, "indent": null}, {"code": 15, "parameters": [4], "indent": null}, {"code": 17, "indent": null}, {"code": 15, "parameters": [4], "indent": null}, {"code": 18, "indent": null}, {"code": 15, "parameters": [4], "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [4]}, {"code": 250, "indent": 0, "parameters": [{"name": "UI系　かっこいい装備音　", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 127, "indent": 0, "parameters": [16, 0, 0, 1, false]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!fsm_chest01", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 55, "y": 23}, {"id": 68, "name": "宝箱", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!fsm_chest01", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>标签核心 : 添加标签 : 宝箱"]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 36, "indent": null}, {"code": 16, "indent": null}, {"code": 15, "parameters": [4], "indent": null}, {"code": 17, "indent": null}, {"code": 15, "parameters": [4], "indent": null}, {"code": 18, "indent": null}, {"code": 15, "parameters": [4], "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [4]}, {"code": 250, "indent": 0, "parameters": [{"name": "UI系　かっこいい装備音　", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 127, "indent": 0, "parameters": [103, 0, 0, 1, false]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!fsm_chest01", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 11, "y": 21}, null, null, {"id": 71, "name": "EV071", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!$Gate1", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<width: 3>"]}, {"code": 408, "indent": 0, "parameters": ["<height: 1>"]}, {"code": 408, "indent": 0, "parameters": ["<offsetX: 0>"]}, {"code": 408, "indent": 0, "parameters": ["<offsetY: 0>"]}, {"code": 108, "indent": 0, "parameters": ["=>持续动作 : 缩放状态 : 缩放比例[1.0]"]}, {"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[25,0]"]}, {"code": 355, "indent": 0, "parameters": ["Zzy.TWF.ToTheWorld(true) "]}, {"code": 356, "indent": 0, "parameters": [">消失动作 : 玩家 : 移动消失 : 时间[60] : 方向角度[270] : 移动距离[40]"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Move1", "volume": 65, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 201, "indent": 0, "parameters": [0, 10, 26, 8, 2, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": false}], "x": 30, "y": 12}, {"id": 72, "name": "EV072", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<width: 1>"]}, {"code": 408, "indent": 0, "parameters": ["<height: 3>"]}, {"code": 355, "indent": 0, "parameters": ["this.count = 0"]}, {"code": 313, "indent": 0, "parameters": [0, 1, 0, 66]}, {"code": 355, "indent": 0, "parameters": ["let XX = $gamePlayer._realX;"]}, {"code": 655, "indent": 0, "parameters": ["$gamePlayer.moveToTarget(XX,64)"]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "!$gamePlayer.isMoved()"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 355, "indent": 1, "parameters": ["this.count++"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "this.count >= 10"]}, {"code": 212, "indent": 1, "parameters": [-1, 182, false]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 313, "indent": 0, "parameters": [0, 1, 1, 66]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 96, "y": 61}, null, null, null, null, {"id": 77, "name": "凹み", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!ruins_dungeon_door", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>重力开关 : 独立开关[A] : 绑定持续触发"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 9, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!ruins_dungeon_door", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Switch3", "volume": 65, "pitch": 80, "pan": 0}]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$gameMap.getGroupBulletListQJ('Trap').length === 0"]}, {"code": 355, "indent": 2, "parameters": ["let target = this._eventId;"]}, {"code": 655, "indent": 2, "parameters": ["QJ.MPMZ.tl.ex_flameSpewingStatueTrap(target)"]}, {"code": 250, "indent": 2, "parameters": [{"name": "Fire1", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 230, "indent": 2, "parameters": [11]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [19]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": false}], "x": 52, "y": 65}, {"id": 78, "name": "下层阶梯", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 67, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["Zzy.TWF.ToTheWorld(true) "]}, {"code": 242, "indent": 0, "parameters": [2]}, {"code": 201, "indent": 0, "parameters": [0, 28, 6, 5, 8, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": false}], "x": 10, "y": 23}, {"id": 79, "name": "凹み", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!ruins_dungeon_door", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>重力开关 : 独立开关[A] : 绑定持续触发"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 9, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!ruins_dungeon_door", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Switch3", "volume": 65, "pitch": 80, "pan": 0}]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$gameMap.getGroupBulletListQJ('Trap').length === 0"]}, {"code": 355, "indent": 2, "parameters": ["let target = this._eventId;"]}, {"code": 655, "indent": 2, "parameters": ["QJ.MPMZ.tl.ex_flameSpewingStatueTrap(target)"]}, {"code": 250, "indent": 2, "parameters": [{"name": "Fire1", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 230, "indent": 2, "parameters": [11]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [19]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": false}], "x": 52, "y": 67}, {"id": 80, "name": "凹み", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!ruins_dungeon_door", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>重力开关 : 独立开关[A] : 绑定持续触发"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 9, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!ruins_dungeon_door", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Switch3", "volume": 65, "pitch": 80, "pan": 0}]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$gameMap.getGroupBulletListQJ('Trap').length === 0"]}, {"code": 355, "indent": 2, "parameters": ["let target = this._eventId;"]}, {"code": 655, "indent": 2, "parameters": ["QJ.MPMZ.tl.ex_flameSpewingStatueTrap(target)"]}, {"code": 250, "indent": 2, "parameters": [{"name": "Fire1", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 230, "indent": 2, "parameters": [11]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [19]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": false}], "x": 52, "y": 68}, null, null, null, null, null, null, null, null, null, {"id": 90, "name": "EV090", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["QJ.MPMZ.Shoot({"]}, {"code": 655, "indent": 0, "parameters": ["img:\"null1\",position:[['E',0],['E',0]],"]}, {"code": 655, "indent": 0, "parameters": ["initialRotation:['S',0],imgRotation:['F'],"]}, {"code": 655, "indent": 0, "parameters": [" moveType:['S',0],"]}, {"code": 655, "indent": 0, "parameters": ["collisionBox:['C',12],"]}, {"code": 655, "indent": 0, "parameters": ["existData:["]}, {"code": 655, "indent": 0, "parameters": ["{t:['G',['\"enemy\"','\"object\"']],a:['C',133,[4,0,0]],p:[-1,false,true],c:['T',0,4,true]},"]}, {"code": 655, "indent": 0, "parameters": ["{t:['P'],a:['C',133,[4,0,0]],p:[-1,false,true],c:['T',0,15,true]},\t\t"]}, {"code": 655, "indent": 0, "parameters": ["   ],"]}, {"code": 655, "indent": 0, "parameters": ["});"]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 47, "y": 29}, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, {"id": 145, "name": "door", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!ruins_dungeon_door", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 10, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!ruins_dungeon_door", "direction": 8, "pattern": 1, "characterIndex": 6}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}], "x": 57, "y": 28}, {"id": 146, "name": "EV146", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!ruins_dungeon", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 205, "indent": 0, "parameters": [145, {"list": [{"code": 17, "indent": null}, {"code": 15, "parameters": [8], "indent": null}, {"code": 18, "indent": null}, {"code": 15, "parameters": [8], "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [8], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [8], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [148, {"list": [{"code": 17, "indent": null}, {"code": 15, "parameters": [8], "indent": null}, {"code": 18, "indent": null}, {"code": 15, "parameters": [8], "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [8], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [8], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 44, "parameters": [{"name": "Open3", "volume": 100, "pitch": 80, "pan": 0}], "indent": null}, {"code": 17, "indent": null}, {"code": 15, "parameters": [8], "indent": null}, {"code": 18, "indent": null}, {"code": 15, "parameters": [8], "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "Open3", "volume": 100, "pitch": 80, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [8], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [8], "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">独立开关 : 事件[145] : A : 开启"]}, {"code": 230, "indent": 0, "parameters": [8]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 10, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!ruins_dungeon", "direction": 6, "pattern": 1, "characterIndex": 1}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 55, "y": 29}, {"id": 147, "name": "2", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!ruins_dungeon", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 44, "parameters": [{"name": "Open3", "volume": 90, "pitch": 80, "pan": 0}], "indent": null}, {"code": 17, "indent": null}, {"code": 15, "parameters": [8], "indent": null}, {"code": 18, "indent": null}, {"code": 15, "parameters": [8], "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "Open3", "volume": 90, "pitch": 80, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [8], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [8], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [8]}, {"code": 250, "indent": 0, "parameters": [{"name": "Earth4", "volume": 90, "pitch": 90, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["$gameSelfSwitches.setValue([28, 5, 'A'], true);"]}, {"code": 225, "indent": 0, "parameters": [5, 5, 90, false]}, {"code": 230, "indent": 0, "parameters": [40]}, {"code": 355, "indent": 0, "parameters": ["var events = $gameMap.drill_COET_getEventsByTag_direct(\"机关桥\");"]}, {"code": 655, "indent": 0, "parameters": ["events.forEach(function(event) {"]}, {"code": 655, "indent": 0, "parameters": ["    var eventId = event.eventId();"]}, {"code": 655, "indent": 0, "parameters": ["    var key = [$gameMap.mapId(), eventId, 'A'];"]}, {"code": 655, "indent": 0, "parameters": ["    $gameSelfSwitches.setValue(key, true);"]}, {"code": 655, "indent": 0, "parameters": ["});"]}, {"code": 230, "indent": 0, "parameters": [50]}, {"code": 355, "indent": 0, "parameters": ["var text;"]}, {"code": 655, "indent": 0, "parameters": ["if ( $gameVariables.value(1) == 1) {"]}, {"code": 655, "indent": 0, "parameters": ["   text = \"どこかの機関が作動した\";"]}, {"code": 655, "indent": 0, "parameters": ["} else if ( $gameVariables.value(1) == 0) {"]}, {"code": 655, "indent": 0, "parameters": ["   text = \"某处的机关运作了！\""]}, {"code": 655, "indent": 0, "parameters": ["} else {"]}, {"code": 655, "indent": 0, "parameters": ["   text = \"A mechanism somewhere has been activated\";"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 655, "indent": 0, "parameters": ["$gameStrings.setValue(5,text);"]}, {"code": 356, "indent": 0, "parameters": [">对话框皮肤 : 只对话框 : 修改样式 : 样式[3]"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[3]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 0, "parameters": ["\\py[120]\\dac\\{\\c[2]\\>\\str[5]"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 还原默认形状样式"]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 11, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!ruins_dungeon", "direction": 6, "pattern": 1, "characterIndex": 1}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}], "x": 59, "y": 23}, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null]}