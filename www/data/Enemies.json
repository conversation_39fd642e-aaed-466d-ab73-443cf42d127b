[null, {"id": 1, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 21, "dataId": 2, "value": 3}], "gold": 0, "name": "初回限定奖励", "note": "<Loot Table>\nWeapon 5:100\nWeapon 15:100\nWeapon 16:100\nWeapon 17:100\nWeapon 103:100\nWeapon 104:100\n</Loot Table>", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 2, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "随机天气", "note": "<Loot Table>\nsunnyDay: 60\ncloudyDay: 30\nrainyDay: 15\n</Loot Table>", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 3, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "通用模板", "note": "<fireworkColor:#ffffff>", "params": [100, 0, 10, 1, 10, 10, 10, 10]}, {"id": 4, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"kind": 3, "dataId": 70, "denominator": 1}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 13, "dataId": 10, "value": 0.01}, {"code": 13, "dataId": 6, "value": 0}, {"code": 13, "dataId": 7, "value": 0}, {"code": 13, "dataId": 11, "value": 0}], "gold": 0, "name": "训练假人", "note": "", "params": [999999, 0, 10, 1, 10, 1, 10, 10]}, {"id": 5, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "自动贩售机", "note": "<Loot Table>\nItem 23: 10\nItem 24: 10\nItem 25: 10\nItem 26: 10\nItem 27: 10\n</Loot Table>", "params": [700, 0, 10, 10, 10, 80, 10, 10]}, {"id": 6, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [], "gold": 0, "name": "aki神秘商店", "note": "<Loot Table>\nItem 30:10\nItem 31:10\n</Loot Table>", "params": [5000, 0, 89, 44, 44, 22, 22, 10]}, {"id": 7, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "浅层遗迹迷宫-罐子", "note": "<Loot Table 66%>\nItem 4:50\nItem 5:50\nItem 6:50\nItem 70:20\nRune: 5\nTarot: 5\nCommon: 5\nUncommon: 5\n</Loot Table>\n\n<Loot Table 33%>\nItem 60:20\nItem 4:50\nItem 5:50\nItem 6:50\nItem 70:20\nRune: 5\nTarot: 5\nCommon: 5\nUncommon: 5\n</Loot Table>\n\n<Loot Table 2%>\nItem 60:20\nItem 8:20\n</Loot Table>\n", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 8, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "浅层遗迹迷宫-蘑菇", "note": "<Loot Table 99%>\nItem 116:5\nItem 117:5\nItem 118:5\nItem 119:2\nItem 120:2\n</Loot Table>\n", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 9, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "浅层遗迹迷宫-花", "note": "<Loot Table 66%>\nItem 4:30\nItem 5:30\nItem 6:20\nItem 7:10\nItem 8:5\nCommon: 5\nUncommon: 5\n</Loot Table>\n\n<Loot Table 10%>\nArmor 26:15\n</Loot Table>", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 10, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "矿洞-矿石", "note": "<Loot Table 99%>\nItem 311:30\nItem 312:30\nArmor 8:50\nArmor 9:10\nRune: 5\nCommon: 5\n</Loot Table>\n\n<Loot Table 50%>\nItem 311:30\nItem 312:30\nArmor 8:50\nArmor 9:10\nRune: 5\nCommon: 5\n</Loot Table>\n", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 11, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "挡住的石壁", "note": "", "params": [2500, 0, 10, 40, 10, 10, 10, 10]}, {"id": 12, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"kind": 3, "dataId": 12, "denominator": 1}, {"kind": 3, "dataId": 13, "denominator": 1}, {"kind": 3, "dataId": 15, "denominator": 1}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "猫史莱姆", "note": "<fireworkColor:#afb4be>\n\n<Loot Table 60%>\nArmor 36:20\nArmor 15:50\nArmor 12:50\nCommon: 2\nUncommon: 2\n</Loot Table>\n\n<Loot Table 50%>\nItem 100:100\nItem 4:50\nItem 5:30\nCommon: 2\nUncommon: 2\n</Loot Table>", "params": [100, 0, 10, 10, 10, 30, 10, 10]}, {"id": 13, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"kind": 1, "dataId": 173, "denominator": 1}, {"kind": 2, "dataId": 60, "denominator": 1}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "香蕉猫", "note": "<fireworkColor:#f9ff49>\n\n<Loot Table 70%>\nItem 173:50\nWeapon 60:50\n</Loot Table>\n\n<Loot Table 66%>\nItem 4:50\nItem 5:30\nItem 6:20\n</Loot Table>", "params": [100, 0, 10, 10, 10, 50, 10, 10]}, {"id": 14, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"kind": 1, "dataId": 175, "denominator": 1}, {"kind": 3, "dataId": 33, "denominator": 1}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "苹果猫", "note": "<fireworkColor:#cc1919>\n\n<Loot Table 40%>\nItem 175:50\nArmor 33:50\n</Loot Table>\n\n<Loot Table 66%>\nItem 4:50\nItem 5:30\nItem 6:20\n</Loot Table>", "params": [100, 0, 10, 10, 10, 50, 10, 10]}, {"id": 15, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"kind": 2, "dataId": 27, "denominator": 1}, {"kind": 1, "dataId": 98, "denominator": 1}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}, {"code": 13, "dataId": 6, "value": 0.1}, {"code": 13, "dataId": 5, "value": 0.1}], "gold": 0, "name": "走路菇", "note": "<fireworkColor:#af83aa>\n\n<Loot Table 20%>\nWeapon 27:100\nItem 98:100\nItem 4:50\nItem 5:30\nItem 6:20\nCommon: 2\nUncommon: 2\n</Loot Table>\n\n<Loot Table 30%>\nWeapon 27:100\n</Loot Table>\n\n<Loot Table 50%>\nItem 98:100\nItem 4:50\nItem 5:30\nItem 6:20\nCommon: 2\nUncommon: 2\n</Loot Table>", "params": [90, 0, 10, 2, 10, 10, 10, 10]}, {"id": 16, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"kind": 1, "dataId": 90, "denominator": 1}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "角兔", "note": "<fireworkColor:#af83aa>\n\n<Loot Table 50%>\nItem 90:30\nCommon: 2\nUncommon: 2\n</Loot Table>\n\n<Loot Table 20%>\nItem 90:30\nCommon: 2\nUncommon: 2\n</Loot Table>", "params": [100, 0, 10, 1, 10, 20, 10, 10]}, {"id": 17, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"kind": 2, "dataId": 18, "denominator": 1}, {"kind": 1, "dataId": 90, "denominator": 1}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "初级兽人", "note": "<fireworkColor:#abbf6c>\n\n<Loot Table 33%>\nWeapon 18:30\nItem 5:30\nItem 6:30\nItem 7:30\nItem 89:50\nItem 90:50\n</Loot Table>\n\n<Loot Table 30%>\nWeapon 18:100\n</Loot Table>\n\n<Loot Table 60%>\nItem 5:20\nItem 6:20\nItem 7:20\nItem 89:50\nItem 90:50\n</Loot Table>", "params": [180, 0, 24, 10, 10, 10, 10, 10]}, {"id": 18, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"kind": 2, "dataId": 13, "denominator": 1}, {"kind": 3, "dataId": 65, "denominator": 1}, {"kind": 3, "dataId": 45, "denominator": 1}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}, {"code": 13, "dataId": 5, "value": 0.1}], "gold": 0, "name": "丧尸", "note": "<fireworkColor:#4b7a45>\n\n<Loot Table 40%>\nItem 3:100\nItem 5:30\nItem 6:30\nWeapon 13:100\nArmor 45:50\nArmor 65:50\n</Loot Table>\n\n<Loot Table 40%>\nWeapon 13:100\nArmor 45:50\nArmor 65:50\n</Loot Table>", "params": [160, 0, 10, 1, 10, 10, 10, 10]}, {"id": 19, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 20, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"kind": 3, "dataId": 40, "denominator": 1}, {"kind": 3, "dataId": 41, "denominator": 1}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "女仆兔", "note": "\n<Loot Table 90%>\nItem 104:30\nItem 6:30\nItem 7:20\nItem 8:10\n</Loot Table>\n\n<Loot Table 25%>\nItem 104:30\nArmor 40:50\nArmor 41:50\n</Loot Table>", "params": [160, 0, 32, 4, 10, 40, 10, 10]}, {"id": 21, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "遗迹之森-箱子", "note": "<Loot Table 66%>\nItem 5:30\nItem 6:30\nItem 7:30\nItem 8:10\nCommon: 10\nUncommon: 10\n</Loot Table>\n\n<Loot Table 95%>\nItem 5:30\nItem 6:30\nItem 7:30\nItem 8:10\nCommon: 20\nUncommon: 20\n</Loot Table>\n\n<Loot Table 50%>\nItem 70:20\nItem 71:20\nItem 72:20\nTarot:20\n</Loot Table>\n\n<Loot Table 20%>\nArmor 126:20\nArmor 127:20\nArmor 128:20\n</Loot Table>\n", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 22, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"kind": 2, "dataId": 8, "denominator": 1}, {"kind": 1, "dataId": 88, "denominator": 1}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "怪鸟", "note": "<fireworkColor:#fcfcfc>\n\n<Loot Table 66%>\nItem 4:50\nItem 5:30\nItem 6:20\n</Loot Table>\n\n<Loot Table 50%>\nItem 88:50\nWeapon 8:15\n</Loot Table>", "params": [240, 0, 10, 6, 10, 50, 10, 10]}, {"id": 23, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"kind": 1, "dataId": 97, "denominator": 1}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}, {"code": 13, "dataId": 6, "value": 0}, {"code": 13, "dataId": 7, "value": 2}], "gold": 0, "name": "水怪", "note": "<fireworkColor:#26bfd5>\n\n<Loot Table 66%>\nItem 4:50\nItem 5:30\nItem 6:20\n</Loot Table>\n\n<Loot Table 20%>\nArmor 8:100\n</Loot Table>\n\n<Loot Table 50%>\nItem 97:10\n</Loot Table>\n", "params": [60, 0, 16, 15, 16, 100, 10, 10]}, {"id": 24, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 25, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "初入茅庐的剑圣", "note": "", "params": [100, 0, 15, 10, 10, 30, 10, 10]}, {"id": 26, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"kind": 3, "dataId": 62, "denominator": 1}, {"kind": 3, "dataId": 8, "denominator": 1}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "鬼火", "note": "<Loot Table 66%>\nItem 4:50\nItem 5:30\nItem 6:20\nRune: 5\nTarot: 5\nCommon: 2\nUncommon: 2\n</Loot Table>\n\n<Loot Table 30%>\nArmor 8:100\nArmor 10:5\nArmor 62:30\n</Loot Table>", "params": [80, 0, 10, 20, 10, 100, 10, 10]}, {"id": 27, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"kind": 2, "dataId": 26, "denominator": 1}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "恶魔猫", "note": "<fireworkColor:#afb4be>\n\n<Loot Table 50%>\nItem 4:50\nItem 5:30\nItem 6:20\nRune: 5\nTarot: 5\nCommon: 2\nUncommon: 2\n</Loot Table>\n\n<Loot Table 40%>\nArmor 36:100\nArmor 11:20\nWeapon 26:200\n</Loot Table>\n\n<Loot Table 10%>\nItem 44:10\nItem 45:10\n</Loot Table>", "params": [100, 0, 10, 2, 10, 80, 10, 10]}, {"id": 28, "actions": [], "battlerHue": 0, "battlerName": "", "dropItems": [{"kind": 3, "dataId": 47, "denominator": 1}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 13, "dataId": 6, "value": 0.1}, {"code": 13, "dataId": 5, "value": 0.1}, {"code": 13, "dataId": 8, "value": 1.5}], "gold": 0, "name": "素质手指", "note": "<Loot Table 66%>\nArmor 46:30\nArmor 47:30\nCommon: 2\nUncommon: 2\n</Loot Table>", "params": [300, 0, 28, 1, 28, 60, 10, 10]}, {"id": 29, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 30, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"kind": 3, "dataId": 14, "denominator": 1}, {"kind": 1, "dataId": 100, "denominator": 1}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "草食史莱姆", "note": "<Loot Table 20%>\nWeapon 1:30\nWeapon 2:30\nArmor 14:60\nCommon: 2\nUncommon: 2\nItem 100:100\nItem 4:50\nItem 5:30\n</Loot Table>\n\n<Loot Table 40%>\nWeapon 1:20\nWeapon 2:20\nWeapon 36:2\nArmor 14:60\nCommon: 2\nUncommon: 2\n</Loot Table>\n\n<Loot Table 50%>\nItem 100:100\nItem 4:50\nItem 5:30\n</Loot Table>", "params": [80, 0, 12, 1, 12, 40, 10, 10]}, {"id": 31, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"kind": 3, "dataId": 42, "denominator": 1}, {"kind": 1, "dataId": 70, "denominator": 1}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "杰克史莱姆", "note": "<Loot Table 33%>\nItem 100:50\nItem 4:50\nItem 5:30\nItem 70:20\nCommon: 2\nUncommon: 2\n</Loot Table>\n\n<Loot Table 90%>\nItem 70:20\nCommon: 2\nUncommon: 2\nArmor 42: 2\n</Loot Table>\n\n<Loot Table 50%>\nItem 100:50\nItem 4:50\nItem 5:30\n</Loot Table>", "params": [2, 0, 12, 999, 12, 999, 10, 10]}, {"id": 32, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"kind": 2, "dataId": 21, "denominator": 1}, {"kind": 3, "dataId": 8, "denominator": 1}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}, {"code": 13, "dataId": 6, "value": 0.1}, {"code": 13, "dataId": 5, "value": 0.1}], "gold": 0, "name": "恶灵史莱姆", "note": "<Loot Table 33%>\nArmor 10:5\nArmor 8:50\nArmor 62:30\nWeapon 21:30\nCommon: 2\nUncommon: 2\nItem 100:100\nItem 4:50\nItem 5:30\nItem 6:30\n</Loot Table>\n\n<Loot Table 20%>\nArmor 10:5\nArmor 8:50\nArmor 62:30\nWeapon 21:30\nCommon: 2\nUncommon: 2\n</Loot Table>\n\n<Loot Table 50%>\nItem 100:100\nItem 4:50\nItem 5:30\nItem 6:30\n</Loot Table>", "params": [220, 0, 32, 5, 12, 100, 10, 10]}, {"id": 33, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"kind": 2, "dataId": 7, "denominator": 1}, {"kind": 2, "dataId": 104, "denominator": 1}, {"kind": 2, "dataId": 15, "denominator": 1}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}, {"code": 13, "dataId": 6, "value": 0.1}, {"code": 13, "dataId": 9, "value": 0}], "gold": 0, "name": "急冻史莱姆", "note": "<Loot Table 33%>\nWeapon 7:15\nWeapon 15:15\nWeapon 104:15\nArmor 8:30\nItem 100:100\nItem 4:50\nItem 5:30\nItem 5:60\nCommon: 2\nUncommon: 2\n</Loot Table>\n\n<Loot Table 40%>\nWeapon 7:15\nWeapon 15:15\nWeapon 104:15\nArmor 8:30\nCommon: 2\nUncommon: 2\n</Loot Table>\n\n<Loot Table 50%>\nItem 100:100\nItem 4:50\nItem 5:30\nItem 5:60\n</Loot Table>", "params": [100, 0, 12, 10, 12, 100, 10, 10]}, {"id": 34, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"kind": 2, "dataId": 16, "denominator": 1}, {"kind": 2, "dataId": 105, "denominator": 1}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}, {"code": 13, "dataId": 6, "value": 0.1}, {"code": 13, "dataId": 7, "value": 0}], "gold": 0, "name": "哔哩哔哩史莱姆", "note": "<Loot Table 33%>\nItem 4:50\nItem 5:30\nItem 5:60\nItem 100:30\nWeapon 16:30\nWeapon 105:10\nArmor 8:30\n</Loot Table>\n\n<Loot Table 40%>\nWeapon 16:30\nWeapon 105:10\n</Loot Table>\n\n<Loot Table 50%>\nItem 100:100\nItem 4:50\nItem 5:30\n</Loot Table>", "params": [150, 0, 12, 5, 12, 100, 10, 10]}, {"id": 35, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"kind": 3, "dataId": 8, "denominator": 1}, {"kind": 3, "dataId": 9, "denominator": 1}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}, {"code": 13, "dataId": 6, "value": 0}, {"code": 13, "dataId": 7, "value": 0}, {"code": 13, "dataId": 5, "value": 0}], "gold": 0, "name": "矿石史莱姆", "note": "<Loot Table 25%>\nArmor 8:30\nArmor 9:5\nCommon: 2\nUncommon: 2\n</Loot Table>\n\n<Loot Table 50%>\nArmor 8:30\nArmor 9:5\nCommon: 2\nUncommon: 2\n</Loot Table>\n\n<Loot Table 50%>\nItem 100:10\n</Loot Table>", "params": [180, 0, 28, 20, 10, 100, 10, 10]}, {"id": 36, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"kind": 3, "dataId": 73, "denominator": 1}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 13, "dataId": 9, "value": 0.1}, {"code": 13, "dataId": 6, "value": 0}, {"code": 13, "dataId": 8, "value": 0}, {"code": 13, "dataId": 10, "value": 0.5}], "gold": 0, "name": "燃烧史莱姆", "note": "<Loot Table 50%>\nArmor 73:30\nUncommon: 2\nRare: 4\n</Loot Table>", "params": [400, 0, 10, 15, 56, 200, 10, 10]}, {"id": 37, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 38, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"kind": 3, "dataId": 7, "denominator": 1}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}, {"code": 13, "dataId": 5, "value": 1.5}, {"code": 13, "dataId": 6, "value": 0.1}], "gold": 0, "name": "小红怪", "note": "<fireworkColor:#ab001c>\n\n<Loot Table 33%>\nWeapon 21:30\nArmor 7:30\nCommon: 1\nUncommon: 1\n</Loot Table>\n\n<Loot Table 40%>\nWeapon 21:30\nArmor 7:30\nCommon: 1\nUncommon: 1\n</Loot Table>\n\n<Loot Table 30%>\nItem 4:50\nItem 5:30\nItem 6:30\n</Loot Table>", "params": [100, 0, 10, 10, 10, 40, 10, 10]}, {"id": 39, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 40, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 13, "dataId": 9, "value": 0.1}, {"code": 13, "dataId": 6, "value": 0}, {"code": 13, "dataId": 8, "value": 0}, {"code": 13, "dataId": 10, "value": 0.5}], "gold": 0, "name": "火焰妖精", "note": "<Loot Table 35%>\nWeapon 6:30\n</Loot Table>\n\n<Loot Table 5%>\nUncommon: 2\nRare: 4\n</Loot Table>", "params": [550, 0, 30, 10, 40, 150, 10, 10]}, {"id": 41, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 13, "dataId": 9, "value": 0.1}, {"code": 13, "dataId": 6, "value": 0}, {"code": 13, "dataId": 8, "value": 0}, {"code": 13, "dataId": 10, "value": 0.1}], "gold": 0, "name": "炎魔", "note": "<Loot Table 55%>\nWeapon 6:30\nWeapon 17:30\n</Loot Table>\n\n<Loot Table 5%>\nUncommon: 2\nRare: 4\nEpic: 4\n</Loot Table>", "params": [900, 0, 45, 20, 90, 150, 10, 10]}, {"id": 42, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 43, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 44, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 45, "actions": [], "battlerHue": 0, "battlerName": "", "dropItems": [{"kind": 1, "dataId": 109, "denominator": 1}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 13, "dataId": 5, "value": 0.3}, {"code": 13, "dataId": 6, "value": 0.3}, {"code": 13, "dataId": 9, "value": 0.2}, {"code": 13, "dataId": 10, "value": 1.5}], "gold": 0, "name": "ASABA", "note": "", "params": [1000, 0, 16, 30, 16, 999, 10, 10]}, {"id": 46, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 47, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 48, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 49, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 50, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 51, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "石中剑", "note": "<Loot Table>\nWeapon 9:5\n</Loot Table>\n", "params": [100, 0, 10, 10, 10, 300, 10, 10]}, {"id": 52, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 53, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 54, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 55, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 56, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 57, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 58, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 59, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 60, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 61, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 62, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 63, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 64, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 65, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 66, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 67, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 68, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 69, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 70, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 71, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 72, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 73, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 74, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 75, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 76, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 77, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 78, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 79, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 80, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 81, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 82, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 83, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 84, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 85, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 86, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 87, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 88, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 89, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 90, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 91, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 92, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 93, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 94, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 95, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 96, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 97, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 98, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 99, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 100, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 101, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 102, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "海鸥", "note": "<dropItem:9, 23>\n<dropItem:13, 55>\n<dropWeapon:27, 33>\n<dropArmor:7, 55>\n<dropItem:9, 23>\n<dropItem:9, 23>\n<dropItem:9, 23>", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 103, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 104, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 105, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 106, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "鱼影", "note": "<Loot Table>\nItem 130:50\nItem 131:30\nItem 132:30\nItem 133:30\nItem 134:30\nItem 142:30\nItem 143:10\n</Loot Table>", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 107, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "椰树", "note": "<Loot Table>\nitem 46:88\nJunk:12\n</Loot Table>", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 108, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "海面上的收集物", "note": "<Loot Table>\nJunk:12\nFish: 30\nSeafood: 40\n</Loot Table>", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 109, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "遗迹之森树木", "note": "<Loot Table 60%>\nWeapon 1:120\nWeapon 2:120\nWeapon 11:60\nWeapon 62:10\nWeapon 63:10\nWeapon 80:10\n</Loot Table>\n\n<Loot Table 60%>\nWeapon 1:120\nWeapon 2:120\nArmor 14:120\n</Loot Table>\n\n<Loot Table 10%>\nWeapon 1:120\nWeapon 2:120\nWeapon 62:10\nWeapon 63:10\nWeapon 80:10\nArmor 14:120\n</Loot Table>\n", "params": [100, 0, 10, 10, 10, 50, 10, 10]}, {"id": 110, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "通用河里掉落", "note": "<Loot Table 50%>\nItem 127:50\nItem 130:50\nItem 131:30\nItem 132:30\nItem 133:30\nItem 134:30\nItem 135:20\nItem 136:20\nItem 137:10\nItem 142:50\nItem 143:10\nWeapon 14:50\n</Loot Table>\n\n<Loot Table 15%>\nItem 127:50\nItem 130:50\nItem 131:30\nItem 132:30\nItem 133:30\nItem 134:30\nItem 135:20\nItem 136:20\nItem 137:10\nItem 142:50\nItem 143:10\nWeapon 14:50\n</Loot Table>\n\n<Loot Table 1%>\nCommon: 10\nUncommon: 10\nRare: 1\n</Loot Table>", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 111, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 112, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 113, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 114, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 115, "actions": [], "battlerHue": 0, "battlerName": "Slime", "dropItems": [{"kind": 0, "dataId": 0, "denominator": 9}, {"kind": 0, "dataId": 0, "denominator": 100}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [], "gold": 0, "name": "八腕的支配者", "note": "\n<Loot Table 66%>\nItem 127:55\nItem 128:33\nWeapon 71:6\nWeapon 72:1\n</Loot Table>", "params": [920, 0, 42, 22, 10, 5, 25, 10]}, {"id": 116, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}, {"code": 13, "dataId": 8, "value": 0.3}, {"code": 13, "dataId": 6, "value": 0.1}, {"code": 13, "dataId": 5, "value": 1.5}, {"code": 13, "dataId": 9, "value": 0.5}, {"code": 13, "dataId": 7, "value": 0.5}], "gold": 0, "name": "狂乱的红兽", "note": "\n<Loot Table 90%>\nWeapon 81:60\nWeapon 107:60\nArmor 6:30\n</Loot Table>\n\n<Loot Table 30%>\nWeapon 81:60\nWeapon 107:60\nArmor 6:30\n</Loot Table>", "params": [3500, 0, 30, 10, 10, 60, 10, 10]}, {"id": 117, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 118, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 119, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 120, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 121, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 122, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 123, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 124, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 125, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 126, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 127, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 128, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 129, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 130, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 131, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 132, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 133, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 134, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 135, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 136, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 137, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 138, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 139, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 140, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 141, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 142, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 143, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 144, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 145, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 146, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 147, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 148, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 149, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 150, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "アレテイオルム", "note": " アレテイオルム\n累積せし理の結実", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 151, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 152, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 153, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 154, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 155, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 156, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 157, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 158, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 159, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 160, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 161, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 162, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 163, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 164, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 165, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 166, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 167, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 168, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 169, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 170, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 171, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 172, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 173, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 174, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 175, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 176, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 177, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 178, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 179, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 180, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "金猪存钱罐", "note": "", "params": [100, 0, 10, 999, 10, 999, 10, 10]}, {"id": 181, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 182, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 183, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 184, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 185, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "堕落圣堂-游魂", "note": "<Loot Table 60%>\nItem 5:30\nItem 6:30\nItem 312:60\nTarot:30\n</Loot Table>\n\n<Loot Table 60%>\nItem 5:30\nItem 6:30\nItem 7:10\nItem 8:10\nItem 312:30\nTarot:30\n</Loot Table>\n\n\n\n", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 186, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 187, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 188, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"kind": 3, "dataId": 32, "denominator": 1}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 13, "dataId": 5, "value": 0}, {"code": 13, "dataId": 6, "value": 0}, {"code": 13, "dataId": 7, "value": 1.5}, {"code": 13, "dataId": 8, "value": 0.2}, {"code": 13, "dataId": 9, "value": 0.4}, {"code": 13, "dataId": 11, "value": 0}, {"code": 13, "dataId": 5, "value": 0.2}], "gold": 0, "name": "茶会商店车", "note": "<Loot Table>\nTarot:30\nFish:50\nItem 14:10\nItem 19:60\nItem 70:100\nItem 102:30\nItem 103:30\nItem 112:30\nItem 113:30\nItem 114:30\nItem 115:30\nArmor 37:50\nWeapon 52:50\nCommon: 50\nUncommon: 20\nRare: 10\nLegendary: 5\n</Loot Table>", "params": [2500, 0, 10, 20, 10, 100, 10, 10]}, {"id": 189, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"kind": 1, "dataId": 1, "denominator": 1}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "扭蛋机", "note": "", "params": [500, 0, 10, 10, 10, 10, 10, 10]}, {"id": 190, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"kind": 1, "dataId": 312, "denominator": 1}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [], "gold": 0, "name": "被破坏的垃圾桶", "note": "<Loot Table>\nItem 3:100\nItem 312:100\n</Loot Table>", "params": [300, 0, 89, 10, 44, 20, 22, 10]}, {"id": 191, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 192, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 193, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 194, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 195, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "", "note": "", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 196, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "扭蛋:白", "note": "<Loot Table>\nCommon: 8000\nUncommon: 500\nRare: 5\nEpic: 1\nLegendary: 1\n</Loot Table>", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 197, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "扭蛋:蓝", "note": "<Loot Table>\nCommon: 1000\nUncommon: 8000\nRare: 50\nEpic: 5\nLegendary: 1\n</Loot Table>", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 198, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "扭蛋:紫", "note": "<Loot Table>\nCommon: 1\nUncommon: 10\nRare: 500\nEpic: 10\nLegendary: 1\n</Loot Table>", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 199, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "扭蛋:粉", "note": "<Loot Table>\nCommon: 1\nUncommon: 1\nRare: 50\nEpic: 800\nLegendary: 5\n</Loot Table>", "params": [100, 0, 10, 10, 10, 10, 10, 10]}, {"id": 200, "actions": [{"conditionParam1": 0, "conditionParam2": 0, "conditionType": 0, "rating": 5, "skillId": 1}], "battlerHue": 0, "battlerName": "", "dropItems": [{"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}, {"dataId": 1, "denominator": 1, "kind": 0}], "exp": 0, "traits": [{"code": 22, "dataId": 0, "value": 0.95}, {"code": 22, "dataId": 1, "value": 0.05}, {"code": 31, "dataId": 1, "value": 0}], "gold": 0, "name": "扭蛋:金", "note": "<Loot Table>\nCommon: 1\nUncommon: 1\nRare: 1\nEpic: 1\nLegendary: 450\n</Loot Table>", "params": [100, 0, 10, 10, 10, 10, 10, 10]}]