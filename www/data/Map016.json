{"autoplayBgm": false, "autoplayBgs": false, "battleback1Name": "Abyss", "battleback2Name": "gray bar", "bgm": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 25, "note": "<深渊>\n<Player Allow Region: 250>\n<Player Allow Region: 8>", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": true, "tilesetId": 18, "width": 21, "data": [6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6056, 2944, 2896, 2944, 6048, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6056, 2944, 2896, 2944, 6048, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6036, 6070, 2944, 2896, 2944, 6072, 6060, 6040, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6056, 2946, 2938, 2896, 2953, 2934, 2948, 6072, 6040, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6036, 6060, 6070, 2930, 2950, 2881, 2900, 2952, 2922, 2948, 6048, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6036, 6070, 2946, 2933, 2951, 2858, 2904, 2874, 2900, 2928, 2936, 6048, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6056, 2946, 2917, 2950, 2859, 2831, 2861, 2880, 2888, 2928, 2936, 6048, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6056, 2928, 2936, 2907, 2901, 2848, 3290, 2880, 2888, 2928, 2936, 6072, 6060, 6040, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6056, 2928, 2914, 2948, 2908, 2848, 3292, 2904, 2889, 2928, 2914, 2932, 2948, 6048, 6032, 6032, 6032, 6032, 6032, 6036, 6060, 6070, 2928, 2916, 2942, 2957, 2857, 2849, 2861, 2908, 2952, 2920, 2912, 2936, 6072, 6040, 6032, 6032, 6032, 6036, 6070, 2946, 2933, 2941, 2950, 1564, 1585, 1561, 1584, 1584, 1561, 1563, 2952, 2940, 2922, 2948, 6048, 6032, 6032, 6032, 6056, 2947, 2941, 2950, 1564, 1585, 1562, 1574, 1574, 1574, 1574, 1574, 1560, 1563, 2862, 2952, 2950, 6048, 6032, 6032, 6032, 6056, 2944, 1564, 1579, 1562, 2841, 1575, 2850, 2836, 2852, 2898, 2900, 1573, 1560, 1561, 1585, 1563, 6048, 6032, 6032, 6032, 6056, 2944, 1554, 1574, 1575, 1554, 2851, 2845, 2844, 2841, 2904, 2874, 2900, 1573, 1574, 1574, 1552, 6072, 6040, 6032, 6032, 6056, 2944, 1554, 2859, 2849, 2849, 2855, 3282, 3284, 2857, 2853, 2904, 2874, 2900, 1571, 2858, 1571, 2954, 6072, 6060, 6036, 6070, 2944, 1572, 1554, 2946, 2948, 3282, 3249, 3250, 3284, 2857, 2861, 2904, 2902, 2851, 2855, 1571, 2929, 2932, 2932, 6056, 2946, 2938, 1572, 2862, 2930, 2950, 3288, 3256, 3248, 3250, 3268, 3284, 2859, 2839, 2855, 1552, 1552, 2952, 2920, 2912, 6070, 2930, 2950, 1554, 2946, 2938, 2851, 2861, 3288, 3276, 3256, 3248, 3250, 3284, 2848, 2954, 2862, 1571, 2858, 2928, 2912, 2932, 2938, 2858, 1572, 2930, 2950, 2848, 2898, 2884, 2900, 3288, 3276, 3276, 3286, 2860, 2929, 2948, 1571, 2848, 2928, 2912, 2940, 2950, 2848, 1554, 2956, 2850, 2842, 2904, 2872, 2866, 2900, 2850, 2837, 2861, 2946, 2917, 2950, 1552, 2848, 2928, 2912, 2849, 2849, 2855, 1556, 2851, 2845, 2826, 2852, 2904, 2892, 2902, 2856, 2841, 2947, 2941, 2950, 2862, 1555, 2860, 2952, 2920, 4132, 4132, 4148, 2859, 2855, 1556, 2856, 2846, 2849, 2849, 2853, 3290, 2848, 2956, 2850, 2852, 1555, 2898, 2884, 2900, 2952, 4112, 4112, 4114, 4132, 4148, 2862, 2898, 2884, 2900, 1556, 2860, 3280, 2860, 1555, 2832, 2818, 2852, 2880, 2864, 2866, 2884, 4112, 4112, 4112, 4112, 4114, 4148, 2880, 2864, 2866, 2900, 1554, 3280, 1571, 2851, 2845, 2828, 2854, 2880, 2864, 2864, 2864, 4112, 4112, 4112, 4112, 4112, 4136, 2880, 2864, 2864, 2888, 1554, 3280, 1552, 2848, 2906, 2848, 2898, 2865, 2864, 2864, 2864, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3571, 3581, 0, 3579, 3569, 3569, 3581, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3579, 3569, 3575, 0, 0, 0, 3194, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3196, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3042, 3044, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3042, 3009, 3032, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3048, 3016, 3032, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3146, 0, 0, 0, 0, 0, 0, 3048, 3046, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3138, 3107, 3140, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3144, 3112, 3106, 3140, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3144, 3132, 3142, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3146, 0, 0, 0, 4180, 4196, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3147, 3143, 0, 3474, 3460, 4160, 4162, 4180, 4196, 0, 0, 0, 0, 3194, 0, 0, 0, 0, 3146, 0, 0, 0, 3474, 3460, 3441, 3440, 4160, 4160, 4160, 4184, 0, 0, 0, 0, 3169, 3188, 0, 0, 0, 3121, 3140, 0, 3474, 3441, 3440, 3440, 3440, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 257, 0, 0, 0, 256, 256, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 273, 0, 0, 0, 272, 274, 257, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 257, 256, 0, 0, 0, 0, 0, 0, 273, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 256, 273, 272, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 274, 317, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 256, 257, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 274, 273, 0, 0, 0, 0, 0, 0, 0, 256, 256, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 257, 0, 0, 0, 0, 0, 256, 274, 272, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 273, 0, 0, 0, 0, 0, 272, 0, 0, 0, 82, 170, 67, 124, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 81, 105, 0, 0, 3, 0, 0, 0, 0, 0, 0, 80, 81, 0, 0, 0, 0, 0, 0, 0, 0, 85, 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 256, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 88, 84, 0, 272, 256, 256, 0, 257, 0, 0, 90, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 274, 346, 0, 273, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 256, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 274, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 301, 0, 267, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 301, 0, 321, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 322, 0, 299, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 295, 309, 1, 307, 308, 294, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 319, 317, 0, 315, 316, 326, 294, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 333, 295, 336, 309, 1, 0, 0, 2, 315, 318, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 295, 327, 344, 317, 0, 0, 0, 0, 0, 321, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 319, 37, 0, 0, 26, 0, 0, 0, 0, 321, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 322, 45, 0, 0, 2, 0, 0, 0, 0, 307, 336, 294, 0, 0, 0, 0, 0, 0, 0, 0, 0, 301, 0, 0, 0, 0, 0, 0, 0, 0, 315, 344, 318, 0, 0, 0, 0, 0, 0, 0, 295, 308, 309, 0, 0, 0, 108, 109, 110, 0, 8, 0, 0, 307, 294, 0, 0, 0, 0, 0, 295, 327, 316, 317, 0, 0, 0, 116, 117, 118, 68, 0, 0, 0, 0, 318, 0, 0, 0, 0, 0, 319, 317, 0, 37, 67, 930, 931, 932, 125, 126, 64, 0, 0, 0, 0, 299, 0, 0, 0, 0, 0, 301, 0, 59, 45, 0, 938, 939, 940, 0, 0, 0, 0, 0, 13, 14, 299, 0, 0, 0, 0, 0, 322, 0, 59, 86, 90, 35, 0, 0, 1, 0, 0, 0, 0, 0, 37, 307, 294, 0, 0, 0, 333, 322, 0, 0, 0, 0, 43, 0, 0, 0, 0, 0, 3, 38, 39, 45, 315, 326, 337, 338, 0, 295, 309, 0, 59, 67, 0, 0, 11, 0, 0, 0, 0, 0, 46, 47, 0, 0, 315, 345, 37, 0, 319, 317, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 88, 0, 0, 0, 45, 339, 309, 2, 0, 154, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 347, 317, 21, 0, 162, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 0, 49, 50, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 48, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 49, 52, 49, 50, 2, 0, 0, 48, 49, 49, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 0, 48, 0, 0, 0, 0, 0, 0, 280, 281, 206, 0, 0, 0, 0, 0, 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 288, 289, 0, 0, 0, 207, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 296, 297, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 5, 0, 5, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 5, 0, 5, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 5, 0, 5, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 5, 5, 5, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 5, 0, 0, 0, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 5, 5, 0, 0, 5, 5, 5, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 5, 0, 0, 0, 0, 0, 5, 5, 5, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 0, 5, 5, 0, 5, 0, 0, 5, 5, 5, 0, 0, 255, 255, 255, 255, 255, 255, 255, 255, 255, 5, 5, 5, 5, 0, 0, 0, 0, 5, 5, 5, 5, 255, 255, 255, 255, 255, 255, 255, 0, 0, 5, 5, 250, 0, 0, 0, 0, 0, 0, 5, 5, 5, 0, 255, 255, 255, 255, 255, 0, 0, 250, 250, 250, 250, 0, 0, 0, 0, 0, 0, 0, 250, 250, 5, 255, 255, 255, 255, 255, 0, 250, 250, 250, 0, 0, 0, 0, 0, 0, 0, 0, 0, 250, 250, 250, 255, 255, 255, 255, 255, 5, 250, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 250, 255, 255, 255, 255, 255, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 255, 255, 5, 0, 0, 5, 5, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 255, 0, 5, 0, 0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 0, 5, 0, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 5, 5, 0, 5, 5, 0, 5, 5, 5, 5, 0, 0, 0, 0, 118, 0, 0, 5, 5, 0, 0, 5, 5, 0, 5, 0, 0, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 5, 5, 5, 0, 0, 5, 5, 0, 5, 0, 0, 0, 0, 5, 5, 5, 0, 0, 0, 0, 5, 5, 5, 0, 0, 1, 5, 5, 8, 8, 8, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 8, 8, 8, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 0, 0, 0, 8, 8, 8, 8, 8, 8, 0, 0, 5, 5, 0, 0, 0, 5, 5, 1, 5, 0, 0, 0, 0, 8, 8, 8, 8, 8, 8, 0, 1, 5, 5, 0, 0, 0, 5, 5, 5, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "EV001", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 0, "characterIndex": 3}, "list": [{"code": 117, "indent": 0, "parameters": [79]}, {"code": 121, "indent": 0, "parameters": [3, 3, 0]}, {"code": 356, "indent": 0, "parameters": [">允许操作玩家移动 : 开启"]}, {"code": 117, "indent": 0, "parameters": [6]}, {"code": 117, "indent": 0, "parameters": [100]}, {"code": 117, "indent": 0, "parameters": [99]}, {"code": 108, "indent": 0, "parameters": ["生成篝火"]}, {"code": 355, "indent": 0, "parameters": ["QJ.SE.spawnXy(1,7,9,15,false);"]}, {"code": 108, "indent": 0, "parameters": ["雨天重置的创造物"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameSelfVariables.value([$gameMap.mapId(), 1, 'rainyCreation']) > 0"]}, {"code": 108, "indent": 1, "parameters": ["生成树"]}, {"code": 355, "indent": 1, "parameters": ["let type = \"tree\";"]}, {"code": 655, "indent": 1, "parameters": ["let times = 3;"]}, {"code": 655, "indent": 1, "parameters": ["let eid = this._eventId;"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.steupCEQJ(75,eid,{creationType:type,creationQuantity:times})"]}, {"code": 108, "indent": 1, "parameters": ["清除标记"]}, {"code": 355, "indent": 1, "parameters": ["$gameSelfVariables.setValue([$gameMap.mapId(), 1, 'rainyCreation'], 0);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "$gameScreen.zoomScale() == 1"]}, {"code": 356, "indent": 1, "parameters": ["d<PERSON><PERSON><PERSON> 2 60 player"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["particle set dust_walk walk:player:0 def below"]}, {"code": 356, "indent": 0, "parameters": ["particle set grass_walk walk:player:5 def below"]}, {"code": 356, "indent": 0, "parameters": ["particle set splash_walk1 walk:player:8 def below"]}, {"code": 356, "indent": 0, "parameters": ["particle set ripple_walk walk:player:8 def below"]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 4, "y": 0}, {"id": 2, "name": "EV002", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 1, "characterIndex": 5}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": false}], "x": 10, "y": 0}, {"id": 3, "name": "EV003", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<width: 5>"]}, {"code": 408, "indent": 0, "parameters": ["<height: 1>"]}, {"code": 408, "indent": 0, "parameters": ["<offsetX: 0>"]}, {"code": 408, "indent": 0, "parameters": ["<offsetY: 0>"]}, {"code": 356, "indent": 0, "parameters": ["PB_BGS_全停止"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Move", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 201, "indent": 0, "parameters": [0, 17, 8, 21, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 9, "y": 0}, null, {"id": 5, "name": "EV005", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 356, "indent": 0, "parameters": [">对话框皮肤 : 只对话框 : 修改样式 : 样式[3]"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 还原默认形状样式"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 设置对话框宽度 : 1920"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 40 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 0, "parameters": ["\\py[120]\\>\\dac\\{\\c[2]⭡⭡迷いの森⭡⭡"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 还原默认对话框宽度"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 9, "y": 7}, {"id": 6, "name": "EV006", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<width: 1>"]}, {"code": 408, "indent": 0, "parameters": ["<height: 5>"]}, {"code": 408, "indent": 0, "parameters": ["<offsetX: 0>"]}, {"code": 408, "indent": 0, "parameters": ["<offsetY: 0>"]}, {"code": 356, "indent": 0, "parameters": ["PB_BGS_全停止"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Move", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 121, "indent": 0, "parameters": [40, 40, 0]}, {"code": 201, "indent": 0, "parameters": [0, 13, 20, 22, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 0, "y": 20}, {"id": 7, "name": "树", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$fsm_Forest_object", "direction": 6, "pattern": 1, "characterIndex": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["let Hp = 750;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSelfVariables.set(this, 'HP', Hp);"]}, {"code": 355, "indent": 0, "parameters": ["let Hp = 750;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSelfVariables.set(this, 'MHP', Hp);"]}, {"code": 355, "indent": 0, "parameters": ["$gameSelfVariables.set(this, 'DEF', 20);"]}, {"code": 355, "indent": 0, "parameters": ["$gameSelfVariables.set(this, 'fudou', 99);"]}, {"code": 355, "indent": 0, "parameters": ["$gameSelfVariables.set(this, 'enemyId', 7);"]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "C", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 1}, "list": [{"code": 111, "indent": 0, "parameters": [1, 60, 0, 2, 0]}, {"code": 123, "indent": 1, "parameters": ["C", 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 214, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$fsm_Forest_object", "direction": 6, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<Group:\"object\">"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$fsm_Forest_object", "direction": 6, "pattern": 1, "characterIndex": 0}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "xへのImpactの影響", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["let day = $gameSystem.day();"]}, {"code": 655, "indent": 0, "parameters": ["$gameSelfVariables.set(this, 'deadDay', day);"]}, {"code": 356, "indent": 0, "parameters": [">方块粉碎效果 : 本事件 : 方块粉碎[8]"]}, {"code": 117, "indent": 0, "parameters": [123]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 123, "indent": 0, "parameters": ["C", 0]}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 123, "indent": 0, "parameters": ["D", 1]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 16, "y": 12}, null, null, {"id": 10, "name": "EV010", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 6, "pattern": 1, "characterIndex": 5}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": false}], "x": 0, "y": 23}, {"id": 11, "name": "兔子洞", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 6, "pattern": 0, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<BoxOffset:0,-12>"]}, {"code": 108, "indent": 0, "parameters": ["=>持续动作 : 缩放状态 : 缩放比例[0.75]"]}, {"code": 108, "indent": 0, "parameters": ["=>行走图动画序列 : 创建动画序列 : 动画序列[3]"]}, {"code": 230, "indent": 0, "parameters": [4]}, {"code": 355, "indent": 0, "parameters": ["let Hp = 80;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSelfVariables.set(this, 'HP', Hp);"]}, {"code": 355, "indent": 0, "parameters": ["let Hp = 80;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSelfVariables.set(this, 'MHP', Hp);"]}, {"code": 355, "indent": 0, "parameters": ["$gameSelfVariables.set(this, 'fudou', 99);"]}, {"code": 356, "indent": 0, "parameters": [">行走图动画序列 : 本事件 : 播放动作元 : 动作元[兔子洞张开]"]}, {"code": 356, "indent": 0, "parameters": [">行走图动画序列 : 本事件 : 播放简单状态元集合 : 集合[兔子洞]"]}, {"code": 355, "indent": 0, "parameters": ["var name = \"Rabbithole\" + this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["    QJ.MPMZ.Shoot({"]}, {"code": 655, "indent": 0, "parameters": ["        img:'null1',position:[['E',0],['E',0]],"]}, {"code": 655, "indent": 0, "parameters": ["        initialRotation:['S',0],groupName:[name],"]}, {"code": 655, "indent": 0, "parameters": ["        moveType:['S',0],existData:[{t:['P'],a:['C',498],p:[-1,false,true],}],"]}, {"code": 655, "indent": 0, "parameters": ["        collisionBox:['R',30,5],anchor:[0.5,2.5],"]}, {"code": 655, "indent": 0, "parameters": [" });"]}, {"code": 230, "indent": 0, "parameters": [80]}, {"code": 355, "indent": 0, "parameters": ["$gameMap.event(this._eventId).drill_COET_addTag(\"兔子洞\");"]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp._drill_COET_needRestatistics = true"]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<BoxType:['C',12]>"]}, {"code": 108, "indent": 0, "parameters": ["<BoxOffset:0,-12>"]}, {"code": 108, "indent": 0, "parameters": ["<Group:\"object\">"]}, {"code": 108, "indent": 0, "parameters": ["=>持续动作 : 缩放状态 : 缩放比例[0.75]"]}, {"code": 108, "indent": 0, "parameters": ["=>行走图动画序列 : 创建动画序列 : 动画序列[3]"]}, {"code": 355, "indent": 0, "parameters": ["var eventID = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["var xx = $gameMap.event(eventID)._x;"]}, {"code": 655, "indent": 0, "parameters": ["var yy = $gameMap.event(eventID)._y + 0.45;"]}, {"code": 655, "indent": 0, "parameters": ["var e = $gameMap.drill_COEM_offspring_createEvent( 1, 64, xx, yy );"]}, {"code": 655, "indent": 0, "parameters": ["e._opacity = 0;"]}, {"code": 356, "indent": 0, "parameters": [">行走图动画序列 : 本事件 : 创建动画序列 : 动画序列[3]"]}, {"code": 356, "indent": 0, "parameters": [">行走图动画序列 : 本事件 : 播放简单状态元集合 : 集合[兔子洞]"]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<BoxOffset:0,-12>"]}, {"code": 108, "indent": 0, "parameters": ["=>持续动作 : 缩放状态 : 缩放比例[0.75]"]}, {"code": 108, "indent": 0, "parameters": ["=>行走图动画序列 : 创建动画序列 : 动画序列[3]"]}, {"code": 355, "indent": 0, "parameters": ["$gameMap.event(this._eventId).drill_COET_removeTag(\"兔子洞\")"]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp._drill_COET_needRestatistics = true"]}, {"code": 230, "indent": 0, "parameters": [4]}, {"code": 355, "indent": 0, "parameters": ["var xx = $gameMap.event(this._eventId)._realX;"]}, {"code": 655, "indent": 0, "parameters": ["var yy = $gameMap.event(this._eventId)._realY;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.drill_COEM_offspring_createEvent( 1, 62, xx, yy );"]}, {"code": 356, "indent": 0, "parameters": [">行走图动画序列 : 本事件 : 播放动作元 : 动作元[兔子洞闭合]"]}, {"code": 356, "indent": 0, "parameters": [">行走图动画序列 : 本事件 : 播放简单状态元集合 : 集合[初始化]"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 355, "indent": 0, "parameters": ["var name = \"Rabbithole\" + this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["QJ.MPMZ.deleteProjectile(name);"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 356, "indent": 0, "parameters": [">事件管理核心 : 本事件 : 彻底删除"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 9, "y": 16}, {"id": 12, "name": "EV012", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 10, "y": 22}, {"id": 13, "name": "EV013", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 12, "y": 22}, {"id": 14, "name": "EV014", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["<width: 1>"]}, {"code": 408, "indent": 0, "parameters": ["<height: 1>"]}, {"code": 408, "indent": 0, "parameters": ["<offsetX: 0>"]}, {"code": 408, "indent": 0, "parameters": ["<offsetY: 0>"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameMap.getGroupBulletListQJ('systemWarn').length > 0"]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "Magic1", "volume": 65, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 1, "parameters": ["    QJ.MPMZ.Shoot({"]}, {"code": 655, "indent": 1, "parameters": ["        img:\"systemWarn[4,8]\","]}, {"code": 655, "indent": 1, "parameters": ["        position:[['Map',11],['Map',24]],"]}, {"code": 655, "indent": 1, "parameters": ["        initialRotation:['S',0],imgRotation:['F'],"]}, {"code": 655, "indent": 1, "parameters": ["        moveType:['S',0],groupName:['systemWarn'],"]}, {"code": 655, "indent": 1, "parameters": ["        opacity:'0|0~20/1~999999|1',"]}, {"code": 655, "indent": 1, "parameters": ["        existData:["]}, {"code": 655, "indent": 1, "parameters": ["          {t:['Time',180],d:[1,30,0]}"]}, {"code": 655, "indent": 1, "parameters": ["        ],"]}, {"code": 655, "indent": 1, "parameters": ["        z:\"W\""]}, {"code": 655, "indent": 1, "parameters": ["    });"]}, {"code": 230, "indent": 1, "parameters": [4]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 11, "y": 24}]}