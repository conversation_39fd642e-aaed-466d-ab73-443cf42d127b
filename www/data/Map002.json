{"autoplayBgm": false, "autoplayBgs": false, "battleback1Name": "room", "battleback2Name": "gray bar", "bgm": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 10, "note": "<宅>", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": true, "tilesetId": 1, "width": 10, "data": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "EV001", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [12, "$dataSystem.gameTitle.includes(\"Deluxe\")"]}, {"code": 355, "indent": 1, "parameters": ["if (ConfigManager.language == 0) {"]}, {"code": 655, "indent": 1, "parameters": ["let text = [\"感谢您购买了豪华版本，这会让我们有更多经费制作更多有趣内容！ \","]}, {"code": 655, "indent": 1, "parameters": ["            \"豪华版本赠送了一个KEY，在购买后会自动发放，\","]}, {"code": 655, "indent": 1, "parameters": ["\"如果您没有收到，请通过Discord联系我领取。\","]}, {"code": 655, "indent": 1, "parameters": ["        \"此KEY可能包含一些非全年龄内容，如果您不想玩到非全年龄内容，\","]}, {"code": 655, "indent": 1, "parameters": ["         \"请不要领取这个KEY！\"];"]}, {"code": 655, "indent": 1, "parameters": ["     text = text.join(\"\\n\")"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.setValue(85, text);"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 355, "indent": 1, "parameters": ["if (ConfigManager.language == 1) {"]}, {"code": 655, "indent": 1, "parameters": ["let text = [\"特典版をご購入いただきありがとうございます！ 皆さんのご支援のおかげで、\","]}, {"code": 655, "indent": 1, "parameters": ["           \"これからもっと面白いコンテンツを作ることができます。\","]}, {"code": 655, "indent": 1, "parameters": ["           \"特典版には、購入後に自動的に配布されるキーが付いています。\","]}, {"code": 655, "indent": 1, "parameters": ["\"もし受け取れていない場合は、Discordでご連絡いただければすぐにお渡しします。\","]}, {"code": 655, "indent": 1, "parameters": ["\"このキーには全年齢でない内容が含まれている場合がありますので、\","]}, {"code": 655, "indent": 1, "parameters": ["\"そういったコンテンツを希望されない方はキーを受け取らないようにしてください。\"];"]}, {"code": 655, "indent": 1, "parameters": ["     text = text.join(\"\\n\")"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.setValue(85, text);"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 355, "indent": 1, "parameters": ["if (ConfigManager.language > 1) {"]}, {"code": 655, "indent": 1, "parameters": ["let text = [\"Thank you for purchasing the Deluxe Edition! \","]}, {"code": 655, "indent": 1, "parameters": ["            \"Your support helps us make even more fun content.\","]}, {"code": 655, "indent": 1, "parameters": ["            \"The Deluxe Edition comes with a key that’s automatically sent to you after purchase. \","]}, {"code": 655, "indent": 1, "parameters": ["            \"If you didn’t receive it, just reach out to me on Discord and I’ll make sure you get it.\","]}, {"code": 655, "indent": 1, "parameters": ["\"Please note: this key might include some NSFW content.\","]}, {"code": 655, "indent": 1, "parameters": ["\"If you don’t want to see that, please don’t redeem the key!\"];"]}, {"code": 655, "indent": 1, "parameters": ["     text = text.join(\"\\n\")"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.setValue(85, text);"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["if (ConfigManager.language == 0) {"]}, {"code": 655, "indent": 1, "parameters": ["let text = [\"这个版本是不包含H内容的DEMO版本， \","]}, {"code": 655, "indent": 1, "parameters": ["                 \"如果你想要体验完整内容，请购买完整版游戏。\","]}, {"code": 655, "indent": 1, "parameters": ["                 \"购买后你将可以领取一个itch的NSFW版本CDKEY， \","]}, {"code": 655, "indent": 1, "parameters": ["                 \"通过此key你可以在itch的游戏库里免费下载后续所有更新。\","]}, {"code": 655, "indent": 1, "parameters": ["                 \"是否跳转到购买页面？\"];"]}, {"code": 655, "indent": 1, "parameters": ["     text = text.join(\"\\n\")"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.setValue(85, text);"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 355, "indent": 1, "parameters": ["if (ConfigManager.language == 1) {"]}, {"code": 655, "indent": 1, "parameters": ["let text = [\"このバージョンはHシーンを含まないデモ版です。 \","]}, {"code": 655, "indent": 1, "parameters": ["                 \"完全版をお楽しみいただくには、製品版をご購入ください。\","]}, {"code": 655, "indent": 1, "parameters": ["                 \"ご購入後、itchのNSFW版CDキーをお渡しします。\","]}, {"code": 655, "indent": 1, "parameters": ["                 \"このキーを使用すると、itchのゲームライブラリから今後の全ての更新を無料でダウンロードできます。\","]}, {"code": 655, "indent": 1, "parameters": ["                 \"購入ページへ移動しますか？\"];"]}, {"code": 655, "indent": 1, "parameters": ["     text = text.join(\"\\n\")"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.setValue(85, text);"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 355, "indent": 1, "parameters": ["if (ConfigManager.language > 1) {"]}, {"code": 655, "indent": 1, "parameters": ["let text = [\"This version is a demo without any lewd content. \","]}, {"code": 655, "indent": 1, "parameters": ["                 \"If you want to experience the full content, please purchase the full version of the game.\","]}, {"code": 655, "indent": 1, "parameters": ["                 \"After purchasing, you’ll receive an itch.io NSFW version CD key,  \","]}, {"code": 655, "indent": 1, "parameters": ["                 \"which will allow you to download all future updates for free from your itch.io game library.\","]}, {"code": 655, "indent": 1, "parameters": ["                 \"Would you like to go to the purchase page?\"];"]}, {"code": 655, "indent": 1, "parameters": ["     text = text.join(\"\\n\")"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.setValue(85, text);"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["let text = $gameVariables.value(85);"]}, {"code": 655, "indent": 0, "parameters": ["const ask = confirm(text);"]}, {"code": 655, "indent": 0, "parameters": ["if (ask) {"]}, {"code": 655, "indent": 0, "parameters": ["if ( Utils.isMobileDevice() ) {"]}, {"code": 655, "indent": 0, "parameters": ["     window.open('https://nlch.itch.io/imouto/purchase', '_system');"]}, {"code": 655, "indent": 0, "parameters": ["    } else {"]}, {"code": 655, "indent": 0, "parameters": ["      require('nw.gui').Shell.openExternal('https://nlch.itch.io/imouto/purchase');"]}, {"code": 655, "indent": 0, "parameters": ["  }"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["加载多语言地图对话模板"]}, {"code": 355, "indent": 0, "parameters": ["const mapId = $gameMap.mapId();"]}, {"code": 655, "indent": 0, "parameters": ["const key = 'MapEventDialogue' + mapId; "]}, {"code": 655, "indent": 0, "parameters": ["if (!window[key]) { "]}, {"code": 655, "indent": 0, "parameters": ["chahuiUtil.loadMapEventDialogue();"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 356, "indent": 0, "parameters": ["hide_actor_hud"]}, {"code": 356, "indent": 0, "parameters": [">金钱固定框 : 隐藏"]}, {"code": 356, "indent": 0, "parameters": [">地图永久漂浮文字 : 漂浮文字[3] : 清除"]}, {"code": 356, "indent": 0, "parameters": [">高级变量框 : 框设置[1] : 隐藏"]}, {"code": 356, "indent": 0, "parameters": [">高级变量框 : 框设置[4] : 隐藏"]}, {"code": 223, "indent": 0, "parameters": [[-68, -68, -68, 0], 999, false]}, {"code": 356, "indent": 0, "parameters": [">对话框皮肤 : 只对话框 : 修改样式 : 样式[3]"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 还原默认形状样式"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 设置对话框宽度 : 1920"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 1]}, {"code": 401, "indent": 0, "parameters": ["\\fn[HonobonoPopTTF]\\py[150]\\fs[38]All characters in this game are 18 years or older "]}, {"code": 401, "indent": 0, "parameters": ["and not related by blood."]}, {"code": 401, "indent": 0, "parameters": ["\\w[120]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 1]}, {"code": 401, "indent": 0, "parameters": ["\\fn[HonobonoPopTTF]\\py[150]\\fs[38]Terms like \"Onii-chan\" or \"Imouto\" are used for "]}, {"code": 401, "indent": 0, "parameters": ["narrative or emotional context only."]}, {"code": 401, "indent": 0, "parameters": ["\\w[120]"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 还原默认形状样式"]}, {"code": 111, "indent": 0, "parameters": [1, 13, 0, 0, 3]}, {"code": 355, "indent": 1, "parameters": ["let ID = $gameVariables.value(13);"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.event(ID).start()"]}, {"code": 122, "indent": 1, "parameters": [13, 13, 0, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameMap.event(2).start()"]}, {"code": 122, "indent": 1, "parameters": [13, 13, 0, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}], "x": 0, "y": 9}, {"id": 2, "name": "序章回忆", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 121, "indent": 0, "parameters": [333, 333, 0]}, {"code": 121, "indent": 0, "parameters": [334, 334, 0]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">外发光效果 : 固定对话框外发光 : 颜色[11] : 厚度[2] : 偏移[3,3]"]}, {"code": 356, "indent": 0, "parameters": [">对话框皮肤 : 只姓名框窗口 : 修改样式 : 样式[3]"]}, {"code": 241, "indent": 0, "parameters": [{"name": "m-art_Rest", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 0, "parameters": [1, "sister_room_BW", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 355, "indent": 0, "parameters": ["var id = \"blur1\";"]}, {"code": 655, "indent": 0, "parameters": ["var filterTarget = 5001;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.createFilter(id, \"blur\", filterTarget);"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.setFilter( id ,[1]);"]}, {"code": 231, "indent": 0, "parameters": [15, "", 0, 0, 0, 0, 100, 100, 255, 2]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[15] : 创建动画序列 : 动画序列[5]"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["姓名框赋值"]}, {"code": 355, "indent": 0, "parameters": ["let index = \"father\";"]}, {"code": 655, "indent": 0, "parameters": ["const mapId = $gameMap.mapId();"]}, {"code": 655, "indent": 0, "parameters": ["const key   = `MapEventDialogue${mapId}`;"]}, {"code": 655, "indent": 0, "parameters": ["let name = window[key][index];"]}, {"code": 655, "indent": 0, "parameters": ["name = name.join();"]}, {"code": 655, "indent": 0, "parameters": ["$gameStrings.setValue(5,name);"]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 108, "indent": 0, "parameters": ["播放操作教程演出"]}, {"code": 111, "indent": 0, "parameters": [12, "!Utils.isMobileDevice()"]}, {"code": 355, "indent": 1, "parameters": ["let id = this._eventId;"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.event(id).steupCEQJ(2)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(0);"]}, {"code": 355, "indent": 0, "parameters": ["let index = 1;"]}, {"code": 655, "indent": 0, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["const mapId = $gameMap.mapId();"]}, {"code": 655, "indent": 0, "parameters": ["const key   = `MapEventDialogue${mapId}`;"]}, {"code": 655, "indent": 0, "parameters": ["let textArray = window[key][eid][String(index)];"]}, {"code": 655, "indent": 0, "parameters": ["textArray[0] = \"\\\\nl<\\\\dDCOG[11:2:2:2]\\\\str[5]>\" + textArray[0];"]}, {"code": 655, "indent": 0, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(2);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(3);"]}, {"code": 355, "indent": 0, "parameters": ["let index = 4;"]}, {"code": 655, "indent": 0, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["const mapId = $gameMap.mapId();"]}, {"code": 655, "indent": 0, "parameters": ["const key   = `MapEventDialogue${mapId}`;"]}, {"code": 655, "indent": 0, "parameters": ["let textArray = window[key][eid][String(index)];"]}, {"code": 655, "indent": 0, "parameters": ["textArray[0] = \"\\\\nl<\\\\dDCOG[11:2:2:2]\\\\str[5]>\" + textArray[0];"]}, {"code": 655, "indent": 0, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(5);"]}, {"code": 355, "indent": 0, "parameters": ["let index = 6;"]}, {"code": 655, "indent": 0, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["const mapId = $gameMap.mapId();"]}, {"code": 655, "indent": 0, "parameters": ["const key   = `MapEventDialogue${mapId}`;"]}, {"code": 655, "indent": 0, "parameters": ["let textArray = window[key][eid][String(index)];"]}, {"code": 655, "indent": 0, "parameters": ["textArray[0] = \"\\\\nl<\\\\dDCOG[11:2:2:2]\\\\str[5]>\" + textArray[0];"]}, {"code": 655, "indent": 0, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(7);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(8);"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nl<\\dDCOG[11:2:2:2]\\str[5]>\\dimg[21:位置[0,2]]\\px[150]\\dimg[21:位置[0,2]]\\px[150] "]}, {"code": 401, "indent": 0, "parameters": ["\\dimg[21:位置[0,2]]\\px[150]\\dimg[21:位置[0,2]]\\px[150]\\dimg[21:位置[0,2]]\\px[150] "]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(9);"]}, {"code": 355, "indent": 0, "parameters": ["let index = 10;"]}, {"code": 655, "indent": 0, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["const mapId = $gameMap.mapId();"]}, {"code": 655, "indent": 0, "parameters": ["const key   = `MapEventDialogue${mapId}`;"]}, {"code": 655, "indent": 0, "parameters": ["let textArray = window[key][eid][String(index)];"]}, {"code": 655, "indent": 0, "parameters": ["textArray[0] = \"\\\\nl<\\\\dDCOG[11:2:2:2]\\\\str[5]>\" + textArray[0];"]}, {"code": 655, "indent": 0, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(11);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(12);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(13);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(14);"]}, {"code": 242, "indent": 0, "parameters": [1]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "ゆめかわシャットダウン・ぴちゅん短", "volume": 55, "pitch": 120, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": [">消失动作 : 图片[1] : 横向挤扁 : 时间[15] : 横向比例[4.0]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["…………"]}, {"code": 401, "indent": 0, "parameters": ["…………\\w[30]\\^"]}, {"code": 108, "indent": 0, "parameters": ["妹妹着装初始化"]}, {"code": 355, "indent": 0, "parameters": ["var array = [154, 155, 156];"]}, {"code": 655, "indent": 0, "parameters": ["var newPanties = array[Math.floor(Math.random() * array.length)];"]}, {"code": 655, "indent": 0, "parameters": ["$gameActors.actor(2).changeEquipById(2, newPanties);"]}, {"code": 355, "indent": 0, "parameters": ["$gameActors.actor(2).changeEquipById(3, 152);"]}, {"code": 655, "indent": 0, "parameters": ["$gameActors.actor(2).changeEquipById(4, 153);"]}, {"code": 108, "indent": 0, "parameters": ["初始化时间"]}, {"code": 355, "indent": 0, "parameters": ["$gameSystem.set_hour(20)"]}, {"code": 356, "indent": 0, "parameters": [">标题界面 : 修改背景音乐 : 音乐[1]"]}, {"code": 108, "indent": 0, "parameters": ["刷新标题画面动画"]}, {"code": 355, "indent": 0, "parameters": ["chahuiUtil.RefreshTitleScreenAnimation(\"idleInBedroom0\")"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 122, "indent": 0, "parameters": [13, 13, 0, 0, 20]}, {"code": 201, "indent": 0, "parameters": [0, 4, 5, 4, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 39, "indent": null}, {"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["let lang = ConfigManager.language;"]}, {"code": 655, "indent": 0, "parameters": ["if (lang > 2) lang = 2;"]}, {"code": 655, "indent": 0, "parameters": ["let IMG = \"instructionalImage3_\" + lang;"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(30, \"gameTutorial\", IMG, 0, 0, 0, 100, 100, 0, 0);"]}, {"code": 356, "indent": 0, "parameters": [">图片快捷操作 : 图片[30] : 修改单属性 : 透明度[255] : 时间[30]"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 356, "indent": 0, "parameters": [">持续动作 : 图片[30] : 渐变闪烁 : 持续时间[无限] : 周期[90]"]}, {"code": 230, "indent": 0, "parameters": [240]}, {"code": 356, "indent": 0, "parameters": [">持续动作 : 图片[30] : 立即终止动作"]}, {"code": 356, "indent": 0, "parameters": [">图片快捷操作 : 图片[30] : 修改单属性 : 透明度[0] : 时间[30]"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 235, "indent": 0, "parameters": [30]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 1, "y": 9}, null, null, null]}