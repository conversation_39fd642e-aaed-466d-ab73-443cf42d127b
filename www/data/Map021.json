{"autoplayBgm": false, "autoplayBgs": false, "battleback1Name": "oniichan_room1", "battleback2Name": "gray bar", "bgm": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "bgs": {"name": "深夜の風鈴と虫の音-02", "pan": 0, "pitch": 100, "volume": 60}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 8, "note": "", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": true, "tilesetId": 1, "width": 10, "data": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "夜间前置事件", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["做噩梦"]}, {"code": 111, "indent": 0, "parameters": [12, "this.nightmare || this.eventPlayback"]}, {"code": 111, "indent": 1, "parameters": [12, "this.eventPlayback"]}, {"code": 119, "indent": 2, "parameters": ["选项"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["let id = this._eventId;"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.event(id).steupCEQJ(2)"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [2, "D", 0]}, {"code": 123, "indent": 1, "parameters": ["D", 1]}, {"code": 231, "indent": 1, "parameters": [60, "", 0, 0, 0, 0, 100, 100, 200, 2]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[60] : 创建动画序列 : 动画序列[5]"]}, {"code": 355, "indent": 1, "parameters": ["var id = \"noise1\";"]}, {"code": 655, "indent": 1, "parameters": ["var filterTarget = 0;"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.createFilter(id, \"noise\", filterTarget);"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.setFilter(id, [0.01]);"]}, {"code": 241, "indent": 1, "parameters": [{"name": "霊体工場(Spirit factory)", "volume": 10, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 1, "parameters": ["AudioManager.fadeInBgm(3)"]}, {"code": 119, "indent": 1, "parameters": ["苏醒"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [4, 1, 5, 129]}, {"code": 111, "indent": 1, "parameters": [1, 25, 0, 20, 2]}, {"code": 122, "indent": 2, "parameters": [25, 25, 0, 0, 25]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 122, "indent": 1, "parameters": [25, 25, 1, 2, 0, 2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 314, "indent": 0, "parameters": [0, 2]}, {"code": 111, "indent": 0, "parameters": [1, 25, 0, 60, 4]}, {"code": 117, "indent": 1, "parameters": [9]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["苏醒"]}, {"code": 356, "indent": 0, "parameters": ["SaveNow"]}, {"code": 355, "indent": 0, "parameters": ["$gameSystem.set_hour(23)"]}, {"code": 117, "indent": 0, "parameters": [8]}, {"code": 245, "indent": 0, "parameters": [{"name": "深夜の風鈴と虫の音-02", "volume": 60, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["var IMG = 'mainroom_S_N_CC';"]}, {"code": 655, "indent": 0, "parameters": ["var path = \"onichann_room\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(1, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 111, "indent": 0, "parameters": [12, "Math.random() > 0.66"]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(0);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "Math.random() > 0.33"]}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(1);"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(2);"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["选项"]}, {"code": 108, "indent": 0, "parameters": ["选项赋予名称"]}, {"code": 355, "indent": 0, "parameters": ["const idx   = 4;"]}, {"code": 655, "indent": 0, "parameters": ["const eid   = String(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["const mapId = $gameMap.mapId();"]}, {"code": 655, "indent": 0, "parameters": ["const key   = `MapEventDialogue${mapId}`;"]}, {"code": 655, "indent": 0, "parameters": ["const dialogueTable = window[key] || {};"]}, {"code": 655, "indent": 0, "parameters": ["const textArray     = dialogueTable[eid]?.[String(idx)];"]}, {"code": 655, "indent": 0, "parameters": ["const option1 = textArray[0];"]}, {"code": 655, "indent": 0, "parameters": ["const option2 = textArray[1];"]}, {"code": 655, "indent": 0, "parameters": [" $gameStrings.setValue(6,option1);"]}, {"code": 655, "indent": 0, "parameters": [" $gameStrings.setValue(7,option2);"]}, {"code": 108, "indent": 0, "parameters": ["修正选项组图标"]}, {"code": 355, "indent": 0, "parameters": ["$gameSystem._drill_DCB_curStyle = 25"]}, {"code": 355, "indent": 0, "parameters": ["let style = [\"eventButtons_bowknot2\",\"eventButtons_sleep\"];"]}, {"code": 655, "indent": 0, "parameters": ["DrillUp.g_DCB_data[24].btn_src = style;"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(3);"]}, {"code": 102, "indent": 0, "parameters": [["\\str[6]", "\\c[15]\\str[7]"], -1, -1, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\str[6]"]}, {"code": 111, "indent": 1, "parameters": [12, "Utils.isOptionValid(\"test\")"]}, {"code": 126, "indent": 2, "parameters": [15, 0, 0, 1]}, {"code": 121, "indent": 2, "parameters": [500, 500, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 122, "indent": 1, "parameters": [13, 13, 0, 0, 7]}, {"code": 108, "indent": 1, "parameters": ["成就：妹妹熬夜打游戏 判定"]}, {"code": 111, "indent": 1, "parameters": [12, "$gameParty.hasItem($dataItems[15]) && !$gameSwitches.value(497) && $gameSwitches.value(500)"]}, {"code": 355, "indent": 2, "parameters": ["let code = \"$gameMap.event(34).steupCEQJ(2)\";"]}, {"code": 655, "indent": 2, "parameters": ["$gameStrings.setValue(20,code);"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["做噩梦"]}, {"code": 111, "indent": 1, "parameters": [12, "$gameScreen.picture(60)"]}, {"code": 355, "indent": 2, "parameters": ["let code = \"$gameMap.event(7).steupCEQJ(1,{ahogeMonster:true})\";"]}, {"code": 655, "indent": 2, "parameters": ["$gameStrings.setValue(20,code);"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "this.eventPlayback"]}, {"code": 355, "indent": 2, "parameters": ["let code = \"$gameMap.event(7).steupCEQJ(1,{secondNightVisit:true})\";"]}, {"code": 655, "indent": 2, "parameters": ["$gameStrings.setValue(20,code);"]}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(10);"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 201, "indent": 1, "parameters": [0, 4, 5, 9, 0, 2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\c[15]\\str[7]"]}, {"code": 108, "indent": 1, "parameters": ["选项赋予名称"]}, {"code": 355, "indent": 1, "parameters": ["const idx   = 6;"]}, {"code": 655, "indent": 1, "parameters": ["const eid   = String(this._eventId);"]}, {"code": 655, "indent": 1, "parameters": ["const mapId = $gameMap.mapId();"]}, {"code": 655, "indent": 1, "parameters": ["const key   = `MapEventDialogue${mapId}`;"]}, {"code": 655, "indent": 1, "parameters": ["const dialogueTable = window[key] || {};"]}, {"code": 655, "indent": 1, "parameters": ["const textArray     = dialogueTable[eid]?.[String(idx)];"]}, {"code": 655, "indent": 1, "parameters": ["const option1 = textArray[0];"]}, {"code": 655, "indent": 1, "parameters": ["const option2 = textArray[1];"]}, {"code": 655, "indent": 1, "parameters": [" $gameStrings.setValue(6,option1);"]}, {"code": 655, "indent": 1, "parameters": [" $gameStrings.setValue(7,option2);"]}, {"code": 108, "indent": 1, "parameters": ["修正选项组图标"]}, {"code": 355, "indent": 1, "parameters": ["$gameSystem._drill_DCB_curStyle = 25"]}, {"code": 355, "indent": 1, "parameters": ["let style = [\"eventButtons_sleep2\", \"eventButtons_returns\"];"]}, {"code": 655, "indent": 1, "parameters": ["DrillUp.g_DCB_data[24].btn_src = style;"]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(5);"]}, {"code": 102, "indent": 1, "parameters": [["\\str[6]", "\\c[15]\\str[7]"], 1, -1, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "\\str[6]"]}, {"code": 111, "indent": 2, "parameters": [12, "this.eventPlayback"]}, {"code": 355, "indent": 3, "parameters": ["this.showMapEventDialogue(11);"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 235, "indent": 2, "parameters": [60]}, {"code": 117, "indent": 2, "parameters": [9]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\c[15]\\str[7]"]}, {"code": 119, "indent": 2, "parameters": ["选项"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["$gameSystem.set_hour(23)"]}, {"code": 117, "indent": 0, "parameters": [8]}, {"code": 245, "indent": 0, "parameters": [{"name": "深夜の風鈴と虫の音-02", "volume": 60, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["var IMG = 'mainroom_S_N_CC';"]}, {"code": 655, "indent": 0, "parameters": ["var path = \"onichann_room\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(1, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 231, "indent": 0, "parameters": [15, "", 0, 0, 0, 0, 100, 100, 255, 2]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[15] : 创建动画序列 : 动画序列[5]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[15] : 播放简单状态元集合 : 集合[集中线]"]}, {"code": 108, "indent": 0, "parameters": ["随机角度震动屏幕"]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.setShakeRandom()"]}, {"code": 225, "indent": 0, "parameters": [2, 8, 180, false]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["let index = 7;"]}, {"code": 655, "indent": 0, "parameters": ["let textArray = window.MapEventDialogue21[eid][String(index)];"]}, {"code": 655, "indent": 0, "parameters": ["var template = \"\\\\{\\\\dDCCE[文本[%TEXT%]:预设[11]]\\\\}\";"]}, {"code": 655, "indent": 0, "parameters": ["textArray = textArray.map(t => template.replace(\"%TEXT%\", t));"]}, {"code": 655, "indent": 0, "parameters": ["textArray[0] += \"\\\\w[150]\\\\^\";"]}, {"code": 655, "indent": 0, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 232, "indent": 0, "parameters": [15, 0, 0, 0, 0, 0, 100, 100, 0, 2, 30, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["…………"]}, {"code": 401, "indent": 0, "parameters": ["…………"]}, {"code": 235, "indent": 0, "parameters": [15]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(8);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(9);"]}, {"code": 355, "indent": 0, "parameters": ["let id = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.event(id).steupCEQJ(1,{eventPlayback:true})"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 2, "y": 7}, {"id": 2, "name": "清晨起床演出", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["修改器防范"]}, {"code": 355, "indent": 0, "parameters": ["let version = $gameStrings.value(1);"]}, {"code": 655, "indent": 0, "parameters": ["let versionPattern = /^0\\.\\d{1,2}[A-Z]?$/;"]}, {"code": 655, "indent": 0, "parameters": ["if (!versionPattern.test(version)) {"]}, {"code": 655, "indent": 0, "parameters": ["   $gameStrings.setValue(1,\"\");"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 118, "indent": 0, "parameters": ["error"]}, {"code": 355, "indent": 0, "parameters": ["let id = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.event(id).steupCEQJ(2)"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["$gameMap.autosave();"]}, {"code": 655, "indent": 0, "parameters": ["SceneManager._scene.updateSaveWindow();"]}, {"code": 111, "indent": 0, "parameters": [12, "this.jumpAanotherPage"]}, {"code": 119, "indent": 1, "parameters": ["start"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["天气效果"]}, {"code": 355, "indent": 0, "parameters": ["chahuiUtil.customWeatherConditionChanges()"]}, {"code": 108, "indent": 0, "parameters": ["防范时间错误"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameSystem.hour() < 7 || $gameSystem.hour() >= 12) {"]}, {"code": 655, "indent": 0, "parameters": ["    $gameSystem.set_hour(7);"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 355, "indent": 0, "parameters": ["var IMG = 'mainroom_CR_D_CC';"]}, {"code": 655, "indent": 0, "parameters": ["var path = \"onichann_room\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(1, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 108, "indent": 0, "parameters": ["预加载多天气图像"]}, {"code": 355, "indent": 0, "parameters": ["let imgName = [\"mainroom_S_D\",\"mainroom_S_M\",\"mainroom_C_D\",\"mainroom_R_D\"];"]}, {"code": 655, "indent": 0, "parameters": ["imgName.forEach(function(img) {"]}, {"code": 655, "indent": 0, "parameters": ["  let path = \"onichann_room/\" + img;"]}, {"code": 655, "indent": 0, "parameters": ["    ImageManager.reservePicture(path);"]}, {"code": 655, "indent": 0, "parameters": [" });"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.startFadeIn(150);"]}, {"code": 108, "indent": 0, "parameters": ["防范未加载"]}, {"code": 355, "indent": 0, "parameters": ["const mapId = $gameMap.mapId();"]}, {"code": 655, "indent": 0, "parameters": ["const key = 'MapEventDialogue' + mapId; "]}, {"code": 655, "indent": 0, "parameters": ["if (!window[key]) { "]}, {"code": 655, "indent": 0, "parameters": ["chahuiUtil.loadMapEventDialogue();"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 230, "indent": 0, "parameters": [180]}, {"code": 111, "indent": 0, "parameters": [1, 25, 0, 60, 1]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(2);"]}, {"code": 122, "indent": 1, "parameters": [25, 25, 2, 0, 15]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "Math.random() > 0.66"]}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(3);"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(4);"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["start"]}, {"code": 250, "indent": 0, "parameters": [{"name": "プルチェーン付きローラーブラインド", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [40]}, {"code": 111, "indent": 0, "parameters": [1, 60, 0, 0, 0]}, {"code": 111, "indent": 1, "parameters": [0, 123, 0]}, {"code": 355, "indent": 2, "parameters": ["var IMG = 'mainroom_S_D';"]}, {"code": 655, "indent": 2, "parameters": ["var path = \"onichann_room\";"]}, {"code": 655, "indent": 2, "parameters": ["$gameScreen.showPictureFromPath(1, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 117, "indent": 2, "parameters": [25]}, {"code": 355, "indent": 2, "parameters": ["AudioManager.fadeInBgm(6)"]}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(5);"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["var IMG = 'mainroom_S_M';"]}, {"code": 655, "indent": 2, "parameters": ["var path = \"onichann_room\";"]}, {"code": 655, "indent": 2, "parameters": ["$gameScreen.showPictureFromPath(1, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 117, "indent": 2, "parameters": [25]}, {"code": 355, "indent": 2, "parameters": ["AudioManager.fadeInBgm(6)"]}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(6);"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 60, 0, 1, 0]}, {"code": 355, "indent": 2, "parameters": ["var IMG = 'mainroom_C_D';"]}, {"code": 655, "indent": 2, "parameters": ["var path = \"onichann_room\";"]}, {"code": 655, "indent": 2, "parameters": ["$gameScreen.showPictureFromPath(1, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 117, "indent": 2, "parameters": [25]}, {"code": 355, "indent": 2, "parameters": ["AudioManager.fadeInBgm(6)"]}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(7);"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [1, 60, 0, 2, 0]}, {"code": 355, "indent": 3, "parameters": ["var IMG = 'mainroom_R_D';"]}, {"code": 655, "indent": 3, "parameters": ["var path = \"onichann_room\";"]}, {"code": 655, "indent": 3, "parameters": ["$gameScreen.showPictureFromPath(1, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 230, "indent": 3, "parameters": [60]}, {"code": 117, "indent": 3, "parameters": [25]}, {"code": 355, "indent": 3, "parameters": ["AudioManager.fadeInBgm(6)"]}, {"code": 355, "indent": 3, "parameters": ["this.showMapEventDialogue(8);"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["选项赋予名称"]}, {"code": 355, "indent": 0, "parameters": ["const idx   = 11;"]}, {"code": 655, "indent": 0, "parameters": ["const eid   = String(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["const mapId = $gameMap.mapId();"]}, {"code": 655, "indent": 0, "parameters": ["const key   = `MapEventDialogue${mapId}`;"]}, {"code": 655, "indent": 0, "parameters": ["const dialogueTable = window[key] || {};"]}, {"code": 655, "indent": 0, "parameters": ["const textArray     = dialogueTable[eid]?.[String(idx)];"]}, {"code": 655, "indent": 0, "parameters": ["const option1 = textArray[0];"]}, {"code": 655, "indent": 0, "parameters": ["const option2 = textArray[1];"]}, {"code": 655, "indent": 0, "parameters": [" $gameStrings.setValue(6,option1);"]}, {"code": 655, "indent": 0, "parameters": [" $gameStrings.setValue(7,option2);"]}, {"code": 108, "indent": 0, "parameters": ["修正选项组图标"]}, {"code": 355, "indent": 0, "parameters": ["$gameSystem._drill_DCB_curStyle = 25"]}, {"code": 355, "indent": 0, "parameters": ["let style = [\"eventButtons_bowknot2\",\"eventButtons_returns\"];"]}, {"code": 655, "indent": 0, "parameters": ["DrillUp.g_DCB_data[24].btn_src = style;"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameSelfVariables.get(this, 'skip') > 2"]}, {"code": 355, "indent": 1, "parameters": ["$gameSelfVariables.set(this, 'skip', 0) "]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(9);"]}, {"code": 119, "indent": 1, "parameters": ["imouto"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(10);"]}, {"code": 102, "indent": 0, "parameters": [["\\str[6]", "<<$gameSystem.day()<=2>>\\c[15]\\str[7]"], 1, 0, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\str[6]"]}, {"code": 355, "indent": 1, "parameters": ["$gameSelfVariables.set(this, 'skip', 0) "]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(12);"]}, {"code": 118, "indent": 1, "parameters": ["imouto"]}, {"code": 122, "indent": 1, "parameters": [14, 14, 0, 0, 5]}, {"code": 117, "indent": 1, "parameters": [3]}, {"code": 121, "indent": 1, "parameters": [40, 40, 0]}, {"code": 122, "indent": 1, "parameters": [13, 13, 0, 0, 2]}, {"code": 201, "indent": 1, "parameters": [0, 54, 7, 5, 0, 2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "<<$gameSystem.day()<=2>>\\c[15]\\str[7]"]}, {"code": 355, "indent": 1, "parameters": ["$gameSelfVariables.add(this, 'skip', 1) "]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(13);"]}, {"code": 122, "indent": 1, "parameters": [14, 14, 0, 0, 20]}, {"code": 117, "indent": 1, "parameters": [3]}, {"code": 121, "indent": 1, "parameters": [40, 40, 0]}, {"code": 122, "indent": 1, "parameters": [13, 13, 0, 0, 2]}, {"code": 201, "indent": 1, "parameters": [0, 24, 4, 4, 0, 2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 1, "y": 7}, null, {"id": 4, "name": "清晨起床演出", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 2, "characterIndex": 1}, "list": [{"code": 356, "indent": 0, "parameters": ["SaveNow"]}, {"code": 108, "indent": 0, "parameters": ["天气效果"]}, {"code": 355, "indent": 0, "parameters": ["chahuiUtil.customWeatherConditionChanges()"]}, {"code": 122, "indent": 0, "parameters": [206, 206, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [205, 205, 0, 0, 0]}, {"code": 231, "indent": 0, "parameters": [10, "exterior_S_D", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$gameSystem.hour() >= 7 && $gameSystem.hour() <= 18"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["$gameSystem.add_minute(15)"]}, {"code": 230, "indent": 2, "parameters": [1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["防范时间错误"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameSystem.hour() < 7 || $gameSystem.hour() >= 12) {"]}, {"code": 655, "indent": 0, "parameters": ["    $gameSystem.set_hour(7);"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 117, "indent": 0, "parameters": [10]}, {"code": 117, "indent": 0, "parameters": [8]}, {"code": 355, "indent": 0, "parameters": ["var IMG = 'mainroom_CR_D_CC';"]}, {"code": 655, "indent": 0, "parameters": ["var path = \"onichann_room\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(1, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 108, "indent": 0, "parameters": ["预加载多天气图像"]}, {"code": 355, "indent": 0, "parameters": ["let imgName = [\"mainroom_S_D\",\"mainroom_S_M\",\"mainroom_C_D\",\"mainroom_R_D\"];"]}, {"code": 655, "indent": 0, "parameters": ["imgName.forEach(function(img) {"]}, {"code": 655, "indent": 0, "parameters": ["  let path = \"onichann_room/\" + img;"]}, {"code": 655, "indent": 0, "parameters": ["    ImageManager.reservePicture(path);"]}, {"code": 655, "indent": 0, "parameters": [" });"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["防范未加载"]}, {"code": 355, "indent": 0, "parameters": ["const mapId = $gameMap.mapId();"]}, {"code": 655, "indent": 0, "parameters": ["const key = 'MapEventDialogue' + mapId; "]}, {"code": 655, "indent": 0, "parameters": ["if (!window[key]) { "]}, {"code": 655, "indent": 0, "parameters": ["chahuiUtil.loadMapEventDialogue();"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 232, "indent": 0, "parameters": [10, 0, 0, 0, 0, 0, 100, 100, 0, 0, 90, true]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(0);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(1);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(2);"]}, {"code": 355, "indent": 0, "parameters": ["let id = 2;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.event(id).steupCEQJ(2,{jumpAanotherPage:true})"]}, {"code": 115, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 1, "y": 6}, {"id": 5, "name": "场景初始化", "note": "<resetSelfSwitch>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 0, "characterIndex": 3}, "list": [{"code": 108, "indent": 0, "parameters": ["加载多语言地图对话模板"]}, {"code": 355, "indent": 0, "parameters": ["const mapId = $gameMap.mapId();"]}, {"code": 655, "indent": 0, "parameters": ["const key = 'MapEventDialogue' + mapId; "]}, {"code": 655, "indent": 0, "parameters": ["if (!window[key]) { "]}, {"code": 655, "indent": 0, "parameters": ["chahuiUtil.loadMapEventDialogue();"]}, {"code": 655, "indent": 0, "parameters": ["this.wait(15);"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 355, "indent": 0, "parameters": ["const maxPictures = 90; "]}, {"code": 655, "indent": 0, "parameters": ["for (let pictureId = 1; pictureId <= maxPictures; pictureId++) {"]}, {"code": 655, "indent": 0, "parameters": ["    let picture = $gameScreen.picture(pictureId);"]}, {"code": 655, "indent": 0, "parameters": ["    if (picture) {"]}, {"code": 655, "indent": 0, "parameters": ["        $gameScreen.erasePicture(pictureId);"]}, {"code": 655, "indent": 0, "parameters": ["    }"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen._pictureCidArray = [];"]}, {"code": 108, "indent": 0, "parameters": ["补充监听器"]}, {"code": 355, "indent": 0, "parameters": ["QJ.MPMZ.tl._imoutoUtilCheckInitialization()"]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 121, "indent": 0, "parameters": [31, 31, 1]}, {"code": 111, "indent": 0, "parameters": [12, "chahuiUtil.checkScriptExecutability()"]}, {"code": 355, "indent": 1, "parameters": ["let code = $gameStrings.value(20);"]}, {"code": 655, "indent": 1, "parameters": ["eval(code);"]}, {"code": 655, "indent": 1, "parameters": ["$gameStrings.setValue(20, \"\");"]}, {"code": 119, "indent": 1, "parameters": ["end"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 13, 0, 0, 3]}, {"code": 355, "indent": 1, "parameters": ["let ID = $gameVariables.value(13);"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.event(ID).start()"]}, {"code": 122, "indent": 1, "parameters": [13, 13, 0, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameMap.event(1).start()"]}, {"code": 122, "indent": 1, "parameters": [13, 13, 0, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["end"]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 7}]}