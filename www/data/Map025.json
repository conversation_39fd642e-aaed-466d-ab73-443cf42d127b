{"autoplayBgm": true, "autoplayBgs": false, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "おっかなびっくり", "pan": 0, "pitch": 100, "volume": 65}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 9, "note": "<宅>", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 1, "width": 9, "data": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "场景初始化", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 0, "characterIndex": 3}, "list": [{"code": 108, "indent": 0, "parameters": ["加载多语言地图对话模板"]}, {"code": 355, "indent": 0, "parameters": ["const mapId = $gameMap.mapId();"]}, {"code": 655, "indent": 0, "parameters": ["const key = 'MapEventDialogue' + mapId; "]}, {"code": 655, "indent": 0, "parameters": ["if (!window[key]) { "]}, {"code": 655, "indent": 0, "parameters": ["chahuiUtil.loadMapEventDialogue();"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["补充监听器"]}, {"code": 355, "indent": 0, "parameters": ["QJ.MPMZ.tl._imoutoUtilCheckInitialization()"]}, {"code": 111, "indent": 0, "parameters": [1, 13, 0, 0, 3]}, {"code": 355, "indent": 1, "parameters": ["let ID = $gameVariables.value(13);"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.event(ID).start()"]}, {"code": 122, "indent": 1, "parameters": [13, 13, 0, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameMap.event(6).start()"]}, {"code": 122, "indent": 1, "parameters": [13, 13, 0, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}], "x": 0, "y": 8}, {"id": 2, "name": "厕所超时后续事件", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["$gameSelfVariables.add(this, 'counter', +1)"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 355, "indent": 0, "parameters": ["const maxPictures = 10; "]}, {"code": 655, "indent": 0, "parameters": ["for (let pictureId = 2; pictureId <= maxPictures; pictureId++) {"]}, {"code": 655, "indent": 0, "parameters": ["    let picture = $gameScreen.picture(pictureId);"]}, {"code": 655, "indent": 0, "parameters": ["    if (picture && picture.opacity() === 255) {"]}, {"code": 655, "indent": 0, "parameters": ["        picture.drill_PCE_stopEffect();"]}, {"code": 655, "indent": 0, "parameters": ["        $gameScreen.erasePicture(pictureId);"]}, {"code": 655, "indent": 0, "parameters": ["        $gameScreen.setPictureRemoveCommon(pictureId);"]}, {"code": 655, "indent": 0, "parameters": ["    }"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 355, "indent": 0, "parameters": ["var IMG = \"hall_S_N_HL_SL_AL_O\";"]}, {"code": 655, "indent": 0, "parameters": ["var path = \"corridor_event\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(1, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 111, "indent": 0, "parameters": [12, "$gameSelfVariables.get(this, 'counter') > 4"]}, {"code": 231, "indent": 1, "parameters": [9, "mio_tachie_=ω=", 0, 0, 300, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 1, "parameters": [">显现动作 : 图片[9] : 移动显现 : 时间[40] : 方向角度[0] : 移动距离[180]"]}, {"code": 356, "indent": 1, "parameters": ["SV_PLAY_VOICE toilet_overtimeEvent_08"]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(0);"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["………………"]}, {"code": 401, "indent": 1, "parameters": ["………………"]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(1);"]}, {"code": 119, "indent": 1, "parameters": ["事件结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$gameSelfVariables.get(this, 'counter') > 3"]}, {"code": 231, "indent": 2, "parameters": [9, "mio_tachie_naku", 0, 0, 300, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": [">显现动作 : 图片[9] : 移动显现 : 时间[40] : 方向角度[0] : 移动距离[180]"]}, {"code": 230, "indent": 2, "parameters": [40]}, {"code": 356, "indent": 2, "parameters": [">持续动作 : 图片[9] : 左右震动 : 持续时间[无限] : 周期[10] : 震动幅度[1]"]}, {"code": 356, "indent": 2, "parameters": ["SV_PLAY_VOICE toilet_overtimeEvent_05"]}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(2);"]}, {"code": 356, "indent": 2, "parameters": ["SV_PLAY_VOICE toilet_overtimeEvent_06"]}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(3);"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["………………"]}, {"code": 401, "indent": 2, "parameters": ["………………"]}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(4);"]}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(5);"]}, {"code": 231, "indent": 2, "parameters": [9, "mio_tachie_kimo", 0, 0, 300, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": [">持续动作 : 图片[9] : 上下震动 : 持续时间[30] : 周期[15] : 震动幅度[3]"]}, {"code": 356, "indent": 2, "parameters": ["SV_PLAY_VOICE toilet_overtimeEvent_07"]}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(6);"]}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(7);"]}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(8);"]}, {"code": 119, "indent": 2, "parameters": ["事件结束"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [12, "$gameSelfVariables.get(this, 'counter') > 2"]}, {"code": 231, "indent": 3, "parameters": [9, "mio_tachie_ase", 0, 0, 300, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 3, "parameters": [">显现动作 : 图片[9] : 移动显现 : 时间[40] : 方向角度[0] : 移动距离[180]"]}, {"code": 230, "indent": 3, "parameters": [40]}, {"code": 356, "indent": 3, "parameters": ["SV_PLAY_VOICE toilet_overtimeEvent_03"]}, {"code": 355, "indent": 3, "parameters": ["this.showMapEventDialogue(9);"]}, {"code": 355, "indent": 3, "parameters": ["this.showMapEventDialogue(10);"]}, {"code": 355, "indent": 3, "parameters": ["this.showMapEventDialogue(11);"]}, {"code": 231, "indent": 3, "parameters": [9, "mio_tachie_OAO", 0, 0, 300, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 3, "parameters": ["SV_PLAY_VOICE toilet_overtimeEvent_04"]}, {"code": 355, "indent": 3, "parameters": ["this.showMapEventDialogue(12);"]}, {"code": 355, "indent": 3, "parameters": ["this.showMapEventDialogue(13);"]}, {"code": 355, "indent": 3, "parameters": ["this.showMapEventDialogue(14);"]}, {"code": 119, "indent": 3, "parameters": ["事件结束"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [12, "$gameSelfVariables.get(this, 'counter') > 1"]}, {"code": 231, "indent": 4, "parameters": [9, "mio_tachie_duqi", 0, 0, 300, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 4, "parameters": [">显现动作 : 图片[9] : 移动显现 : 时间[40] : 方向角度[0] : 移动距离[180]"]}, {"code": 230, "indent": 4, "parameters": [40]}, {"code": 356, "indent": 4, "parameters": ["SV_PLAY_VOICE toilet_overtimeEvent_02"]}, {"code": 355, "indent": 4, "parameters": ["this.showMapEventDialogue(15);"]}, {"code": 355, "indent": 4, "parameters": ["this.showMapEventDialogue(16);"]}, {"code": 355, "indent": 4, "parameters": ["this.showMapEventDialogue(17);"]}, {"code": 231, "indent": 4, "parameters": [9, "mio_tachie_IAI", 0, 0, 300, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 4, "parameters": ["SV_PLAY_VOICE toilet_overtimeEvent_09"]}, {"code": 355, "indent": 4, "parameters": ["this.showMapEventDialogue(18);"]}, {"code": 119, "indent": 4, "parameters": ["事件结束"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 111, "indent": 4, "parameters": [12, "$gameSelfVariables.get(this, 'counter') > 0"]}, {"code": 231, "indent": 5, "parameters": [9, "mio_tachie_duqi", 0, 0, 300, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 5, "parameters": [">显现动作 : 图片[9] : 移动显现 : 时间[40] : 方向角度[0] : 移动距离[180]"]}, {"code": 230, "indent": 5, "parameters": [30]}, {"code": 356, "indent": 5, "parameters": ["SV_PLAY_VOICE toilet_overtimeEvent_01"]}, {"code": 355, "indent": 5, "parameters": ["this.showMapEventDialogue(19);"]}, {"code": 355, "indent": 5, "parameters": ["this.showMapEventDialogue(20);"]}, {"code": 355, "indent": 5, "parameters": ["this.showMapEventDialogue(21);"]}, {"code": 119, "indent": 5, "parameters": ["事件结束"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["事件结束"]}, {"code": 122, "indent": 0, "parameters": [13, 13, 0, 0, 1]}, {"code": 201, "indent": 0, "parameters": [0, 11, 4, 5, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 1, "y": 8}, null, {"id": 4, "name": "EV004", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 231, "indent": 0, "parameters": [1, "hall_S_N_SL_AL_O", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 250, "indent": 0, "parameters": [{"name": "Open4", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["………………"]}, {"code": 401, "indent": 0, "parameters": ["………………"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(0);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(1);"]}, {"code": 122, "indent": 0, "parameters": [13, 13, 0, 0, 1]}, {"code": 201, "indent": 0, "parameters": [0, 21, 4, 5, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 3, "y": 8}, null, {"id": 6, "name": "妹妹尿急", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 241, "indent": 0, "parameters": [{"name": "毒キノコにでもあたった？", "volume": 90, "pitch": 120, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": [">外发光效果 : 固定对话框外发光 : 颜色[11] : 厚度[2] : 偏移[3,3]"]}, {"code": 355, "indent": 0, "parameters": ["var IMG = \"hall_S_N_HL_SL\";"]}, {"code": 655, "indent": 0, "parameters": ["var path = \"corridor_event\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(1, path, IMG, 0, 0, 0, 50, 50, 255, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var IMG = 'toilet_S_N_L(have paper)';"]}, {"code": 655, "indent": 0, "parameters": ["var path = \"toilet_nozoku\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(10, path, IMG, 1, 960, 540, 110, 110, 255, 0)"]}, {"code": 250, "indent": 0, "parameters": [{"name": "トイレを流す音", "volume": 70, "pitch": 100, "pan": 0}]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 122, "indent": 0, "parameters": [14, 14, 0, 0, 30]}, {"code": 117, "indent": 0, "parameters": [3]}, {"code": 355, "indent": 0, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["let index = 0;"]}, {"code": 655, "indent": 0, "parameters": ["let textArray = window.MapEventDialogue25[eid][String(index)];"]}, {"code": 655, "indent": 0, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 108, "indent": 0, "parameters": ["腹泻演出"]}, {"code": 355, "indent": 0, "parameters": ["let id = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["let count = 1;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.event(id).steupCEQJ(2,{needLoop:count})"]}, {"code": 355, "indent": 0, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["let index = 1;"]}, {"code": 655, "indent": 0, "parameters": ["let textArray = window.MapEventDialogue25[eid][String(index)];"]}, {"code": 655, "indent": 0, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 355, "indent": 0, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["let index = 2;"]}, {"code": 655, "indent": 0, "parameters": ["let textArray = window.MapEventDialogue25[eid][String(index)];"]}, {"code": 655, "indent": 0, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 355, "indent": 0, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["let index = 3;"]}, {"code": 655, "indent": 0, "parameters": ["let textArray = window.MapEventDialogue25[eid][String(index)];"]}, {"code": 655, "indent": 0, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE foodPoisoningEvent_03"]}, {"code": 355, "indent": 0, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["let index = 4;"]}, {"code": 655, "indent": 0, "parameters": ["let textArray = window.MapEventDialogue25[eid][String(index)];"]}, {"code": 655, "indent": 0, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 355, "indent": 0, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["let index = 5;"]}, {"code": 655, "indent": 0, "parameters": ["let textArray = window.MapEventDialogue25[eid][String(index)];"]}, {"code": 655, "indent": 0, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 355, "indent": 0, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["let index = 6;"]}, {"code": 655, "indent": 0, "parameters": ["let textArray = window.MapEventDialogue25[eid][String(index)];"]}, {"code": 655, "indent": 0, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [14, 14, 0, 0, 10]}, {"code": 117, "indent": 0, "parameters": [3]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["腹泻演出"]}, {"code": 355, "indent": 0, "parameters": ["let id = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["let count = 1;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.event(id).steupCEQJ(2,{needLoop:count})"]}, {"code": 355, "indent": 0, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["let index = 7;"]}, {"code": 655, "indent": 0, "parameters": ["let textArray = window.MapEventDialogue25[eid][String(index)];"]}, {"code": 655, "indent": 0, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 355, "indent": 0, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["let index = 8;"]}, {"code": 655, "indent": 0, "parameters": ["let textArray = window.MapEventDialogue25[eid][String(index)];"]}, {"code": 655, "indent": 0, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE foodPoisoningEvent_04"]}, {"code": 355, "indent": 0, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["let index = 9;"]}, {"code": 655, "indent": 0, "parameters": ["let textArray = window.MapEventDialogue25[eid][String(index)];"]}, {"code": 655, "indent": 0, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["if ($gameScreen.picture(10)) {"]}, {"code": 655, "indent": 0, "parameters": [" $gameScreen.picture(10)._opacity = 0;"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 355, "indent": 0, "parameters": ["var index = Math.randomInt(2);"]}, {"code": 655, "indent": 0, "parameters": ["var IMG = \"corridor_sis_knockDoor_\" + index;"]}, {"code": 655, "indent": 0, "parameters": ["IMG += \"A\";"]}, {"code": 655, "indent": 0, "parameters": ["var path = \"corridor_event\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(5, path, IMG, 0, 0, 0, 50, 50, 255, 0)"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 251, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE foodPoisoningEvent_05"]}, {"code": 355, "indent": 0, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["let index = 10;"]}, {"code": 655, "indent": 0, "parameters": ["let textArray = window.MapEventDialogue25[eid][String(index)];"]}, {"code": 655, "indent": 0, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["if ($gameScreen.picture(10)) {"]}, {"code": 655, "indent": 0, "parameters": [" $gameScreen.picture(10)._opacity = 255;"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["腹泻演出"]}, {"code": 355, "indent": 0, "parameters": ["let id = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["let count = 2;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.event(id).steupCEQJ(2,{needLoop:count})"]}, {"code": 355, "indent": 0, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["let index = 11;"]}, {"code": 655, "indent": 0, "parameters": ["let textArray = window.MapEventDialogue25[eid][String(index)];"]}, {"code": 655, "indent": 0, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 355, "indent": 0, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["let index = 12;"]}, {"code": 655, "indent": 0, "parameters": ["let textArray = window.MapEventDialogue25[eid][String(index)];"]}, {"code": 655, "indent": 0, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 122, "indent": 0, "parameters": [14, 14, 0, 0, 10]}, {"code": 117, "indent": 0, "parameters": [3]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["if ($gameScreen.picture(10)) {"]}, {"code": 655, "indent": 0, "parameters": [" $gameScreen.picture(10)._opacity = 0;"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 355, "indent": 0, "parameters": ["var index = Math.randomInt(2);"]}, {"code": 655, "indent": 0, "parameters": ["var IMG = \"corridor_sis_knockDoor_\" + index;"]}, {"code": 655, "indent": 0, "parameters": ["IMG += \"A\";"]}, {"code": 655, "indent": 0, "parameters": ["var path = \"corridor_event\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(5, path, IMG, 0, 0, 0, 50, 50, 255, 0)"]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.picture( 5 ).drill_PCE_playSustainingShakeLR( 1260000,6,0.6 )"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$gameScreen._shakeDuration <= 1"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [6]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 222, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "obieru", "volume": 40, "pitch": 90, "pan": 0}]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE foodPoisoningEvent_06"]}, {"code": 355, "indent": 0, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["let index = 13;"]}, {"code": 655, "indent": 0, "parameters": ["let textArray = window.MapEventDialogue25[eid][String(index)];"]}, {"code": 655, "indent": 0, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 246, "indent": 0, "parameters": [1]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["if ($gameScreen.picture(10)) {"]}, {"code": 655, "indent": 0, "parameters": [" $gameScreen.picture(10)._opacity = 255;"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["腹泻演出"]}, {"code": 355, "indent": 0, "parameters": ["let id = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["let count = 3;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.event(id).steupCEQJ(2,{needLoop:count})"]}, {"code": 355, "indent": 0, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["let index = 14;"]}, {"code": 655, "indent": 0, "parameters": ["let textArray = window.MapEventDialogue25[eid][String(index)];"]}, {"code": 655, "indent": 0, "parameters": ["var template = \"\\\\{\\\\dDCCE[文本[%TEXT%]:预设[11]]\\\\}\";"]}, {"code": 655, "indent": 0, "parameters": ["textArray = textArray.map(t => template.replace(\"%TEXT%\", t));"]}, {"code": 655, "indent": 0, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 122, "indent": 0, "parameters": [14, 14, 0, 0, 10]}, {"code": 117, "indent": 0, "parameters": [3]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["if ($gameScreen.picture(10)) {"]}, {"code": 655, "indent": 0, "parameters": [" $gameScreen.picture(10)._opacity = 0;"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 355, "indent": 0, "parameters": ["var index = Math.randomInt(2);"]}, {"code": 655, "indent": 0, "parameters": ["var IMG = \"corridor_sis_knockDoor_\" + index;"]}, {"code": 655, "indent": 0, "parameters": ["IMG += \"B\";"]}, {"code": 655, "indent": 0, "parameters": ["var path = \"corridor_event\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(5, path, IMG, 0, 0, 0, 50, 50, 255, 0)"]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.picture( 5 ).drill_PCE_playSustainingShakeLR( 1260000,6,0.6 )"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$gameScreen._shakeDuration <= 1"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [6]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 222, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "obieru", "volume": 40, "pitch": 90, "pan": 0}]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE foodPoisoningEvent_07"]}, {"code": 355, "indent": 0, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["let index = 15;"]}, {"code": 655, "indent": 0, "parameters": ["let textArray = window.MapEventDialogue25[eid][String(index)];"]}, {"code": 655, "indent": 0, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 246, "indent": 0, "parameters": [1]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["if ($gameScreen.picture(10)) {"]}, {"code": 655, "indent": 0, "parameters": [" $gameScreen.picture(10)._opacity = 255;"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 231, "indent": 0, "parameters": [70, "", 0, 0, 0, 0, 100, 100, 255, 2]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[70] : 创建动画序列 : 动画序列[5]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[70] : 播放简单状态元集合 : 集合[集中线]"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 108, "indent": 0, "parameters": ["随机角度震动屏幕"]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.setShakeRandom()"]}, {"code": 225, "indent": 0, "parameters": [2, 8, 999, false]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["var random = 1 + Math.randomInt(4);"]}, {"code": 655, "indent": 0, "parameters": ["var seName = \"Hungry03-\" + random;"]}, {"code": 655, "indent": 0, "parameters": ["var randomPitch = Math.randomInt(40) + 80;"]}, {"code": 655, "indent": 0, "parameters": ["var se = { name: seName, volume: 100, pitch: randomPitch, pan: 0 };"]}, {"code": 655, "indent": 0, "parameters": ["AudioManager.playSe(se);"]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 224, "indent": 0, "parameters": [[0, 0, 0, 153], 60, false]}, {"code": 108, "indent": 0, "parameters": ["扣除体力"]}, {"code": 355, "indent": 0, "parameters": ["var actor = $gameParty.leader();"]}, {"code": 655, "indent": 0, "parameters": ["var maxHp = actor.mhp;"]}, {"code": 655, "indent": 0, "parameters": ["var baseDmg  = Math.randomInt(11) + 10;  "]}, {"code": 655, "indent": 0, "parameters": ["var extraDmg = Math.floor(maxHp * 0.05);"]}, {"code": 655, "indent": 0, "parameters": ["var intended = baseDmg + extraDmg;"]}, {"code": 655, "indent": 0, "parameters": ["var curHp = actor.hp;"]}, {"code": 655, "indent": 0, "parameters": ["var damage = Math.max(0, Math.min(intended, curHp - 1));"]}, {"code": 655, "indent": 0, "parameters": ["actor.gainHp(-damage);"]}, {"code": 355, "indent": 0, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["let index = 16;"]}, {"code": 655, "indent": 0, "parameters": ["let textArray = window.MapEventDialogue25[eid][String(index)];"]}, {"code": 655, "indent": 0, "parameters": ["var template = \"\\\\{\\\\dDCCE[文本[%TEXT%]:预设[11]]\\\\}\";"]}, {"code": 655, "indent": 0, "parameters": ["textArray = textArray.map(t => template.replace(\"%TEXT%\", t));"]}, {"code": 655, "indent": 0, "parameters": ["textArray[0] += \"\\\\w[120]\\\\^\";"]}, {"code": 655, "indent": 0, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 122, "indent": 0, "parameters": [14, 14, 0, 0, 10]}, {"code": 117, "indent": 0, "parameters": [3]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["if ($gameScreen.picture(10)) {"]}, {"code": 655, "indent": 0, "parameters": [" $gameScreen.picture(10)._opacity = 0;"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["放大镜头"]}, {"code": 355, "indent": 0, "parameters": ["var IMG = \"hall_S_N_HL_SL\";"]}, {"code": 655, "indent": 0, "parameters": ["var path = \"corridor_event\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(1, path, IMG, 0, -1500, -850, 100, 100, 255, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var index = Math.randomInt(2);"]}, {"code": 655, "indent": 0, "parameters": ["var IMG = \"corridor_sis_knockDoor_\" + index;"]}, {"code": 655, "indent": 0, "parameters": ["IMG += \"C\";"]}, {"code": 655, "indent": 0, "parameters": ["var path = \"corridor_event\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(5, path, IMG, 0, -1500, -850, 100, 100, 255, 0)"]}, {"code": 108, "indent": 0, "parameters": ["随机角度震动屏幕"]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.setShakeRandom()"]}, {"code": 225, "indent": 0, "parameters": [2, 8, 999, false]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 251, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "Explosion2", "volume": 80, "pitch": 90, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE foodPoisoningEvent_08"]}, {"code": 355, "indent": 0, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["let index = 17;"]}, {"code": 655, "indent": 0, "parameters": ["let textArray = window.MapEventDialogue25[eid][String(index)];"]}, {"code": 655, "indent": 0, "parameters": ["var template = \"\\\\{\\\\dDCCE[文本[%TEXT%]:预设[11]]\\\\}\";"]}, {"code": 655, "indent": 0, "parameters": ["textArray = textArray.map(t => template.replace(\"%TEXT%\", t));"]}, {"code": 655, "indent": 0, "parameters": ["textArray[1] += \"\\\\w[300]\\\\^\";"]}, {"code": 655, "indent": 0, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 231, "indent": 0, "parameters": [10, "exterior_S_N_L", 1, 0, 960, 540, 120, 120, 255, 0]}, {"code": 108, "indent": 0, "parameters": ["随机角度震动屏幕"]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.setShakeRandom()"]}, {"code": 225, "indent": 0, "parameters": [4, 9, 999, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "お漏らし②", "volume": 50, "pitch": 100, "pan": -40}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["腹泻演出"]}, {"code": 355, "indent": 0, "parameters": ["let id = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["let count = 3;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.event(id).steupCEQJ(2,{needLoop:count})"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE foodPoisoningEvent_09"]}, {"code": 355, "indent": 0, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["let index = 18;"]}, {"code": 655, "indent": 0, "parameters": ["let textArray = window.MapEventDialogue25[eid][String(index)];"]}, {"code": 655, "indent": 0, "parameters": ["var template = \"\\\\{\\\\dDCCE[文本[%TEXT%]:预设[11]]\\\\}\";"]}, {"code": 655, "indent": 0, "parameters": ["textArray = textArray.map(t => template.replace(\"%TEXT%\", t));"]}, {"code": 655, "indent": 0, "parameters": ["textArray[1] += \"\\\\w[240]\\\\^\";"]}, {"code": 655, "indent": 0, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["let index = 19;"]}, {"code": 655, "indent": 0, "parameters": ["let textArray = window.MapEventDialogue25[eid][String(index)];"]}, {"code": 655, "indent": 0, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 355, "indent": 0, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["let index = 20;"]}, {"code": 655, "indent": 0, "parameters": ["let textArray = window.MapEventDialogue25[eid][String(index)];"]}, {"code": 655, "indent": 0, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 355, "indent": 0, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["let index = 21;"]}, {"code": 655, "indent": 0, "parameters": ["let textArray = window.MapEventDialogue25[eid][String(index)];"]}, {"code": 655, "indent": 0, "parameters": ["chahuiUtil.multilingualCompatibleDisplayText.call(this, textArray);"]}, {"code": 108, "indent": 0, "parameters": ["消除食物中毒状态，并考虑辣椒效果"]}, {"code": 355, "indent": 0, "parameters": ["let user = $gameActors.actor(1);"]}, {"code": 655, "indent": 0, "parameters": ["user.removeState(58);"]}, {"code": 655, "indent": 0, "parameters": ["if ( $gameNumberArray.value(16).includes(106) ) {"]}, {"code": 655, "indent": 0, "parameters": ["  user.addState(61);"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 122, "indent": 0, "parameters": [14, 14, 0, 0, 30]}, {"code": 117, "indent": 0, "parameters": [3]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 122, "indent": 0, "parameters": [13, 13, 0, 0, 1]}, {"code": 201, "indent": 0, "parameters": [0, 4, 7, 5, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["腹泻演出"]}, {"code": 118, "indent": 0, "parameters": ["loop"]}, {"code": 355, "indent": 0, "parameters": ["if (this.needLoop) {"]}, {"code": 655, "indent": 0, "parameters": ["this.needLoop -= 1;"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 355, "indent": 0, "parameters": ["var random = 1 + Math.randomInt(4);"]}, {"code": 655, "indent": 0, "parameters": ["var seName = \"Hungry03-\" + random;"]}, {"code": 655, "indent": 0, "parameters": ["var randomPitch = Math.randomInt(40) + 80;"]}, {"code": 655, "indent": 0, "parameters": ["var se = { name: seName, volume: 100, pitch: randomPitch, pan: 0 };"]}, {"code": 655, "indent": 0, "parameters": ["AudioManager.playSe(se);"]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 108, "indent": 0, "parameters": ["随机角度震动屏幕"]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.setShakeRandom()"]}, {"code": 225, "indent": 0, "parameters": [2, 8, 60, false]}, {"code": 224, "indent": 0, "parameters": [[0, 0, 0, 153], 60, false]}, {"code": 108, "indent": 0, "parameters": ["扣除体力"]}, {"code": 355, "indent": 0, "parameters": ["var actor = $gameParty.leader();"]}, {"code": 655, "indent": 0, "parameters": ["var maxHp = actor.mhp;"]}, {"code": 655, "indent": 0, "parameters": ["var baseDmg  = Math.randomInt(11) + 10;  "]}, {"code": 655, "indent": 0, "parameters": ["var extraDmg = Math.floor(maxHp * 0.02);"]}, {"code": 655, "indent": 0, "parameters": ["var intended = baseDmg + extraDmg;"]}, {"code": 655, "indent": 0, "parameters": ["var curHp = actor.hp;"]}, {"code": 655, "indent": 0, "parameters": ["var damage = Math.max(0, Math.min(intended, curHp - 1));"]}, {"code": 655, "indent": 0, "parameters": ["actor.gainHp(-damage);"]}, {"code": 111, "indent": 0, "parameters": [12, "this.needLoop && this.needLoop > 0"]}, {"code": 230, "indent": 1, "parameters": [40]}, {"code": 119, "indent": 1, "parameters": ["loop"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 6}]}