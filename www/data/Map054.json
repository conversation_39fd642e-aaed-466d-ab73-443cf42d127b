{"autoplayBgm": false, "autoplayBgs": false, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 12, "note": "<宅>", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 1, "width": 12, "data": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "场景初始化", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 0, "characterIndex": 3}, "list": [{"code": 108, "indent": 0, "parameters": ["加载多语言地图对话模板"]}, {"code": 355, "indent": 0, "parameters": ["const mapId = $gameMap.mapId();"]}, {"code": 655, "indent": 0, "parameters": ["const key = 'MapEventDialogue' + mapId; "]}, {"code": 655, "indent": 0, "parameters": ["if (!window[key]) { "]}, {"code": 655, "indent": 0, "parameters": ["chahuiUtil.loadMapEventDialogue();"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 355, "indent": 0, "parameters": ["const maxPictures = 90; "]}, {"code": 655, "indent": 0, "parameters": ["for (let pictureId = 1; pictureId <= maxPictures; pictureId++) {"]}, {"code": 655, "indent": 0, "parameters": ["    let picture = $gameScreen.picture(pictureId);"]}, {"code": 655, "indent": 0, "parameters": ["    if (picture) {"]}, {"code": 655, "indent": 0, "parameters": ["        $gameScreen.erasePicture(pictureId);"]}, {"code": 655, "indent": 0, "parameters": ["    }"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen._pictureCidArray = [];"]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 356, "indent": 0, "parameters": ["PB_BGS_全停止"]}, {"code": 314, "indent": 0, "parameters": [0, 2]}, {"code": 111, "indent": 0, "parameters": [1, 13, 0, 0, 3]}, {"code": 355, "indent": 1, "parameters": ["let ID = $gameVariables.value(13);"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.event(ID).start()"]}, {"code": 122, "indent": 1, "parameters": [13, 13, 0, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameMap.event(2).start()"]}, {"code": 122, "indent": 1, "parameters": [13, 13, 0, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}], "x": 0, "y": 11}, {"id": 2, "name": "清晨事件", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 111, "indent": 0, "parameters": [12, "Utils.isOptionValid(\"test\")"]}, {"code": 355, "indent": 1, "parameters": ["$gameSelfVariables.setValue([1, 2, 'healing'],0)"]}, {"code": 355, "indent": 1, "parameters": ["let times = $gameSelfVariables.value([1, 2, 'faceRub']);"]}, {"code": 655, "indent": 1, "parameters": ["times += 1;"]}, {"code": 655, "indent": 1, "parameters": ["$gameSelfVariables.setValue([1, 2, 'faceRub'], times);"]}, {"code": 355, "indent": 1, "parameters": ["let times = $gameSelfVariables.value([54, 3, 'forceWakeUp']);"]}, {"code": 655, "indent": 1, "parameters": ["times += 4;"]}, {"code": 655, "indent": 1, "parameters": ["$gameSelfVariables.setValue([54, 3, 'forceWakeUp'], times);"]}, {"code": 122, "indent": 1, "parameters": [60, 60, 0, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [207, 207, 0, 0, 0]}, {"code": 313, "indent": 0, "parameters": [0, 2, 1, 35]}, {"code": 313, "indent": 0, "parameters": [0, 2, 1, 41]}, {"code": 313, "indent": 0, "parameters": [0, 2, 0, 31]}, {"code": 111, "indent": 0, "parameters": [1, 60, 0, 2, 0]}, {"code": 231, "indent": 1, "parameters": [1, "sister_room_day_rain", 0, 0, 0, 0, 50, 50, 255, 0]}, {"code": 231, "indent": 1, "parameters": [3, "sis_chibi_sleep1_day2", 0, 0, 0, 0, 50, 50, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 60, 0, 1, 0]}, {"code": 231, "indent": 2, "parameters": [1, "sister_room_day_cloudy", 0, 0, 0, 0, 50, 50, 255, 0]}, {"code": 231, "indent": 2, "parameters": [3, "sis_chibi_sleep1_day2", 0, 0, 0, 0, 50, 50, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 231, "indent": 2, "parameters": [1, "sister_room_day_fine", 0, 0, 0, 0, 50, 50, 255, 0]}, {"code": 231, "indent": 2, "parameters": [3, "sis_chibi_sleep1_day", 0, 0, 0, 0, 50, 50, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "Utils.isMobileDevice()"]}, {"code": 356, "indent": 1, "parameters": ["P_CALL_CE 3 17 3"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 356, "indent": 1, "parameters": ["P_CALL_CE 3 17 1"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": [">图片与鼠标控制核心 : 图片[3] : 修改为像素判定"]}, {"code": 356, "indent": 0, "parameters": [">鼠标悬停触发图片 : 图片[3] : 绑定设置[1] : 绑定单次触发-悬停[一帧]时 : 执行公共事件[15]"]}, {"code": 356, "indent": 0, "parameters": [">鼠标悬停触发图片 : 图片[3] : 绑定设置[2] : 绑定单次触发-离开悬停[一帧]时 : 执行公共事件[16]"]}, {"code": 230, "indent": 0, "parameters": [70]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 122, "indent": 0, "parameters": [14, 14, 0, 0, 15]}, {"code": 117, "indent": 0, "parameters": [3]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 3, "y": 11}, {"id": 3, "name": "清晨摸头事件", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 1, "characterIndex": 3}, "list": [{"code": 117, "indent": 0, "parameters": [33]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 118, "indent": 0, "parameters": ["选项"]}, {"code": 108, "indent": 0, "parameters": ["选项赋予名称"]}, {"code": 355, "indent": 0, "parameters": ["const idx   = 1;"]}, {"code": 655, "indent": 0, "parameters": ["const eid   = String(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["const mapId = $gameMap.mapId();"]}, {"code": 655, "indent": 0, "parameters": ["const key   = `MapEventDialogue${mapId}`;"]}, {"code": 655, "indent": 0, "parameters": ["const dialogueTable = window[key] || {};"]}, {"code": 655, "indent": 0, "parameters": ["const textArray     = dialogueTable[eid]?.[String(idx)];"]}, {"code": 655, "indent": 0, "parameters": ["const option1 = textArray[0];"]}, {"code": 655, "indent": 0, "parameters": ["const option2 = textArray[1];"]}, {"code": 655, "indent": 0, "parameters": ["const option3 = textArray[2];"]}, {"code": 655, "indent": 0, "parameters": [" $gameStrings.setValue(6,option1);"]}, {"code": 655, "indent": 0, "parameters": [" $gameStrings.setValue(7,option2);"]}, {"code": 655, "indent": 0, "parameters": [" $gameStrings.setValue(8,option3);"]}, {"code": 108, "indent": 0, "parameters": ["修正选项组图标"]}, {"code": 355, "indent": 0, "parameters": ["$gameSystem._drill_DCB_curStyle = 25"]}, {"code": 355, "indent": 0, "parameters": ["let style = [\"eventButtons_touch\", \"eventButtons_talk2\", \"eventButtons_returns\"];"]}, {"code": 655, "indent": 0, "parameters": ["DrillUp.g_DCB_data[24].btn_src = style;"]}, {"code": 355, "indent": 0, "parameters": ["ImageManager.reservePicture(\"sis_chibi_sleep1_dayB\"); "]}, {"code": 655, "indent": 0, "parameters": ["ImageManager.reservePicture(\"sis_chibi_sleep1_day2B\");"]}, {"code": 356, "indent": 0, "parameters": [">图片与鼠标控制核心 : 图片[3] : 修改为像素判定"]}, {"code": 356, "indent": 0, "parameters": [">鼠标悬停触发图片 : 图片[3] : 绑定设置[1] : 绑定单次触发-悬停[一帧]时 : 执行公共事件[15]"]}, {"code": 356, "indent": 0, "parameters": [">鼠标悬停触发图片 : 图片[3] : 绑定设置[2] : 绑定单次触发-离开悬停[一帧]时 : 执行公共事件[16]"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(0);"]}, {"code": 102, "indent": 0, "parameters": [["<<1==1>>\\str[6]", "\\c[15]\\str[7]", "\\c[15]\\str[8]"], -1, -1, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "<<1==1>>\\str[6]"]}, {"code": 108, "indent": 1, "parameters": ["喂过药的分支"]}, {"code": 111, "indent": 1, "parameters": [12, "$gameSelfVariables.value([1, 2, 'healing']) > 0"]}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(2);"]}, {"code": 355, "indent": 2, "parameters": ["let oldName = $gameScreen.picture(3)._name;"]}, {"code": 655, "indent": 2, "parameters": ["let newName = oldName + \"B\";"]}, {"code": 655, "indent": 2, "parameters": ["$gameScreen.changePictureName(3, newName);"]}, {"code": 250, "indent": 2, "parameters": [{"name": "041myuu_YumeSE_FukidashiAngry01", "volume": 20, "pitch": 90, "pan": 0}]}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(3);"]}, {"code": 356, "indent": 2, "parameters": ["SV_STOP_VOICE"]}, {"code": 356, "indent": 2, "parameters": ["SV_PLAY_VOICE sis_room_itazura_07"]}, {"code": 250, "indent": 2, "parameters": [{"name": "014myuu_YumeSE_SystemBuzzer03", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 2, "parameters": [">持续动作 : 图片[3] : 左右震动 : 持续时间[20] : 周期[10] : 震动幅度[2]"]}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(4);"]}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(5);"]}, {"code": 355, "indent": 2, "parameters": ["$gameScreen._pictureCidArray = [];"]}, {"code": 250, "indent": 2, "parameters": [{"name": "着替え１、シャツを着る極短、装備変更", "volume": 75, "pitch": 60, "pan": 0}]}, {"code": 356, "indent": 2, "parameters": [">图片快捷操作 : 图片[3] : 修改单属性 : 透明度[0] : 时间[60]"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 355, "indent": 2, "parameters": ["$gameMap.event(4).steupCEQJ(1)"]}, {"code": 115, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 108, "indent": 2, "parameters": ["选项赋予名称"]}, {"code": 355, "indent": 2, "parameters": ["const idx   = 7;"]}, {"code": 655, "indent": 2, "parameters": ["const eid   = String(this._eventId);"]}, {"code": 655, "indent": 2, "parameters": ["const mapId = $gameMap.mapId();"]}, {"code": 655, "indent": 2, "parameters": ["const key   = `MapEventDialogue${mapId}`;"]}, {"code": 655, "indent": 2, "parameters": ["const dialogueTable = window[key] || {};"]}, {"code": 655, "indent": 2, "parameters": ["const textArray     = dialogueTable[eid]?.[String(idx)];"]}, {"code": 655, "indent": 2, "parameters": ["const option1 = textArray[0];"]}, {"code": 655, "indent": 2, "parameters": ["const option2 = textArray[1];"]}, {"code": 655, "indent": 2, "parameters": [" $gameStrings.setValue(6,option1);"]}, {"code": 655, "indent": 2, "parameters": [" $gameStrings.setValue(7,option2);"]}, {"code": 108, "indent": 2, "parameters": ["修正选项组图标"]}, {"code": 355, "indent": 2, "parameters": ["$gameSystem._drill_DCB_curStyle = 25"]}, {"code": 355, "indent": 2, "parameters": ["let style = [\"eventButtons_touch2\", \"eventButtons_returns\"];"]}, {"code": 655, "indent": 2, "parameters": ["DrillUp.g_DCB_data[24].btn_src = style;"]}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(6);"]}, {"code": 102, "indent": 2, "parameters": [["\\str[6]", "\\c[15]\\str[7]"], 1, -1, 2, 0]}, {"code": 402, "indent": 2, "parameters": [0, "\\str[6]"]}, {"code": 250, "indent": 3, "parameters": [{"name": "041myuu_YumeSE_FukidashiAngry01", "volume": 20, "pitch": 90, "pan": 0}]}, {"code": 355, "indent": 3, "parameters": ["ImageManager.reservePicture(\"sis_chibi_sleep1_dayA\");"]}, {"code": 655, "indent": 3, "parameters": ["ImageManager.reservePicture(\"sis_chibi_sleep1_dayB\");"]}, {"code": 655, "indent": 3, "parameters": ["ImageManager.reservePicture(\"sis_chibi_sleep1_day2A\"); "]}, {"code": 655, "indent": 3, "parameters": ["ImageManager.reservePicture(\"sis_chibi_sleep1_day2B\");"]}, {"code": 356, "indent": 3, "parameters": [">持续动作 : 图片[3] : 左右震动 : 持续时间[20] : 周期[10] : 震动幅度[2]"]}, {"code": 230, "indent": 3, "parameters": [60]}, {"code": 355, "indent": 3, "parameters": ["$gameSelfVariables.add(this, 'forceWakeUp', 1)"]}, {"code": 108, "indent": 3, "parameters": ["连续三次的效果"]}, {"code": 111, "indent": 3, "parameters": [12, "$gameSelfVariables.get(this, 'forceWakeUp') >= 3 && Math.random() > 0.2"]}, {"code": 355, "indent": 4, "parameters": ["$gameScreen._pictureCidArray = [];"]}, {"code": 250, "indent": 4, "parameters": [{"name": "着替え１、シャツを着る極短、装備変更", "volume": 75, "pitch": 60, "pan": 0}]}, {"code": 356, "indent": 4, "parameters": [">图片快捷操作 : 图片[3] : 修改单属性 : 透明度[0] : 时间[60]"]}, {"code": 108, "indent": 4, "parameters": ["防范妹妹呼吸音持续"]}, {"code": 355, "indent": 4, "parameters": ["AudioManager.stopVoice(null, 3)"]}, {"code": 230, "indent": 4, "parameters": [60]}, {"code": 355, "indent": 4, "parameters": ["let id = 4;"]}, {"code": 655, "indent": 4, "parameters": ["$gameMap.event(id).steupCEQJ(1,{forceWakeUp:true})"]}, {"code": 115, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 402, "indent": 2, "parameters": [1, "\\c[15]\\str[7]"]}, {"code": 119, "indent": 3, "parameters": ["选项"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 404, "indent": 2, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["let oldName = $gameScreen.picture(3)._name;"]}, {"code": 655, "indent": 2, "parameters": ["let newName = oldName + \"A\";"]}, {"code": 655, "indent": 2, "parameters": ["$gameScreen.changePictureName(3, newName);"]}, {"code": 111, "indent": 2, "parameters": [12, "Math.random() >= 0.75"]}, {"code": 355, "indent": 3, "parameters": ["var name = \"sis_room_itazura03_\" + Math.randomInt(3);"]}, {"code": 655, "indent": 3, "parameters": ["var args = [name, \"40\", \"100\", \"0\"];"]}, {"code": 655, "indent": 3, "parameters": ["this.execPlayVoice(args, false);"]}, {"code": 355, "indent": 3, "parameters": ["this.showMapEventDialogue(8,0);"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [12, "Math.random() >= 0.5"]}, {"code": 355, "indent": 4, "parameters": ["var name = \"sis_room_itazura04_\" + Math.randomInt(3);"]}, {"code": 655, "indent": 4, "parameters": ["var args = [name, \"40\", \"100\", \"0\"];"]}, {"code": 655, "indent": 4, "parameters": ["this.execPlayVoice(args, false);"]}, {"code": 355, "indent": 4, "parameters": ["this.showMapEventDialogue(8,1);"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 355, "indent": 4, "parameters": ["var name = \"sis_room_itazura01_\" + Math.randomInt(3);"]}, {"code": 655, "indent": 4, "parameters": ["var args = [name, \"40\", \"100\", \"0\"];"]}, {"code": 655, "indent": 4, "parameters": ["this.execPlayVoice(args, false);"]}, {"code": 355, "indent": 4, "parameters": ["this.showMapEventDialogue(8,2);"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 355, "indent": 3, "parameters": ["let oldName = $gameScreen.picture(3)._name;"]}, {"code": 655, "indent": 3, "parameters": ["let newName = oldName.slice(0, -1); "]}, {"code": 655, "indent": 3, "parameters": ["$gameScreen.changePictureName(3, newName);"]}, {"code": 355, "indent": 3, "parameters": ["this.showMapEventDialogue(9);"]}, {"code": 108, "indent": 3, "parameters": ["时间影响"]}, {"code": 122, "indent": 3, "parameters": [14, 14, 0, 2, 5, 12]}, {"code": 117, "indent": 3, "parameters": [3]}, {"code": 111, "indent": 3, "parameters": [12, "$gameSystem.hour() >= 8"]}, {"code": 119, "indent": 4, "parameters": ["情景结束"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 119, "indent": 3, "parameters": ["选项"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\c[15]\\str[7]"]}, {"code": 108, "indent": 1, "parameters": ["预定为摸头后有概率转变睡姿"]}, {"code": 355, "indent": 1, "parameters": ["ImageManager.reservePicture(\"sis_chibi_sleep1_dayB\"); "]}, {"code": 655, "indent": 1, "parameters": ["ImageManager.reservePicture(\"sis_chibi_sleep1_day2B\");"]}, {"code": 355, "indent": 1, "parameters": ["let oldName = $gameScreen.picture(3)._name;"]}, {"code": 655, "indent": 1, "parameters": ["let newName = oldName + \"A\";"]}, {"code": 655, "indent": 1, "parameters": ["$gameScreen.changePictureName(3, newName);"]}, {"code": 111, "indent": 1, "parameters": [12, "Math.random() >= 0.75"]}, {"code": 355, "indent": 2, "parameters": ["var name = \"sis_room_itazura03_\" + Math.randomInt(3);"]}, {"code": 655, "indent": 2, "parameters": ["var args = [name, \"40\", \"100\", \"0\"];"]}, {"code": 655, "indent": 2, "parameters": ["this.execPlayVoice(args, false);"]}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(8,0);"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [12, "Math.random() >= 0.5"]}, {"code": 355, "indent": 3, "parameters": ["var name = \"sis_room_itazura04_\" + Math.randomInt(3);"]}, {"code": 655, "indent": 3, "parameters": ["var args = [name, \"40\", \"100\", \"0\"];"]}, {"code": 655, "indent": 3, "parameters": ["this.execPlayVoice(args, false);"]}, {"code": 355, "indent": 3, "parameters": ["this.showMapEventDialogue(8,1);"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 355, "indent": 3, "parameters": ["var name = \"sis_room_itazura01_\" + Math.randomInt(3);"]}, {"code": 655, "indent": 3, "parameters": ["var args = [name, \"40\", \"100\", \"0\"];"]}, {"code": 655, "indent": 3, "parameters": ["this.execPlayVoice(args, false);"]}, {"code": 355, "indent": 3, "parameters": ["this.showMapEventDialogue(8,2);"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["let oldName = $gameScreen.picture(3)._name;"]}, {"code": 655, "indent": 2, "parameters": ["let newName = oldName.slice(0, -1); "]}, {"code": 655, "indent": 2, "parameters": ["$gameScreen.changePictureName(3, newName);"]}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(9);"]}, {"code": 108, "indent": 2, "parameters": ["时间影响"]}, {"code": 122, "indent": 2, "parameters": [14, 14, 0, 2, 5, 12]}, {"code": 117, "indent": 2, "parameters": [3]}, {"code": 111, "indent": 2, "parameters": [12, "$gameSystem.hour() >= 8"]}, {"code": 119, "indent": 3, "parameters": ["情景结束"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 119, "indent": 2, "parameters": ["选项"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(10);"]}, {"code": 355, "indent": 1, "parameters": ["let oldName = $gameScreen.picture(3)._name;"]}, {"code": 655, "indent": 1, "parameters": ["let newName = oldName.slice(0, -1); "]}, {"code": 655, "indent": 1, "parameters": ["$gameScreen.changePictureName(3, newName);"]}, {"code": 108, "indent": 1, "parameters": ["觉醒值影响"]}, {"code": 355, "indent": 1, "parameters": ["var adjust = $gameVariables.value(15);"]}, {"code": 655, "indent": 1, "parameters": ["var base = Math.randomInt(3);"]}, {"code": 655, "indent": 1, "parameters": ["var value19 = $gameVariables.value(19);"]}, {"code": 655, "indent": 1, "parameters": ["var effect = Math.min(5, base + adjust);"]}, {"code": 655, "indent": 1, "parameters": ["$gameVariables.setValue(19, value19 - effect);"]}, {"code": 108, "indent": 1, "parameters": ["时间影响"]}, {"code": 122, "indent": 1, "parameters": [14, 14, 0, 2, 5, 12]}, {"code": 117, "indent": 1, "parameters": [3]}, {"code": 111, "indent": 1, "parameters": [12, "$gameSystem.hour() >= 8"]}, {"code": 119, "indent": 2, "parameters": ["情景结束"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 119, "indent": 1, "parameters": ["选项"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "\\c[15]\\str[8]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["情景结束"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(11);"]}, {"code": 122, "indent": 0, "parameters": [13, 13, 0, 0, 10]}, {"code": 121, "indent": 0, "parameters": [40, 40, 0]}, {"code": 201, "indent": 0, "parameters": [0, 11, 5, 6, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 3, "y": 10}, null, {"id": 5, "name": "客厅看电视", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [12, "Utils.isOptionValid(\"test\")"]}, {"code": 121, "indent": 1, "parameters": [31, 31, 1]}, {"code": 122, "indent": 1, "parameters": [15, 15, 0, 0, 3]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 241, "indent": 0, "parameters": [{"name": "白い小鳥に憧れて", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["AudioManager.fadeInBgm(3)"]}, {"code": 355, "indent": 0, "parameters": ["var IMG = 'living_S_N_RL';"]}, {"code": 655, "indent": 0, "parameters": ["var path = \"living_room\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(1, path, IMG, 0, 0, 0, 50, 50, 255, 0)"]}, {"code": 111, "indent": 0, "parameters": [2, "B", 0]}, {"code": 119, "indent": 1, "parameters": ["together"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 119, "indent": 1, "parameters": ["solo"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["solo"]}, {"code": 108, "indent": 0, "parameters": ["妹妹一个人坐在沙发上"]}, {"code": 355, "indent": 0, "parameters": ["var IMG = 'livingRoom_Imouto_normal1';"]}, {"code": 655, "indent": 0, "parameters": ["if ($gameSwitches.value(31)) IMG = 'livingRoom_Imouto_normal2';"]}, {"code": 655, "indent": 0, "parameters": ["var path = \"living_room\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(5, path, IMG, 0, 1060, 400, 50, 50, 255, 0)"]}, {"code": 108, "indent": 0, "parameters": ["哥哥一个人坐在沙发上"]}, {"code": 355, "indent": 0, "parameters": ["var IMG = 'livingRoom_oniichan_sitting';"]}, {"code": 655, "indent": 0, "parameters": ["var path = \"living_room\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(7, path, IMG, 0, 960, 365, 50, 50, 255, 0)"]}, {"code": 119, "indent": 0, "parameters": ["end"]}, {"code": 118, "indent": 0, "parameters": ["together"]}, {"code": 108, "indent": 0, "parameters": ["兄妹坐在一起"]}, {"code": 355, "indent": 0, "parameters": ["var IMG = 'livingRoom_Imouto_sittingTogether4';"]}, {"code": 655, "indent": 0, "parameters": ["var path = \"living_room\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(5, path, IMG, 0, 950, 340, 50, 50, 255, 0)"]}, {"code": 119, "indent": 0, "parameters": ["end"]}, {"code": 118, "indent": 0, "parameters": ["end"]}, {"code": 108, "indent": 0, "parameters": ["电视遮挡"]}, {"code": 355, "indent": 0, "parameters": ["var IMG = 'living_tv_light';"]}, {"code": 655, "indent": 0, "parameters": ["var path = \"living_room\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(39, path, IMG, 0, 0, 0, 50, 50, 255, 0)"]}, {"code": 355, "indent": 0, "parameters": ["var IMG = 'living_S_N_RL_mask';"]}, {"code": 655, "indent": 0, "parameters": ["var path = \"living_room\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(40, path, IMG, 0, 0, 0, 50, 50, 0, 0)"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["let id = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.event(id).steupCEQJ(2)"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [60]}, {"code": 108, "indent": 0, "parameters": ["根据进度选择观看集数"]}, {"code": 355, "indent": 0, "parameters": ["let value = $gameSelfVariables.value([$gameMap.mapId(), this._eventId, 'animeEpisode']);"]}, {"code": 655, "indent": 0, "parameters": ["value += 1;"]}, {"code": 655, "indent": 0, "parameters": ["let labelName = 'animeEpisode' + value;"]}, {"code": 655, "indent": 0, "parameters": ["for (var i = 0; i < this._list.length; i++) {"]}, {"code": 655, "indent": 0, "parameters": ["    var command = this._list[i];"]}, {"code": 655, "indent": 0, "parameters": ["    if (command.code === 118 && command.parameters[0] === labelName) {"]}, {"code": 655, "indent": 0, "parameters": ["        this.jumpTo(i);"]}, {"code": 655, "indent": 0, "parameters": ["        break;"]}, {"code": 655, "indent": 0, "parameters": ["    }"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 119, "indent": 0, "parameters": ["error"]}, {"code": 118, "indent": 0, "parameters": ["animeEpisode1"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(0);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(1);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(2);"]}, {"code": 108, "indent": 0, "parameters": ["优化过长文本的显示"]}, {"code": 355, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['x_value'] = 200;"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(3);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(4);"]}, {"code": 108, "indent": 0, "parameters": ["还原规格"]}, {"code": 355, "indent": 0, "parameters": ["var id = $gameSystem._drill_DOp_curStyle['id'];"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['x_value'] = DrillUp.g_DOp_list[ id-1 ]['x_value'];"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['y_value'] = DrillUp.g_DOp_list[ id-1 ]['y_value'];"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(5);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(6);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(7);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(8);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(9);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(10);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(11);"]}, {"code": 119, "indent": 0, "parameters": ["end"]}, {"code": 118, "indent": 0, "parameters": ["animeEpisode2"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(0);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(12);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(13);"]}, {"code": 108, "indent": 0, "parameters": ["优化过长文本的显示"]}, {"code": 355, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['x_value'] = 200;"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(14);"]}, {"code": 108, "indent": 0, "parameters": ["还原规格"]}, {"code": 355, "indent": 0, "parameters": ["var id = $gameSystem._drill_DOp_curStyle['id'];"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['x_value'] = DrillUp.g_DOp_list[ id-1 ]['x_value'];"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['y_value'] = DrillUp.g_DOp_list[ id-1 ]['y_value'];"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(7);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(15);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(16);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(17);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(18);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(11);"]}, {"code": 119, "indent": 0, "parameters": ["end"]}, {"code": 118, "indent": 0, "parameters": ["animeEpisode3"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(0);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(12);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(19);"]}, {"code": 108, "indent": 0, "parameters": ["优化过长文本的显示"]}, {"code": 355, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['x_value'] = 200;"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(20);"]}, {"code": 108, "indent": 0, "parameters": ["还原规格"]}, {"code": 355, "indent": 0, "parameters": ["var id = $gameSystem._drill_DOp_curStyle['id'];"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['x_value'] = DrillUp.g_DOp_list[ id-1 ]['x_value'];"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['y_value'] = DrillUp.g_DOp_list[ id-1 ]['y_value'];"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(7);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(21);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(22);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(23);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(24);"]}, {"code": 119, "indent": 0, "parameters": ["end"]}, {"code": 118, "indent": 0, "parameters": ["animeEpisode4"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(0);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(25);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(26);"]}, {"code": 108, "indent": 0, "parameters": ["优化过长文本的显示"]}, {"code": 355, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['x_value'] = 200;"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(27);"]}, {"code": 108, "indent": 0, "parameters": ["还原规格"]}, {"code": 355, "indent": 0, "parameters": ["var id = $gameSystem._drill_DOp_curStyle['id'];"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['x_value'] = DrillUp.g_DOp_list[ id-1 ]['x_value'];"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['y_value'] = DrillUp.g_DOp_list[ id-1 ]['y_value'];"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(28);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(29);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(23);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(24);"]}, {"code": 119, "indent": 0, "parameters": ["end"]}, {"code": 118, "indent": 0, "parameters": ["animeEpisode5"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(0);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(25);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(30);"]}, {"code": 108, "indent": 0, "parameters": ["优化过长文本的显示"]}, {"code": 355, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['x_value'] = 200;"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(31);"]}, {"code": 108, "indent": 0, "parameters": ["还原规格"]}, {"code": 355, "indent": 0, "parameters": ["var id = $gameSystem._drill_DOp_curStyle['id'];"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['x_value'] = DrillUp.g_DOp_list[ id-1 ]['x_value'];"]}, {"code": 655, "indent": 0, "parameters": ["$gameSystem._drill_DOp_curStyle['y_value'] = DrillUp.g_DOp_list[ id-1 ]['y_value'];"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(32);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(33);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(34);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(35);"]}, {"code": 119, "indent": 0, "parameters": ["end"]}, {"code": 118, "indent": 0, "parameters": ["end"]}, {"code": 108, "indent": 0, "parameters": ["推进进度"]}, {"code": 355, "indent": 0, "parameters": ["let key = [$gameMap.mapId(), this._eventId, 'animeEpisode'];"]}, {"code": 655, "indent": 0, "parameters": ["let value = $gameSelfVariables.value(key);"]}, {"code": 655, "indent": 0, "parameters": ["value += 1;"]}, {"code": 655, "indent": 0, "parameters": ["$gameSelfVariables.setValue(key, value);"]}, {"code": 111, "indent": 0, "parameters": [12, "Utils.isOptionValid(\"test\")"]}, {"code": 355, "indent": 1, "parameters": ["$gameSystem.add_day(7)"]}, {"code": 122, "indent": 1, "parameters": [13, 13, 0, 0, 1]}, {"code": 122, "indent": 1, "parameters": [14, 14, 0, 0, 20]}, {"code": 117, "indent": 1, "parameters": [3]}, {"code": 201, "indent": 1, "parameters": [0, 4, 7, 6, 0, 2]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["error"]}, {"code": 122, "indent": 0, "parameters": [17, 17, 1, 2, 10, 25]}, {"code": 122, "indent": 0, "parameters": [20, 20, 1, 2, 5, 15]}, {"code": 122, "indent": 0, "parameters": [14, 14, 0, 0, 30]}, {"code": 117, "indent": 0, "parameters": [3]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 122, "indent": 0, "parameters": [13, 13, 0, 0, 1]}, {"code": 201, "indent": 0, "parameters": [0, 4, 7, 6, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 8, "y": 11}, null, {"id": 7, "name": "白天提前结束探索后续事件", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 231, "indent": 0, "parameters": [1, "sister_room_night_fine", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 355, "indent": 0, "parameters": ["var IMG = 'sis_chibi_sleep1_night_fine';"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPicture(5, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 117, "indent": 0, "parameters": [25]}, {"code": 355, "indent": 0, "parameters": ["AudioManager.fadeInBgm(2)"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.startFadeIn(180);"]}, {"code": 230, "indent": 0, "parameters": [180]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(0);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(1);"]}, {"code": 250, "indent": 0, "parameters": [{"name": "014myuu_YumeSE_SystemBuzzer03", "volume": 55, "pitch": 80, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": [">持续动作 : 图片[5] : 左右震动 : 持续时间[20] : 周期[10] : 震动幅度[2]"]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE sis_room_sleep_11"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(2);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(3);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(4);"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE sis_room_sleep_15"]}, {"code": 230, "indent": 0, "parameters": [210]}, {"code": 356, "indent": 0, "parameters": [">持续动作 : 图片[5] : 左右震动 : 持续时间[20] : 周期[10] : 震动幅度[2]"]}, {"code": 230, "indent": 0, "parameters": [40]}, {"code": 356, "indent": 0, "parameters": [">图片快捷操作 : 图片[5] : 修改单属性 : 透明度[0] : 时间[40]"]}, {"code": 250, "indent": 0, "parameters": [{"name": "着替え１、シャツを着る極短、装備変更", "volume": 75, "pitch": 60, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [40]}, {"code": 313, "indent": 0, "parameters": [0, 2, 0, 26]}, {"code": 117, "indent": 0, "parameters": [26]}, {"code": 231, "indent": 0, "parameters": [16, "mio_tachie_kao_-A-", 0, 0, 1000, 1600, 100, 100, 255, 0]}, {"code": 355, "indent": 0, "parameters": ["var pic_ids = $gameNumberArray.value(41);"]}, {"code": 655, "indent": 0, "parameters": ["pic_ids.forEach(function(pic_id) {"]}, {"code": 655, "indent": 0, "parameters": ["    var picture = $gameScreen.picture(pic_id);"]}, {"code": 655, "indent": 0, "parameters": ["var data = {\t\t\t\t\t\t\t\t\"type\":\"smoothMove\","]}, {"code": 655, "indent": 0, "parameters": ["\"valueX\":0,"]}, {"code": 655, "indent": 0, "parameters": ["\"valueY\":-1450,"]}, {"code": 655, "indent": 0, "parameters": ["\"time\":150,"]}, {"code": 655, "indent": 0, "parameters": ["\"cur_time\":0,\"cur_speedX\":0,\"cur_speedY\":0,}"]}, {"code": 655, "indent": 0, "parameters": ["    if (picture) { picture._drill_PSh_commandChangeTank.push(data);}});"]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE sis_room_sleep_12"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(5);"]}, {"code": 231, "indent": 0, "parameters": [16, "mio_tachie_kao_happy", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 122, "indent": 0, "parameters": [204, 204, 0, 4, "\"呼吸\""]}, {"code": 117, "indent": 0, "parameters": [35]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE sis_room_sleep_13"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(6);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(7);"]}, {"code": 231, "indent": 0, "parameters": [16, "mio_tachie_kao_naku", 0, 0, 1000, 150, 100, 100, 255, 0]}, {"code": 122, "indent": 0, "parameters": [204, 204, 0, 4, "\"左右震动\""]}, {"code": 117, "indent": 0, "parameters": [35]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE sis_room_sleep_14"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(8);"]}, {"code": 122, "indent": 0, "parameters": [13, 13, 0, 0, 1]}, {"code": 201, "indent": 0, "parameters": [0, 11, 6, 5, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 11, "y": 0}]}