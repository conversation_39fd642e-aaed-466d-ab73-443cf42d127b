[null, {"id": 1, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 2, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 3, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 4, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 5, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 6, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 7, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 8, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 9, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 10, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 11, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 12, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 13, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 14, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 15, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 16, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 17, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 18, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 19, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 20, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 21, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 22, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 23, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 24, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 25, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 26, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 27, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 28, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 29, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 30, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 31, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 32, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 33, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 34, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 35, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 36, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 37, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 38, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 39, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 40, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 41, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 42, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 43, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 44, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 45, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 46, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 47, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 48, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 49, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 50, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 51, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 52, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 53, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 54, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 55, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 56, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 57, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 58, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 59, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 60, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 61, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 62, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 63, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 64, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 65, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 66, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 67, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 68, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 69, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 70, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 71, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 72, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 73, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 74, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 75, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 76, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 77, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 78, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 79, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 80, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 81, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 82, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 83, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 84, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 85, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 86, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 87, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 88, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 89, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 90, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 91, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 92, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 93, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 94, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 95, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 96, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 97, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 98, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 99, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}, {"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": true}, "list": [{"code": 356, "indent": 0, "parameters": [">战斗背景 : 背景[1] : 显示"]}, {"code": 356, "indent": 0, "parameters": [">战斗背景 : 背景[1] : 修改单属性 : 缩放X[0.5] : 时间[1]"]}, {"code": 356, "indent": 0, "parameters": [">战斗背景 : 背景[1] : 修改单属性 : 缩放Y[0.5] : 时间[1]"]}, {"code": 356, "indent": 0, "parameters": [">战斗背景 : 背景[11] : 移动到(延迟)-匀速移动 : 位置[100,100] : 时间[1] : 延迟执行时间[1]"]}, {"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 100, "members": [{"enemyId": 200, "x": 501, "y": 444, "hidden": false}, {"enemyId": 199, "x": 408, "y": 436, "hidden": false}, {"enemyId": 198, "x": 408, "y": 436, "hidden": false}, {"enemyId": 197, "x": 408, "y": 436, "hidden": false}], "name": "妹妹大作战", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": true}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}]