{"autoplayBgm": false, "autoplayBgs": false, "battleback1Name": "diningroom", "battleback2Name": "gray bar", "bgm": {"name": "Lemonade", "pan": 0, "pitch": 100, "volume": 90}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 11, "note": "<宅>", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": true, "tilesetId": 1, "width": 11, "data": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "晚饭事件", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 111, "indent": 0, "parameters": [12, "Utils.isOptionValid(\"test\")"]}, {"code": 121, "indent": 1, "parameters": [499, 499, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["$gameMap.chahuiPreloadPicture(\"kitchen_event\", \"kitchenEvent\");"]}, {"code": 655, "indent": 0, "parameters": ["this.setWaitMode('image')"]}, {"code": 314, "indent": 0, "parameters": [0, 2]}, {"code": 241, "indent": 0, "parameters": [{"name": "The-Little-Witch_s-Home", "volume": 70, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["AudioManager.fadeInBgm(2)"]}, {"code": 355, "indent": 0, "parameters": ["var IMG = 'dk_S_E_RL';"]}, {"code": 655, "indent": 0, "parameters": ["var path = \"kitchen_event\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(1, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 313, "indent": 0, "parameters": [0, 2, 1, 40]}, {"code": 313, "indent": 0, "parameters": [0, 2, 0, 32]}, {"code": 121, "indent": 0, "parameters": [37, 37, 1]}, {"code": 108, "indent": 0, "parameters": ["确认视频路径"]}, {"code": 355, "indent": 0, "parameters": ["let vName = \"kitchen_Imouto_idle1\";"]}, {"code": 655, "indent": 0, "parameters": ["if ($gameVariables.value(20) > 66) {"]}, {"code": 655, "indent": 0, "parameters": ["    vName = \"kitchen_Imouto_idle2\";"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.setVideoPictureName(vName, true, false);"]}, {"code": 108, "indent": 0, "parameters": ["绑定视频"]}, {"code": 355, "indent": 0, "parameters": ["let pid = 5;"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPicture(pid, '', 0, 550, 550, 100, 100, 255, 0);"]}, {"code": 655, "indent": 0, "parameters": ["const pic = $gameScreen.picture(pid);"]}, {"code": 655, "indent": 0, "parameters": ["if (pic) {"]}, {"code": 655, "indent": 0, "parameters": ["pic.set<PERSON><PERSON><PERSON>(true);"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 355, "indent": 0, "parameters": ["var IMG = 'knife&fork';"]}, {"code": 655, "indent": 0, "parameters": ["var path = \"kitchen_event\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(6, path, IMG, 0, 0,0, 100, 100, 255, 0)"]}, {"code": 111, "indent": 0, "parameters": [12, "Utils.isMobileDevice()"]}, {"code": 356, "indent": 1, "parameters": ["P_CALL_CE 5 17 3"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 356, "indent": 1, "parameters": ["P_CALL_CE 5 17 1"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["P_CALL_CE 5 15 4"]}, {"code": 356, "indent": 0, "parameters": ["P_CALL_CE 5 16 5"]}, {"code": 108, "indent": 0, "parameters": ["选项监听"]}, {"code": 355, "indent": 0, "parameters": ["let id = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.event(id).steupCEQJ(2)"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["清理掉可能存在的金钱道具"]}, {"code": 355, "indent": 0, "parameters": ["    const itemIdArray = [4,5,6,7,8,9,10,11,12];"]}, {"code": 655, "indent": 0, "parameters": ["    const allItems = $gameParty.allItems();"]}, {"code": 655, "indent": 0, "parameters": ["    for (const item of allItems) {"]}, {"code": 655, "indent": 0, "parameters": ["        if (!item) continue;"]}, {"code": 655, "indent": 0, "parameters": ["        if (!DataManager.isItem(item)) continue;"]}, {"code": 655, "indent": 0, "parameters": ["        if (!itemIdArray.includes(item.id)) continue;"]}, {"code": 655, "indent": 0, "parameters": ["        const qty = $gameParty.numItems(item);"]}, {"code": 655, "indent": 0, "parameters": ["        for (let i = 0; i < qty; i++) {"]}, {"code": 655, "indent": 0, "parameters": ["            QJ.MPMZ.tl.ex_playerDropsValueChange(item);"]}, {"code": 655, "indent": 0, "parameters": ["        }"]}, {"code": 655, "indent": 0, "parameters": ["    }\t"]}, {"code": 108, "indent": 0, "parameters": ["刷新标题画面动画"]}, {"code": 355, "indent": 0, "parameters": ["chahuiUtil.RefreshTitleScreenAnimation(\"idleInDining\")"]}, {"code": 115, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 112, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "SceneManager._scene._messageWindow._choiceWindow && SceneManager._scene._messageWindow._choiceWindow.active"]}, {"code": 111, "indent": 2, "parameters": [12, "SceneManager._scene._messageWindow._choiceWindow.index() === 0"]}, {"code": 111, "indent": 3, "parameters": [12, "!SceneManager._scene._messageWindow._choiceWindow._eventRan"]}, {"code": 355, "indent": 4, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 4, "parameters": ["let textArray = window.MapEventDialogue11[eid][\"0\"];"]}, {"code": 655, "indent": 4, "parameters": ["let text = textArray.join();"]}, {"code": 655, "indent": 4, "parameters": ["if (ConfigManager.language !== 0) {"]}, {"code": 655, "indent": 4, "parameters": ["    text = \"\\\\fn[RiiT<PERSON><PERSON><PERSON><PERSON>]\\\\dDCOG[11:2:2:2]\\\\fs[32]\" + text;"]}, {"code": 655, "indent": 4, "parameters": ["} else {"]}, {"code": 655, "indent": 4, "parameters": ["    text = \"\\\\dDCOG[11:2:2:2]\\\\fs[32]\" + text;"]}, {"code": 655, "indent": 4, "parameters": ["}"]}, {"code": 655, "indent": 4, "parameters": ["$gameTemp.drill_GFTT_createSimple( [800,600], text, 2, 9, 180 );"]}, {"code": 230, "indent": 4, "parameters": [180]}, {"code": 355, "indent": 4, "parameters": ["SceneManager._scene._messageWindow._choiceWindow._eventRan = true;"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 111, "indent": 2, "parameters": [12, "SceneManager._scene._messageWindow._choiceWindow.index() === 1"]}, {"code": 111, "indent": 3, "parameters": [12, "!SceneManager._scene._messageWindow._choiceWindow._eventRan"]}, {"code": 355, "indent": 4, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 4, "parameters": ["let textArray = window.MapEventDialogue11[eid][\"1\"];"]}, {"code": 655, "indent": 4, "parameters": ["let text = textArray.join();"]}, {"code": 655, "indent": 4, "parameters": ["if (ConfigManager.language !== 0) {"]}, {"code": 655, "indent": 4, "parameters": ["    text = \"\\\\fn[RiiT<PERSON><PERSON><PERSON><PERSON>]\\\\dDCOG[11:2:2:2]\\\\fs[32]\" + text;"]}, {"code": 655, "indent": 4, "parameters": ["} else {"]}, {"code": 655, "indent": 4, "parameters": ["    text = \"\\\\dDCOG[11:2:2:2]\\\\fs[32]\" + text;"]}, {"code": 655, "indent": 4, "parameters": ["}"]}, {"code": 655, "indent": 4, "parameters": ["$gameTemp.drill_GFTT_createSimple( [800,600], text, 2, 9, 180 );"]}, {"code": 230, "indent": 4, "parameters": [180]}, {"code": 355, "indent": 4, "parameters": ["SceneManager._scene._messageWindow._choiceWindow._eventRan = true;"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [10]}, {"code": 111, "indent": 1, "parameters": [0, 55, 0]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 1, "y": 10}, null, null, {"id": 4, "name": "料理制作系统初始化", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["防妹妹小人误触"]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.setPictureRemoveCommon(5)"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 241, "indent": 0, "parameters": [{"name": "cook_cook!", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["AudioManager.fadeInBgm(2)"]}, {"code": 355, "indent": 0, "parameters": ["for (var i = 1; i <= 20; i++) {"]}, {"code": 655, "indent": 0, "parameters": ["    $gameScreen.erasePicture(i);"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 108, "indent": 0, "parameters": ["预加载背景素材"]}, {"code": 355, "indent": 0, "parameters": ["ImageManager.reservePicture(\"kitchen_night_back\");"]}, {"code": 655, "indent": 0, "parameters": ["ImageManager.reservePicture(\"kitchen_night_front\");"]}, {"code": 655, "indent": 0, "parameters": ["this.setWaitMode('image')"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameSystem.hour() >= 12 "]}, {"code": 235, "indent": 1, "parameters": [8]}, {"code": 231, "indent": 1, "parameters": [1, "kitchen_night_back", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 1, "parameters": [10, "kitchen_night_front", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 235, "indent": 1, "parameters": [8]}, {"code": 231, "indent": 1, "parameters": [1, "kitchen_day_back", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 1, "parameters": [10, "kitchen_day_front", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [75, 75, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [76, 76, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [77, 77, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [78, 78, 0, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["let IMG = 'cook_slot_background';"]}, {"code": 655, "indent": 0, "parameters": ["let path = \"crafting_scene\";"]}, {"code": 655, "indent": 0, "parameters": ["let X = 750;"]}, {"code": 655, "indent": 0, "parameters": ["let Y = 160;"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(21, path, IMG, 0, X, Y, 100, 100, 255, 0)"]}, {"code": 355, "indent": 0, "parameters": ["let IMG = 'undefined_matter';"]}, {"code": 655, "indent": 0, "parameters": ["let path = \"crafting_scene\";"]}, {"code": 655, "indent": 0, "parameters": ["let X = 830;"]}, {"code": 655, "indent": 0, "parameters": ["let Y = 230;"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(22, path, IMG, 0, X, Y, 100, 100, 255, 0)"]}, {"code": 355, "indent": 0, "parameters": ["let IMG = 'undefined_matter';"]}, {"code": 655, "indent": 0, "parameters": ["let path = \"crafting_scene\";"]}, {"code": 655, "indent": 0, "parameters": ["let X = 830;"]}, {"code": 655, "indent": 0, "parameters": ["let Y = 435;"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(23, path, IMG, 0, X, Y, 100, 100, 255, 0)"]}, {"code": 355, "indent": 0, "parameters": ["let IMG = 'undefined_matter';"]}, {"code": 655, "indent": 0, "parameters": ["let path = \"crafting_scene\";"]}, {"code": 655, "indent": 0, "parameters": ["let X = 830;"]}, {"code": 655, "indent": 0, "parameters": ["let Y = 640;"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(24, path, IMG, 0, X, Y, 100, 100, 255, 0)"]}, {"code": 356, "indent": 0, "parameters": [">地图按钮集 : 按钮[5] : 显示"]}, {"code": 356, "indent": 0, "parameters": [">地图按钮集 : 按钮[5] : 设置状态 : 封印"]}, {"code": 356, "indent": 0, "parameters": ["P_CALL_CE 22 65 1"]}, {"code": 356, "indent": 0, "parameters": ["P_CALL_CE 23 65 1"]}, {"code": 356, "indent": 0, "parameters": ["P_CALL_CE 24 65 1"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 356, "indent": 0, "parameters": [">持续动作 : 图片[22] : 渐变闪烁 : 持续时间[无限] : 周期[150]"]}, {"code": 356, "indent": 0, "parameters": [">持续动作 : 图片[23] : 渐变闪烁 : 持续时间[无限] : 周期[150]"]}, {"code": 356, "indent": 0, "parameters": [">持续动作 : 图片[24] : 渐变闪烁 : 持续时间[无限] : 周期[150]"]}, {"code": 355, "indent": 0, "parameters": ["$gameMap.event(5).steupCEQJ(4)"]}, {"code": 121, "indent": 0, "parameters": [55, 55, 0]}, {"code": 121, "indent": 0, "parameters": [30, 30, 1]}, {"code": 356, "indent": 0, "parameters": ["SetSelectItemType 0"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 1, "y": 0}, {"id": 5, "name": "食材格判定", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 1, "characterIndex": 3}, "list": [{"code": 108, "indent": 0, "parameters": ["第一个食材格"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameVariables.value(75) > 0) {"]}, {"code": 655, "indent": 0, "parameters": ["var itemId = $gameVariables.value(75); "]}, {"code": 655, "indent": 0, "parameters": ["var item = $dataItems[itemId]; "]}, {"code": 655, "indent": 0, "parameters": ["if (item) {"]}, {"code": 655, "indent": 0, "parameters": ["    $gameParty.gainItem(item, 1); "]}, {"code": 655, "indent": 0, "parameters": ["  }"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 355, "indent": 0, "parameters": ["let IMG = 'undefined_matter';"]}, {"code": 655, "indent": 0, "parameters": ["let path = \"crafting_scene\";"]}, {"code": 655, "indent": 0, "parameters": ["let X = 830;"]}, {"code": 655, "indent": 0, "parameters": ["let Y = 230;"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(22, path, IMG, 0, X, Y, 100, 100, 255, 0)"]}, {"code": 104, "indent": 0, "parameters": [75, 2]}, {"code": 111, "indent": 0, "parameters": [1, 75, 0, 1, 3]}, {"code": 355, "indent": 1, "parameters": ["var itemId = $gameVariables.value(75); "]}, {"code": 655, "indent": 1, "parameters": ["var item = $dataItems[itemId]; "]}, {"code": 655, "indent": 1, "parameters": ["if (item) {"]}, {"code": 655, "indent": 1, "parameters": ["    $gameParty.gainItem(item, -1); "]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 356, "indent": 1, "parameters": [">图标图片 : 图片[22] : 设置物品的图标 : 物品变量[75] : 像素缩放[2.0]"]}, {"code": 122, "indent": 1, "parameters": [74, 74, 0, 1, 75]}, {"code": 356, "indent": 1, "parameters": [">持续动作 : 图片[22] : 呼吸效果 : 持续时间[30] : 周期[15] : 呼吸幅度[5]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["let IMG = 'undefined_matter';"]}, {"code": 655, "indent": 1, "parameters": ["let path = \"crafting_scene\";"]}, {"code": 655, "indent": 1, "parameters": ["let X = 830;"]}, {"code": 655, "indent": 1, "parameters": ["let Y = 230;"]}, {"code": 655, "indent": 1, "parameters": ["$gameScreen.showPictureFromPath(22, path, IMG, 0, X, Y, 100, 100, 255, 0)"]}, {"code": 122, "indent": 1, "parameters": [74, 74, 0, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 75, 0, 1, 3]}, {"code": 356, "indent": 1, "parameters": [">地图按钮集 : 按钮[5] : 设置状态 : 激活"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 76, 0, 1, 3]}, {"code": 356, "indent": 2, "parameters": [">地图按钮集 : 按钮[5] : 设置状态 : 激活"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [1, 77, 0, 1, 3]}, {"code": 356, "indent": 3, "parameters": [">地图按钮集 : 按钮[5] : 设置状态 : 激活"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 356, "indent": 3, "parameters": [">地图按钮集 : 按钮[5] : 设置状态 : 封印"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["$gameMap.event(12).start()"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 1, "characterIndex": 3}, "list": [{"code": 108, "indent": 0, "parameters": ["第二个食材格"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameVariables.value(76) > 0) {"]}, {"code": 655, "indent": 0, "parameters": ["var itemId = $gameVariables.value(76); "]}, {"code": 655, "indent": 0, "parameters": ["var item = $dataItems[itemId]; "]}, {"code": 655, "indent": 0, "parameters": ["if (item) {"]}, {"code": 655, "indent": 0, "parameters": ["    $gameParty.gainItem(item, 1); "]}, {"code": 655, "indent": 0, "parameters": ["  }"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 355, "indent": 0, "parameters": ["let IMG = 'undefined_matter';"]}, {"code": 655, "indent": 0, "parameters": ["let path = \"crafting_scene\";"]}, {"code": 655, "indent": 0, "parameters": ["let X = 830;"]}, {"code": 655, "indent": 0, "parameters": ["let Y = 435;"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(23, path, IMG, 0, X, Y, 100, 100, 255, 0)"]}, {"code": 104, "indent": 0, "parameters": [76, 2]}, {"code": 111, "indent": 0, "parameters": [1, 76, 0, 1, 3]}, {"code": 355, "indent": 1, "parameters": ["if ($gameVariables.value(76) > 0) {"]}, {"code": 655, "indent": 1, "parameters": ["var itemId = $gameVariables.value(76); "]}, {"code": 655, "indent": 1, "parameters": ["var item = $dataItems[itemId]; "]}, {"code": 655, "indent": 1, "parameters": ["if (item) {"]}, {"code": 655, "indent": 1, "parameters": ["    $gameParty.gainItem(item, -1); "]}, {"code": 655, "indent": 1, "parameters": ["  }"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 356, "indent": 1, "parameters": [">图标图片 : 图片[23] : 设置物品的图标 : 物品变量[76] : 像素缩放[2.0]"]}, {"code": 122, "indent": 1, "parameters": [74, 74, 0, 1, 76]}, {"code": 356, "indent": 1, "parameters": [">持续动作 : 图片[23] : 呼吸效果 : 持续时间[30] : 周期[15] : 呼吸幅度[5]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["let IMG = 'undefined_matter';"]}, {"code": 655, "indent": 1, "parameters": ["let path = \"crafting_scene\";"]}, {"code": 655, "indent": 1, "parameters": ["let X = 830;"]}, {"code": 655, "indent": 1, "parameters": ["let Y = 435;"]}, {"code": 655, "indent": 1, "parameters": ["$gameScreen.showPictureFromPath(23, path, IMG, 0, X, Y, 100, 100, 255, 0)"]}, {"code": 122, "indent": 1, "parameters": [74, 74, 0, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 75, 0, 1, 3]}, {"code": 356, "indent": 1, "parameters": [">地图按钮集 : 按钮[5] : 设置状态 : 激活"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 76, 0, 1, 3]}, {"code": 356, "indent": 2, "parameters": [">地图按钮集 : 按钮[5] : 设置状态 : 激活"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [1, 77, 0, 1, 3]}, {"code": 356, "indent": 3, "parameters": [">地图按钮集 : 按钮[5] : 设置状态 : 激活"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 356, "indent": 3, "parameters": [">地图按钮集 : 按钮[5] : 设置状态 : 封印"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["$gameMap.event(12).start()"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 1, "characterIndex": 3}, "list": [{"code": 108, "indent": 0, "parameters": ["第三个食材格"]}, {"code": 355, "indent": 0, "parameters": ["if ($gameVariables.value(77) > 0) {"]}, {"code": 655, "indent": 0, "parameters": ["var itemId = $gameVariables.value(77); "]}, {"code": 655, "indent": 0, "parameters": ["var item = $dataItems[itemId]; "]}, {"code": 655, "indent": 0, "parameters": ["if (item) {"]}, {"code": 655, "indent": 0, "parameters": ["    $gameParty.gainItem(item, 1); "]}, {"code": 655, "indent": 0, "parameters": ["  }"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 355, "indent": 0, "parameters": ["let IMG = 'undefined_matter';"]}, {"code": 655, "indent": 0, "parameters": ["let path = \"crafting_scene\";"]}, {"code": 655, "indent": 0, "parameters": ["let X = 830;"]}, {"code": 655, "indent": 0, "parameters": ["let Y = 640;"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(24, path, IMG, 0, X, Y, 100, 100, 255, 0)"]}, {"code": 104, "indent": 0, "parameters": [77, 2]}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 1, 3]}, {"code": 355, "indent": 1, "parameters": ["if ($gameVariables.value(77) > 0) {"]}, {"code": 655, "indent": 1, "parameters": ["var itemId = $gameVariables.value(77); "]}, {"code": 655, "indent": 1, "parameters": ["var item = $dataItems[itemId]; "]}, {"code": 655, "indent": 1, "parameters": ["if (item) {"]}, {"code": 655, "indent": 1, "parameters": ["    $gameParty.gainItem(item, -1); "]}, {"code": 655, "indent": 1, "parameters": ["  }"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 356, "indent": 1, "parameters": [">图标图片 : 图片[24] : 设置物品的图标 : 物品变量[77] : 像素缩放[2.0]"]}, {"code": 122, "indent": 1, "parameters": [74, 74, 0, 1, 77]}, {"code": 356, "indent": 1, "parameters": [">持续动作 : 图片[24] : 呼吸效果 : 持续时间[30] : 周期[15] : 呼吸幅度[5]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["let IMG = 'undefined_matter';"]}, {"code": 655, "indent": 1, "parameters": ["let path = \"crafting_scene\";"]}, {"code": 655, "indent": 1, "parameters": ["let X = 830;"]}, {"code": 655, "indent": 1, "parameters": ["let Y = 640;"]}, {"code": 655, "indent": 1, "parameters": ["$gameScreen.showPictureFromPath(24, path, IMG, 0, X, Y, 100, 100, 255, 0)"]}, {"code": 122, "indent": 1, "parameters": [74, 74, 0, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 75, 0, 1, 3]}, {"code": 356, "indent": 1, "parameters": [">地图按钮集 : 按钮[5] : 设置状态 : 激活"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 76, 0, 1, 3]}, {"code": 356, "indent": 2, "parameters": [">地图按钮集 : 按钮[5] : 设置状态 : 激活"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [1, 77, 0, 1, 3]}, {"code": 356, "indent": 3, "parameters": [">地图按钮集 : 按钮[5] : 设置状态 : 激活"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 356, "indent": 3, "parameters": [">地图按钮集 : 按钮[5] : 设置状态 : 封印"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["$gameMap.event(12).start()"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 55, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 4, "pattern": 1, "characterIndex": 1}, "list": [{"code": 230, "indent": 0, "parameters": [60]}, {"code": 231, "indent": 0, "parameters": [8, "mio_tachie_happy", 0, 0, 100, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": [">显现动作 : 图片[8] : 移动显现 : 时间[60] : 方向角度[180] : 移动距离[300]"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE kitchen_event_sis_02"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 355, "indent": 0, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 0, "parameters": ["let textArray = window.MapEventDialogue11[eid][\"0\"];"]}, {"code": 655, "indent": 0, "parameters": ["let text = textArray.join();"]}, {"code": 655, "indent": 0, "parameters": ["if (ConfigManager.language !== 0) {"]}, {"code": 655, "indent": 0, "parameters": ["    text = \"\\\\fn[RiiT<PERSON><PERSON><PERSON><PERSON>]\\\\dDCOG[11:2:2:2]\\\\fs[32]\" + text;"]}, {"code": 655, "indent": 0, "parameters": ["} else {"]}, {"code": 655, "indent": 0, "parameters": ["    text = \"\\\\dDCOG[11:2:2:2]\\\\fs[32]\" + text;"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 655, "indent": 0, "parameters": ["$gameTemp.drill_GFTT_createSimple( [400,180], text, 2, 9, 180 );"]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 231, "indent": 0, "parameters": [8, "mio_tachie_smile2", 0, 0, 100, 150, 100, 100, 255, 0]}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 1, "y": 1}, null, null, null, {"id": 9, "name": "场景初始化", "note": "<resetSelfSwitch>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 0, "characterIndex": 3}, "list": [{"code": 108, "indent": 0, "parameters": ["加载多语言地图对话模板"]}, {"code": 355, "indent": 0, "parameters": ["const mapId = $gameMap.mapId();"]}, {"code": 655, "indent": 0, "parameters": ["const key = 'MapEventDialogue' + mapId; "]}, {"code": 655, "indent": 0, "parameters": ["if (!window[key]) { "]}, {"code": 655, "indent": 0, "parameters": ["chahuiUtil.loadMapEventDialogue();"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameScreen.picture(92) || $gameScreen.picture(93)"]}, {"code": 355, "indent": 1, "parameters": ["for (let pictureId = 90; pictureId <= 93; pictureId++) {"]}, {"code": 655, "indent": 1, "parameters": ["  $gameScreen.erasePicture(pictureId);"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["预加载背景素材"]}, {"code": 355, "indent": 0, "parameters": ["ImageManager.reservePicture(\"kitchen_event/dk_S_E_RL\");"]}, {"code": 655, "indent": 0, "parameters": ["this.setWaitMode('image')"]}, {"code": 108, "indent": 0, "parameters": ["补充监听器"]}, {"code": 355, "indent": 0, "parameters": ["QJ.MPMZ.tl._imoutoUtilCheckInitialization()"]}, {"code": 111, "indent": 0, "parameters": [12, "chahuiUtil.checkScriptExecutability()"]}, {"code": 355, "indent": 1, "parameters": ["let code = $gameStrings.value(20);"]}, {"code": 655, "indent": 1, "parameters": ["eval(code);"]}, {"code": 655, "indent": 1, "parameters": ["$gameStrings.setValue(20, \"\");"]}, {"code": 119, "indent": 1, "parameters": ["end"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 13, 0, 0, 3]}, {"code": 355, "indent": 1, "parameters": ["let ID = $gameVariables.value(13);"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.event(ID).start()"]}, {"code": 122, "indent": 1, "parameters": [13, 13, 0, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameMap.event(1).start()"]}, {"code": 122, "indent": 1, "parameters": [13, 13, 0, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["end"]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 10}, {"id": 10, "name": "清晨早餐事件", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 111, "indent": 0, "parameters": [1, 60, 0, 0, 5]}, {"code": 355, "indent": 1, "parameters": ["var IMG = 'dk_CR_D_RL';"]}, {"code": 655, "indent": 1, "parameters": ["var path = \"kitchen_event\";"]}, {"code": 655, "indent": 1, "parameters": ["$gameScreen.showPictureFromPath(1, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["var IMG = 'dk_S_D';"]}, {"code": 655, "indent": 1, "parameters": ["var path = \"kitchen_event\";"]}, {"code": 655, "indent": 1, "parameters": ["$gameScreen.showPictureFromPath(1, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["和妹妹一起吃早餐"]}, {"code": 111, "indent": 0, "parameters": [0, 124, 0]}, {"code": 111, "indent": 1, "parameters": [1, 60, 0, 0, 5]}, {"code": 355, "indent": 2, "parameters": ["var IMG = 'kitchen_sis_backA0_cloudy';"]}, {"code": 655, "indent": 2, "parameters": ["var path = \"kitchen_event\";"]}, {"code": 655, "indent": 2, "parameters": ["$gameScreen.showPictureFromPath(5, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["var IMG = 'kitchen_sis_backA0_sunshine';"]}, {"code": 655, "indent": 2, "parameters": ["var path = \"kitchen_event\";"]}, {"code": 655, "indent": 2, "parameters": ["$gameScreen.showPictureFromPath(5, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(0);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(1);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "Math.randomInt(101) > 75"]}, {"code": 250, "indent": 1, "parameters": [{"name": "キラン☆キラーン 派手なインパクト3", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 60, true]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(2);"]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(3);"]}, {"code": 250, "indent": 1, "parameters": [{"name": "039myuu_YumeSE_FukidashiHeart01", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 314, "indent": 1, "parameters": [0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "Math.randomInt(101) > 50"]}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(4);"]}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(5);"]}, {"code": 250, "indent": 2, "parameters": [{"name": "039myuu_YumeSE_FukidashiHeart01", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 2, "parameters": ["var actor = $gameActors.actor(1);"]}, {"code": 655, "indent": 2, "parameters": ["var recoverAmount = actor.mhp * 0.15;"]}, {"code": 655, "indent": 2, "parameters": ["actor.gainHp(Math.floor(recoverAmount));"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [12, "Math.randomInt(101) > 25"]}, {"code": 355, "indent": 3, "parameters": ["this.showMapEventDialogue(6);"]}, {"code": 355, "indent": 3, "parameters": ["this.showMapEventDialogue(7);"]}, {"code": 250, "indent": 3, "parameters": [{"name": "039myuu_YumeSE_FukidashiHeart01", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 3, "parameters": ["var actor = $gameActors.actor(1);"]}, {"code": 655, "indent": 3, "parameters": ["var recoverAmount = actor.mhp * 0.05;"]}, {"code": 655, "indent": 3, "parameters": ["actor.gainHp(Math.floor(recoverAmount));"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 224, "indent": 3, "parameters": [[34, 34, 34, 136], 30, true]}, {"code": 250, "indent": 3, "parameters": [{"name": "Explosion2", "volume": 90, "pitch": 110, "pan": 0}]}, {"code": 230, "indent": 3, "parameters": [60]}, {"code": 355, "indent": 3, "parameters": ["this.showMapEventDialogue(8);"]}, {"code": 355, "indent": 3, "parameters": ["this.showMapEventDialogue(9);"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 122, "indent": 0, "parameters": [14, 14, 0, 0, 45]}, {"code": 117, "indent": 0, "parameters": [3]}, {"code": 111, "indent": 0, "parameters": [0, 124, 1]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(10);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 21, 0, 2, 2]}, {"code": 122, "indent": 1, "parameters": [13, 13, 0, 0, 4]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 122, "indent": 1, "parameters": [13, 13, 0, 0, 2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 24, 4, 6, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [1, 60, 0, 0, 5]}, {"code": 355, "indent": 1, "parameters": ["var IMG = 'kitchen_sis_backA0_cloudy';"]}, {"code": 655, "indent": 1, "parameters": ["var path = \"kitchen_event\";"]}, {"code": 655, "indent": 1, "parameters": ["$gameScreen.showPictureFromPath(5, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["var IMG = 'kitchen_sis_backA0_sunshine';"]}, {"code": 655, "indent": 1, "parameters": ["var path = \"kitchen_event\";"]}, {"code": 655, "indent": 1, "parameters": ["$gameScreen.showPictureFromPath(5, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["难得和妹妹一起吃早餐，得加把劲了！"]}, {"code": 111, "indent": 0, "parameters": [12, "Math.randomInt(101) > 75"]}, {"code": 250, "indent": 1, "parameters": [{"name": "キラン☆キラーン 派手なインパクト3", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 60, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["「哦哦——！?在发着光茫——！看起来真好吃！」"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": ["料理大成功!\\w[30]"]}, {"code": 401, "indent": 1, "parameters": ["因为非常地美味、体力完全恢复了！"]}, {"code": 250, "indent": 1, "parameters": [{"name": "039myuu_YumeSE_FukidashiHeart01", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 314, "indent": 1, "parameters": [0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "Math.randomInt(101) > 50"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["「今天状态不错、看起来很美味的样子…」"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 2, "parameters": ["安静地吃完了早晨、体力感觉恢复了一些。"]}, {"code": 250, "indent": 2, "parameters": [{"name": "039myuu_YumeSE_FukidashiHeart01", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 2, "parameters": ["var actor = $gameActors.actor(1);"]}, {"code": 655, "indent": 2, "parameters": ["var recoverAmount = actor.mhp * 0.15;"]}, {"code": 655, "indent": 2, "parameters": ["actor.gainHp(Math.floor(recoverAmount));"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [12, "Math.randomInt(101) > 25"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["糟糕、\\w[30]有点烧焦了……"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["随便吃下了一些味道有些微妙的早餐、"]}, {"code": 401, "indent": 3, "parameters": ["感觉不是很满足啊…"]}, {"code": 250, "indent": 3, "parameters": [{"name": "039myuu_YumeSE_FukidashiHeart01", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 3, "parameters": ["var actor = $gameActors.actor(1);"]}, {"code": 655, "indent": 3, "parameters": ["var recoverAmount = actor.mhp * 0.05;"]}, {"code": 655, "indent": 3, "parameters": ["actor.gainHp(Math.floor(recoverAmount));"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 224, "indent": 3, "parameters": [[34, 34, 34, 136], 30, true]}, {"code": 250, "indent": 3, "parameters": [{"name": "Explosion2", "volume": 90, "pitch": 110, "pan": 0}]}, {"code": 230, "indent": 3, "parameters": [60]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["啊啊————\\w[30]完全烧焦了。"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 3, "parameters": ["早餐完全变成了像焦炭一样、"]}, {"code": 401, "indent": 3, "parameters": ["尝了下什么味道也没有……"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 122, "indent": 0, "parameters": [14, 14, 0, 0, 45]}, {"code": 117, "indent": 0, "parameters": [3]}, {"code": 122, "indent": 0, "parameters": [13, 13, 0, 0, 2]}, {"code": 201, "indent": 0, "parameters": [0, 24, 4, 6, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 10, "y": 10}, null, {"id": 12, "name": "食材选择妹妹吐槽", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 4, "pattern": 1, "characterIndex": 1}, "list": [{"code": 355, "indent": 0, "parameters": ["if (SceneManager._scene instanceof Scene_Map) {"]}, {"code": 655, "indent": 0, "parameters": ["    var windows = SceneManager._scene._drill_GFTT_windowTank;"]}, {"code": 655, "indent": 0, "parameters": ["    if (windows && windows.length > 0) {"]}, {"code": 655, "indent": 0, "parameters": ["        windows.forEach(function(win) {"]}, {"code": 655, "indent": 0, "parameters": ["            SceneManager._scene.drill_GFTT_layerRemoveSprite(win);  // 移除层级中的贴图"]}, {"code": 655, "indent": 0, "parameters": ["        });"]}, {"code": 655, "indent": 0, "parameters": ["        SceneManager._scene._drill_GFTT_windowTank = [];  // 清空数组"]}, {"code": 655, "indent": 0, "parameters": ["    }"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 111, "indent": 0, "parameters": [1, 74, 0, 0, 0]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [12, "/<Loot Pool: (.*?)>/.test($dataItems[$gameVariables.value(74)].note)"]}, {"code": 111, "indent": 1, "parameters": [12, "$dataItems[$gameVariables.value(74)].note.match(/<Loot Pool: (.*?)>/)[1] === \"Ahoge\""]}, {"code": 356, "indent": 2, "parameters": ["SV_PLAY_VOICE kitchen_event_sis_22"]}, {"code": 355, "indent": 2, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 2, "parameters": ["let textArray = window.MapEventDialogue11[eid][\"0\"];"]}, {"code": 655, "indent": 2, "parameters": ["let text = textArray.join();"]}, {"code": 655, "indent": 2, "parameters": ["if (ConfigManager.language !== 0) {"]}, {"code": 655, "indent": 2, "parameters": ["    text = \"\\\\fn[RiiT<PERSON><PERSON><PERSON><PERSON>]\\\\dDCOG[11:2:2:2]\\\\fs[32]\" + text;"]}, {"code": 655, "indent": 2, "parameters": ["} else {"]}, {"code": 655, "indent": 2, "parameters": ["    text = \"\\\\dDCOG[11:2:2:2]\\\\fs[32]\" + text;"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 655, "indent": 2, "parameters": ["$gameTemp.drill_GFTT_createSimple( [400,180], text, 2, 9, 180 );"]}, {"code": 231, "indent": 2, "parameters": [8, "mio_tachie_OAO", 0, 0, 100, 150, 100, 100, 255, 0]}, {"code": 115, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$dataItems[$gameVariables.value(74)].note.match(/<Loot Pool: (.*?)>/)[1] === \"Rice\""]}, {"code": 356, "indent": 2, "parameters": ["SV_PLAY_VOICE kitchen_event_sis_05"]}, {"code": 355, "indent": 2, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 2, "parameters": ["let textArray = window.MapEventDialogue11[eid][\"1\"];"]}, {"code": 655, "indent": 2, "parameters": ["let text = textArray.join();"]}, {"code": 655, "indent": 2, "parameters": ["if (ConfigManager.language !== 0) {"]}, {"code": 655, "indent": 2, "parameters": ["    text = \"\\\\fn[RiiT<PERSON><PERSON><PERSON><PERSON>]\\\\dDCOG[11:2:2:2]\\\\fs[32]\" + text;"]}, {"code": 655, "indent": 2, "parameters": ["} else {"]}, {"code": 655, "indent": 2, "parameters": ["    text = \"\\\\dDCOG[11:2:2:2]\\\\fs[32]\" + text;"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 655, "indent": 2, "parameters": ["$gameTemp.drill_GFTT_createSimple( [400,180], text, 2, 9, 180 );"]}, {"code": 231, "indent": 2, "parameters": [8, "mio_tachie_IAI", 0, 0, 100, 150, 100, 100, 255, 0]}, {"code": 115, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$dataItems[$gameVariables.value(74)].note.match(/<Loot Pool: (.*?)>/)[1] === \"Flavoring\""]}, {"code": 356, "indent": 2, "parameters": ["SV_PLAY_VOICE kitchen_event_sis_06"]}, {"code": 355, "indent": 2, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 2, "parameters": ["let textArray = window.MapEventDialogue11[eid][\"2\"];"]}, {"code": 655, "indent": 2, "parameters": ["let text = textArray.join();"]}, {"code": 655, "indent": 2, "parameters": ["if (ConfigManager.language !== 0) {"]}, {"code": 655, "indent": 2, "parameters": ["    text = \"\\\\fn[RiiT<PERSON><PERSON><PERSON><PERSON>]\\\\dDCOG[11:2:2:2]\\\\fs[32]\" + text;"]}, {"code": 655, "indent": 2, "parameters": ["} else {"]}, {"code": 655, "indent": 2, "parameters": ["    text = \"\\\\dDCOG[11:2:2:2]\\\\fs[32]\" + text;"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 655, "indent": 2, "parameters": ["$gameTemp.drill_GFTT_createSimple( [400,180], text, 2, 9, 180 );"]}, {"code": 231, "indent": 2, "parameters": [8, "mio_tachie_smile2", 0, 0, 100, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": [">持续动作 : 图片[8] : 左右震动 : 持续时间[60] : 周期[30] : 震动幅度[3]"]}, {"code": 115, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$dataItems[$gameVariables.value(74)].note.match(/<Loot Pool: (.*?)>/)[1] === \"Fish\""]}, {"code": 356, "indent": 2, "parameters": ["SV_PLAY_VOICE kitchen_event_sis_04"]}, {"code": 355, "indent": 2, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 2, "parameters": ["let textArray = window.MapEventDialogue11[eid][\"3\"];"]}, {"code": 655, "indent": 2, "parameters": ["let text = textArray.join();"]}, {"code": 655, "indent": 2, "parameters": ["if (ConfigManager.language !== 0) {"]}, {"code": 655, "indent": 2, "parameters": ["    text = \"\\\\fn[RiiT<PERSON><PERSON><PERSON><PERSON>]\\\\dDCOG[11:2:2:2]\\\\fs[32]\" + text;"]}, {"code": 655, "indent": 2, "parameters": ["} else {"]}, {"code": 655, "indent": 2, "parameters": ["    text = \"\\\\dDCOG[11:2:2:2]\\\\fs[32]\" + text;"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 655, "indent": 2, "parameters": ["$gameTemp.drill_GFTT_createSimple( [400,180], text, 2, 9, 180 );"]}, {"code": 231, "indent": 2, "parameters": [8, "mio_tachie_happy", 0, 0, 100, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": [">持续动作 : 图片[8] : 上下震动 : 持续时间[60] : 周期[30] : 震动幅度[3]"]}, {"code": 115, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$dataItems[$gameVariables.value(74)].note.match(/<Loot Pool: (.*?)>/)[1] === \"Junk1\""]}, {"code": 356, "indent": 2, "parameters": ["SV_PLAY_VOICE kitchen_event_sis_11"]}, {"code": 355, "indent": 2, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 2, "parameters": ["let textArray = window.MapEventDialogue11[eid][\"4\"];"]}, {"code": 655, "indent": 2, "parameters": ["let text = textArray.join();"]}, {"code": 655, "indent": 2, "parameters": ["if (ConfigManager.language !== 0) {"]}, {"code": 655, "indent": 2, "parameters": ["    text = \"\\\\fn[RiiT<PERSON><PERSON><PERSON><PERSON>]\\\\dDCOG[11:2:2:2]\\\\fs[32]\" + text;"]}, {"code": 655, "indent": 2, "parameters": ["} else {"]}, {"code": 655, "indent": 2, "parameters": ["    text = \"\\\\dDCOG[11:2:2:2]\\\\fs[32]\" + text;"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 655, "indent": 2, "parameters": ["$gameTemp.drill_GFTT_createSimple( [400,180], text, 2, 9, 180 );"]}, {"code": 231, "indent": 2, "parameters": [8, "mio_tachie_kimo", 0, 0, 100, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": [">持续动作 : 图片[8] : 左右震动 : 持续时间[60] : 周期[30] : 震动幅度[3]"]}, {"code": 115, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$dataItems[$gameVariables.value(74)].note.match(/<Loot Pool: (.*?)>/)[1] === \"Hot\""]}, {"code": 356, "indent": 2, "parameters": ["SV_PLAY_VOICE kitchen_event_sis_07"]}, {"code": 355, "indent": 2, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 2, "parameters": ["let textArray = window.MapEventDialogue11[eid][\"5\"];"]}, {"code": 655, "indent": 2, "parameters": ["let text = textArray.join();"]}, {"code": 655, "indent": 2, "parameters": ["if (ConfigManager.language !== 0) {"]}, {"code": 655, "indent": 2, "parameters": ["    text = \"\\\\fn[RiiT<PERSON><PERSON><PERSON><PERSON>]\\\\dDCOG[11:2:2:2]\\\\fs[32]\" + text;"]}, {"code": 655, "indent": 2, "parameters": ["} else {"]}, {"code": 655, "indent": 2, "parameters": ["    text = \"\\\\dDCOG[11:2:2:2]\\\\fs[32]\" + text;"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 655, "indent": 2, "parameters": ["$gameTemp.drill_GFTT_createSimple( [400,180], text, 2, 9, 180 );"]}, {"code": 231, "indent": 2, "parameters": [8, "mio_tachie_ida", 0, 0, 100, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": [">持续动作 : 图片[8] : 上下震动 : 持续时间[60] : 周期[30] : 震动幅度[3]"]}, {"code": 115, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$dataItems[$gameVariables.value(74)].note.match(/<Loot Pool: (.*?)>/)[1] === \"Vegetable\""]}, {"code": 356, "indent": 2, "parameters": ["SV_PLAY_VOICE kitchen_event_sis_08"]}, {"code": 355, "indent": 2, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 2, "parameters": ["let textArray = window.MapEventDialogue11[eid][\"6\"];"]}, {"code": 655, "indent": 2, "parameters": ["let text = textArray.join();"]}, {"code": 655, "indent": 2, "parameters": ["if (ConfigManager.language !== 0) {"]}, {"code": 655, "indent": 2, "parameters": ["    text = \"\\\\fn[RiiT<PERSON><PERSON><PERSON><PERSON>]\\\\dDCOG[11:2:2:2]\\\\fs[32]\" + text;"]}, {"code": 655, "indent": 2, "parameters": ["} else {"]}, {"code": 655, "indent": 2, "parameters": ["    text = \"\\\\dDCOG[11:2:2:2]\\\\fs[32]\" + text;"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 655, "indent": 2, "parameters": ["$gameTemp.drill_GFTT_createSimple( [400,180], text, 2, 9, 180 );"]}, {"code": 231, "indent": 2, "parameters": [8, "mio_tachie_naku", 0, 0, 100, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": [">持续动作 : 图片[8] : 左右震动 : 持续时间[60] : 周期[30] : 震动幅度[3]"]}, {"code": 115, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$dataItems[$gameVariables.value(74)].note.match(/<Loot Pool: (.*?)>/)[1] === \"Meat\""]}, {"code": 356, "indent": 2, "parameters": ["SV_PLAY_VOICE kitchen_event_sis_03"]}, {"code": 355, "indent": 2, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 2, "parameters": ["let textArray = window.MapEventDialogue11[eid][\"7\"];"]}, {"code": 655, "indent": 2, "parameters": ["let text = textArray.join();"]}, {"code": 655, "indent": 2, "parameters": ["if (ConfigManager.language !== 0) {"]}, {"code": 655, "indent": 2, "parameters": ["    text = \"\\\\fn[RiiT<PERSON><PERSON><PERSON><PERSON>]\\\\dDCOG[11:2:2:2]\\\\fs[32]\" + text;"]}, {"code": 655, "indent": 2, "parameters": ["} else {"]}, {"code": 655, "indent": 2, "parameters": ["    text = \"\\\\dDCOG[11:2:2:2]\\\\fs[32]\" + text;"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 655, "indent": 2, "parameters": ["$gameTemp.drill_GFTT_createSimple( [400,180], text, 2, 9, 180 );"]}, {"code": 231, "indent": 2, "parameters": [8, "mio_tachie_happy", 0, 0, 100, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": [">持续动作 : 图片[8] : 上下震动 : 持续时间[60] : 周期[30] : 震动幅度[3]"]}, {"code": 115, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$dataItems[$gameVariables.value(74)].note.match(/<Loot Pool: (.*?)>/)[1] === \"Junk2\""]}, {"code": 356, "indent": 2, "parameters": ["SV_PLAY_VOICE kitchen_event_sis_10"]}, {"code": 355, "indent": 2, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 2, "parameters": ["let textArray = window.MapEventDialogue11[eid][\"8\"];"]}, {"code": 655, "indent": 2, "parameters": ["let text = textArray.join();"]}, {"code": 655, "indent": 2, "parameters": ["if (ConfigManager.language !== 0) {"]}, {"code": 655, "indent": 2, "parameters": ["    text = \"\\\\fn[RiiT<PERSON><PERSON><PERSON><PERSON>]\\\\dDCOG[11:2:2:2]\\\\fs[32]\" + text;"]}, {"code": 655, "indent": 2, "parameters": ["} else {"]}, {"code": 655, "indent": 2, "parameters": ["    text = \"\\\\dDCOG[11:2:2:2]\\\\fs[32]\" + text;"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 655, "indent": 2, "parameters": ["$gameTemp.drill_GFTT_createSimple( [400,180], text, 2, 9, 180 );"]}, {"code": 231, "indent": 2, "parameters": [8, "mio_tachie_OAO", 0, 0, 100, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": [">持续动作 : 图片[8] : 左右震动 : 持续时间[60] : 周期[30] : 震动幅度[3]"]}, {"code": 115, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$dataItems[$gameVariables.value(74)].note.match(/<Loot Pool: (.*?)>/)[1] === \"Fruit\""]}, {"code": 356, "indent": 2, "parameters": ["SV_PLAY_VOICE kitchen_event_sis_09"]}, {"code": 355, "indent": 2, "parameters": ["let eid = String(this._eventId);"]}, {"code": 655, "indent": 2, "parameters": ["let textArray = window.MapEventDialogue11[eid][\"9\"];"]}, {"code": 655, "indent": 2, "parameters": ["let text = textArray.join();"]}, {"code": 655, "indent": 2, "parameters": ["if (ConfigManager.language !== 0) {"]}, {"code": 655, "indent": 2, "parameters": ["    text = \"\\\\fn[RiiT<PERSON><PERSON><PERSON><PERSON>]\\\\dDCOG[11:2:2:2]\\\\fs[32]\" + text;"]}, {"code": 655, "indent": 2, "parameters": ["} else {"]}, {"code": 655, "indent": 2, "parameters": ["    text = \"\\\\dDCOG[11:2:2:2]\\\\fs[32]\" + text;"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 655, "indent": 2, "parameters": ["$gameTemp.drill_GFTT_createSimple( [400,180], text, 2, 9, 180 );"]}, {"code": 231, "indent": 2, "parameters": [8, "mio_tachie_=ω=", 0, 0, 100, 150, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": [">持续动作 : 图片[8] : 上下震动 : 持续时间[60] : 周期[30] : 震动幅度[3]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["行走菇"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameNumberArray.value(16).includes(98) && $gameVariables.value(78) > 3"]}, {"code": 355, "indent": 1, "parameters": ["if (!$gameNumberArray.value(36).includes(1) && !$gameNumberArray.value(37).includes(1)) {"]}, {"code": 655, "indent": 1, "parameters": ["    $gameNumberArray.value(36).push(1);"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["兽人的肉"]}, {"code": 111, "indent": 0, "parameters": [12, "$dataItems[$gameVariables.value(78)].note.includes(\"<肉料理>\")"]}, {"code": 355, "indent": 1, "parameters": ["if (!$gameNumberArray.value(36).includes(3) && !$gameNumberArray.value(37).includes(3)) {"]}, {"code": 655, "indent": 1, "parameters": ["    $gameNumberArray.value(36).push(3);"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["史莱姆"]}, {"code": 111, "indent": 0, "parameters": [12, "$dataItems[$gameVariables.value(78)].note.includes(\"<史莱姆料理>\")"]}, {"code": 355, "indent": 1, "parameters": ["if (!$gameNumberArray.value(36).includes(8) && !$gameNumberArray.value(37).includes(8)) {"]}, {"code": 655, "indent": 1, "parameters": ["    $gameNumberArray.value(36).push(8);"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 2, "y": 0}, {"id": 13, "name": "料理完成后", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 4, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["防范贴图残留"]}, {"code": 355, "indent": 0, "parameters": ["for (let pictureId = 90; pictureId <= 92; pictureId++) {"]}, {"code": 655, "indent": 0, "parameters": ["  $gameScreen.erasePicture(pictureId);"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 356, "indent": 0, "parameters": [">地图按钮集 : 按钮[5] : 隐藏"]}, {"code": 108, "indent": 0, "parameters": ["清除点击判定"]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen._pictureCidArray = [];"]}, {"code": 121, "indent": 0, "parameters": [37, 37, 1]}, {"code": 111, "indent": 0, "parameters": [12, "$gameVariables.value(78) <= 0 || $dataItems[$gameVariables.value(78)].note.includes(\"<黑暗料理>\")"]}, {"code": 313, "indent": 1, "parameters": [0, 1, 0, 21]}, {"code": 355, "indent": 1, "parameters": ["var itemIndex = $gameNumberArray.value(16);"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.applyItemEffectsFromDish(itemIndex)"]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(0);"]}, {"code": 119, "indent": 1, "parameters": ["end"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["实绩结算"]}, {"code": 355, "indent": 1, "parameters": ["let id = 12;"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.event(id).steupCEQJ(2)"]}, {"code": 111, "indent": 1, "parameters": [12, "$dataItems[$gameVariables.value(78)].note.includes(\"<完美料理>\")"]}, {"code": 313, "indent": 2, "parameters": [0, 1, 0, 22]}, {"code": 355, "indent": 2, "parameters": ["var itemIndex = $gameNumberArray.value(16);"]}, {"code": 655, "indent": 2, "parameters": ["$gameMap.applyItemEffectsFromDish(itemIndex)"]}, {"code": 111, "indent": 2, "parameters": [12, "$dataItems[$gameVariables.value(78)].note.includes(\"<肉料理>\")"]}, {"code": 313, "indent": 3, "parameters": [0, 2, 0, 41]}, {"code": 122, "indent": 3, "parameters": [17, 17, 1, 0, 15]}, {"code": 111, "indent": 3, "parameters": [12, "$gameActors.actor(1).isStateAffected(46)"]}, {"code": 313, "indent": 4, "parameters": [0, 1, 1, 46]}, {"code": 128, "indent": 4, "parameters": [129, 0, 0, 1, false]}, {"code": 122, "indent": 4, "parameters": [17, 17, 1, 0, 25]}, {"code": 111, "indent": 4, "parameters": [1, 20, 0, 85, 2]}, {"code": 111, "indent": 5, "parameters": [1, 20, 0, 40, 2]}, {"code": 122, "indent": 6, "parameters": [20, 20, 0, 0, 40]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 412, "indent": 5, "parameters": []}, {"code": 122, "indent": 5, "parameters": [20, 20, 1, 0, 25]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 122, "indent": 4, "parameters": [17, 17, 1, 0, 15]}, {"code": 111, "indent": 4, "parameters": [1, 20, 0, 85, 2]}, {"code": 111, "indent": 5, "parameters": [1, 20, 0, 40, 2]}, {"code": 122, "indent": 6, "parameters": [20, 20, 0, 0, 40]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 412, "indent": 5, "parameters": []}, {"code": 122, "indent": 5, "parameters": [20, 20, 1, 0, 15]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 355, "indent": 3, "parameters": ["this.showMapEventDialogue(1);"]}, {"code": 108, "indent": 3, "parameters": ["刷新标题画面动画"]}, {"code": 355, "indent": 3, "parameters": ["chahuiUtil.RefreshTitleScreenAnimation(\"hamburgerSteak\")"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 122, "indent": 3, "parameters": [17, 17, 1, 2, 5, 10]}, {"code": 355, "indent": 3, "parameters": ["this.showMapEventDialogue(2);"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 119, "indent": 2, "parameters": ["end"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(2);"]}, {"code": 122, "indent": 2, "parameters": [14, 14, 0, 0, 45]}, {"code": 117, "indent": 2, "parameters": [3]}, {"code": 119, "indent": 2, "parameters": ["end"]}, {"code": 115, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["end"]}, {"code": 122, "indent": 0, "parameters": [14, 14, 0, 0, 45]}, {"code": 117, "indent": 0, "parameters": [3]}, {"code": 355, "indent": 0, "parameters": ["let id = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.event(id).steupCEQJ(2)"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 4, "pattern": 1, "characterIndex": 1}, "list": [{"code": 121, "indent": 0, "parameters": [55, 55, 1]}, {"code": 122, "indent": 0, "parameters": [13, 13, 0, 0, 1]}, {"code": 313, "indent": 0, "parameters": [0, 1, 1, 41]}, {"code": 111, "indent": 0, "parameters": [4, 2, 6, 40]}, {"code": 111, "indent": 1, "parameters": [12, "$dataItems[$gameVariables.value(78)].note.includes(\"<完美料理>\")"]}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(3);"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["this.showMapEventDialogue(4);"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 4, 7, 5, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 0}, {"id": 14, "name": "妹妹星星眼", "note": "<重置独立开关>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE kitchen_event_sis_15"]}, {"code": 355, "indent": 0, "parameters": ["let IMG = \"kitchen_sis_wakuwaku_kao_1A\";"]}, {"code": 655, "indent": 0, "parameters": ["let path = \"kitchen_event\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(12, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 230, "indent": 0, "parameters": [8]}, {"code": 355, "indent": 0, "parameters": ["let IMG = \"kitchen_sis_wakuwaku_kao_2\";"]}, {"code": 655, "indent": 0, "parameters": ["let path = \"kitchen_event\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(12, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 230, "indent": 0, "parameters": [8]}, {"code": 355, "indent": 0, "parameters": ["let IMG = \"kitchen_sis_wakuwaku_kao_3\";"]}, {"code": 655, "indent": 0, "parameters": ["let path = \"kitchen_event\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(12, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 230, "indent": 0, "parameters": [8]}, {"code": 108, "indent": 0, "parameters": ["文字演出"]}, {"code": 355, "indent": 0, "parameters": ["let id = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.event(id).steupCEQJ(2)"]}, {"code": 108, "indent": 0, "parameters": ["动画开始"]}, {"code": 355, "indent": 0, "parameters": ["$gameSelfVariables.set(this, 'frames', 7);"]}, {"code": 355, "indent": 0, "parameters": ["let IMG = \"kitchen_sis_wakuwaku_kao7\";"]}, {"code": 655, "indent": 0, "parameters": ["let path = \"kitchen_event\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(12, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 355, "indent": 0, "parameters": ["let IMG = \"kitchen_sis_wakuwaku_star1\";"]}, {"code": 655, "indent": 0, "parameters": ["let path = \"kitchen_event\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(14, path, IMG, 0, 1200, 150, 100, 100, 255, 0)"]}, {"code": 355, "indent": 0, "parameters": ["let IMG = \"kitchen_sis_wakuwaku_star2\";"]}, {"code": 655, "indent": 0, "parameters": ["let path = \"kitchen_event\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(15, path, IMG, 0, 400, 200, 100, 100, 255, 0)"]}, {"code": 245, "indent": 0, "parameters": [{"name": "オノマトペ キラキラ11-2(長)", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": [">持续动作 : 批量图片[14,15] : 渐变闪烁 : 持续时间[无限] : 周期[45]"]}, {"code": 118, "indent": 0, "parameters": ["增长"]}, {"code": 355, "indent": 0, "parameters": ["let id = $gameSelfVariables.get(this, 'frames');"]}, {"code": 655, "indent": 0, "parameters": ["let IMG = \"kitchen_sis_wakuwaku_kao\" + id;"]}, {"code": 655, "indent": 0, "parameters": ["let path = \"kitchen_event\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(12, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 355, "indent": 0, "parameters": ["$gameSelfVariables.add(this, 'frames', +1)"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameSelfVariables.get(this, 'frames') >= 7"]}, {"code": 230, "indent": 1, "parameters": [4]}, {"code": 355, "indent": 1, "parameters": ["$gameSelfVariables.set(this, 'frames', $gameSelfVariables.get(this, 'frames') - 1)"]}, {"code": 119, "indent": 1, "parameters": ["减少"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [2]}, {"code": 119, "indent": 0, "parameters": ["增长"]}, {"code": 118, "indent": 0, "parameters": ["减少"]}, {"code": 355, "indent": 0, "parameters": ["let id = $gameSelfVariables.get(this, 'frames');"]}, {"code": 655, "indent": 0, "parameters": ["let IMG = \"kitchen_sis_wakuwaku_kao\" + id;"]}, {"code": 655, "indent": 0, "parameters": ["let path = \"kitchen_event\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(12, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 355, "indent": 0, "parameters": ["$gameSelfVariables.add(this, 'frames', -1)"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameSelfVariables.get(this, 'frames') <= 0"]}, {"code": 230, "indent": 1, "parameters": [4]}, {"code": 355, "indent": 1, "parameters": ["$gameSelfVariables.set(this, 'frames', $gameSelfVariables.get(this, 'frames') + 1)"]}, {"code": 119, "indent": 1, "parameters": ["增长"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [2]}, {"code": 119, "indent": 0, "parameters": ["减少"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 4, "pattern": 1, "characterIndex": 1}, "list": [{"code": 230, "indent": 0, "parameters": [60]}, {"code": 355, "indent": 0, "parameters": ["let IMG = \"umasou\";"]}, {"code": 655, "indent": 0, "parameters": ["let path = \"kitchen_event\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(18, path, IMG, 0, 1100, 380, 100, 100, 0, 0)"]}, {"code": 356, "indent": 0, "parameters": [">图片快捷操作 : 图片[18] : 修改单属性 : 透明度[255] : 时间[40]"]}, {"code": 356, "indent": 0, "parameters": [">图片快捷操作 : 图片[18] : 增减速移动到 : 位置[1400,80] : 时间[40]"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE kitchen_event_sis_16 90 100 0 2"]}, {"code": 230, "indent": 0, "parameters": [150]}, {"code": 355, "indent": 0, "parameters": ["$gameMap.event(13).start()"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 9, "y": 3}, {"id": 15, "name": "妹妹反应判定", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 2, "characterIndex": 1}, "list": [{"code": 231, "indent": 0, "parameters": [1, "dk_CW_N_RL", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["let IMG = \"kitchen_sis_wakuwaku_back\";"]}, {"code": 655, "indent": 0, "parameters": ["let path = \"kitchen_event\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(6, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 355, "indent": 0, "parameters": ["let IMG = \"kitchen_sis_wakuwaku_shoulder0\";"]}, {"code": 655, "indent": 0, "parameters": ["let path = \"kitchen_event\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(7, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 355, "indent": 0, "parameters": ["let IMG = \"kitchen_sis_wakuwaku_body\";"]}, {"code": 655, "indent": 0, "parameters": ["let path = \"kitchen_event\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(8, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 355, "indent": 0, "parameters": ["let IMG = \"kitchen_sis_wakuwaku_head\";"]}, {"code": 655, "indent": 0, "parameters": ["let path = \"kitchen_event\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(9, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 355, "indent": 0, "parameters": ["let IMG = \"kitchen_sis_wakuwaku_kao_0A\";"]}, {"code": 655, "indent": 0, "parameters": ["let path = \"kitchen_event\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(12, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 355, "indent": 0, "parameters": ["let IMG = \"kitchen_sis_wakuwaku_hand0\";"]}, {"code": 655, "indent": 0, "parameters": ["let path = \"kitchen_event\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(13, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 108, "indent": 0, "parameters": ["追加图钉"]}, {"code": 355, "indent": 0, "parameters": ["[6,7,8,9,13].forEach(n => {"]}, {"code": 655, "indent": 0, "parameters": ["        $gameScreen.picture(n).drill_PTh_bindPic(12);"]}, {"code": 655, "indent": 0, "parameters": ["});"]}, {"code": 355, "indent": 0, "parameters": ["var specificKeys = [6,7,8,9,12,13]; "]}, {"code": 655, "indent": 0, "parameters": ["for (var i = 0; i < specificKeys.length; i++) { "]}, {"code": 655, "indent": 0, "parameters": ["    var k = specificKeys[i]; "]}, {"code": 655, "indent": 0, "parameters": ["    if ($gameScreen.picture(k)) { "]}, {"code": 655, "indent": 0, "parameters": ["        $gameScreen.picture(k).drill_PCE_stopEffect(); "]}, {"code": 655, "indent": 0, "parameters": ["        $gameScreen.picture(k).drill_PFIE_playShowingMoveAppear( 40,90,800 );"]}, {"code": 655, "indent": 0, "parameters": ["    }"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 111, "indent": 0, "parameters": [12, "$gameVariables.value(78) <= 0 || $dataItems[$gameVariables.value(78)].note.includes(\"<黑暗料理>\")"]}, {"code": 108, "indent": 1, "parameters": ["做出黑暗料理"]}, {"code": 355, "indent": 1, "parameters": ["let id = this._eventId;"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.event(id).steupCEQJ(2)"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [12, "$dataItems[$gameVariables.value(78)].note.includes(\"<完美料理>\")"]}, {"code": 355, "indent": 2, "parameters": ["chahuiUtil.changeSelfSwitchForEvent(14, \"A\");"]}, {"code": 355, "indent": 2, "parameters": ["chahuiUtil.changeSelfSwitchForEvent(16, \"A\");"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["chahuiUtil.changeSelfSwitchForEvent(14, \"A\");"]}, {"code": 355, "indent": 2, "parameters": ["chahuiUtil.changeSelfSwitchForEvent(16, \"A\");"]}, {"code": 230, "indent": 2, "parameters": [180]}, {"code": 355, "indent": 2, "parameters": ["$gameMap.event(13).start()"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 4, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["黑暗料理演出"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE kitchen_event_sis_13"]}, {"code": 355, "indent": 0, "parameters": ["let IMG = \"kitchen_sis_wakuwaku_kao_1\";"]}, {"code": 655, "indent": 0, "parameters": ["let path = \"kitchen_event\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(12, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 230, "indent": 0, "parameters": [8]}, {"code": 355, "indent": 0, "parameters": ["let IMG = \"kitchen_sis_wakuwaku_kao_2\";"]}, {"code": 655, "indent": 0, "parameters": ["let path = \"kitchen_event\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(12, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 230, "indent": 0, "parameters": [8]}, {"code": 355, "indent": 0, "parameters": ["let IMG = \"kitchen_sis_wakuwaku_obieru\";"]}, {"code": 655, "indent": 0, "parameters": ["let path = \"kitchen_event\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(14, path, IMG, 0, 0, 0, 100, 100, 0, 0)"]}, {"code": 356, "indent": 0, "parameters": [">图片快捷操作 : 图片[14] : 修改单属性 : 透明度[255] : 时间[60]"]}, {"code": 356, "indent": 0, "parameters": [">消失动作 : 图片[13] : 移动消失 : 时间[120] : 方向角度[90] : 移动距离[600]"]}, {"code": 121, "indent": 0, "parameters": [6, 6, 0]}, {"code": 230, "indent": 0, "parameters": [45]}, {"code": 355, "indent": 0, "parameters": ["let IMG = \"nanisoregomi\";"]}, {"code": 655, "indent": 0, "parameters": ["let path = \"kitchen_event\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(18, path, IMG, 0, 1400, 80, 100, 100, 0, 0)"]}, {"code": 356, "indent": 0, "parameters": [">图片快捷操作 : 图片[18] : 修改单属性 : 透明度[255] : 时间[120]"]}, {"code": 230, "indent": 0, "parameters": [45]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE kitchen_event_sis_14 90 100 0 2"]}, {"code": 355, "indent": 0, "parameters": ["$gameNumberArray.setValue(41,[6,7,8,9,12,14,18]);"]}, {"code": 122, "indent": 0, "parameters": [204, 204, 0, 4, "\"害怕\""]}, {"code": 117, "indent": 0, "parameters": [35]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 355, "indent": 0, "parameters": ["$gameMap.event(13).start()"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 8, "y": 3}, {"id": 16, "name": "妹妹肩膀、手部动画", "note": "<重置独立开关>", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 230, "indent": 0, "parameters": [30]}, {"code": 355, "indent": 0, "parameters": ["$gameSelfVariables.set(this, 'frames', 0);"]}, {"code": 108, "indent": 0, "parameters": ["动画开始"]}, {"code": 118, "indent": 0, "parameters": ["增长"]}, {"code": 355, "indent": 0, "parameters": ["let id = $gameSelfVariables.get(this, 'frames');"]}, {"code": 655, "indent": 0, "parameters": ["let IMG = \"kitchen_sis_wakuwaku_shoulder\" + id;"]}, {"code": 655, "indent": 0, "parameters": ["let path = \"kitchen_event\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(7, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 355, "indent": 0, "parameters": ["let id = $gameSelfVariables.get(this, 'frames');"]}, {"code": 655, "indent": 0, "parameters": ["let IMG = \"kitchen_sis_wakuwaku_hand\" + id;"]}, {"code": 655, "indent": 0, "parameters": ["let path = \"kitchen_event\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(13, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 355, "indent": 0, "parameters": ["$gameSelfVariables.add(this, 'frames', +1)"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameSelfVariables.get(this, 'frames') >= 7"]}, {"code": 230, "indent": 1, "parameters": [8]}, {"code": 355, "indent": 1, "parameters": ["$gameSelfVariables.set(this, 'frames', $gameSelfVariables.get(this, 'frames') - 1)"]}, {"code": 119, "indent": 1, "parameters": ["减少"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [3]}, {"code": 119, "indent": 0, "parameters": ["增长"]}, {"code": 118, "indent": 0, "parameters": ["减少"]}, {"code": 355, "indent": 0, "parameters": ["let id = $gameSelfVariables.get(this, 'frames');"]}, {"code": 655, "indent": 0, "parameters": ["let IMG = \"kitchen_sis_wakuwaku_shoulder\" + id;"]}, {"code": 655, "indent": 0, "parameters": ["let path = \"kitchen_event\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(7, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 355, "indent": 0, "parameters": ["let id = $gameSelfVariables.get(this, 'frames');"]}, {"code": 655, "indent": 0, "parameters": ["let IMG = \"kitchen_sis_wakuwaku_hand\" + id;"]}, {"code": 655, "indent": 0, "parameters": ["let path = \"kitchen_event\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(13, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 355, "indent": 0, "parameters": ["$gameSelfVariables.add(this, 'frames', -1)"]}, {"code": 111, "indent": 0, "parameters": [12, "$gameSelfVariables.get(this, 'frames') <= -1"]}, {"code": 230, "indent": 1, "parameters": [8]}, {"code": 355, "indent": 1, "parameters": ["$gameSelfVariables.set(this, 'frames', $gameSelfVariables.get(this, 'frames') + 1)"]}, {"code": 119, "indent": 1, "parameters": ["增长"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [3]}, {"code": 119, "indent": 0, "parameters": ["减少"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 9, "y": 4}, {"id": 17, "name": "浑身恶臭事件后续", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 2, "characterIndex": 1}, "list": [{"code": 356, "indent": 0, "parameters": ["PB_BGS_全停止"]}, {"code": 355, "indent": 0, "parameters": ["var IMG = 'dk_CW_N_RL';"]}, {"code": 655, "indent": 0, "parameters": ["var path = \"kitchen_event\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(1, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 313, "indent": 0, "parameters": [0, 2, 0, 24]}, {"code": 117, "indent": 0, "parameters": [26]}, {"code": 231, "indent": 0, "parameters": [16, "mio_tachie_kao_eee", 0, 0, 200, 1600, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["for (var i = 10; i <= 20; i++) {"]}, {"code": 655, "indent": 0, "parameters": ["  if($gameScreen.picture( i )) {"]}, {"code": 655, "indent": 0, "parameters": ["  $gameScreen.picture(i)._x = 200;"]}, {"code": 655, "indent": 0, "parameters": ["  $gameScreen.picture(i)._y = 1600;"]}, {"code": 655, "indent": 0, "parameters": ["  $gameScreen.picture(i)._opacity = 255;"]}, {"code": 655, "indent": 0, "parameters": ["  }"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.picture(20)._x = 500;"]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 355, "indent": 0, "parameters": ["var pic_ids = $gameNumberArray.value(41);"]}, {"code": 655, "indent": 0, "parameters": ["pic_ids.forEach(function(pic_id) {"]}, {"code": 655, "indent": 0, "parameters": ["    var picture = $gameScreen.picture(pic_id);"]}, {"code": 655, "indent": 0, "parameters": ["var data = {\t\t\t\t\t\t\t\t\"type\":\"smoothMove\","]}, {"code": 655, "indent": 0, "parameters": ["\"valueX\":0,"]}, {"code": 655, "indent": 0, "parameters": ["\"valueY\":-1450,"]}, {"code": 655, "indent": 0, "parameters": ["\"time\":60,"]}, {"code": 655, "indent": 0, "parameters": ["\"cur_time\":0,\"cur_speedX\":0,\"cur_speedY\":0,}"]}, {"code": 655, "indent": 0, "parameters": ["    if (picture) { picture._drill_PSh_commandChangeTank.push(data);}});"]}, {"code": 230, "indent": 0, "parameters": [40]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE kitchen_event_sis_21"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(0);"]}, {"code": 355, "indent": 0, "parameters": ["for (var i = 10; i <= 20; i++) {"]}, {"code": 655, "indent": 0, "parameters": ["  if($gameScreen.picture( i )) {"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.erasePicture(i);"]}, {"code": 655, "indent": 0, "parameters": ["  }"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 230, "indent": 0, "parameters": [4]}, {"code": 355, "indent": 0, "parameters": ["$gameMap.event(1).start()"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 5, "y": 10}, {"id": 18, "name": "妹妹料理固定演出", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 4, "pattern": 1, "characterIndex": 1}, "list": [{"code": 111, "indent": 0, "parameters": [12, "Utils.isOptionValid(\"test\")"]}, {"code": 108, "indent": 1, "parameters": ["妹妹着装初始化"]}, {"code": 355, "indent": 1, "parameters": ["var array = [154, 155, 156];"]}, {"code": 655, "indent": 1, "parameters": ["var newPanties = array[Math.floor(Math.random() * array.length)];"]}, {"code": 655, "indent": 1, "parameters": ["$gameActors.actor(2).changeEquipById(2, newPanties);"]}, {"code": 355, "indent": 1, "parameters": ["$gameActors.actor(2).changeEquipById(3, 152);"]}, {"code": 655, "indent": 1, "parameters": ["$gameActors.actor(2).changeEquipById(4, 153);"]}, {"code": 230, "indent": 1, "parameters": [90]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 231, "indent": 0, "parameters": [1, "kitchen_night_back", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 235, "indent": 0, "parameters": [10]}, {"code": 313, "indent": 0, "parameters": [0, 2, 0, 26]}, {"code": 355, "indent": 0, "parameters": ["chahuiUtil.imoutoOutfitloading(500,150)"]}, {"code": 231, "indent": 0, "parameters": [16, "mio_tachie_kao_happy", 0, 0, 500, 150, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [28, "kitchen_night_bowl", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [29, "kitchen_night_front", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 108, "indent": 0, "parameters": ["计算玩家持有的食材情况"]}, {"code": 355, "indent": 0, "parameters": ["QJ.MPMZ.tl._imoutoUtilImoutoCookingPickIngredients()"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "軽快ピアノのポップなお料理系ジングル", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE sis_room_achievement_10"]}, {"code": 355, "indent": 0, "parameters": ["this.count = 0"]}, {"code": 356, "indent": 0, "parameters": [">图片的层级与堆叠级 : 图片[28] : 设置层级 : 最顶层"]}, {"code": 355, "indent": 0, "parameters": ["$gameNumberArray.value(41).forEach(function(k) {"]}, {"code": 655, "indent": 0, "parameters": [" if($gameScreen.picture( k )) {"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.picture( k ).drill_PCE_stopEffect();"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.picture( k ).drill_PCE_playSustainingBreathing( 600,15,3 )"]}, {"code": 655, "indent": 0, "parameters": ["  }"]}, {"code": 655, "indent": 0, "parameters": ["});"]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["this.count++"]}, {"code": 355, "indent": 1, "parameters": ["QJ.MPMZ.tl._imoutoUtilImoutoCookingAnimation();"]}, {"code": 655, "indent": 1, "parameters": ["let time = 2 + Math.randomInt(8);"]}, {"code": 655, "indent": 1, "parameters": ["this.wait(time)"]}, {"code": 111, "indent": 1, "parameters": [12, "this.count >= 30"]}, {"code": 230, "indent": 2, "parameters": [30]}, {"code": 355, "indent": 2, "parameters": ["$gameNumberArray.value(41).forEach(function(k) {"]}, {"code": 655, "indent": 2, "parameters": [" if($gameScreen.picture( k )) {"]}, {"code": 655, "indent": 2, "parameters": ["$gameScreen.picture( k ).drill_PCE_stopEffect();"]}, {"code": 655, "indent": 2, "parameters": ["  }"]}, {"code": 655, "indent": 2, "parameters": ["});"]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["妹妹随机选取食材"]}, {"code": 355, "indent": 0, "parameters": ["    let count = Math.randomInt(3) + 1;"]}, {"code": 655, "indent": 0, "parameters": ["    let result = $gameNumberArray.value(15);"]}, {"code": 655, "indent": 0, "parameters": ["    count = Math.min(count, result.length);   "]}, {"code": 655, "indent": 0, "parameters": ["    for (let i = 0; i < count; i++) {"]}, {"code": 655, "indent": 0, "parameters": ["        let idx = Math.randomInt(result.length);"]}, {"code": 655, "indent": 0, "parameters": ["        let index = 75 + i; "]}, {"code": 655, "indent": 0, "parameters": ["        $gameVariables.setValue(index,result[idx]);"]}, {"code": 655, "indent": 0, "parameters": ["        result.splice(idx, 1);"]}, {"code": 655, "indent": 0, "parameters": ["    }"]}, {"code": 111, "indent": 0, "parameters": [12, "this.first<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>e"]}, {"code": 122, "indent": 1, "parameters": [78, 78, 0, 4, "0"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 122, "indent": 1, "parameters": [78, 78, 0, 4, "$gameMap.checkCraftingFormula()"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 235, "indent": 0, "parameters": [28]}, {"code": 241, "indent": 0, "parameters": [{"name": "The-Little-Witch_s-Home", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["AudioManager.fadeInBgm(3)"]}, {"code": 111, "indent": 0, "parameters": [1, 78, 0, 0, 3]}, {"code": 118, "indent": 1, "parameters": ["good"]}, {"code": 355, "indent": 1, "parameters": ["let IMG = 'Crafting_Finish';"]}, {"code": 655, "indent": 1, "parameters": ["let path = \"crafting_scene\";"]}, {"code": 655, "indent": 1, "parameters": ["$gameScreen.showPictureFromPath(30, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 250, "indent": 1, "parameters": [{"name": "032myuu_YumeSE_MassagePositive01", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 111, "indent": 1, "parameters": [12, "$gameNumberArray.value(8).includes($gameVariables.value(78))"]}, {"code": 356, "indent": 2, "parameters": [">地图临时漂浮物品框 : 修改样式 : 样式[3]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 356, "indent": 2, "parameters": [">地图临时漂浮物品框 : 修改样式 : 样式[2]"]}, {"code": 355, "indent": 2, "parameters": ["$gameNumberArray.value(8).push($gameVariables.value(78))"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["大奇迹修正术"]}, {"code": 111, "indent": 1, "parameters": [12, "Math.random() > 0.1"]}, {"code": 119, "indent": 2, "parameters": ["good"]}, {"code": 115, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["let IMG = 'Crafting_unsuccess';"]}, {"code": 655, "indent": 1, "parameters": ["let path = \"crafting_scene\";"]}, {"code": 655, "indent": 1, "parameters": ["$gameScreen.showPictureFromPath(30, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 250, "indent": 1, "parameters": [{"name": "034myuu_YumeSE_MassageGag01", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 111, "indent": 1, "parameters": [12, "$gameNumberArray.value(8).includes($gameVariables.value(78))"]}, {"code": 356, "indent": 2, "parameters": [">地图临时漂浮物品框 : 修改样式 : 样式[3]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 356, "indent": 2, "parameters": [">地图临时漂浮物品框 : 修改样式 : 样式[2]"]}, {"code": 355, "indent": 2, "parameters": ["$gameNumberArray.value(8).push($gameVariables.value(78))"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 78, 0, 3, 3]}, {"code": 355, "indent": 1, "parameters": ["var IMG = 'crafting' + $gameVariables.value(78);"]}, {"code": 655, "indent": 1, "parameters": ["var path = \"crafting_scene\";"]}, {"code": 655, "indent": 1, "parameters": ["var X = 893;"]}, {"code": 655, "indent": 1, "parameters": ["var Y = 474;"]}, {"code": 655, "indent": 1, "parameters": ["$gameScreen.showPictureFromPath(31, path, IMG, 0, X, Y, 100, 100, 255, 0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["var IMG = 'crafting999';"]}, {"code": 655, "indent": 1, "parameters": ["var path = \"crafting_scene\";"]}, {"code": 655, "indent": 1, "parameters": ["var X = 893;"]}, {"code": 655, "indent": 1, "parameters": ["var Y = 474;"]}, {"code": 655, "indent": 1, "parameters": ["$gameScreen.showPictureFromPath(31, path, IMG, 0, X, Y, 100, 100, 255, 0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 355, "indent": 0, "parameters": ["for (var i = 24; i <= 31; ++i) {"]}, {"code": 655, "indent": 0, "parameters": ["  if($gameScreen.picture(i)){"]}, {"code": 655, "indent": 0, "parameters": ["  $gameScreen.erasePicture(i);"]}, {"code": 655, "indent": 0, "parameters": [" }"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.erasePicture(30)"]}, {"code": 111, "indent": 0, "parameters": [0, 61, 0]}, {"code": 355, "indent": 1, "parameters": ["var IMG = 'dk_S_D';"]}, {"code": 655, "indent": 1, "parameters": ["var path = \"kitchen_event\";"]}, {"code": 655, "indent": 1, "parameters": ["$gameScreen.showPictureFromPath(1, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["var IMG = 'dk_S_E_RL';"]}, {"code": 655, "indent": 1, "parameters": ["var path = \"kitchen_event\";"]}, {"code": 655, "indent": 1, "parameters": ["$gameScreen.showPictureFromPath(1, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [2]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.changePictureName(16, \"mio_tachie_kao_happy\");"]}, {"code": 355, "indent": 0, "parameters": ["for (var i = 10; i <= 20; i++) {"]}, {"code": 655, "indent": 0, "parameters": ["  if($gameScreen.picture( i )) {"]}, {"code": 655, "indent": 0, "parameters": ["  $gameScreen.picture(i)._x = 350;"]}, {"code": 655, "indent": 0, "parameters": ["  $gameScreen.picture(i)._y = 150;"]}, {"code": 655, "indent": 0, "parameters": ["  }"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.picture(20)._x = 650;"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["重伤环节料理事件"]}, {"code": 111, "indent": 0, "parameters": [12, "this.first<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>e"]}, {"code": 355, "indent": 1, "parameters": ["let id = 22;"]}, {"code": 655, "indent": 1, "parameters": ["$gameMap.event(id).steupCEQJ(3)"]}, {"code": 115, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 108, "indent": 0, "parameters": ["重伤环节料理事件后续"]}, {"code": 355, "indent": 0, "parameters": ["let id = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.event(id).steupCEQJ(2)"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE sis_room_achievement_09"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(0);"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["………………!?"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(1);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(2);"]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.changePictureName(16, \"mio_tachie_kao_nununu\");"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE kitchen_event_sis_18"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(3);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(4);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(5);"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Explosion2", "volume": 90, "pitch": 80, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": ["DS_方向設定 90"]}, {"code": 225, "indent": 0, "parameters": [9, 8, 60, false]}, {"code": 224, "indent": 0, "parameters": [[0, 0, 0, 185], 90, false]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(6);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(7);"]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.changePictureName(16, \"mio_tachie_kao_shy2\");"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE kitchen_event_sis_19"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(8);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(9);"]}, {"code": 314, "indent": 0, "parameters": [0, 1]}, {"code": 250, "indent": 0, "parameters": [{"name": "キラン☆キラーン 派手なインパクト3", "volume": 50, "pitch": 130, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": ["particle group set buff_sp screen"]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(10);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(11);"]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.changePictureName(16, \"mio_tachie_kao_naku\");"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE kitchen_event_sis_20"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(12);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(13);"]}, {"code": 122, "indent": 0, "parameters": [14, 14, 0, 0, 45]}, {"code": 117, "indent": 0, "parameters": [3]}, {"code": 122, "indent": 0, "parameters": [13, 13, 0, 0, 1]}, {"code": 201, "indent": 0, "parameters": [0, 4, 7, 5, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 3, "y": 9}, null, null, {"id": 21, "name": "妹妹小人点击菜单", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 1, "characterIndex": 3}, "list": [{"code": 117, "indent": 0, "parameters": [16]}, {"code": 118, "indent": 0, "parameters": ["返回选项"]}, {"code": 356, "indent": 0, "parameters": [">对话选项按钮组 : 切换为按钮组 : 样式[12]"]}, {"code": 102, "indent": 0, "parameters": [["料理", "食谱", "存读档"], -2, -1, 2, 0]}, {"code": 402, "indent": 0, "parameters": [0, "料理"]}, {"code": 111, "indent": 1, "parameters": [0, 499, 0]}, {"code": 108, "indent": 2, "parameters": ["选项赋予名称"]}, {"code": 355, "indent": 2, "parameters": ["const idx   = 1;"]}, {"code": 655, "indent": 2, "parameters": ["const eid   = String(this._eventId);"]}, {"code": 655, "indent": 2, "parameters": ["const mapId = $gameMap.mapId();"]}, {"code": 655, "indent": 2, "parameters": ["const key   = `MapEventDialogue${mapId}`;"]}, {"code": 655, "indent": 2, "parameters": ["const dialogueTable = window[key] || {};"]}, {"code": 655, "indent": 2, "parameters": ["const textArray     = dialogueTable[eid]?.[String(idx)];"]}, {"code": 655, "indent": 2, "parameters": ["const option1 = textArray[0];"]}, {"code": 655, "indent": 2, "parameters": ["const option2 = textArray[1];"]}, {"code": 655, "indent": 2, "parameters": [" $gameStrings.setValue(6,option1);"]}, {"code": 655, "indent": 2, "parameters": [" $gameStrings.setValue(7,option2);"]}, {"code": 108, "indent": 2, "parameters": ["修正选项组图标"]}, {"code": 355, "indent": 2, "parameters": ["$gameSystem._drill_DCB_curStyle = 25"]}, {"code": 355, "indent": 2, "parameters": ["let style = [\"eventButtons_sis_crafting\",\"eventButtons_self_crafting\"];"]}, {"code": 655, "indent": 2, "parameters": ["DrillUp.g_DCB_data[24].btn_src = style;"]}, {"code": 102, "indent": 2, "parameters": [["\\str[6]", "\\c[15]\\str[7]"], -2, -1, 2, 0]}, {"code": 402, "indent": 2, "parameters": [0, "\\str[6]"]}, {"code": 355, "indent": 3, "parameters": ["chahuiUtil.resetAllSelfSwitchesForEvents(2,7)"]}, {"code": 230, "indent": 3, "parameters": [1]}, {"code": 356, "indent": 3, "parameters": [">图片快捷操作 : 批量图片[5,6] : 修改单属性 : 透明度[0] : 时间[30]"]}, {"code": 230, "indent": 3, "parameters": [30]}, {"code": 313, "indent": 3, "parameters": [0, 2, 0, 24]}, {"code": 117, "indent": 3, "parameters": [26]}, {"code": 231, "indent": 3, "parameters": [16, "mio_tachie_kao_nununu3", 0, 0, 200, 1600, 100, 100, 0, 0]}, {"code": 355, "indent": 3, "parameters": ["for (var i = 10; i <= 20; i++) {"]}, {"code": 655, "indent": 3, "parameters": ["  if($gameScreen.picture( i )) {"]}, {"code": 655, "indent": 3, "parameters": ["  $gameScreen.picture(i)._x = 200;"]}, {"code": 655, "indent": 3, "parameters": ["  $gameScreen.picture(i)._y = 1600;"]}, {"code": 655, "indent": 3, "parameters": ["  $gameScreen.picture(i)._opacity = 255;"]}, {"code": 655, "indent": 3, "parameters": ["  }"]}, {"code": 655, "indent": 3, "parameters": ["}"]}, {"code": 355, "indent": 3, "parameters": ["$gameScreen.picture(20)._x = 500;"]}, {"code": 230, "indent": 3, "parameters": [1]}, {"code": 355, "indent": 3, "parameters": ["var pic_ids = $gameNumberArray.value(41);"]}, {"code": 655, "indent": 3, "parameters": ["pic_ids.forEach(function(pic_id) {"]}, {"code": 655, "indent": 3, "parameters": ["    var picture = $gameScreen.picture(pic_id);"]}, {"code": 655, "indent": 3, "parameters": ["var data = {\t\t\t\t\t\t\t\t\"type\":\"smoothMove\","]}, {"code": 655, "indent": 3, "parameters": ["\"valueX\":0,"]}, {"code": 655, "indent": 3, "parameters": ["\"valueY\":-1450,"]}, {"code": 655, "indent": 3, "parameters": ["\"time\":60,"]}, {"code": 655, "indent": 3, "parameters": ["\"cur_time\":0,\"cur_speedX\":0,\"cur_speedY\":0,}"]}, {"code": 655, "indent": 3, "parameters": ["    if (picture) { picture._drill_PSh_commandChangeTank.push(data);}});"]}, {"code": 230, "indent": 3, "parameters": [60]}, {"code": 356, "indent": 3, "parameters": ["SV_PLAY_VOICE kitchen_event_sis_17"]}, {"code": 355, "indent": 3, "parameters": ["this.showMapEventDialogue(2);"]}, {"code": 355, "indent": 3, "parameters": ["this.showMapEventDialogue(3);"]}, {"code": 108, "indent": 3, "parameters": ["删除漂浮字"]}, {"code": 355, "indent": 3, "parameters": ["var scene = SceneManager._scene;"]}, {"code": 655, "indent": 3, "parameters": ["  for( var i = scene._drill_GFTT_windowTank.length-1; i >= 0; i-- ){"]}, {"code": 655, "indent": 3, "parameters": ["var temp_window = scene._drill_GFTT_windowTank[i];"]}, {"code": 655, "indent": 3, "parameters": ["scene.drill_GFTT_layerRemoveSprite( temp_window );     "]}, {"code": 655, "indent": 3, "parameters": ["scene._drill_GFTT_windowTank.splice( i, 1 );"]}, {"code": 655, "indent": 3, "parameters": ["    }"]}, {"code": 355, "indent": 3, "parameters": ["$gameMap.event(4).start()"]}, {"code": 115, "indent": 3, "parameters": []}, {"code": 355, "indent": 3, "parameters": ["let id = this._eventId;"]}, {"code": 655, "indent": 3, "parameters": ["$gameMap.event(id).steupCEQJ(2)"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 402, "indent": 2, "parameters": [1, "\\c[15]\\str[7]"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 403, "indent": 2, "parameters": [6, null]}, {"code": 119, "indent": 3, "parameters": ["返回选项"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 404, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["删除漂浮字"]}, {"code": 355, "indent": 1, "parameters": ["var scene = SceneManager._scene;"]}, {"code": 655, "indent": 1, "parameters": ["  for( var i = scene._drill_GFTT_windowTank.length-1; i >= 0; i-- ){"]}, {"code": 655, "indent": 1, "parameters": ["var temp_window = scene._drill_GFTT_windowTank[i];"]}, {"code": 655, "indent": 1, "parameters": ["scene.drill_GFTT_layerRemoveSprite( temp_window );     "]}, {"code": 655, "indent": 1, "parameters": ["scene._drill_GFTT_windowTank.splice( i, 1 );"]}, {"code": 655, "indent": 1, "parameters": ["    }"]}, {"code": 355, "indent": 1, "parameters": ["$gameMap.event(4).start()"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "食谱"]}, {"code": 355, "indent": 1, "parameters": ["this.showMapEventDialogue(0);"]}, {"code": 119, "indent": 1, "parameters": ["返回选项"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "存读档"]}, {"code": 355, "indent": 1, "parameters": ["QJ.MPMZ.deleteProjectile('imoutoUtil');"]}, {"code": 352, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["QJ.MPMZ.tl._imoutoUtilCheckInitialization()"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 403, "indent": 0, "parameters": [6, null]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 2, "y": 10}, {"id": 22, "name": "妹妹的第一次料理", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "help", "direction": 2, "pattern": 2, "characterIndex": 1}, "list": [{"code": 241, "indent": 0, "parameters": [{"name": "The-Little-Witch_s-Home", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["AudioManager.fadeInBgm(3)"]}, {"code": 355, "indent": 0, "parameters": ["var IMG = 'dk_CW_N_RL';"]}, {"code": 655, "indent": 0, "parameters": ["var path = \"kitchen_event\";"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.showPictureFromPath(1, path, IMG, 0, 0, 0, 100, 100, 255, 0)"]}, {"code": 355, "indent": 0, "parameters": ["$gameNumberArray.setValue(41,[10,11,13,16,19,20])"]}, {"code": 231, "indent": 0, "parameters": [10, "mio_tachie_hair", 0, 0, 200, 1600, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [13, "mio_tachie_no<PERSON><PERSON>_no<PERSON>ao", 0, 0, 200, 1600, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [11, "mio_tachie_handpose1", 0, 0, 200, 1600, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [16, "mio_tachie_kao_nununu2", 0, 0, 200, 1600, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [19, "mio_tachie_bowknot", 0, 0, 200, 1600, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [20, "mio_tachie_ahoge", 0, 0, 200, 1600, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["for (var i = 10; i <= 20; i++) {"]}, {"code": 655, "indent": 0, "parameters": ["  if($gameScreen.picture( i )) {"]}, {"code": 655, "indent": 0, "parameters": ["  $gameScreen.picture(i)._x = -600;"]}, {"code": 655, "indent": 0, "parameters": ["  $gameScreen.picture(i)._y = 150;"]}, {"code": 655, "indent": 0, "parameters": ["  $gameScreen.picture(i)._opacity = 255;"]}, {"code": 655, "indent": 0, "parameters": ["  }"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.picture(20)._x = -300;"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [4]}, {"code": 355, "indent": 0, "parameters": ["let id = this._eventId;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.event(id).steupCEQJ(2)"]}, {"code": 356, "indent": 0, "parameters": [">图片快捷操作 : 批量图片[10,11,13,14,16,19,20] : 增减速移动到 : 相对位置[300,0] : 时间[120]"]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.changePictureName(16, \"mio_tachie_kao_nununu\");"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.changePictureName(16, \"mio_tachie_kao_nununu2\");"]}, {"code": 356, "indent": 0, "parameters": [">图片快捷操作 : 批量图片[10,11,13,14,16,19,20] : 增减速移动到 : 相对位置[200,0] : 时间[120]"]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.changePictureName(16, \"mio_tachie_kao_nununu\");"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.changePictureName(16, \"mio_tachie_kao_nununu2\");"]}, {"code": 356, "indent": 0, "parameters": [">图片快捷操作 : 批量图片[10,11,13,14,16,19,20] : 增减速移动到 : 相对位置[550,0] : 时间[240]"]}, {"code": 230, "indent": 0, "parameters": [240]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.changePictureName(16, \"mio_tachie_kao_nununu\");"]}, {"code": 356, "indent": 0, "parameters": [">持续动作 : 批量图片[10,11,13,14,16,19,20] : 左右震动(渐变) : 持续时间[180] : 周期[8] : 震动幅度[1] : 开始时间[20] : 结束时间[60]"]}, {"code": 230, "indent": 0, "parameters": [200]}, {"code": 356, "indent": 0, "parameters": [">持续动作 : 批量图片[10,11,13,14,16,19,20] : 左右震动(渐变) : 结束动作"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE sis_room_achievement_07"]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.changePictureName(16, \"mio_tachie_kao_naku\");"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(1);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(2);"]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.changePictureName(16, \"mio_tachie_kao_smile\");"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE sis_room_achievement_08"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(3);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(4);"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["let id = 18;"]}, {"code": 655, "indent": 0, "parameters": ["$gameMap.event(id).steupCEQJ(1,{firstDeathCuisine:true})"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE sis_room_achievement_06"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(0);"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["for (var i = 24; i <= 31; ++i) {"]}, {"code": 655, "indent": 0, "parameters": ["  if($gameScreen.picture(i)){"]}, {"code": 655, "indent": 0, "parameters": ["  $gameScreen.erasePicture(i);"]}, {"code": 655, "indent": 0, "parameters": [" }"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 655, "indent": 0, "parameters": ["$gameScreen.erasePicture(30)"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE sis_room_achievement_09"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(5);"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["………………!?"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(6);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(7);"]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.changePictureName(16, \"mio_tachie_kao_nununu\");"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE sis_room_achievement_11"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(8);"]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.changePictureName(16, \"mio_tachie_kao_smile2\");"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE sis_room_achievement_12"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(9);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(10);"]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.changePictureName(16, \"mio_tachie_kao_ase\");"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(11);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(12);"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Explosion2", "volume": 90, "pitch": 80, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": ["DS_方向設定 90"]}, {"code": 225, "indent": 0, "parameters": [9, 8, 60, false]}, {"code": 224, "indent": 0, "parameters": [[0, 0, 0, 185], 90, false]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.startFadeOut(1500);"]}, {"code": 121, "indent": 0, "parameters": [6, 6, 0]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(13);"]}, {"code": 355, "indent": 0, "parameters": ["$gameScreen.changePictureName(16, \"mio_tachie_kao_kimo\");"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE sis_room_achievement_13"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(14);"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(15);"]}, {"code": 356, "indent": 0, "parameters": ["SV_PLAY_VOICE sis_room_achievement_14"]}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(16);"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["this.showMapEventDialogue(17);"]}, {"code": 122, "indent": 0, "parameters": [13, 13, 0, 0, 4]}, {"code": 201, "indent": 0, "parameters": [0, 36, 6, 4, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 3, "y": 10}, null, null, null, null]}