/*:
 * @plugindesc 
 * <AUTHOR>
 *
 * @help
 * 
 */
 

function resetMessageWindow() {
        var messageWindow = SceneManager._scene._messageWindow;
		var cw = SceneManager._scene._messageWindow._choiceWindow;
        if (messageWindow) {
            messageWindow._textState = null;          // 重置文本状态
            messageWindow.close();                    // 关闭消息窗口
            $gameMessage.clear();                     // 清除游戏消息
            messageWindow.updatePlacement();          // 更新消息窗口位置
            messageWindow.contents.clear();           // 清除消息窗口内容

            // 重置输入等待状态
            messageWindow._showFast = false;
            messageWindow._lineShowFast = false;
            messageWindow.pause = false;
            messageWindow._pauseSkip = false;
            messageWindow._waitCount = 0;

            // 隐藏姓名框（如果存在）
            if (messageWindow._drill_DNB_nameWindow) {
                messageWindow._drill_DNB_nameWindow.deactivate();
                messageWindow._drill_DNB_nameWindow.hide();
            }
        }
         // 清空当前选项数据
        if (cw && cw.active) {
         $gameMessage._choices = [];
         // 关闭选项窗口，不触发回调
         cw.deactivate();
         cw.close();
        }		
		
}
 
// 新游戏初始化
chahuiUtil.newGameInitialization = function () {

    $gameSystem.time_system(false);
	
    const title = $dataSystem.gameTitle;
    if      (title.includes("和存在感薄弱妹妹一起的简单生活")) {  
        $gameVariables.setValue(1, 0);
    } else if (title.includes("存在感薄い妹との簡単生活")) {        
        $gameVariables.setValue(1, 1);
    } else if (title.includes("A Simple Life with My Unobtrusive Sister")) { 
        $gameVariables.setValue(1, 2);
    }


    $gameNumberArray.setValue(1, []);  
    $dataWeapons.forEach(w => {
        if (w?.note?.includes("<颜色:13>")) {
            $gameNumberArray.value(1).push(w.id);
        }
    });

    $gameNumberArray.setValue(2, []);
    $dataArmors.forEach(a => {
        if (a?.note?.includes("<颜色:13>")) {
            $gameNumberArray.value(2).push(a.id);
        }
    });

    /* ---------- 玩家初始属性 ---------- */
    $gamePlayer._moveSpeed  = 8;
    $gamePlayer._throwPower = 5;
    $gameActors.actor(1).changeEquipById(1, 1);   // 为防止技能等级异常累加，不能初始自带武器

    /* ---------- 初始化深渊生成计数 ---------- */
    const abyss = $gameNumberArray.value(20) || [];
    abyss.forEach(mapIndex => {
        $gameSelfVariables.setValue([mapIndex, 1, 'rainyCreation'], 2);
    });
};

// 检查事件变量是否合法
chahuiUtil.checkScriptExecutability = function () {
let code = $gameStrings.value(20);
if (code.includes("steupCEQJ")) {
  return true;
} else {
	$gameStrings.setValue(20,"");
	return false;
}
};

// 根据用户端跳转Discord
chahuiUtil.jumpToDiscordServer = function () {
	
		if ( Utils.isMobileDevice() ) {
            window.open('https://discord.gg/KAXzMmGfUJ', '_system');
           } else {
            require('nw.gui').Shell.openExternal('https://discord.gg/KAXzMmGfUJ');
        }
				
};

// 刷新游戏标题画面的动画演出
chahuiUtil.RefreshTitleScreenAnimation = function (type) {

   let animation = {
     "brushingTeeth": {background: 4, video: 1},
     "idleInDining": {background: 5, video: 2},
     "idleInBedroom0": {background: 2, video: 3},
	 "idleInBedroom1": {background: 2, video: 4},
	 "peekingBath": {background: 6, video: 0},
	 "peekingUndressing": {background: 7, video: 5},
	 "rubEyes": {background: 2, video: 6},
	 "hamburgerSteak": {background: 5, video: 7},
     
   };

   // 先取消所有背景图的显示
   for(var i=1; i < DrillUp.global_TBa_visibleTank.length; i++){
       DrillUp.global_TBa_visibleTank[i] = false;
   }

   // 先取消所有视频的显示
   for(var i=0; i < DrillUp.global_TVi_visibleTank.length; i++){
       DrillUp.global_TVi_visibleTank[i] = false;
   }
   // 显示指定类型动画
   if (!animation[type]) return;
   let backgroundId = animation[type]["background"] - 1;
   DrillUp.global_TBa_visibleTank[backgroundId] = true;
   
   let videoId = animation[type]["video"] - 1;
   
   if (Utils.isMobileDevice()) {
	   if (videoId == 4) videoId = 3;
   }   
   
   if (videoId > 0) {
   DrillUp.global_TVi_visibleTank[videoId] = true;
     }
   
   // 储存修改记录
   StorageManager.drill_TBa_saveData();
   StorageManager.drill_TVi_saveData(); 

};


// 为场景物件绑定文本描述
chahuiUtil.bindDescriptionToSceneObjects = function (pid, index) {

    if (!window.dataSceneObjectDescriptionText) {
        console.error("sceneObjectDescriptionText 数据未加载");
        return;
    }

	let isMobileDevice = Utils.isMobileDevice();
	
	let descData = window.dataSceneObjectDescriptionText;
	let raw = descData[String(index)];
	if (!raw) return;
	let prefix = "\\fs[14]\\fi\\c[110]";
	if (isMobileDevice) prefix = "\\fs[24]\\fi\\c[110]";	
	let lines = raw.map(function(line) {
    if (!line.startsWith(prefix)) {
        return prefix + line;
       }
        return line;
    });
	
    let picture = $gameScreen.picture(pid);
	if (!picture) return;
    let bind = DrillUp.g_MPFP_list[6];
	if (isMobileDevice) bind = DrillUp.g_MPFP_list[11];
	
	
    if (!picture._drill_MPFP_bean) {
      picture._drill_MPFP_bean = new Drill_MPFP_Bean();
      $gameTemp._drill_MPFP_needRestatistics = true; 
      picture.drill_COPWM_checkData(); 
   }
   
    picture._drill_MPFP_bean.drill_bean_setVisible(true);
    picture._drill_MPFP_bean.drill_bean_setContextList(lines);
    picture._drill_MPFP_bean.drill_bean_setSkinStyle(bind['style_mode'], bind['style_lockedId']);	
	
};

chahuiUtil.resetAllSelfSwitchesForEvents = function (...eventIds) {
    let mapId = $gameMap._mapId;
    let selfSwitches = ["A", "B", "C", "D", "E"];

    eventIds.forEach(function(eventId) {
        selfSwitches.forEach(function(switchChar) {
            let key = [mapId, eventId, switchChar];
            $gameSelfSwitches.setValue(key, false);
        });
    });
};

chahuiUtil.changeSelfSwitchForEvent = function (eventId, switchCharToActivate) {
    let mapId = $gameMap._mapId;
    let selfSwitches = ["A", "B", "C", "D", "E"];

    selfSwitches.forEach(function(switchChar) {
        if (switchChar !== switchCharToActivate) {
            let key = [mapId, eventId, switchChar];
            $gameSelfSwitches.setValue(key, false);
        }
    });

    let keyToActivate = [mapId, eventId, switchCharToActivate];
    $gameSelfSwitches.setValue(keyToActivate, true);
};

chahuiUtil.checkSelfSwitchesForEvent = function (eventId) {
    let mapId = $gameMap._mapId;
    let selfSwitches = ["A", "B", "C", "D", "E"];
    let isActive = false;

    for (let i = 0; i < selfSwitches.length; i++) {
        let key = [mapId, eventId, selfSwitches[i]];
        if ($gameSelfSwitches.value(key)) {
            isActive = true;
            break;
        }
    }

    if (isActive) {
        selfSwitches.forEach(function(switchChar) {
            let key = [mapId, eventId, switchChar];
            $gameSelfSwitches.setValue(key, false);
        });
        return true;
    } else {
        return false;
    }
};


chahuiUtil.changesisterKao = function (eventId) {
    let Id = $gameVariables.value(36);
    let mapId = $gameMap._mapId;
    let selfSwitches = ["A", "B", "C", "D", "E"];

    selfSwitches.forEach(function(switchChar) {
        let key = [mapId, eventId, switchChar];
        $gameSelfSwitches.setValue(key, false);
    });
   
    if (Id >= 0 && Id < selfSwitches.length) {
        let key = [mapId, eventId, selfSwitches[Id]];
        $gameSelfSwitches.setValue(key, true);
    }
};

//预加载
Game_Map.prototype.chahuiPreloadPicture = function (Path, Id) {
    return true;
};

chahuiUtil.getEventsAroundPlayer = function(range) {
    range = range || 2; 
    var playerX = $gamePlayer.x;
    var playerY = $gamePlayer.y;
    var eventIds = [];

    for (var y = playerY - range; y <= playerY + range; y++) {
        for (var x = playerX - range; x <= playerX + range; x++) {
            // 确保坐标在地图范围内
            if (x >= 0 && y >= 0 && x < $gameMap.width() && y < $gameMap.height()) {
                var events = $gameMap.eventsXy(x, y);
                if (events.length > 0) {
                    events.forEach(function(event) {
                        if (chahuiUtil.eventContainsComment(event, "canMove")) {
                            $gameNumberArray.value(20).push(event.eventId());
                        }
                    });
                }
            }
        }
    }
    return eventIds;
};

chahuiUtil.getEventsAroundEvent = function(eventID) {
	var event = $gameMap.event(eventID);
    var range = 2; 
    var XX = event.x;
    var YY = event.y;
    var eventIds = [];

    for (var y = YY - range; y <= YY + range; y++) {
        for (var x = XX - range; x <= XX + range; x++) {
            // 确保坐标在地图范围内
            if (x >= 0 && y >= 0 && x < $gameMap.width() && y < $gameMap.height()) {
                var events = $gameMap.eventsXy(x, y);
                if (events.length > 0) {
                    events.forEach(function(event) {
						var distance = $gameMap.event(eventID).calcDistance(event.eventId());
                        if (chahuiUtil.eventContainsComment(event, "<Money>") && distance < 32) {
                            event.requestBalloon(2);
                        }
                    });
                }
            }
        }
    }
};

chahuiUtil.eventContainsComment = function(event, comment) {
    var pages = event.event().pages;
    for (var i = 0; i < pages.length; i++) {
        var list = pages[i].list;
        for (var j = 0; j < list.length; j++) {
            var command = list[j];
            if ((command.code === 108 || command.code === 408) && command.parameters[0].includes(comment)) {
                return true;
            }
        }
    }
    return false;
};

chahuiUtil.getVarianceDamage = function(type,variance) {
	switch (type) {
	  case 1:		
	var baseDamage = $gameActors.actor(1).atk; 
	  break;
	  case 2:		
	var baseDamage = $gameActors.actor(1).mat; 
	  break;
	  case 3:		
	var baseDamage = Math.round(0.5 * $gameActors.actor(1).atk + 0.5 * $gameActors.actor(1).mat); 
	  break;
  }	
	variance = variance || 10;
    variance = variance / 100; 
    var minDamage = baseDamage * (1 - variance); 
    var maxDamage = baseDamage * (1 + variance); 
    var randomDamage = Math.random() * (maxDamage - minDamage) + minDamage; 
    return Math.round(randomDamage); 
};

// 魔法抗性减伤
chahuiUtil.magicDefenseDamageReduction = function(mdf) {
    const a = 1.65;
    const K = 167;

    // 幂运算
    const Ra = Math.pow(mdf, a);
    const Ka = Math.pow(K, a);

    // 减伤公式： R^a / (R^a + K^a)
	const reduction = Ra / (Ra + Ka);
    return Math.floor(reduction*100);    
};

chahuiUtil.lerp = function(start, end, amt) {
    return (1 - amt) * start + amt * end;
};

chahuiUtil.getToneByTime = function() {
    // 获取当前时间或帧数
    const time = Graphics.frameCount; // 或者使用其他时间/帧计数的方法

    // 定义色调变化的周期
    const cycleLength = 240;

    // 根据当前时间计算周期的位置
    const cyclePosition = (time % cycleLength) / cycleLength;

    // 定义起始和结束色调
    const startTone = [255, 0, 0, 100]; // 红色
    const endTone = [0, 0, 255, 100]; // 蓝色

    // 计算两个色调之间的过渡比例
    const amt = cyclePosition;
    // 对每个颜色通道进行插值
    const r = chahuiUtil.lerp(startTone[0], endTone[0], amt);
    const g = chahuiUtil.lerp(startTone[1], endTone[1], amt);
    const b = chahuiUtil.lerp(startTone[2], endTone[2], amt);
    const a = chahuiUtil.lerp(startTone[3], endTone[3], amt);; // 如果你也想过渡alpha值

    return [r, g, b, a];
};

chahuiUtil.getCharacterAngle = function(characterId) {
    var angle;
    var character;

    // 确定角色是玩家还是特定事件
    if (characterId === -1) {
        character = $gamePlayer; // 玩家
    } else if (characterId > 0) {
        character = $gameMap.event(characterId); // 指定事件
    } else {
        return $gamePlayer; 
    }

    // 获取角色的朝向
    switch (character.direction()) {
        case 2:  // 下
            angle = 180;
            break;
        case 4:  // 左
            angle = 270;
            break;
        case 6:  // 右
            angle = 90;
            break;
        case 8:  // 上
            angle = 0;
            break;
        case 1:  // 左下
            angle = 225;
            break;
        case 3:  // 右下
            angle = 135;
            break;
        case 7:  // 左上
            angle = 315;
            break;
        case 9:  // 右上
            angle = 45;
            break;
        default: 
            angle = 0;
            break;
    }

    return angle;
};


chahuiUtil.checkWeaponQuality = function(paramName) {
    var weaponId = $gameActors.actor(1).equips()[0].baseItemId; 
	var weapon = $dataWeapons[weaponId]; 
    if (weapon) {
        var notes = weapon.note.split(/[\r\n]+/); 
        var regex = new RegExp("<" + paramName + ":(\\d+)>"); 
        for (var i = 0; i < notes.length; i++) {
            var note = notes[i];
            var match = note.match(regex); 
            if (match) {
                return Number(match[1]); 
            }
        }
    }
    return 0; 
};

chahuiUtil.tsubamegaeshi = function() {
let flat = 210;
let angle = QJ.BL.calculateAngleByDirection($gamePlayer.direction())*180/Math.PI;
    angle += flat;
return angle;
};

chahuiUtil.checkSlashBlend = function() {
    var weaponId = $gameActors.actor(1).equips()[0].baseItemId;
    var weapon = $dataWeapons[weaponId];
    if (weapon) {
        var notes = weapon.note.split(/[\r\n]+/);
        var regex = /<SlashBlend:\[([\d,]+)\]>/;
        for (var i = 0; i < notes.length; i++) {
            var note = notes[i];
            var match = note.match(regex);
            if (match) {
                var numbers = match[1].split(',').map(Number);
                return numbers;
            }
        }
    }
    return "[0,0,0,0]"; 
};

chahuiUtil.weightedRandom = function(baseWeights) {
    var numbers = [0, 1, 2, 3, 4, 5]; 
    if ($gameParty.leader().isLearnedSkill(50)) {
        baseWeights[0] = Math.max(baseWeights[0] - 10, 0);
        baseWeights[1] = Math.max(baseWeights[1] - 10, 0);
    }
    var totalWeight = baseWeights.reduce((acc, cur) => acc + cur, 0);
    var randomNum = Math.random() * totalWeight;
    var weightSum = 0;

    for (var i = 0; i < numbers.length; i++) {
        weightSum += baseWeights[i];
        if (randomNum < weightSum) {
            return numbers[i];
        }
    }
};

chahuiUtil.extendStateTime = function(stateId, time) {	
	stateId = stateId.toString();
	var extendTime = time.toString();
    if ($gameSystem._timeEvents) {
     let found = false; 
    $gameSystem._timeEvents.forEach(function(event) {
        if (event.command === "remove" && event.target === stateId) {
            event.minutes += time;
            found = true;
        }
    });
    if (!found) {
        var args = ['remove', extendTime, '100', stateId];
        $gameSystem.addTimeEvent(args);
        }
    } else {
    var args = ['remove', extendTime, '100', stateId];
    $gameSystem.addTimeEvent(args);
    }
};

chahuiUtil.adjustWeaponAngle = function(direction) {
    let ax = ($gamePlayer._realX + 0.5 - $gameMap.displayX()) * 48;
    let ay = ($gamePlayer._realY + 0.5 - $gameMap.displayY()) * 48;
    let bx = TouchInput.x / 2 + $gameMap.displayX();
    let by = TouchInput.y / 2 + $gameMap.displayY();
    let deg = QJ.BL.calculateAngleByTwoPoint(ax, ay, bx, by);
    deg = Math.round((deg * 180) / Math.PI);

    let minAngle, maxAngle;
    switch (direction) {
        case 8: // 上
            minAngle = 300;
            maxAngle = 60;
            break;
        case 4: // 左
            minAngle = 210;
            maxAngle = 330;
            break;
        case 6: // 右
            minAngle = 30;
            maxAngle = 150;
            break;
        case 2: // 下
            minAngle = 120;
            maxAngle = 240;
            break;
        default:
            return deg * (Math.PI / 180); // 如果方向无效，返回原始角度
    }

    // 将角度转换为 0-359 度范围内
    deg = (deg + 360) % 360;

    const angleDifference = (a, b) => {
        let diff = Math.abs(a - b);
        return Math.min(diff, 360 - diff);
    };

    // 处理跨越360°的情况
    if (minAngle > maxAngle) {
        if (deg >= minAngle || deg <= maxAngle) {
            return deg * (Math.PI / 180);
        }
    } else {
        if (deg >= minAngle && deg <= maxAngle) {
            return deg * (Math.PI / 180);
        }
    }

    // 计算到 minAngle 和 maxAngle 的最小距离
    let distanceToMin = angleDifference(deg, minAngle);
    let distanceToMax = angleDifference(deg, maxAngle);

    let adjustedAngle = (distanceToMin < distanceToMax) ? minAngle : maxAngle;
    return adjustedAngle * (Math.PI / 180);
};

chahuiUtil.findAttackStartingAngle = function(type) {
	var direction = $gamePlayer.direction();
    let minAngle, maxAngle;
    switch (direction) {
        case 8: // 上
            minAngle = 300;
            maxAngle = 60;
            break;
        case 4: // 左
            minAngle = 210;
            maxAngle = 330;
            break;
        case 6: // 右
            minAngle = 30;
            maxAngle = 150;
            break;
        case 2: // 下
            minAngle = 120;
            maxAngle = 240;
            break;
        default:
            return 0; 
    }
	if (type == 0) {
		return minAngle;
	} else if (type == 1){
	    return maxAngle;
	}
};

chahuiUtil.gachaWeightedCalculate = function(luk) {

    luk = Math.max(0, Math.min(luk, 999));

    let c, u, r, e, l;

    if (luk < 40) {
        let f = luk / 40;
        // 下限值: C=6500,U=2200,R=1000,E=300,L=0
        // 基准值: C=5000,U=2500,R=1500,E=800,L=200
        // 插值计算: (end - start)*f + start
        c = 6500 - 1500 * f;   // 6500→5000
        u = 2200 + 300 * f;    // 2200→2500
        r = 1000 + 500 * f;    // 1000→1500
        e = 300 + 500 * f;     // 300→800
        l = 0 + 200 * f;       // 0→200
    } else {
        let f = (luk - 40) / 959;
        // 基准值: C=5000,U=2500,R=1500,E=800,L=200
        // 上限值: C=2000,U=1200,R=3000,E=1900,L=1900
        c = 5000 - 3000 * f;   // 5000→2000
        u = 2500 - 1300 * f;   // 2500→1200
        r = 1500 + 1500 * f;   // 1500→3000
        e = 800 + 1100 * f;    // 800→1900
        l = 200 + 1700 * f;    // 200→1900
    }

    c = Math.round(c);
    u = Math.round(u);
    r = Math.round(r);
    e = Math.round(e);
    l = Math.round(l);

    return [c, u, r, e, l];
};

chahuiUtil.getImoutoMoodReaction = function() {
	let kimochi = $gameVariables.value(20);
    kimochi = Math.max(0, Math.min(kimochi, 100));
    let c,u,r,e,l;

    if (kimochi <= 50) {
        let f = kimochi / 50.0;
        c = 40 - 20 * f; //40→20
        u = 30 - 10 * f; //30→20
        r = 20;          //20→20
        e = 5  + 15 * f; //5→20
        l = 5  + 15 * f; //5→20
    } else {
        let f = (kimochi - 50) / 50.0;
        c = 20 - 15 * f; //20→5
        u = 20 - 15 * f; //20→5
        r = 20;          //20→20
        e = 20 + 10 * f; //20→30
        l = 20 + 20 * f; //20→40
    }

    c = Math.round(c * 10);
    u = Math.round(u * 10);
    r = Math.round(r * 10);
    e = Math.round(e * 10);
    l = Math.round(l * 10);

    return [c,u,r,e,l];
};

chahuiUtil.gachaWeightedRandom = function(numbers = [1, 2, 3, 4, 5], weights = [50, 25, 15, 8, 2]) {
    
	// 扭蛋保底
	if ( $gameSelfSwitches.value([1, 32, 'pity']) ) {
		$gameSelfSwitches.setValue([1, 32, 'pity'], false);
		return 5;
	}
	
    // 计算总权重
    const totalWeight = weights.reduce((acc, weight) => acc + weight, 0);

    // 生成一个在 0 和 totalWeight 之间的随机数
    const randomNum = Math.random() * totalWeight;

    // 根据权重分布返回一个数字
    let cumulativeWeight = 0;
    for (let i = 0; i < numbers.length; i++) {
        cumulativeWeight += weights[i];
        if (randomNum < cumulativeWeight) {
            return numbers[i];
        }
    }
};

chahuiUtil.gachaNumberRandom = function(inputValue) {
    let value = 0;
    let minRange = 100;
    let maxRange = 500;
    
	if (inputValue === 11111) return 11;
	
    while (inputValue >= minRange) {       
        let randomDecrement = Math.floor(Math.random() * (maxRange - minRange + 1)) + minRange;
        if (inputValue >= randomDecrement) {
            inputValue -= randomDecrement;
            value++;
        } else {
            break; 
        }
    }

    return value;
};

chahuiUtil.gachaRandomRocation = function (EID) {
    let event = $gameMap.event(EID);
    let condition = DrillUp.g_COFA_condition_list[10];
    var c_area = $gameMap.drill_COFA_getCustomPointsByIdWithCondition(EID, 7, condition);

    let xPlus, yPlus;

    if (c_area.length > 0) {
        var p = c_area[Math.floor(Math.random() * c_area.length)];
        if (p && typeof p.x === 'number' && typeof p.y === 'number') {
            // 确保 p 存在且 p.x 和 p.y 是有效的数值
            xPlus = p.x - event.x;
            yPlus = p.y - event.y;
        } else {
            // 如果 p 无效或 p.x/p.y 无法读取，则使用随机值
            xPlus = Math.randomInt(9) - 4;
            yPlus = Math.randomInt(9) - 4;
        }
    } else {
        // 如果 c_area 没有元素，则使用随机值
        xPlus = Math.randomInt(9) - 4;
        yPlus = Math.randomInt(9) - 4;
    }

    $gameMap.event(EID).jump(xPlus, yPlus);
    $gameMap.event(EID)._opacity = 255;
};

chahuiUtil.terminateSpecifiedEvents = function(events) {
	
var eventIds = events;
eventIds.forEach(function(eventId) {
    var event = $gameMap.event(eventId);
    if (event) {
        // 检查地图解释器是否在处理该事件
        if ($gameMap._interpreter._eventId === eventId) {
            if (typeof $gameMap._interpreter.terminate === 'function') {
                $gameMap._interpreter.terminate();
            }
            $gameMap._interpreter.clear();
        }
        // 检查事件自己的解释器是否在运行
        if (event._interpreter && event._interpreter.isRunning()) {
            if (typeof event._interpreter.terminate === 'function') {
                event._interpreter.terminate();
            }
            event._interpreter.clear();
        }
    }
});

// 关闭消息窗口并重置状态
var messageWindow = SceneManager._scene._messageWindow;
if (messageWindow) {
    messageWindow._textState = null;          // 重置文本状态
    messageWindow.close();                    // 关闭消息窗口
    $gameMessage.clear();                     // 清除游戏消息
    messageWindow.updatePlacement();          // 更新消息窗口位置
    messageWindow.contents.clear();           // 清除消息窗口内容
    // 重置输入等待状态
    messageWindow._showFast = false;
    messageWindow._lineShowFast = false;
    messageWindow.pause = false;
    messageWindow._pauseSkip = false;
    messageWindow._waitCount = 0;
}
   // 隐藏姓名框
    if (messageWindow._drill_DNB_nameWindow) {
        messageWindow._drill_DNB_nameWindow.deactivate();
        messageWindow._drill_DNB_nameWindow.hide();
    }
	
};

// 强制中断正在执行的事件
chahuiUtil.abortEventById = function(eventId) {
    // 获取调用该指令的当前事件 ID（支持串行或平行事件）
    var callerId = 0;
    if (this && typeof this.eventId === 'function') {
        callerId = this.eventId();
    } else if ($gameMap._interpreter && $gameMap._interpreter._eventId) {
        callerId = $gameMap._interpreter._eventId;
    }
    
    // 内部函数：重置消息窗口状态


    // 内部函数：终止事件的自定义解释器（新增运行模式）
    function terminateCommonEventQJ(interpreters) {
        if (interpreters && interpreters.length > 0) {
            interpreters.forEach(function(interpreter) {
                if (interpreter && interpreter.isRunning && interpreter.isRunning()) {
                    if (typeof interpreter.terminate === 'function') {
                        interpreter.terminate();
                    }
                }
            });
            // 清空数组
            interpreters.length = 0;
        }
    }

    // 如果传入的是数组，则逐个调用
    if (Array.isArray(eventId)) {
        eventId.forEach(function(id) {
            chahuiUtil.abortEventById(id);
        });
        return;
    }

    // eventId 为 -1 时，中断所有事件，但排除当前调用该指令的事件
    if (eventId === -1) {
        // 首先处理地图上所有标准事件
        $gameMap.events().forEach(function(event) {
            var id;
            if (event.page() && event.page().trigger > 2) { // 并行处理事件
                id = event._eventId;
            } else {
                id = event.eventId();
            }
            // 排除当前事件
            if (id === callerId) return;
            if (event.page() && event.page().trigger > 2) {
                $gameMap.eraseEvent(event.eventId());
            } else {
                if ($gameMap._interpreter && $gameMap._interpreter._eventId === id) {
                    if (typeof $gameMap._interpreter.terminate === 'function') {
                        $gameMap._interpreter.terminate();
                    }
                    $gameMap._interpreter.clear();
                }
            }
            // 同时终止该事件自定义的解释器（新增运行模式）
            if (event._commonEventQJ) {
                terminateCommonEventQJ(event._commonEventQJ);
            }
        });
        // 处理地图层新增的自定义解释器（例如公共事件模式）
        if ($gameMap._commonEventQJ) {
            terminateCommonEventQJ($gameMap._commonEventQJ);
        }
        resetMessageWindow();
        return;
    }

    // 单独处理指定的事件
    var event = $gameMap.event(eventId);
    if (!event) return;
    
    var id;
    if (event.page() && event.page().trigger === 4) { // 并行处理事件
        id = event._eventId;
    } else {
        id = event.eventId();
    }
    // 如果目标事件就是当前调用该指令的事件，则跳过
    if (id === callerId) return;
    
    if (event.page() && event.page().trigger === 4) {
        $gameMap.eraseEvent(event.eventId());
    } else {
        if ($gameMap._interpreter && $gameMap._interpreter._eventId === id) {
            if (typeof $gameMap._interpreter.terminate === 'function') {
                $gameMap._interpreter.terminate();
            }
            $gameMap._interpreter.clear();
        }
    }
    // 同时终止该事件自定义的解释器（新增运行模式）
    if (event._commonEventQJ) {
        terminateCommonEventQJ(event._commonEventQJ);
    }
    resetMessageWindow();
};



// 妹妹立绘组合
chahuiUtil.imoutoOutfitloading = function (posX, posY, bathTowel) {
    // 妹妹胖次检测
    let imouto = $gameActors.actor(2);

    if (!posX || !posY) {
        posX = 1000;
        posY = 1600;
    }
    let ahogeX = posX + 300;


    if (!$gameScreen.picture(16)) {
        $gameScreen.showPicture(16, "", 0, posX, posY, 100, 100, 255, 0); // 表情占位符
    } else {
        let kao = $gameScreen.picture(16).name();
        $gameScreen.showPicture(16, kao, 0, posX, posY, 100, 100, 255, 0); // 重置坐标
    }

//=============================================================================
// 妹妹常态（穿外套）
//=============================================================================
    if (imouto.isStateAffected(24)) { 
        $gameNumberArray.setValue(41, [11, 12, 16, 17, 19, 20]);
		
        $gameScreen.showPicture(11, "mio_tachie_hair", 0, posX, posY, 100, 100, 255, 0);
		$gameScreen.showPicture(12, "mio_tachie_noHand_noKao", 0, posX, posY, 100, 100, 255, 0);
		
        if (imouto.isStateAffected(36) || ($gameSystem.hour() >= 21 && $gameScreen.picture(1).name().includes("sister_room_night_fine"))) {
            // 妹妹犯困
            $gameScreen.showPicture(17, "mio_tachie_coat_nemui_shortpants", 0, posX, posY, 100, 100, 255, 0);
            //$gameScreen.picture(13).drill_PLAZ_setZIndex(16.5);            
        } else {
			$gameScreen.showPicture(17, "mio_tachie_coat0", 0, posX, posY, 100, 100, 255, 0);
		}
		    
        $gameScreen.showPicture(19, "mio_tachie_bowknot", 0, posX, posY, 100, 100, 255, 0);
        $gameScreen.showPicture(20, "mio_tachie_ahoge", 0, ahogeX, posY, 100, 100, 255, 0);
    }
//=============================================================================
// 妹妹洗澡后（无外套）
//=============================================================================
    if (imouto.isStateAffected(26)) { 
	    $gameNumberArray.setValue(41, [11, 12, 13, 16, 17, 19, 20]);
		
		$gameScreen.showPicture(11, "mio_tachie_hair", 0, posX, posY, 100, 100, 255, 0);
		$gameScreen.showPicture(13, "mio_tachie_noHand_noKao", 0, posX, posY, 100, 100, 255, 0);
		
        if (imouto.isStateAffected(36) || ($gameSystem.hour() >= 21 && $gameScreen.picture(1).name().includes("sister_room_night_fine"))) {
            // 妹妹犯困
            $gameScreen.showPicture(17, "mio_tachie_hand_nemui", 0, posX, posY, 100, 100, 255, 0);
            //$gameScreen.picture(13).drill_PLAZ_setZIndex(16.5);            
        } else {
			$gameScreen.showPicture(12, "mio_tachie_handpose1", 0, posX, posY, 100, 100, 255, 0);
		}
		    
        $gameScreen.showPicture(19, "mio_tachie_bowknot", 0, posX, posY, 100, 100, 255, 0);
        $gameScreen.showPicture(20, "mio_tachie_ahoge", 0, ahogeX, posY, 100, 100, 255, 0);
    }

    // 建立图层图钉
    $gameNumberArray.value(41).forEach(n => {
        if (n !== 16 && n !== 20) {
            if ($gameScreen.picture(n)) {
                $gameScreen.picture(n).drill_PTh_bindPic(16);
            }
        }
    });
};


//游戏时间流逝和自动进位
chahuiUtil.systemTimeProgression = function (time) {

if (!time) time = 0;

if (time > 0) {	
let text = "\\fn[RiiTegakiFude]\\dDCOG[11:1:1:1]+" + time;
$gameTemp.drill_GFTT_createSimple( [1845, 70], text, 1, 6, 60 );	
let randomSeArray = ["パパッ", "パッ", "ペタッ"];
let randomSe = randomSeArray[Math.floor(Math.random() * randomSeArray.length)];	
AudioManager.playSe({ name: randomSe, volume: 90, pitch: 100, pan: 0 });
}
	
var currentHour = $gameSystem.hour();
var currentMinute = $gameSystem.minute();
currentMinute += time;

if (currentMinute >= 60) {
    var extraHours = Math.floor(currentMinute / 60);
    currentMinute = currentMinute % 60;
    currentHour += extraHours;
}  if (currentHour >= 24) {
    currentHour = currentHour % 24;  
	$gameSystem.add_day(1);
}
$gameSystem.set_hour(currentHour);
$gameSystem.set_minute(currentMinute);  	

};

//妹妹小人按钮显示初始化
chahuiUtil.imoutoChibiButtonInitialization = function () {
    $gameSystem._drill_DCB_curStyle = 14;

    let style = [];
    style.push("button_touch2","button_talk","button_skinShip");

    if ($gameSystem.hour() <= 20 && $gameSwitches.value(500)) {  //一起玩游戏
        style.push("button_game");
    } else if ($gameSystem.hour() === 20 && $gameSwitches.value(485)) {  //去客厅看电视
        style.push("button_television");
    } 
	
    DrillUp.g_DCB_data[13].btn_src = style;
    DrillUp.g_DCB_data[13].x = 545;
    DrillUp.g_DCB_data[13].y = 445;    
	
};

chahuiUtil.quickInteractionIconInitialize = function() {

   if ($gameMap.getGroupBulletListQJ('imoutoUtil').length == 0) {
	  QJ.MPMZ.tl._imoutoUtilCheckInitialization(true);
   }
    
   let iconScale = 1;
   let collisionBox = 24;  
	// 移动端适配
   if ( Utils.isMobileDevice() ) {
	   iconScale = 1.5;   
   }
	
   if ($gameMap.getGroupBulletListQJ('optionButton').length == 0) {
    var optionButton = QJ.MPMZ.Shoot({
    groupName:["optionButton","imoutoUtilIcon"],
    img:"imoutoUtil/button_option",
	initialRotation:['S',0],
    position:[['S',1694],['S',502]],
	z:"A",
	blendMode:0,
    imgRotation:['S',0],
	moveType: ['S',0],
    opacity:'0|0~30/1~99999|1',
	scale:iconScale,
	collisionBox:['C',collisionBox],
	anchor:[0.56,0.55],
    existData:[ 
	  { t: ['S', '!this._activated', false], d: [1, 30, 1.5], a: ["S","SoundManager.playOk();$gameMap.steupCEQJ(48,1,{optionFunction:true})"] },
	  { t: ['S', '$gameMessage.isBusy()||$gameMap.isAnyEventStartingQJ()', true], d: [1, 30, 1.5],c: ['S', 'this.time>30'] },
	],
	moveF:[
	  
	],
    timeline:['S',0,120,[180,5,60]],
   });
   }

   if ($gameMap.getGroupBulletListQJ('saveButton').length == 0) {
    var saveButton = QJ.MPMZ.Shoot({
    groupName:["saveButton","imoutoUtilIcon"],
    img:"imoutoUtil/button_save",
	initialRotation:['S',0],
    position:[['S',148],['S',525]],
	z:"A",
	blendMode:0,
    imgRotation:['S',0],
	moveType: ['S',0],
    opacity:'0|0~30/1~99999|1',
	scale:iconScale,
	collisionBox:['C',collisionBox],
	anchor:[0.56,0.55],
    existData:[ 
	  { t: ['S', '!this._activated', false], d: [1, 30, 1.5], a: ["S","SoundManager.playOk();$gameMap.steupCEQJ(48,1,{saveFunction:true})"] },
	  { t: ['S', '$gameMessage.isBusy()||$gameMap.isAnyEventStartingQJ()', true], d: [1, 30, 1.5], c: ['S', 'this.time>30'] },
	],
	moveF:[
	  
	],
    timeline:['S',0,120,[180,5,60]],
   });
   }

   if ($gameMap.getGroupBulletListQJ('exitButton').length == 0) {
    var exitButton = QJ.MPMZ.Shoot({
    groupName:["exitButton","imoutoUtilIcon"],
    img:"imoutoUtil/button_exit",
	initialRotation:['S',0],
    position:[['S',315],['S',715]],
	z:"A",
	blendMode:0,
    imgRotation:['S',0],
	moveType: ['S',0],
    opacity:'0|0~30/1~99999|1',
	scale:iconScale,
	collisionBox:['C',collisionBox],
	anchor:[0.56,0.55],
    existData:[ 
	  { t: ['S', '!this._activated', false], d: [1, 30, 1.5], a: ["S","SoundManager.playOk();$gameMap.steupCEQJ(48,1,{exitFunction:true})"] },
	  { t: ['S', '$gameMessage.isBusy()||$gameMap.isAnyEventStartingQJ()', true], d: [1, 30, 1.5], c: ['S', 'this.time>30'] },
	],
	moveF:[
	  
	],
    timeline:['S',0,120,[180,5,60]],
   });
   }   

   if ($gameMap.getGroupBulletListQJ('sleepButton').length == 0) {
    var sleepButton = QJ.MPMZ.Shoot({
    groupName:["sleepButton","imoutoUtilIcon"],
    img:"imoutoUtil/button_sleep",
	initialRotation:['S',0],
    position:[['S',1528],['S',480]],
	z:"A",
	blendMode:0,
    imgRotation:['S',0],
	moveType: ['S',0],
    opacity:'0|0~30/1~99999|1',
	scale:iconScale,
	collisionBox:['C',collisionBox],
	anchor:[0.56,0.55],
    existData:[ 
	  { t: ['S', '!this._activated', false], d: [1, 30, 1.5], a: ["S","SoundManager.playOk();$gameMap.event(24).steupCEQJ(1)"] },
	  { t: ['S', '$gameMessage.isBusy()||$gameMap.isAnyEventStartingQJ()', true], d: [1, 30, 1.5], c: ['S', 'this.time>30'] },
	],
	moveF:[
	  
	],
    timeline:['S',0,120,[180,5,60]],
   });
   }   

    // 喝茶选项图标
	if ( $gameMap.getGroupBulletListQJ('teaButton').length == 0 && $gameSwitches.value(493) ) {
    var teaButton = QJ.MPMZ.Shoot({
    groupName:["teaButton","imoutoUtilIcon"],
    img:"imoutoUtil/button_tea",
	initialRotation:['S',0],
    position:[['S',180],['S',610]],
	z:"A",
	blendMode:0,
    imgRotation:['S',0],
	moveType: ['S',0],
    opacity:'0|0~30/1~99999|1',
	scale:iconScale,
	collisionBox:['C',collisionBox],
	anchor:[0.56,0.55],
    existData:[ 
	  { t: ['S', '!this._activated', false], d: [1, 30, 1.5], a: ["S","SoundManager.playOk();$gameMap.event(44).steupCEQJ(1)"] },
	  { t: ['S', '$gameMessage.isBusy()||$gameMap.isAnyEventStartingQJ()', true], d: [1, 30, 1.5], c: ['S', 'this.time>30'] },
	],
	moveF:[
	  //[30,6,chahuiUtil.imoutoUtilIconClickDetection],
	],
    timeline:['S',0,120,[180,5,60]],
   });
	}

};


//读取妹妹的心之容器状态
chahuiUtil.imoutoHeartContainerInitialization = function() {

    let max = 100 + ($gameVariables.value(15) * 100);
    let dataBind = $gameSystem._drill_GFV_bindTank[7];
    dataBind['slot_list'][0]['level_max'] = max;
    dataBind['commandParamChanged'] = true;

    // 若满足红心转化条件
    //    (好感度17 >= max && 红心<7)，则+1颗红心，清空好感度
    if ($gameVariables.value(17) >= max && $gameVariables.value(15) < 7) {
        $gameVariables.setValue(15, $gameVariables.value(15) + 1);
        $gameVariables.setValue(17, 0);
        AudioManager.playSe({ 
            name: "032myuu_YumeSE_MassagePositive01", 
            volume: 70, 
            pitch: 100, 
            pan: 0 
        });
    }

    // 红心不足时无法发生紫心转化
    if ($gameVariables.value(16) >= 100 && $gameVariables.value(15) < 1) {
        $gameVariables.setValue(16, 99);
    }

    // 若满足紫心转化条件
    if ($gameVariables.value(16) >= 100 && $gameVariables.value(15) >= 1) {
        let curPurple = $gameVariables.value(18) + 1;
        if (curPurple > 7) curPurple = 7;  // 紫心最大7颗
        $gameVariables.setValue(18, curPurple);
        $gameVariables.setValue(16, 0);
        AudioManager.playSe({ 
            name: "039myuu_YumeSE_FukidashiHeart01", 
            volume: 80, 
            pitch: 90, 
            pan: 0 
        });
    }

    // 构造心形UI

    let redHeartCount = $gameVariables.value(15);   
    let purpleHeartCount = $gameVariables.value(18); 

    const maxHearts = 7;
    const displayedRedHearts = Math.min(redHeartCount, maxHearts);
    const displayedPurpleHearts = Math.min(purpleHeartCount, displayedRedHearts);

    const emptyType       = 0;
    const purpleHeartType = 2;
    const redHeartType    = 3;

    // 构造心形表示数组
    let heartDisplay = Array(maxHearts).fill(emptyType);

    // 先填红心
    for (let i = 0; i < displayedRedHearts; i++) {
        heartDisplay[i] = redHeartType;
    }
    // 再转化紫心
    for (let i = 0; i < displayedPurpleHearts; i++) {
        heartDisplay[i] = purpleHeartType;
    }
    const heartDisplayValue = parseInt(heartDisplay.join(''), 10);
    $gameVariables.setValue(34, heartDisplayValue);
};

//检查指定装备装备数量
chahuiUtil.checkEquippedItem = function(baseItemId) {
    var actor = $gameParty.leader(); 
    var equips = actor.equips();    
    var equippedCount = 0;         

    for (var i = 0; i < equips.length; i++) {
        var item = equips[i];
        if (item && item.baseItemId === baseItemId) {
            equippedCount++; 
        }
    }

    return equippedCount
};

//丢弃指定装备
chahuiUtil.removeAndDiscardEquip = function(equipId) {
    var actor = $gameParty.leader(); 
    var equips = actor.equips();    

    for (var i = 0; i < equips.length; i++) {
        var item = equips[i];
        if (item && item.etypeId === 2 && item.baseItemId === equipId) {
            // 移除装备
            actor.changeEquipById(i + 1, null);
            // 丢弃装备
            var obj = $dataArmors[item.id]; 
            $gameParty.loseItem(obj, 1);
            break; 
        }
    }
};

// 天气自定义条件变化
chahuiUtil.customWeatherConditionChanges = function () {
	
    // 重置一系列事件开关
	let AtsuatsuBouzu = false;
	$gameSelfSwitches.setValue([1, 41, 'A'], false);
	$gameSelfSwitches.setValue([1, 41, 'B'], false);
    $gameSelfSwitches.setValue([1, 94, 'D'], false);
	$gameSelfVariables.setValue([1, 2, 'ahogePulled'], 0); // 重置拔呆毛次数
	$gameSelfVariables.setValue([4, 25, "level"], 0); // 重置问胖次次数
	$gameSelfSwitches.setValue([4, 13, 'B'], false);  // 重置偷窥被发现开关
	$gameSwitches.setValue(28, false);    // 自动更新履历标记
	$gameSwitches.setValue(30, false);    // 重置喝茶界面的字体和UI
	$gameSwitches.setValue(55, false);    // 重置料理界面的字体和UI
    $gameSwitches.setValue(123, false);    // 炎热天气标记
    $gameSwitches.setValue(124, false);    // 妹妹早起标记
    $gameSwitches.setValue(440, false);    // 寿司布丁标记

    // 初始化天气预报数组
    if ($gameNumberArray.value(30).length !== 7) {
		$gameNumberArray.setValue(30,[]);
        for (let i = 0; i < 7; i++) {
            $gameNumberArray.value(30).push(dingk.Loot.calculateRandomItemIndex(2));
        }
    }

    // 当天的天气取第一个元素
    let todayWeather = $gameNumberArray.value(30).shift();

    // 娃娃影响
    if (chahuiUtil.checkEquippedItem(126) > 0) {
		todayWeather = 400;
		chahuiUtil.removeAndDiscardEquip(126);
	}	
    if (chahuiUtil.checkEquippedItem(127) > 0) {
		todayWeather = 401;
		chahuiUtil.removeAndDiscardEquip(127);
	}
	// 炎热天气娃娃
    if (chahuiUtil.checkEquippedItem(125) > 0) {
		todayWeather = 400;
		chahuiUtil.removeAndDiscardEquip(125);
		AtsuatsuBouzu = true;
	}	
    if (chahuiUtil.checkEquippedItem(128) > 0) {
		todayWeather = 402;
		chahuiUtil.removeAndDiscardEquip(128);
	}	
	
    // 补充新的一天的天气
    $gameNumberArray.value(30).push(dingk.Loot.calculateRandomItemIndex(2));
    let mappedValue;

    // 从掉落物ID转化成天气ID
    switch (todayWeather) {
        case 400:
            mappedValue = 0;
            break;
        case 401:
            mappedValue = 1;
            break;
        case 402:
            mappedValue = 2;
            break;
        default:
            mappedValue = 0; 
            break;
    }
    $gameVariables.setValue(60, mappedValue);

    // 晴天的变化
    if (mappedValue === 0) {
		
    }

    // 雨天的变化
    if (mappedValue === 2) {
        if ($gameVariables.value(60) === 2) {
            let abyss = $gameNumberArray.value(99);
            $gameSystem.deleteSaveDataSpawnEventMapQJ(abyss, true);
            abyss.forEach(function (mapIndex) {
                $gameSelfVariables.setValue([mapIndex, 1, 'rainyCreation'], 2);
            });
        }
    }
};

// 平滑改变BGM音调
chahuiUtil.changeBgmPitch = function(targetRate, duration) {

        if (!AudioManager._currentBgm) return;

        let b = AudioManager._bgmBuffer;

        if (!b || !b._sourceNode || !WebAudio._context) return;

        let now = WebAudio._context.currentTime;

        b._sourceNode.playbackRate.cancelScheduledValues(now);

        let currentRate = b._sourceNode.playbackRate.value;
        b._sourceNode.playbackRate.setValueAtTime(currentRate, now);

        b._sourceNode.playbackRate.linearRampToValueAtTime(targetRate, now + duration);

        b._pitch = targetRate;
		
};

// 多边形碰撞箱预设模板
chahuiUtil.polySets = {
  // 立绘-短裤
  tachieShortpants: [
    { x: 1233, y: 795 },
    { x: 1478, y: 788 },
    { x: 1522, y: 948 },
    { x: 1505, y: 995 },
	{ x: 1211, y: 995 },
	{ x: 1199, y: 934 }	
  ],	
  // 立绘-胖次
  tachiePanties: [
    { x: 1236, y: 806 },
    { x: 1357, y: 840 },
    { x: 1478, y: 800 },
    { x: 1376, y: 908 },
	{ x: 1338, y: 908 }
  ],
  // 立绘-小穴
  tachieOmanko: [
    { x: 1340, y: 890 },
    { x: 1354, y: 875 },
    { x: 1373, y: 897 },
	{ x: 1359, y: 909 }
  ],
  // 立绘-下巴
  tachieChin: [
    { x: 1324, y: 428 },
    { x: 1370, y: 440 },
    { x: 1416, y: 407 },
    { x: 1400, y: 445 },
	{ x: 1352, y: 447 }
  ],  
  // 立绘-锁骨
  tachieClavicle: [
    { x: 1297, y: 478 },
    { x: 1369, y: 488 },
    { x: 1443, y: 480 },
	{ x: 1369, y: 503 },
  ],
  // 立绘-右耳
  tachieRightEar: [
    { x: 1251, y: 387 },
    { x: 1290, y: 372 },
    { x: 1301, y: 400 },
  ], 
  // 立绘-左耳
  tachieLeftEar: [
    { x: 1442, y: 344 },
    { x: 1488, y: 344 },
    { x: 1445, y: 378 },
  ],
  // T恤-衣领
  TshirtCollar: [
    { x: 1295, y: 470 },
    { x: 1310, y: 460 },
    { x: 1385, y: 520 },
    { x: 1508, y: 525 },
    { x: 1508, y: 550 },
    { x: 1378, y: 555 },
    { x: 1320, y: 518 },	
  ],
  // T恤-下摆
  TshirtHem: [
    { x: 1218, y: 845 },
    { x: 1360, y: 875 },
    { x: 1515, y: 886 },
    { x: 1508, y: 942 },
    { x: 1355, y: 940 },
    { x: 1214, y: 900 },	
  ],  
};

// 圆形碰撞箱预设模板
chahuiUtil.circleSets = {

  // 立绘-肚脐
  tachieNavel: {
    cx: 1350,  // 圆心 x
    cy: 760,   // 圆心 y
    r: 30      // 半径
  }, 
  // 立绘-左手
  tachieLeftHand: {
    cx: 1584,  // 圆心 x
    cy: 917,   // 圆心 y
    r: 50      // 半径
  },  
  // 立绘-右手
  tachieRightHand: {
    cx: 1183,  // 圆心 x
    cy: 508,   // 圆心 y
    r: 50      // 半径
  },   
};

// 椭圆形碰撞箱预设模板
chahuiUtil.ellipseSets = {
  // 立绘-摸头
  tachieHead: {
    cx: 1355,     // 椭圆中心 x 坐标
    cy: 240,     // 椭圆中心 y 坐标
    rx: 107,     // x方向半径
    ry: 50       // y方向半径
  }
};
	
// 判断鼠标是否位于指定多边形范围内
chahuiUtil.pointInPolygo = function(presetName) {

    var polygonVertices = chahuiUtil.polySets[presetName];
    if (!polygonVertices) {
        console.error("未找到预设区域名称：" + presetName);
        return false;
    }
	
    var mouseX = _drill_mouse_x;
    var mouseY = _drill_mouse_y;
    
    var inside = false;
    // 使用射线法检测：遍历多边形的每一条边
    for (var i = 0, j = polygonVertices.length - 1; i < polygonVertices.length; j = i++) {
        var xi = polygonVertices[i].x, yi = polygonVertices[i].y;
        var xj = polygonVertices[j].x, yj = polygonVertices[j].y;
        // 如果当前边跨过了水平线 mouseY，计算交点的X坐标
        var intersect = ((yi > mouseY) !== (yj > mouseY)) &&
                        (mouseX < (xj - xi) * (mouseY - yi) / (yj - yi) + xi);
        if (intersect) {
            inside = !inside;
        }
    }
    return inside;
};	

// 判断鼠标是否位于指定圆形范围内
chahuiUtil.pointInCircle = function(presetName) {
    var circle = chahuiUtil.circleSets[presetName];
    if (!circle) {
        console.error("pointInCircle: 未找到圆形预设名称：" + presetName);
        return false;
    }
    // 取鼠标坐标
    var mx = _drill_mouse_x;
    var my = _drill_mouse_y;

    // 圆心(cx, cy) 及半径 r
    var cx = circle.cx;
    var cy = circle.cy;
    var r  = circle.r;

    // 判断 (mx - cx)^2 + (my - cy)^2 <= (r)^2
    var dx = mx - cx;
    var dy = my - cy;
    return (dx * dx + dy * dy <= r * r);
};

// 判断鼠标是否位于指定椭圆形范围内
chahuiUtil.pointInEllipse = function(presetName) {
    var ellipse = chahuiUtil.ellipseSets[presetName];
    if (!ellipse) {
        console.error("pointInEllipse: 未找到椭圆预设名称：" + presetName);
        return false;
    }

    // 当前鼠标坐标
    var mx = _drill_mouse_x;
    var my = _drill_mouse_y;

    // 椭圆中心 & 半径
    var cx = ellipse.cx;
    var cy = ellipse.cy;
    var rx = ellipse.rx;
    var ry = ellipse.ry;

    // 如果半径无效，直接返回false
    if (rx <= 0 || ry <= 0) {
        return false;
    }

    // 计算 (mx - cx)^2 / rx^2 + (my - cy)^2 / ry^2
    var dx = mx - cx;
    var dy = my - cy;
    var val = (dx * dx) / (rx * rx) + (dy * dy) / (ry * ry);

    return val <= 1; // <=1 表示在椭圆内部
};

// 测试用方法：在当前场景中绘制指定预设区域的填充多边形
chahuiUtil.drawPolygonOverlay = function(presetName, color) {
    // 获取预设多边形顶点集合
    var polygonVertices = chahuiUtil.polySets[presetName];
    if (!polygonVertices) {
        console.error("chahuiUtil.drawPolygonOverlay: 未找到预设区域名称：" + presetName);
        return null;
    }
    
    // 如果未提供颜色，则默认使用半透明红色
    color = color || "rgba(255, 0, 0, 0.5)";
    
    // 创建一个覆盖整个游戏窗口的 Bitmap 与 Sprite
    var bitmap = new Bitmap(Graphics.boxWidth, Graphics.boxHeight);
    var sprite = new Sprite(bitmap);
    // 将该 Sprite 设置到较高层级，以便显示在最前
    sprite.z = 10000;
    
    // 获取 Bitmap 的 canvas context 对象进行绘制
    var ctx = bitmap._context;
    if (!ctx) {
        console.error("chahuiUtil.drawPolygonOverlay: 无法获取 Bitmap 上下文");
        return null;
    }
    
    // 开始绘制多边形
    ctx.beginPath();
    // 注意：这里假设预设顶点坐标已经是屏幕坐标
    ctx.moveTo(polygonVertices[0].x, polygonVertices[0].y);
    for (var i = 1; i < polygonVertices.length; i++) {
        ctx.lineTo(polygonVertices[i].x, polygonVertices[i].y);
    }
    ctx.closePath();
    
    // 设置填充色
    ctx.fillStyle = color;
    ctx.fill();
    
    // 通知 Bitmap 更新（使 canvas 的改变生效）
    bitmap._setDirty();
    
    // 将绘制好的 Sprite 添加到当前场景（例如 Scene_Map）中
    if (SceneManager._scene) {
        SceneManager._scene.addChild(sprite);
    } else {
        console.error("chahuiUtil.drawPolygonOverlay: 当前场景不存在，无法添加 Sprite");
    }
    
    // 返回该 Sprite，便于后续可能的移除操作
    return sprite;
};

// 测试用方法：在当前场景中绘制指定预设区域的填充圆形
chahuiUtil.drawCircleOverlay = function(presetName, color) {
    var circle = chahuiUtil.circleSets[presetName];
    if (!circle) {
        console.error("drawCircleOverlay: 未找到圆形预设名称：" + presetName);
        return null;
    }
    // 若不指定颜色，默认半透明红
    color = color || "rgba(255, 0, 0, 0.5)";

    // 创建一个覆盖整个画面的 Bitmap + Sprite
    var bitmap = new Bitmap(Graphics.boxWidth, Graphics.boxHeight);
    var sprite = new Sprite(bitmap);
    sprite.z = 9999; // 或 10000, 保证在最前端显示

    // 获取 canvas context
    var ctx = bitmap._context;
    if (!ctx) {
        console.error("drawCircleOverlay: 无法获取 canvas 上下文");
        return null;
    }

    // 读取圆心与半径
    var cx = circle.cx;
    var cy = circle.cy;
    var r  = circle.r;

    // 开始绘制
    ctx.beginPath();
    ctx.arc(cx, cy, r, 0, Math.PI * 2);
    ctx.closePath();
    ctx.fillStyle = color;
    ctx.fill();

    // 通知位图更新
    bitmap._setDirty();

    // 将 sprite 添加到当前场景
    if (SceneManager._scene) {
        SceneManager._scene.addChild(sprite);
    } else {
        console.error("drawCircleOverlay: 当前无可用场景");
    }

    // 返回 sprite，以便后续可能移除
    return sprite;
};

// 测试用方法：在当前场景中绘制指定预设区域的填充椭圆形
chahuiUtil.drawEllipseOverlay = function(presetName, color) {
    var ellipse = chahuiUtil.ellipseSets[presetName];
    if (!ellipse) {
        console.error("drawEllipseOverlay: 未找到椭圆预设：" + presetName);
        return null;
    }
    color = color || "rgba(255,0,0,0.5)";

    var cx = ellipse.cx, cy = ellipse.cy;
    var rx = ellipse.rx, ry = ellipse.ry;
    if (rx <= 0 || ry <= 0) {
        console.warn("drawEllipseOverlay: 非法椭圆半径 rx/ry");
        return null;
    }

    // 创建一个覆盖画面的 Bitmap+Sprite
    var bitmap = new Bitmap(Graphics.boxWidth, Graphics.boxHeight);
    var sprite = new Sprite(bitmap);
    sprite.z = 9999; // 显示在最前
    var ctx = bitmap._context;
    if (!ctx) {
        console.error("drawEllipseOverlay: 无法获取 canvas context");
        return null;
    }

    // 使用 canvas 的 ellipse 方法
    ctx.beginPath();
    // ellipse(cx, cy, rx, ry, rotation, startAngle, endAngle, anticlockwise)
    ctx.ellipse(cx, cy, rx, ry, 0, 0, Math.PI * 2);
    ctx.closePath();
    ctx.fillStyle = color;
    ctx.fill();

    // 使bitmap刷新
    bitmap._setDirty();

    // 将sprite添加到当前场景
    if (SceneManager._scene) {
        SceneManager._scene.addChild(sprite);
    } else {
        console.error("drawEllipseOverlay: 当前无可用场景");
    }
    return sprite;
};



chahuiUtil.oldSaveWarningText = {
	
    0:[
       ["\\c[10]检查到来自旧版本的存档数据。\n由于一些底层数据发生变动的影响，\n来自0.70前版本的存档文件必须采取\n重置掉一些数据的兼容性处理"],
	   ["\\c[10]请根据需求备份好存档文件，\n以便之后出现更优策略时可以及时复原。 "]
    ],
    1:[
       ["\\c[10]旧バージョンのセーブデータが検出されました。\n一部の基盤データが変更された影響により、\nバージョン0.70以前のセーブデータには、\n一部データのリセットを伴う互換性処理が必要です。"],
	   ["\\c[10]将来的により適切な解決策が示された際に\n迅速に復元できるよう、\nセーブデータのバックアップを必ず実施してください。"]
    ],	
    2:[
       ["\\c[10]Old version save data detected.\nDue to changes in core data structures, save files \nfrom versions prior to 0.70 require compatibility \nadjustments that will reset certain data."],
	   ["\\c[10]Please back up your save files to ensure \nrestoration if improved solutions become available \nin the future."]
    ]	
};

    var _Game_Screen_prototype_showPicture = Game_Screen.prototype.showPicture;
    Game_Screen.prototype.showPictureFromPath = function(pictureId, path, filename, origin, x, y, scaleX, scaleY, opacity, blendMode) {
        var fullPath = path.endsWith('/') ? path : path + '/';
        var combinedFilename = fullPath + filename;
        _Game_Screen_prototype_showPicture.call(this, pictureId, combinedFilename, origin, x, y, scaleX, scaleY, opacity, blendMode);
};

Game_Map.prototype.checkPlayerCombatPower = function() {
        let actor = $gameActors.actor(1);
        let weapon = actor.atk + actor.mat;
        let CRB = actor.cri * $gameVariables.value(114) * 0.01;
        weapon *= (1 - actor.cri + CRB);
        let armor = actor.def + actor.mdf;
        let boost = 1 + 4 * ((actor.mhp - 70) / 930);
        let combatPower = Math.round(boost * 1.2 * weapon + 0.8 * armor + actor.agi);

        let num;
		let style;
        if (combatPower >= $gameVariables.value(113)) {
            num = "+" + (combatPower - $gameVariables.value(113)); 
			style = 7;
        } else {
            num = "-" + ($gameVariables.value(113) - combatPower);
			style = 8;
        }
        //战斗力变动演出
        $gameTemp.drill_GFN_createSimple([100, 80], num.toString(), style, 2, 120);
        $gameVariables.setValue(113, combatPower);
};