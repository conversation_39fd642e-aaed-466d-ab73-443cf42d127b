//=============================================================================
// Drill_CoreOfGaugeMeter.js
//=============================================================================

/*:
 * @plugindesc [v1.7]        系统 - 参数条核心
 * <AUTHOR>
 * 
 * @Drill_LE_param "参数条样式-%d"
 * @Drill_LE_parentKey "---参数条样式%d至%d---"
 * @Drill_LE_var "DrillUp.g_COGM_list_length"
 * 
 * 
 * @help  
 * =============================================================================
 * +++ Drill_CoreOfGaugeMeter +++
 * 作者：Drill_up
 * 如果你有兴趣，也可以来看看更多我写的drill插件哦ヽ(*。>Д<)o゜
 * https://rpg.blue/thread-409713-1-1.html
 * =============================================================================
 * 该插件描述了一个复杂的 参数条 贴图结构，并提供各项扩展操作接口。
 * ★★尽量放在最靠上的位置★★
 *
 * -----------------------------------------------------------------------------
 * ----插件扩展
 * 该插件为基础核心，单独使用没有效果。
 * 需要基于核心才能运行，并作用于子插件：
 * 基于：
 *   - Drill_CoreOfBallistics       系统-弹道核心★★v1.6及以上★★
 * 作用于：
 *   - Drill_GaugeForBoss           UI-高级BOSS生命固定框
 *   - Drill_GaugeForVariable       UI-高级变量固定框
 *   - Drill_GaugeOfBufferTimeBar   UI-缓冲时间条
 *   - Drill_GaugeTimerHud          UI-时间计时器
 *   ……
 * 
 * -----------------------------------------------------------------------------
 * ----设定注意事项
 * 1.插件的作用域：菜单界面、地图界面、战斗界面。
 *   作用于任意贴图。
 * 2.具体可以去看看 "1.系统 > 关于参数条.docx"。
 *   文档中有相关图解，比纯文字容易理解。
 * 弹道：
 *   (1.弹出条的弹道支持情况如下：
 *        极坐标模式    √
 *        直角坐标模式  √
 *        轨道锚点模式  x  (不适合弹出条)
 *        两点式        x  (不适合弹出条)
 *   (2.弹出条的轨迹可以通过弹道设置进行设计。
 *      具体配置方式可以看看 "1.系统 > 关于弹道.docx"。
 * 主体：
 *   (1.参数条是贴图。用于实时显示生命、魔法、时间等变量值。
 *   (2.参数条有下列固定且硬性结构：
 *      只能为长方形、中心锚点在左上角、只从左向右伸缩、没有外框。
 *   (3.在上述固定结构的基础上，
 *      你可以使用遮罩做成平行四边形或圆角矩形，
 *      也可以修改旋转角度使其看起来为 从右向左 或 从下往上 的伸缩结构。
 * 参数条与外框：
 *   (1.参数条主体是一个完全裸露的条，没有外框。
 *   (2.相关子插件会提供外框的设计，比如 2框+1参数条，2框+3参数条 的结
 *      构。注意，参数条如果旋转了，外框也需要旋转。
 * 段：
 *   (1.段 表示 参数条图片 被分割的贴图部分。
 *   (2.段上限 表示 单段 能够容纳的最大参数值。公式为：
 *      当前参数值 / 段上限 = 当前段长度 / 资源图片长度
 *   (3.段 具有多段结构，通过 段数 来划分。
 *      段 具有流动效果，通过 段长度 来划分。
 * 凹槽条：
 *   (1.凹槽条是只处于上段与下段中间的条。当参数值（比如生命值）被打出
 *      空缺时，凹槽条不会立即扣除而留下红印，停留一段时间后再缩短。
 *   (2.多段时，如果一整段被你打掉了。凹槽条会立即结束延迟，开始缩短，
 *      直到完全缩短为0后，再从下一段开始重新计算凹槽条缩短
 * 弹出条：
 *   (1.参数值减少时，上段会切掉减少的部分，形成弹出条，用于播放 段的
 *      扣除动画效果。
 *   (2.注意，弹出条是不会被遮罩挡住的。如果你的遮罩遮挡后是一个不规则
 *      形状，那么弹出条弹出的形状仍然为长方形不变。
 * 粒子：
 *   (1.粒子效果只在参数条内部冒出。
 * 游标：
 *   (1.游标是跟随当前条进度移动的一个贴图。
 *      可以是单张贴图，也可以是gif贴图。
 *   (2.游标会根据加满的情况浮动，默认情况下，只要 单段 加满了，游标则
 *      会一直处于满状态。多段情况下，需要开启多段复位，实现游标复位。
 * 加满动画：
 *   (1.加满动画 是指参数条从无到有的一个动画过程。
 *   (2.部分子插件会屏蔽此功能，比如时间条，时间条是持续减少/增加的，
 *      不需要加满动画。
 *
 * -----------------------------------------------------------------------------
 * ----关联文件
 * 资源路径：img/Special__meter （Special后面有两个下划线）
 * 先确保项目img文件夹下是否有Special__meter文件夹。
 * 要查看所有关联资源文件的插件，可以去看看"插件清单.xlsx"。
 * 如果没有，需要自己建立。需要配置资源文件：
 * 
 * 样式1 资源-参数条
 * 样式1 资源-参数条遮罩
 * 样式1 资源-凹槽条
 * 样式1 资源-粒子
 * 样式1 资源-游标
 * 样式2 ……
 * ……
 * 
 * 参数条的资源非常多，你需要仔细给你的文件分门别类。
 * 
 * -----------------------------------------------------------------------------
 * ----插件性能
 * 测试仪器：   4G 内存，Intel Core i5-2520M CPU 2.5GHz 处理器
 *              Intel(R) HD Graphics 3000 集显 的垃圾笔记本
 *              (笔记本的3dmark综合分：571，鲁大师综合分：48456)
 * 总时段：     20000.00ms左右
 * 对照表：     0.00ms  - 40.00ms （几乎无消耗）
 *              40.00ms - 80.00ms （低消耗）
 *              80.00ms - 120.00ms（中消耗）
 *              120.00ms以上      （高消耗）
 * 工作类型：   持续执行
 * 时间复杂度： o(n^4)*o(参数条数)*o(贴图处理) 每帧
 * 测试方法：   主要基于该核心的子插件来判断。
 * 测试结果：   地图界面，平均消耗为：【52.86ms】
 *              战斗界面，平均消耗为：【43.67ms】
 * 测试方法2：  主菜单界面中显示4个角色固定框x4的参数条。
 * 测试结果2：  菜单界面中，消耗为：【38.83ms】
 * 测试方法3：  同时开7个弹出条开启的时间条物体，在持续时间内制造了210个弹出条。
 * 测试结果3：  测出消耗为：【143.48ms】
 *              （另外牵连弹道核心208.17ms）
 * 
 * 1.插件只在自己作用域下工作消耗性能，在其它作用域下是不工作的。
 *   测试结果并不是精确值，范围在给定值的10ms范围内波动。
 *   更多性能介绍，去看看 "0.性能测试报告 > 关于插件性能.docx"。
 * 2.子插件的参数条消耗，都算作参数条核心的消耗，所以这里的消耗
 *   为不同子插件的相对平均值。
 * 3.参数条有个特殊的地方需要注意，使用 缓冲时间条 时，不建议开启
 *   弹出条 且 同时建立大量时间条物体。这样会造成大量消耗。
 * 
 * -----------------------------------------------------------------------------
 * ----更新日志
 * [v1.0]
 * 完成插件ヽ(*。>Д<)o゜
 * [v1.1]
 * 修复了弹道的多行 自定义公式 中无法执行且出错的bug。
 * [v1.2]
 * 重新规划 瞬间缩短、匀速缩短、弹性缩短三个设置。
 * [v1.3]
 * 分离并添加了 瞬间伸长、匀速伸长、弹性伸长 设置。
 * [v1.4]
 * 优化了内部结构，减少性能消耗。
 * [v1.5]
 * 整理规范了插件的数据结构。
 * [v1.6]
 * 优化了内部结构，减少性能消耗。
 * [v1.7]
 * 设置了自动预加载资源的功能。
 * 
 * 
 *
 * @param 是否启用预加载
 * @type boolean
 * @on 启用
 * @off 关闭
 * @desc 核心中所有配置的参数条资源，都在游戏初始化时执行预加载。
 * @default true
 *
 * @param ---参数条样式 1至20---
 * @default
 *
 * @param 参数条样式-1
 * @parent ---参数条样式 1至20---
 * @type struct<GaugeMeter>
 * @desc 配置参数条的样式信息。
 * @default {"标签":"==时间条-原始==","---主体---":"","整体旋转角度":"0","资源-参数条":"时间精简风格-段资源","资源-参数条遮罩":"","---段---":"","段数":"8","段是否循环":"true","缩短方式":"瞬间缩短","弹性缩短比":"15","匀速缩短速度":"2.5","伸长方式":"瞬间伸长","弹性伸长比":"15","匀速伸长速度":"10.5","是否使用流动效果":"false","流动方向":"从右往左","流动速度":"0.5","流动段划分模式":"三等份划分","段长度":"0","---凹槽条---":"","是否启用凹槽条":"false","资源-凹槽条":"凹槽条-默认","扣除速度":"15.0","扣除延迟":"60","连续扣除是否刷新延迟":"true","---弹出条---":"","是否启用弹出效果":"false","弹出条块模式":"当前参数条","弹出条弹道":"{}","弹出条最大数量":"30","---粒子效果---":"","是否启用粒子效果":"false","资源-粒子":"参数条-默认粒子","粒子出现模式":"底部出现","粒子X速度":"0","粒子Y速度":"-1.5","粒子数量":"20","粒子持续时间":"20","---游标---":"","是否启用游标":"false","资源-游标":"[]","动画帧间隔":"4","是否倒放":"false","偏移-游标 X":"0","偏移-游标 Y":"0","游标显示模式":"一直显示","是否启用多段复位":"false","遮罩是否能遮挡游标":"false"}
 *
 * @param 参数条样式-2
 * @parent ---参数条样式 1至20---
 * @type struct<GaugeMeter>
 * @desc 配置参数条的样式信息。
 * @default {"标签":"==时间条-游标==","---主体---":"","整体旋转角度":"0","资源-参数条":"时间精简风格-段资源","资源-参数条遮罩":"","---段---":"","段数":"8","段是否循环":"true","缩短方式":"匀速缩短","弹性缩短比":"15","匀速缩短速度":"2.5","伸长方式":"匀速伸长","弹性伸长比":"15","匀速伸长速度":"2.5","是否使用流动效果":"false","流动方向":"从右往左","流动速度":"0.5","流动段划分模式":"三等份划分","段长度":"0","---凹槽条---":"","是否启用凹槽条":"false","资源-凹槽条":"凹槽条-默认","扣除速度":"15.0","扣除延迟":"60","连续扣除是否刷新延迟":"true","---弹出条---":"","是否启用弹出效果":"false","弹出条块模式":"当前参数条","弹出条弹道":"{}","弹出条最大数量":"30","---粒子效果---":"","是否启用粒子效果":"false","资源-粒子":"参数条-默认粒子","粒子出现模式":"底部出现","粒子X速度":"0","粒子Y速度":"-1.5","粒子数量":"20","粒子持续时间":"20","---游标---":"","是否启用游标":"true","资源-游标":"[\"时间精简风格-游标\"]","动画帧间隔":"4","是否倒放":"false","偏移-游标 X":"0","偏移-游标 Y":"0","游标显示模式":"一直显示","是否启用多段复位":"false","遮罩是否能遮挡游标":"false"}
 *
 * @param 参数条样式-3
 * @parent ---参数条样式 1至20---
 * @type struct<GaugeMeter>
 * @desc 配置参数条的样式信息。
 * @default {"标签":"==时间条-游标(多段复位)==","---主体---":"","整体旋转角度":"0","资源-参数条":"时间精简风格-段资源","资源-参数条遮罩":"","---段---":"","段数":"8","段是否循环":"true","缩短方式":"匀速缩短","弹性缩短比":"15","匀速缩短速度":"2.5","伸长方式":"匀速伸长","弹性伸长比":"15","匀速伸长速度":"2.5","是否使用流动效果":"false","流动方向":"从右往左","流动速度":"0.5","流动段划分模式":"三等份划分","段长度":"0","---凹槽条---":"","是否启用凹槽条":"false","资源-凹槽条":"凹槽条-默认","扣除速度":"15.0","扣除延迟":"60","连续扣除是否刷新延迟":"true","---弹出条---":"","是否启用弹出效果":"false","弹出条块模式":"当前参数条","弹出条弹道":"{}","弹出条最大数量":"30","---粒子效果---":"","是否启用粒子效果":"false","资源-粒子":"参数条-默认粒子","粒子出现模式":"底部出现","粒子X速度":"0","粒子Y速度":"-1.5","粒子数量":"20","粒子持续时间":"20","---游标---":"","是否启用游标":"true","资源-游标":"[\"时间精简风格-游标\"]","动画帧间隔":"4","是否倒放":"false","偏移-游标 X":"0","偏移-游标 Y":"0","游标显示模式":"一直显示","是否启用多段复位":"true","遮罩是否能遮挡游标":"false"}
 *
 * @param 参数条样式-4
 * @parent ---参数条样式 1至20---
 * @type struct<GaugeMeter>
 * @desc 配置参数条的样式信息。
 * @default {"标签":"==时间条-流动波==","---主体---":"","整体旋转角度":"0","资源-参数条":"时间波形风格-段资源","资源-参数条遮罩":"","---段---":"","段数":"8","段是否循环":"true","缩短方式":"匀速缩短","弹性缩短比":"15","匀速缩短速度":"4.5","伸长方式":"匀速伸长","弹性伸长比":"15","匀速伸长速度":"4.5","是否使用流动效果":"true","流动方向":"从右往左","流动速度":"1.2","流动段划分模式":"指定段长度划分","段长度":"100","---凹槽条---":"","是否启用凹槽条":"false","资源-凹槽条":"凹槽条-默认","扣除速度":"15.0","扣除延迟":"60","连续扣除是否刷新延迟":"true","---弹出条---":"","是否启用弹出效果":"false","弹出条块模式":"当前参数条","弹出条弹道":"{}","弹出条最大数量":"30","---粒子效果---":"","是否启用粒子效果":"false","资源-粒子":"参数条-默认粒子","粒子出现模式":"底部出现","粒子X速度":"0","粒子Y速度":"-1.5","粒子数量":"20","粒子持续时间":"20","---游标---":"","是否启用游标":"false","资源-游标":"[\"时间精简风格-游标\"]","动画帧间隔":"4","是否倒放":"false","偏移-游标 X":"0","偏移-游标 Y":"0","游标显示模式":"一直显示","是否启用多段复位":"true","遮罩是否能遮挡游标":"false"}
 *
 * @param 参数条样式-5
 * @parent ---参数条样式 1至20---
 * @type struct<GaugeMeter>
 * @desc 配置参数条的样式信息。
 * @default {"标签":"==时间条-流动波+游标==","---主体---":"","整体旋转角度":"0","资源-参数条":"时间波形风格-段资源","资源-参数条遮罩":"","---段---":"","段数":"8","段是否循环":"true","缩短方式":"匀速缩短","弹性缩短比":"15","匀速缩短速度":"4.5","伸长方式":"匀速伸长","弹性伸长比":"15","匀速伸长速度":"4.5","是否使用流动效果":"true","流动方向":"从右往左","流动速度":"1.2","流动段划分模式":"指定段长度划分","段长度":"100","---凹槽条---":"","是否启用凹槽条":"false","资源-凹槽条":"凹槽条-默认","扣除速度":"15.0","扣除延迟":"60","连续扣除是否刷新延迟":"true","---弹出条---":"","是否启用弹出效果":"false","弹出条块模式":"当前参数条","弹出条弹道":"{}","弹出条最大数量":"30","---粒子效果---":"","是否启用粒子效果":"false","资源-粒子":"参数条-默认粒子","粒子出现模式":"底部出现","粒子X速度":"0","粒子Y速度":"-1.5","粒子数量":"20","粒子持续时间":"20","---游标---":"","是否启用游标":"true","资源-游标":"[\"时间精简风格-游标\"]","动画帧间隔":"4","是否倒放":"false","偏移-游标 X":"0","偏移-游标 Y":"0","游标显示模式":"一直显示","是否启用多段复位":"false","遮罩是否能遮挡游标":"false"}
 *
 * @param 参数条样式-6
 * @parent ---参数条样式 1至20---
 * @type struct<GaugeMeter>
 * @desc 配置参数条的样式信息。
 * @default {"标签":"==时间条-流动波+游标(多段复位)==","---主体---":"","整体旋转角度":"0","资源-参数条":"时间波形风格-段资源","资源-参数条遮罩":"","---段---":"","段数":"8","段是否循环":"true","缩短方式":"匀速缩短","弹性缩短比":"15","匀速缩短速度":"4.5","伸长方式":"匀速伸长","弹性伸长比":"15","匀速伸长速度":"4.5","是否使用流动效果":"true","流动方向":"从右往左","流动速度":"1.2","流动段划分模式":"指定段长度划分","段长度":"100","---凹槽条---":"","是否启用凹槽条":"false","资源-凹槽条":"凹槽条-默认","扣除速度":"15.0","扣除延迟":"60","连续扣除是否刷新延迟":"true","---弹出条---":"","是否启用弹出效果":"false","弹出条块模式":"当前参数条","弹出条弹道":"{}","弹出条最大数量":"30","---粒子效果---":"","是否启用粒子效果":"false","资源-粒子":"参数条-默认粒子","粒子出现模式":"底部出现","粒子X速度":"0","粒子Y速度":"-1.5","粒子数量":"20","粒子持续时间":"20","---游标---":"","是否启用游标":"true","资源-游标":"[\"时间精简风格-游标\"]","动画帧间隔":"4","是否倒放":"false","偏移-游标 X":"0","偏移-游标 Y":"0","游标显示模式":"一直显示","是否启用多段复位":"true","遮罩是否能遮挡游标":"false"}
 *
 * @param 参数条样式-7
 * @parent ---参数条样式 1至20---
 * @type struct<GaugeMeter>
 * @desc 配置参数条的样式信息。
 * @default {"标签":"==时间条-胶囊==","---主体---":"","整体旋转角度":"0","资源-参数条":"时间精简风格-段资源","资源-参数条遮罩":"时间胶囊风格-遮罩","---段---":"","段数":"8","段是否循环":"true","缩短方式":"瞬间缩短","弹性缩短比":"15","匀速缩短速度":"2.5","伸长方式":"瞬间伸长","弹性伸长比":"15","匀速伸长速度":"10.5","是否使用流动效果":"false","流动方向":"从右往左","流动速度":"0.5","流动段划分模式":"三等份划分","段长度":"0","---凹槽条---":"","是否启用凹槽条":"false","资源-凹槽条":"凹槽条-默认","扣除速度":"15.0","扣除延迟":"60","连续扣除是否刷新延迟":"true","---弹出条---":"","是否启用弹出效果":"false","弹出条块模式":"当前参数条","弹出条弹道":"{}","弹出条最大数量":"30","---粒子效果---":"","是否启用粒子效果":"false","资源-粒子":"参数条-默认粒子","粒子出现模式":"底部出现","粒子X速度":"0","粒子Y速度":"-1.5","粒子数量":"20","粒子持续时间":"20","---游标---":"","是否启用游标":"false","资源-游标":"[]","动画帧间隔":"4","是否倒放":"false","偏移-游标 X":"0","偏移-游标 Y":"0","游标显示模式":"一直显示","是否启用多段复位":"false","遮罩是否能遮挡游标":"false"}
 *
 * @param 参数条样式-8
 * @parent ---参数条样式 1至20---
 * @type struct<GaugeMeter>
 * @desc 配置参数条的样式信息。
 * @default {"标签":"==时间条-垂直胶囊==","---主体---":"","整体旋转角度":"90","资源-参数条":"时间精简风格-段资源","资源-参数条遮罩":"时间胶囊风格-遮罩","---段---":"","段数":"8","段是否循环":"true","缩短方式":"瞬间缩短","弹性缩短比":"15","匀速缩短速度":"2.5","伸长方式":"瞬间伸长","弹性伸长比":"15","匀速伸长速度":"10.5","是否使用流动效果":"false","流动方向":"从右往左","流动速度":"0.5","流动段划分模式":"三等份划分","段长度":"0","---凹槽条---":"","是否启用凹槽条":"false","资源-凹槽条":"凹槽条-默认","扣除速度":"15.0","扣除延迟":"60","连续扣除是否刷新延迟":"true","---弹出条---":"","是否启用弹出效果":"false","弹出条块模式":"当前参数条","弹出条弹道":"{}","弹出条最大数量":"30","---粒子效果---":"","是否启用粒子效果":"false","资源-粒子":"参数条-默认粒子","粒子出现模式":"底部出现","粒子X速度":"0","粒子Y速度":"-1.5","粒子数量":"20","粒子持续时间":"20","---游标---":"","是否启用游标":"false","资源-游标":"[]","动画帧间隔":"4","是否倒放":"false","偏移-游标 X":"0","偏移-游标 Y":"0","游标显示模式":"一直显示","是否启用多段复位":"false","遮罩是否能遮挡游标":"false"}
 *
 * @param 参数条样式-9
 * @parent ---参数条样式 1至20---
 * @type struct<GaugeMeter>
 * @desc 配置参数条的样式信息。
 * @default {"标签":"==时间条-斜向胶囊==","---主体---":"","整体旋转角度":"225","资源-参数条":"时间精简风格-段资源","资源-参数条遮罩":"时间胶囊风格-遮罩","---段---":"","段数":"8","段是否循环":"true","缩短方式":"瞬间缩短","弹性缩短比":"15","匀速缩短速度":"2.5","伸长方式":"瞬间伸长","弹性伸长比":"15","匀速伸长速度":"10.5","是否使用流动效果":"false","流动方向":"从右往左","流动速度":"0.5","流动段划分模式":"三等份划分","段长度":"0","---凹槽条---":"","是否启用凹槽条":"false","资源-凹槽条":"凹槽条-默认","扣除速度":"15.0","扣除延迟":"60","连续扣除是否刷新延迟":"true","---弹出条---":"","是否启用弹出效果":"false","弹出条块模式":"当前参数条","弹出条弹道":"{}","弹出条最大数量":"30","---粒子效果---":"","是否启用粒子效果":"false","资源-粒子":"参数条-默认粒子","粒子出现模式":"底部出现","粒子X速度":"0","粒子Y速度":"-1.5","粒子数量":"20","粒子持续时间":"20","---游标---":"","是否启用游标":"false","资源-游标":"[]","动画帧间隔":"4","是否倒放":"false","偏移-游标 X":"0","偏移-游标 Y":"0","游标显示模式":"一直显示","是否启用多段复位":"false","遮罩是否能遮挡游标":"false"}
 *
 * @param 参数条样式-10
 * @parent ---参数条样式 1至20---
 * @type struct<GaugeMeter>
 * @desc 配置参数条的样式信息。
 * @default {"标签":"==时间条-雕花==","---主体---":"","整体旋转角度":"0","资源-参数条":"时间雕花风格-段资源","资源-参数条遮罩":"时间雕花风格-遮罩","---段---":"","段数":"8","段是否循环":"true","缩短方式":"瞬间缩短","弹性缩短比":"15","匀速缩短速度":"2.5","伸长方式":"瞬间伸长","弹性伸长比":"15","匀速伸长速度":"10.5","是否使用流动效果":"false","流动方向":"从右往左","流动速度":"0.5","流动段划分模式":"三等份划分","段长度":"0","---凹槽条---":"","是否启用凹槽条":"false","资源-凹槽条":"凹槽条-默认","扣除速度":"15.0","扣除延迟":"60","连续扣除是否刷新延迟":"true","---弹出条---":"","是否启用弹出效果":"false","弹出条块模式":"当前参数条","弹出条弹道":"{}","弹出条最大数量":"30","---粒子效果---":"","是否启用粒子效果":"false","资源-粒子":"参数条-默认粒子","粒子出现模式":"底部出现","粒子X速度":"0","粒子Y速度":"-1.5","粒子数量":"20","粒子持续时间":"20","---游标---":"","是否启用游标":"false","资源-游标":"[\"时间精简风格-游标\"]","动画帧间隔":"4","是否倒放":"false","偏移-游标 X":"0","偏移-游标 Y":"0","游标显示模式":"一直显示","是否启用多段复位":"true","遮罩是否能遮挡游标":"false"}
 *
 * @param 参数条样式-11
 * @parent ---参数条样式 1至20---
 * @type struct<GaugeMeter>
 * @desc 配置参数条的样式信息。
 * @default 
 *
 * @param 参数条样式-12
 * @parent ---参数条样式 1至20---
 * @type struct<GaugeMeter>
 * @desc 配置参数条的样式信息。
 * @default {"标签":"==时间条-白火花(弹出条)==","---主体---":"","整体旋转角度":"0","资源-参数条":"时间精简风格-段资源","资源-参数条遮罩":"","---段---":"","段数":"8","段是否循环":"true","缩短方式":"瞬间缩短","弹性缩短比":"15","匀速缩短速度":"2.5","伸长方式":"瞬间伸长","弹性伸长比":"15","匀速伸长速度":"10.5","是否使用流动效果":"false","流动方向":"从右往左","流动速度":"0.5","流动段划分模式":"三等份划分","段长度":"0","---凹槽条---":"","是否启用凹槽条":"false","资源-凹槽条":"凹槽条-默认","扣除速度":"15.0","扣除延迟":"60","连续扣除是否刷新延迟":"true","---弹出条---":"","是否启用弹出效果":"true","弹出条块模式":"白色块","弹出条弹道":"{\"标签\":\"==落下弹道==\",\"移动时长\":\"30\",\"移动模式\":\"直角坐标模式\",\"---极坐标模式---\":\"\",\"速度类型\":\"只初速度\",\"初速度\":\"1.0\",\"速度随机波动量\":\"2.0\",\"加速度\":\"0.0\",\"最大速度\":\"99.0\",\"最小速度\":\"0.0\",\"路程计算公式\":\"\\\"return 0.0\\\"\",\"方向类型\":\"四周扩散(线性)\",\"固定方向\":\"90.0\",\"扇形朝向\":\"45.0\",\"扇形角度\":\"90.0\",\"方向公式\":\"\\\"return 0.0\\\"\",\"---直角坐标模式---\":\"\",\"X轴速度类型\":\"只初速度\",\"X轴初速度\":\"0.0\",\"X轴速度随机波动量\":\"2.0\",\"X轴加速度\":\"0.0\",\"X轴最大速度\":\"99.0\",\"X轴最小速度\":\"0.0\",\"X轴路程计算公式\":\"\\\"return 0.0\\\"\",\"Y轴速度类型\":\"初速度+波动量+加速度\",\"Y轴初速度\":\"1.0\",\"Y轴速度随机波动量\":\"2.0\",\"Y轴加速度\":\"0.05\",\"Y轴最大速度\":\"99.0\",\"Y轴最小速度\":\"0.0\",\"Y轴路程计算公式\":\"\\\"return 0.0\\\"\"}","弹出条最大数量":"30","---粒子效果---":"","是否启用粒子效果":"false","资源-粒子":"参数条-默认粒子","粒子出现模式":"底部出现","粒子X速度":"0","粒子Y速度":"-1.5","粒子数量":"20","粒子持续时间":"20","---游标---":"","是否启用游标":"false","资源-游标":"[]","动画帧间隔":"4","是否倒放":"false","偏移-游标 X":"0","偏移-游标 Y":"0","游标显示模式":"一直显示","是否启用多段复位":"false","遮罩是否能遮挡游标":"false"}
 *
 * @param 参数条样式-13
 * @parent ---参数条样式 1至20---
 * @type struct<GaugeMeter>
 * @desc 配置参数条的样式信息。
 * @default {"标签":"==时间条-黑火花(弹出条)==","---主体---":"","整体旋转角度":"0","资源-参数条":"时间精简风格-段资源","资源-参数条遮罩":"","---段---":"","段数":"8","段是否循环":"true","缩短方式":"瞬间缩短","弹性缩短比":"15","匀速缩短速度":"2.5","伸长方式":"瞬间伸长","弹性伸长比":"15","匀速伸长速度":"10.5","是否使用流动效果":"false","流动方向":"从右往左","流动速度":"0.5","流动段划分模式":"三等份划分","段长度":"0","---凹槽条---":"","是否启用凹槽条":"false","资源-凹槽条":"凹槽条-默认","扣除速度":"15.0","扣除延迟":"60","连续扣除是否刷新延迟":"true","---弹出条---":"","是否启用弹出效果":"true","弹出条块模式":"黑色块","弹出条弹道":"{\"标签\":\"==落下弹道==\",\"移动时长\":\"30\",\"移动模式\":\"直角坐标模式\",\"---极坐标模式---\":\"\",\"速度类型\":\"只初速度\",\"初速度\":\"1.0\",\"速度随机波动量\":\"2.0\",\"加速度\":\"0.0\",\"最大速度\":\"99.0\",\"最小速度\":\"0.0\",\"路程计算公式\":\"\\\"return 0.0\\\"\",\"方向类型\":\"四周扩散(线性)\",\"固定方向\":\"90.0\",\"扇形朝向\":\"45.0\",\"扇形角度\":\"90.0\",\"方向公式\":\"\\\"return 0.0\\\"\",\"---直角坐标模式---\":\"\",\"X轴速度类型\":\"只初速度\",\"X轴初速度\":\"0.0\",\"X轴速度随机波动量\":\"2.0\",\"X轴加速度\":\"0.0\",\"X轴最大速度\":\"99.0\",\"X轴最小速度\":\"0.0\",\"X轴路程计算公式\":\"\\\"return 0.0\\\"\",\"Y轴速度类型\":\"初速度+波动量+加速度\",\"Y轴初速度\":\"1.0\",\"Y轴速度随机波动量\":\"2.0\",\"Y轴加速度\":\"0.05\",\"Y轴最大速度\":\"99.0\",\"Y轴最小速度\":\"0.0\",\"Y轴路程计算公式\":\"\\\"return 0.0\\\"\"}","弹出条最大数量":"30","---粒子效果---":"","是否启用粒子效果":"false","资源-粒子":"参数条-默认粒子","粒子出现模式":"底部出现","粒子X速度":"0","粒子Y速度":"-1.5","粒子数量":"20","粒子持续时间":"20","---游标---":"","是否启用游标":"false","资源-游标":"[]","动画帧间隔":"4","是否倒放":"false","偏移-游标 X":"0","偏移-游标 Y":"0","游标显示模式":"一直显示","是否启用多段复位":"false","遮罩是否能遮挡游标":"false"}
 *
 * @param 参数条样式-14
 * @parent ---参数条样式 1至20---
 * @type struct<GaugeMeter>
 * @desc 配置参数条的样式信息。
 * @default {"标签":"==时间条-火花A(弹出条)==","---主体---":"","整体旋转角度":"0","资源-参数条":"时间精简风格-段资源","资源-参数条遮罩":"","---段---":"","段数":"8","段是否循环":"true","缩短方式":"瞬间缩短","弹性缩短比":"15","匀速缩短速度":"2.5","伸长方式":"瞬间伸长","弹性伸长比":"15","匀速伸长速度":"10.5","是否使用流动效果":"false","流动方向":"从右往左","流动速度":"0.5","流动段划分模式":"三等份划分","段长度":"0","---凹槽条---":"","是否启用凹槽条":"false","资源-凹槽条":"凹槽条-默认","扣除速度":"15.0","扣除延迟":"60","连续扣除是否刷新延迟":"true","---弹出条---":"","是否启用弹出效果":"true","弹出条块模式":"当前参数条","弹出条弹道":"{\"标签\":\"==落下弹道==\",\"移动时长\":\"30\",\"移动模式\":\"直角坐标模式\",\"---极坐标模式---\":\"\",\"速度类型\":\"只初速度\",\"初速度\":\"1.0\",\"速度随机波动量\":\"2.0\",\"加速度\":\"0.0\",\"最大速度\":\"99.0\",\"最小速度\":\"0.0\",\"路程计算公式\":\"\\\"return 0.0\\\"\",\"方向类型\":\"四周扩散(线性)\",\"固定方向\":\"90.0\",\"扇形朝向\":\"45.0\",\"扇形角度\":\"90.0\",\"方向公式\":\"\\\"return 0.0\\\"\",\"---直角坐标模式---\":\"\",\"X轴速度类型\":\"只初速度\",\"X轴初速度\":\"0.0\",\"X轴速度随机波动量\":\"2.0\",\"X轴加速度\":\"0.0\",\"X轴最大速度\":\"99.0\",\"X轴最小速度\":\"0.0\",\"X轴路程计算公式\":\"\\\"return 0.0\\\"\",\"Y轴速度类型\":\"初速度+波动量+加速度\",\"Y轴初速度\":\"1.0\",\"Y轴速度随机波动量\":\"2.0\",\"Y轴加速度\":\"0.05\",\"Y轴最大速度\":\"99.0\",\"Y轴最小速度\":\"0.0\",\"Y轴路程计算公式\":\"\\\"return 0.0\\\"\"}","弹出条最大数量":"30","---粒子效果---":"","是否启用粒子效果":"false","资源-粒子":"参数条-默认粒子","粒子出现模式":"底部出现","粒子X速度":"0","粒子Y速度":"-1.5","粒子数量":"20","粒子持续时间":"20","---游标---":"","是否启用游标":"false","资源-游标":"[]","动画帧间隔":"4","是否倒放":"false","偏移-游标 X":"0","偏移-游标 Y":"0","游标显示模式":"一直显示","是否启用多段复位":"false","遮罩是否能遮挡游标":"false"}
 *
 * @param 参数条样式-15
 * @parent ---参数条样式 1至20---
 * @type struct<GaugeMeter>
 * @desc 配置参数条的样式信息。
 * @default {"标签":"==时间条-火花B(弹出条)==","---主体---":"","整体旋转角度":"0","资源-参数条":"时间精简风格-段资源","资源-参数条遮罩":"","---段---":"","段数":"8","段是否循环":"true","缩短方式":"瞬间缩短","弹性缩短比":"15","匀速缩短速度":"2.5","伸长方式":"瞬间伸长","弹性伸长比":"15","匀速伸长速度":"10.5","是否使用流动效果":"false","流动方向":"从右往左","流动速度":"0.5","流动段划分模式":"三等份划分","段长度":"0","---凹槽条---":"","是否启用凹槽条":"false","资源-凹槽条":"凹槽条-默认","扣除速度":"15.0","扣除延迟":"60","连续扣除是否刷新延迟":"true","---弹出条---":"","是否启用弹出效果":"true","弹出条块模式":"当前参数条","弹出条弹道":"{\"标签\":\"==扩散弹道==\",\"移动时长\":\"30\",\"移动模式\":\"极坐标模式\",\"---极坐标模式---\":\"\",\"速度类型\":\"初速度+波动量+加速度\",\"初速度\":\"1.0\",\"速度随机波动量\":\"2.0\",\"加速度\":\"0.05\",\"最大速度\":\"99.0\",\"最小速度\":\"0.0\",\"路程计算公式\":\"\\\"return 0.0\\\"\",\"方向类型\":\"四周扩散(随机)\",\"固定方向\":\"90.0\",\"扇形朝向\":\"45.0\",\"扇形角度\":\"90.0\",\"方向公式\":\"\\\"return 0.0\\\"\",\"---直角坐标模式---\":\"\",\"X轴速度类型\":\"只初速度\",\"X轴初速度\":\"1.0\",\"X轴速度随机波动量\":\"2.0\",\"X轴加速度\":\"0.0\",\"X轴最大速度\":\"99.0\",\"X轴最小速度\":\"0.0\",\"X轴路程计算公式\":\"\\\"return 0.0\\\"\",\"Y轴速度类型\":\"只初速度\",\"Y轴初速度\":\"1.0\",\"Y轴速度随机波动量\":\"2.0\",\"Y轴加速度\":\"0.0\",\"Y轴最大速度\":\"99.0\",\"Y轴最小速度\":\"0.0\",\"Y轴路程计算公式\":\"\\\"return 0.0\\\"\"}","弹出条最大数量":"30","---粒子效果---":"","是否启用粒子效果":"false","资源-粒子":"参数条-默认粒子","粒子出现模式":"底部出现","粒子X速度":"0","粒子Y速度":"-1.5","粒子数量":"20","粒子持续时间":"20","---游标---":"","是否启用游标":"false","资源-游标":"[]","动画帧间隔":"4","是否倒放":"false","偏移-游标 X":"0","偏移-游标 Y":"0","游标显示模式":"一直显示","是否启用多段复位":"false","遮罩是否能遮挡游标":"false"}
 *
 * @param 参数条样式-16
 * @parent ---参数条样式 1至20---
 * @type struct<GaugeMeter>
 * @desc 配置参数条的样式信息。
 * @default {"标签":"==时间条-火花C(弹出条)==","---主体---":"","整体旋转角度":"0","资源-参数条":"时间精简风格-段资源","资源-参数条遮罩":"","---段---":"","段数":"8","段是否循环":"true","缩短方式":"瞬间缩短","弹性缩短比":"15","匀速缩短速度":"2.5","伸长方式":"瞬间伸长","弹性伸长比":"15","匀速伸长速度":"10.5","是否使用流动效果":"false","流动方向":"从右往左","流动速度":"0.5","流动段划分模式":"三等份划分","段长度":"0","---凹槽条---":"","是否启用凹槽条":"false","资源-凹槽条":"凹槽条-默认","扣除速度":"15.0","扣除延迟":"60","连续扣除是否刷新延迟":"true","---弹出条---":"","是否启用弹出效果":"true","弹出条块模式":"当前参数条","弹出条弹道":"{\"标签\":\"==抛物线弹道==\",\"移动时长\":\"30\",\"移动模式\":\"直角坐标模式\",\"---极坐标模式---\":\"\",\"速度类型\":\"只初速度\",\"初速度\":\"1.0\",\"速度随机波动量\":\"2.0\",\"加速度\":\"0.0\",\"最大速度\":\"99.0\",\"最小速度\":\"0.0\",\"路程计算公式\":\"\\\"return 0.0\\\"\",\"方向类型\":\"四周扩散(线性)\",\"固定方向\":\"90.0\",\"扇形朝向\":\"45.0\",\"扇形角度\":\"90.0\",\"方向公式\":\"\\\"return 0.0\\\"\",\"---直角坐标模式---\":\"\",\"X轴速度类型\":\"初速度+波动量\",\"X轴初速度\":\"-0.5\",\"X轴速度随机波动量\":\"1.0\",\"X轴加速度\":\"0.0\",\"X轴最大速度\":\"99.0\",\"X轴最小速度\":\"0.0\",\"X轴路程计算公式\":\"\\\"return 0.0\\\"\",\"Y轴速度类型\":\"初速度+波动量+加速度\",\"Y轴初速度\":\"-2.0\",\"Y轴速度随机波动量\":\"1.0\",\"Y轴加速度\":\"0.34\",\"Y轴最大速度\":\"99.0\",\"Y轴最小速度\":\"-2.0\",\"Y轴路程计算公式\":\"\\\"return 0.0\\\"\"}","弹出条最大数量":"30","---粒子效果---":"","是否启用粒子效果":"false","资源-粒子":"参数条-默认粒子","粒子出现模式":"底部出现","粒子X速度":"0","粒子Y速度":"-1.5","粒子数量":"20","粒子持续时间":"20","---游标---":"","是否启用游标":"false","资源-游标":"[]","动画帧间隔":"4","是否倒放":"false","偏移-游标 X":"0","偏移-游标 Y":"0","游标显示模式":"一直显示","是否启用多段复位":"false","遮罩是否能遮挡游标":"false"}
 *
 * @param 参数条样式-17
 * @parent ---参数条样式 1至20---
 * @type struct<GaugeMeter>
 * @desc 配置参数条的样式信息。
 * @default {"标签":"==时间条-火花D(弹出条)==","---主体---":"","整体旋转角度":"0","资源-参数条":"时间精简风格-段资源","资源-参数条遮罩":"","---段---":"","段数":"8","段是否循环":"true","缩短方式":"瞬间缩短","弹性缩短比":"15","匀速缩短速度":"2.5","伸长方式":"瞬间伸长","弹性伸长比":"15","匀速伸长速度":"10.5","是否使用流动效果":"false","流动方向":"从右往左","流动速度":"0.5","流动段划分模式":"三等份划分","段长度":"0","---凹槽条---":"","是否启用凹槽条":"false","资源-凹槽条":"凹槽条-默认","扣除速度":"15.0","扣除延迟":"60","连续扣除是否刷新延迟":"true","---弹出条---":"","是否启用弹出效果":"true","弹出条块模式":"白色块","弹出条弹道":"{\"标签\":\"==不移动的弹道==\",\"移动时长\":\"30\",\"移动模式\":\"极坐标模式\",\"---极坐标模式---\":\"\",\"速度类型\":\"只初速度\",\"初速度\":\"0.0\",\"速度随机波动量\":\"0.0\",\"加速度\":\"0.0\",\"最大速度\":\"99.0\",\"最小速度\":\"0.0\",\"路程计算公式\":\"\\\"return 0.0\\\"\",\"方向类型\":\"四周扩散(线性)\",\"固定方向\":\"90.0\",\"扇形朝向\":\"45.0\",\"扇形角度\":\"90.0\",\"方向公式\":\"\\\"return 0.0\\\"\",\"---直角坐标模式---\":\"\",\"X轴速度类型\":\"只初速度\",\"X轴初速度\":\"1.0\",\"X轴速度随机波动量\":\"2.0\",\"X轴加速度\":\"0.0\",\"X轴最大速度\":\"99.0\",\"X轴最小速度\":\"0.0\",\"X轴路程计算公式\":\"\\\"return 0.0\\\"\",\"Y轴速度类型\":\"只初速度\",\"Y轴初速度\":\"1.0\",\"Y轴速度随机波动量\":\"2.0\",\"Y轴加速度\":\"0.0\",\"Y轴最大速度\":\"99.0\",\"Y轴最小速度\":\"0.0\",\"Y轴路程计算公式\":\"\\\"return 0.0\\\"\"}","弹出条最大数量":"30","---粒子效果---":"","是否启用粒子效果":"false","资源-粒子":"参数条-默认粒子","粒子出现模式":"底部出现","粒子X速度":"0","粒子Y速度":"-1.5","粒子数量":"20","粒子持续时间":"20","---游标---":"","是否启用游标":"false","资源-游标":"[]","动画帧间隔":"4","是否倒放":"false","偏移-游标 X":"0","偏移-游标 Y":"0","游标显示模式":"一直显示","是否启用多段复位":"false","遮罩是否能遮挡游标":"false"}
 *
 * @param 参数条样式-18
 * @parent ---参数条样式 1至20---
 * @type struct<GaugeMeter>
 * @desc 配置参数条的样式信息。
 * @default {"标签":"==时间条-进度计==","---主体---":"","整体旋转角度":"0","资源-参数条":"时间进度计风格-段资源","资源-参数条遮罩":"时间进度计风格-遮罩","---段---":"","段数":"8","段是否循环":"true","缩短方式":"匀速缩短","弹性缩短比":"15","匀速缩短速度":"4.5","伸长方式":"匀速伸长","弹性伸长比":"15","匀速伸长速度":"4.5","是否使用流动效果":"true","流动方向":"从左往右","流动速度":"1.1","流动段划分模式":"指定段长度划分","段长度":"100","---凹槽条---":"","是否启用凹槽条":"false","资源-凹槽条":"凹槽条-默认","扣除速度":"15.0","扣除延迟":"60","连续扣除是否刷新延迟":"true","---弹出条---":"","是否启用弹出效果":"false","弹出条块模式":"当前参数条","弹出条弹道":"{}","弹出条最大数量":"30","---粒子效果---":"","是否启用粒子效果":"false","资源-粒子":"参数条-默认粒子","粒子出现模式":"底部出现","粒子X速度":"0","粒子Y速度":"-1.5","粒子数量":"20","粒子持续时间":"20","---游标---":"","是否启用游标":"false","资源-游标":"[\"参数条-默认游标\"]","动画帧间隔":"4","是否倒放":"false","偏移-游标 X":"0","偏移-游标 Y":"0","游标显示模式":"一直显示","是否启用多段复位":"false","遮罩是否能遮挡游标":"false"}
 *
 * @param 参数条样式-19
 * @parent ---参数条样式 1至20---
 * @type struct<GaugeMeter>
 * @desc 配置参数条的样式信息。
 * @default 
 *
 * @param 参数条样式-20
 * @parent ---参数条样式 1至20---
 * @type struct<GaugeMeter>
 * @desc 配置参数条的样式信息。
 * @default 
 * 
 * @param ---参数条样式21至40---
 * @default
 *
 * @param 参数条样式-21
 * @parent ---参数条样式21至40---
 * @type struct<GaugeMeter>
 * @desc 配置参数条的样式信息。
 * @default {"标签":"==BOSS精简风格-生命条==","---主体---":"","整体旋转角度":"0","资源-参数条":"BOSS精简风格-生命条-段资源","资源-参数条遮罩":"","---段---":"","段数":"8","段是否循环":"true","缩短方式":"瞬间缩短","弹性缩短比":"15","匀速缩短速度":"3.5","伸长方式":"弹性伸长","弹性伸长比":"15","匀速伸长速度":"10.5","是否使用流动效果":"true","流动方向":"从右往左","流动速度":"5.0","流动段划分模式":"指定段长度划分","段长度":"543","---凹槽条---":"","是否启用凹槽条":"true","资源-凹槽条":"BOSS精简风格-生命条-凹槽条","扣除速度":"15.0","扣除延迟":"60","连续扣除是否刷新延迟":"true","---弹出条---":"","是否启用弹出效果":"true","弹出条块模式":"当前参数条","弹出条弹道":"{\"标签\":\"==自由落体向下==\",\"移动时长\":\"120\",\"移动模式\":\"直角坐标模式\",\"---极坐标模式---\":\"\",\"速度类型\":\"只初速度\",\"初速度\":\"1.0\",\"速度随机波动量\":\"2.0\",\"加速度\":\"0.0\",\"最大速度\":\"99.0\",\"最小速度\":\"0.0\",\"路程计算公式\":\"\\\"return 0.0\\\"\",\"方向类型\":\"四周扩散(线性)\",\"固定方向\":\"90.0\",\"扇形朝向\":\"45.0\",\"扇形角度\":\"90.0\",\"方向公式\":\"\\\"return 0.0\\\"\",\"---直角坐标模式---\":\"\",\"X轴速度类型\":\"只初速度\",\"X轴初速度\":\"0.0\",\"X轴速度随机波动量\":\"0.0\",\"X轴加速度\":\"0.0\",\"X轴最大速度\":\"99.0\",\"X轴最小速度\":\"0.0\",\"X轴路程计算公式\":\"\\\"return 0.0\\\"\",\"Y轴速度类型\":\"初速度+波动量+加速度\",\"Y轴初速度\":\"0.4\",\"Y轴速度随机波动量\":\"0.8\",\"Y轴加速度\":\"0.05\",\"Y轴最大速度\":\"99.0\",\"Y轴最小速度\":\"0.0\",\"Y轴路程计算公式\":\"\\\"return 0.0\\\"\"}","弹出条最大数量":"30","---粒子效果---":"","是否启用粒子效果":"false","资源-粒子":"参数条-默认粒子","粒子出现模式":"底部出现","粒子X速度":"0","粒子Y速度":"-1.5","粒子数量":"20","粒子持续时间":"20","---游标---":"","是否启用游标":"true","资源-游标":"[\"BOSS精简风格-生命条-游标\"]","动画帧间隔":"4","是否倒放":"false","偏移-游标 X":"0","偏移-游标 Y":"0","游标显示模式":"受伤模式","是否启用多段复位":"true","遮罩是否能遮挡游标":"false"}
 *
 * @param 参数条样式-22
 * @parent ---参数条样式21至40---
 * @type struct<GaugeMeter>
 * @desc 配置参数条的样式信息。
 * @default {"标签":"==BOSS像素风格-生命条==","---主体---":"","整体旋转角度":"0","资源-参数条":"BOSS像素风格-生命条-段资源","资源-参数条遮罩":"","---段---":"","段数":"8","段是否循环":"true","缩短方式":"匀速缩短","弹性缩短比":"15","匀速缩短速度":"3.5","伸长方式":"匀速伸长","弹性伸长比":"15","匀速伸长速度":"10.5","是否使用流动效果":"false","流动方向":"从右往左","流动速度":"5.0","流动段划分模式":"指定段长度划分","段长度":"550","---凹槽条---":"","是否启用凹槽条":"true","资源-凹槽条":"BOSS像素风格-生命条-凹槽条","扣除速度":"15.0","扣除延迟":"60","连续扣除是否刷新延迟":"true","---弹出条---":"","是否启用弹出效果":"true","弹出条块模式":"当前参数条","弹出条弹道":"{\"标签\":\"==自由落体向下==\",\"移动时长\":\"120\",\"移动模式\":\"直角坐标模式\",\"---极坐标模式---\":\"\",\"速度类型\":\"只初速度\",\"初速度\":\"1.0\",\"速度随机波动量\":\"2.0\",\"加速度\":\"0.0\",\"最大速度\":\"99.0\",\"最小速度\":\"0.0\",\"路程计算公式\":\"\\\"return 0.0\\\"\",\"方向类型\":\"四周扩散(线性)\",\"固定方向\":\"90.0\",\"扇形朝向\":\"45.0\",\"扇形角度\":\"90.0\",\"方向公式\":\"\\\"return 0.0\\\"\",\"---直角坐标模式---\":\"\",\"X轴速度类型\":\"只初速度\",\"X轴初速度\":\"0.0\",\"X轴速度随机波动量\":\"0.0\",\"X轴加速度\":\"0.0\",\"X轴最大速度\":\"99.0\",\"X轴最小速度\":\"0.0\",\"X轴路程计算公式\":\"\\\"return 0.0\\\"\",\"Y轴速度类型\":\"初速度+波动量+加速度\",\"Y轴初速度\":\"0.4\",\"Y轴速度随机波动量\":\"0.8\",\"Y轴加速度\":\"0.05\",\"Y轴最大速度\":\"99.0\",\"Y轴最小速度\":\"0.0\",\"Y轴路程计算公式\":\"\\\"return 0.0\\\"\"}","弹出条最大数量":"30","---粒子效果---":"","是否启用粒子效果":"false","资源-粒子":"参数条-默认粒子","粒子出现模式":"底部出现","粒子X速度":"0","粒子Y速度":"-1.5","粒子数量":"20","粒子持续时间":"20","---游标---":"","是否启用游标":"false","资源-游标":"[]","动画帧间隔":"4","是否倒放":"false","偏移-游标 X":"0","偏移-游标 Y":"0","游标显示模式":"受伤模式","是否启用多段复位":"true","遮罩是否能遮挡游标":"false"}
 *
 * @param 参数条样式-23
 * @parent ---参数条样式21至40---
 * @type struct<GaugeMeter>
 * @desc 配置参数条的样式信息。
 * @default {"标签":"==BOSS粗风格-生命条==","---主体---":"","整体旋转角度":"0","资源-参数条":"BOSS粗风格-生命条-段资源","资源-参数条遮罩":"","---段---":"","段数":"8","段是否循环":"true","缩短方式":"瞬间缩短","弹性缩短比":"15","匀速缩短速度":"3.5","伸长方式":"瞬间伸长","弹性伸长比":"15","匀速伸长速度":"10.5","是否使用流动效果":"false","流动方向":"从右往左","流动速度":"0.5","流动段划分模式":"三等份划分","段长度":"0","---凹槽条---":"","是否启用凹槽条":"true","资源-凹槽条":"BOSS粗风格-生命条-凹槽条","扣除速度":"15.0","扣除延迟":"60","连续扣除是否刷新延迟":"true","---弹出条---":"","是否启用弹出效果":"true","弹出条块模式":"当前参数条","弹出条弹道":"{\"标签\":\"==减速扩散弹道==\",\"移动时长\":\"150\",\"移动模式\":\"极坐标模式\",\"---极坐标模式---\":\"\",\"速度类型\":\"初速度+波动量+加速度+最大最小\",\"初速度\":\"4.0\",\"速度随机波动量\":\"1.8\",\"加速度\":\"-0.08\",\"最大速度\":\"99.0\",\"最小速度\":\"0.0\",\"路程计算公式\":\"\\\"return 0.0\\\"\",\"方向类型\":\"四周扩散(随机)\",\"固定方向\":\"90.0\",\"扇形朝向\":\"45.0\",\"扇形角度\":\"90.0\",\"方向计算公式\":\"\\\"return 0.0\\\"\",\"---直角坐标模式---\":\"\",\"直角坐标整体旋转\":\"0.0\",\"X轴速度类型\":\"只初速度\",\"X轴初速度\":\"1.0\",\"X轴速度随机波动量\":\"2.0\",\"X轴加速度\":\"0.0\",\"X轴最大速度\":\"99.0\",\"X轴最小速度\":\"0.0\",\"X轴路程计算公式\":\"\\\"return 0.0\\\"\",\"Y轴速度类型\":\"只初速度\",\"Y轴初速度\":\"1.0\",\"Y轴速度随机波动量\":\"2.0\",\"Y轴加速度\":\"0.0\",\"Y轴最大速度\":\"99.0\",\"Y轴最小速度\":\"0.0\",\"Y轴路程计算公式\":\"\\\"return 0.0\\\"\"}","弹出条最大数量":"30","---粒子效果---":"","是否启用粒子效果":"false","资源-粒子":"参数条-默认粒子","粒子出现模式":"底部出现","粒子X速度":"0","粒子Y速度":"-1.5","粒子数量":"20","粒子持续时间":"20","---游标---":"","是否启用游标":"false","资源-游标":"[\"参数条-默认游标\"]","动画帧间隔":"4","是否倒放":"false","偏移-游标 X":"0","偏移-游标 Y":"0","游标显示模式":"一直显示","是否启用多段复位":"false","遮罩是否能遮挡游标":"false"}
 *
 * @param 参数条样式-24
 * @parent ---参数条样式21至40---
 * @type struct<GaugeMeter>
 * @desc 配置参数条的样式信息。
 * @default {"标签":"==BOSS标准风格-生命条==","---主体---":"","整体旋转角度":"0","资源-参数条":"BOSS标准风格-生命条-段资源","资源-参数条遮罩":"","---段---":"","段数":"8","段是否循环":"true","缩短方式":"瞬间缩短","弹性缩短比":"15","匀速缩短速度":"3.5","伸长方式":"瞬间伸长","弹性伸长比":"15","匀速伸长速度":"10.5","是否使用流动效果":"true","流动方向":"从左往右","流动速度":"4.2","流动段划分模式":"三等份划分","段长度":"0","---凹槽条---":"","是否启用凹槽条":"true","资源-凹槽条":"BOSS标准风格-生命条-凹槽条","扣除速度":"15.0","扣除延迟":"60","连续扣除是否刷新延迟":"true","---弹出条---":"","是否启用弹出效果":"true","弹出条块模式":"当前参数条","弹出条弹道":"{\"标签\":\"==抛物线弹道==\",\"移动时长\":\"90\",\"移动模式\":\"直角坐标模式\",\"---极坐标模式---\":\"\",\"速度类型\":\"只初速度\",\"初速度\":\"1.0\",\"速度随机波动量\":\"2.0\",\"加速度\":\"0.0\",\"最大速度\":\"99.0\",\"最小速度\":\"0.0\",\"路程计算公式\":\"\\\"return 0.0\\\"\",\"方向类型\":\"四周扩散(线性)\",\"固定方向\":\"90.0\",\"扇形朝向\":\"45.0\",\"扇形角度\":\"90.0\",\"方向公式\":\"\\\"return 0.0\\\"\",\"---直角坐标模式---\":\"\",\"X轴速度类型\":\"初速度+波动量\",\"X轴初速度\":\"-0.5\",\"X轴速度随机波动量\":\"1.0\",\"X轴加速度\":\"0.0\",\"X轴最大速度\":\"99.0\",\"X轴最小速度\":\"0.0\",\"X轴路程计算公式\":\"\\\"return 0.0\\\"\",\"Y轴速度类型\":\"初速度+波动量+加速度\",\"Y轴初速度\":\"-2.0\",\"Y轴速度随机波动量\":\"1.0\",\"Y轴加速度\":\"0.34\",\"Y轴最大速度\":\"99.0\",\"Y轴最小速度\":\"-2.0\",\"Y轴路程计算公式\":\"\\\"return 0.0\\\"\"}","弹出条最大数量":"30","---粒子效果---":"","是否启用粒子效果":"false","资源-粒子":"参数条-默认粒子","粒子出现模式":"底部出现","粒子X速度":"0","粒子Y速度":"-1.5","粒子数量":"20","粒子持续时间":"20","---游标---":"","是否启用游标":"false","资源-游标":"[\"参数条-默认游标\"]","动画帧间隔":"4","是否倒放":"false","偏移-游标 X":"0","偏移-游标 Y":"0","游标显示模式":"一直显示","是否启用多段复位":"false","遮罩是否能遮挡游标":"false"}
 *
 * @param 参数条样式-25
 * @parent ---参数条样式21至40---
 * @type struct<GaugeMeter>
 * @desc 配置参数条的样式信息。
 * @default {"标签":"==BOSS标准风格-魔法条==","---主体---":"","整体旋转角度":"0","资源-参数条":"BOSS标准风格-魔法条-段资源","资源-参数条遮罩":"","---段---":"","段数":"1","段是否循环":"false","缩短方式":"匀速缩短","弹性缩短比":"15","匀速缩短速度":"3.5","伸长方式":"匀速伸长","弹性伸长比":"15","匀速伸长速度":"10.5","是否使用流动效果":"true","流动方向":"从左往右","流动速度":"3.4","流动段划分模式":"三等份划分","段长度":"0","---凹槽条---":"","是否启用凹槽条":"false","资源-凹槽条":"凹槽条-默认","扣除速度":"15.0","扣除延迟":"60","连续扣除是否刷新延迟":"true","---弹出条---":"","是否启用弹出效果":"false","弹出条块模式":"当前参数条","弹出条弹道":"{}","弹出条最大数量":"30","---粒子效果---":"","是否启用粒子效果":"false","资源-粒子":"参数条-默认粒子","粒子出现模式":"底部出现","粒子X速度":"0","粒子Y速度":"-1.5","粒子数量":"20","粒子持续时间":"20","---游标---":"","是否启用游标":"false","资源-游标":"[\"参数条-默认游标\"]","动画帧间隔":"4","是否倒放":"false","偏移-游标 X":"0","偏移-游标 Y":"0","游标显示模式":"一直显示","是否启用多段复位":"false","遮罩是否能遮挡游标":"false"}
 *
 * @param 参数条样式-26
 * @parent ---参数条样式21至40---
 * @type struct<GaugeMeter>
 * @desc 配置参数条的样式信息。
 * @default {"标签":"==BOSS标准风格-怒气条==","---主体---":"","整体旋转角度":"0","资源-参数条":"BOSS标准风格-怒气条-段资源","资源-参数条遮罩":"","---段---":"","段数":"1","段是否循环":"false","缩短方式":"匀速缩短","弹性缩短比":"15","匀速缩短速度":"3.5","伸长方式":"匀速伸长","弹性伸长比":"15","匀速伸长速度":"10.5","是否使用流动效果":"true","流动方向":"从左往右","流动速度":"2.3","流动段划分模式":"三等份划分","段长度":"0","---凹槽条---":"","是否启用凹槽条":"false","资源-凹槽条":"凹槽条-默认","扣除速度":"15.0","扣除延迟":"60","连续扣除是否刷新延迟":"true","---弹出条---":"","是否启用弹出效果":"false","弹出条块模式":"当前参数条","弹出条弹道":"{}","弹出条最大数量":"30","---粒子效果---":"","是否启用粒子效果":"false","资源-粒子":"参数条-默认粒子","粒子出现模式":"底部出现","粒子X速度":"0","粒子Y速度":"-1.5","粒子数量":"20","粒子持续时间":"20","---游标---":"","是否启用游标":"false","资源-游标":"[\"参数条-默认游标\"]","动画帧间隔":"4","是否倒放":"false","偏移-游标 X":"0","偏移-游标 Y":"0","游标显示模式":"一直显示","是否启用多段复位":"false","遮罩是否能遮挡游标":"false"}
 *
 * @param 参数条样式-27
 * @parent ---参数条样式21至40---
 * @type struct<GaugeMeter>
 * @desc 配置参数条的样式信息。
 * @default {"标签":"==BOSS反向风格-生命条==","---主体---":"","整体旋转角度":"180","资源-参数条":"BOSS反向风格-生命条-段资源","资源-参数条遮罩":"BOSS反向风格-生命条-遮罩","---段---":"","段数":"8","段是否循环":"true","缩短方式":"瞬间缩短","弹性缩短比":"15","匀速缩短速度":"3.5","伸长方式":"瞬间伸长","弹性伸长比":"15","匀速伸长速度":"10.5","是否使用流动效果":"false","流动方向":"从左往右","流动速度":"4.2","流动段划分模式":"三等份划分","段长度":"0","---凹槽条---":"","是否启用凹槽条":"false","资源-凹槽条":"","扣除速度":"15.0","扣除延迟":"60","连续扣除是否刷新延迟":"true","---弹出条---":"","是否启用弹出效果":"true","弹出条块模式":"白色块","弹出条弹道":"{\"标签\":\"==不移动的轨迹==\",\"移动时长\":\"30\",\"移动模式\":\"极坐标模式\",\"---极坐标模式---\":\"\",\"速度类型\":\"只初速度\",\"初速度\":\"0.0\",\"速度随机波动量\":\"0.0\",\"加速度\":\"0.0\",\"最大速度\":\"99.0\",\"最小速度\":\"0.0\",\"路程计算公式\":\"\\\"return 0.0\\\"\",\"方向类型\":\"四周扩散(线性)\",\"固定方向\":\"90.0\",\"扇形朝向\":\"45.0\",\"扇形角度\":\"90.0\",\"方向公式\":\"\\\"return 0.0\\\"\",\"---直角坐标模式---\":\"\",\"X轴速度类型\":\"初速度+波动量\",\"X轴初速度\":\"-0.5\",\"X轴速度随机波动量\":\"1.0\",\"X轴加速度\":\"0.0\",\"X轴最大速度\":\"99.0\",\"X轴最小速度\":\"0.0\",\"X轴路程计算公式\":\"\\\"return 0.0\\\"\",\"Y轴速度类型\":\"初速度+波动量+加速度\",\"Y轴初速度\":\"-2.0\",\"Y轴速度随机波动量\":\"1.0\",\"Y轴加速度\":\"0.34\",\"Y轴最大速度\":\"99.0\",\"Y轴最小速度\":\"-2.0\",\"Y轴路程计算公式\":\"\\\"return 0.0\\\"\"}","弹出条最大数量":"30","---粒子效果---":"","是否启用粒子效果":"true","资源-粒子":"BOSS反向风格-生命条-粒子","粒子出现模式":"底部出现","粒子X速度":"0","粒子Y速度":"-1.5","粒子数量":"20","粒子持续时间":"20","---游标---":"","是否启用游标":"false","资源-游标":"[\"参数条-默认游标\"]","动画帧间隔":"4","是否倒放":"false","偏移-游标 X":"0","偏移-游标 Y":"0","游标显示模式":"一直显示","是否启用多段复位":"false","遮罩是否能遮挡游标":"false"}
 *
 * @param 参数条样式-28
 * @parent ---参数条样式21至40---
 * @type struct<GaugeMeter>
 * @desc 配置参数条的样式信息。
 * @default {"标签":"==BOSS反向风格-魔法条==","---主体---":"","整体旋转角度":"180","资源-参数条":"BOSS反向风格-魔法条-段资源","资源-参数条遮罩":"BOSS反向风格-魔法条-遮罩","---段---":"","段数":"1","段是否循环":"true","缩短方式":"匀速缩短","弹性缩短比":"15","匀速缩短速度":"3.5","伸长方式":"匀速伸长","弹性伸长比":"15","匀速伸长速度":"10.5","是否使用流动效果":"false","流动方向":"从右往左","流动速度":"0.5","流动段划分模式":"三等份划分","段长度":"0","---凹槽条---":"","是否启用凹槽条":"false","资源-凹槽条":"凹槽条-默认","扣除速度":"15.0","扣除延迟":"60","连续扣除是否刷新延迟":"true","---弹出条---":"","是否启用弹出效果":"false","弹出条块模式":"当前参数条","弹出条弹道":"{}","弹出条最大数量":"30","---粒子效果---":"","是否启用粒子效果":"false","资源-粒子":"参数条-默认粒子","粒子出现模式":"底部出现","粒子X速度":"0","粒子Y速度":"-1.5","粒子数量":"20","粒子持续时间":"20","---游标---":"","是否启用游标":"false","资源-游标":"[\"参数条-默认游标\"]","动画帧间隔":"4","是否倒放":"false","偏移-游标 X":"0","偏移-游标 Y":"0","游标显示模式":"一直显示","是否启用多段复位":"false","遮罩是否能遮挡游标":"false"}
 *
 * @param 参数条样式-29
 * @parent ---参数条样式21至40---
 * @type struct<GaugeMeter>
 * @desc 配置参数条的样式信息。
 * @default 
 *
 * @param 参数条样式-30
 * @parent ---参数条样式21至40---
 * @type struct<GaugeMeter>
 * @desc 配置参数条的样式信息。
 * @default 
 *
 * @param 参数条样式-31
 * @parent ---参数条样式21至40---
 * @type struct<GaugeMeter>
 * @desc 配置参数条的样式信息。
 * @default 
 *
 * @param 参数条样式-32
 * @parent ---参数条样式21至40---
 * @type struct<GaugeMeter>
 * @desc 配置参数条的样式信息。
 * @default 
 *
 * @param 参数条样式-33
 * @parent ---参数条样式21至40---
 * @type struct<GaugeMeter>
 * @desc 配置参数条的样式信息。
 * @default 
 *
 * @param 参数条样式-34
 * @parent ---参数条样式21至40---
 * @type struct<GaugeMeter>
 * @desc 配置参数条的样式信息。
 * @default {"标签":"==战斗简单生命条==","---主体---":"","整体旋转角度":"0","资源-参数条":"战斗简单生命框-生命条","资源-参数条遮罩":"战斗简单生命框-生命条-遮罩","---段---":"","段数":"1","段是否循环":"true","缩短方式":"匀速缩短","弹性缩短比":"15","匀速缩短速度":"1.5","伸长方式":"瞬间伸长","弹性伸长比":"15","匀速伸长速度":"10.5","是否使用流动效果":"true","流动方向":"从右往左","流动速度":"2.5","流动段划分模式":"三等份划分","段长度":"0","---凹槽条---":"","是否启用凹槽条":"true","资源-凹槽条":"战斗简单生命框-凹槽条","扣除速度":"1.35","扣除延迟":"60","连续扣除是否刷新延迟":"true","---弹出条---":"","是否启用弹出效果":"false","弹出条块模式":"当前参数条","弹出条弹道":"{}","弹出条最大数量":"30","---粒子效果---":"","是否启用粒子效果":"false","资源-粒子":"参数条-默认粒子","粒子出现模式":"底部出现","粒子X速度":"0","粒子Y速度":"-1.5","粒子数量":"20","粒子持续时间":"20","---游标---":"","是否启用游标":"false","资源-游标":"[\"参数条-默认游标\"]","动画帧间隔":"4","是否倒放":"false","偏移-游标 X":"0","偏移-游标 Y":"0","游标显示模式":"一直显示","是否启用多段复位":"false","遮罩是否能遮挡游标":"false"}
 *
 * @param 参数条样式-35
 * @parent ---参数条样式21至40---
 * @type struct<GaugeMeter>
 * @desc 配置参数条的样式信息。
 * @default {"标签":"==战斗简单魔法条==","---主体---":"","整体旋转角度":"0","资源-参数条":"战斗简单生命框-魔法条","资源-参数条遮罩":"战斗简单生命框-魔法条-遮罩","---段---":"","段数":"1","段是否循环":"true","缩短方式":"匀速缩短","弹性缩短比":"15","匀速缩短速度":"1.5","伸长方式":"瞬间伸长","弹性伸长比":"15","匀速伸长速度":"10.5","是否使用流动效果":"true","流动方向":"从右往左","流动速度":"2.5","流动段划分模式":"三等份划分","段长度":"0","---凹槽条---":"","是否启用凹槽条":"false","资源-凹槽条":"","扣除速度":"1.35","扣除延迟":"60","连续扣除是否刷新延迟":"true","---弹出条---":"","是否启用弹出效果":"false","弹出条块模式":"当前参数条","弹出条弹道":"{}","弹出条最大数量":"30","---粒子效果---":"","是否启用粒子效果":"false","资源-粒子":"参数条-默认粒子","粒子出现模式":"底部出现","粒子X速度":"0","粒子Y速度":"-1.5","粒子数量":"20","粒子持续时间":"20","---游标---":"","是否启用游标":"false","资源-游标":"[\"参数条-默认游标\"]","动画帧间隔":"4","是否倒放":"false","偏移-游标 X":"0","偏移-游标 Y":"0","游标显示模式":"一直显示","是否启用多段复位":"false","遮罩是否能遮挡游标":"false"}
 *
 * @param 参数条样式-36
 * @parent ---参数条样式21至40---
 * @type struct<GaugeMeter>
 * @desc 配置参数条的样式信息。
 * @default 
 *
 * @param 参数条样式-37
 * @parent ---参数条样式21至40---
 * @type struct<GaugeMeter>
 * @desc 配置参数条的样式信息。
 * @default 
 *
 * @param 参数条样式-38
 * @parent ---参数条样式21至40---
 * @type struct<GaugeMeter>
 * @desc 配置参数条的样式信息。
 * @default 
 *
 * @param 参数条样式-39
 * @parent ---参数条样式21至40---
 * @type struct<GaugeMeter>
 * @desc 配置参数条的样式信息。
 * @default {"标签":"==地图简单生命条==","---主体---":"","整体旋转角度":"0","资源-参数条":"地图简单生命框-生命条","资源-参数条遮罩":"","---段---":"","段数":"1","段是否循环":"false","缩短方式":"匀速缩短","弹性缩短比":"15","匀速缩短速度":"3.5","伸长方式":"瞬间伸长","弹性伸长比":"15","匀速伸长速度":"10.5","是否使用流动效果":"true","流动方向":"从右往左","流动速度":"1.5","流动段划分模式":"三等份划分","段长度":"0","---凹槽条---":"","是否启用凹槽条":"false","资源-凹槽条":"凹槽条-默认","扣除速度":"15.0","扣除延迟":"60","连续扣除是否刷新延迟":"true","---弹出条---":"","是否启用弹出效果":"true","弹出条块模式":"白色块","弹出条弹道":"{\"标签\":\"==减速扩散弹道==\",\"移动时长\":\"150\",\"移动模式\":\"极坐标模式\",\"---极坐标模式---\":\"\",\"速度类型\":\"初速度+波动量+加速度+最大最小\",\"初速度\":\"2.4\",\"速度随机波动量\":\"1.8\",\"加速度\":\"-0.08\",\"最大速度\":\"99.0\",\"最小速度\":\"0.0\",\"路程计算公式\":\"\\\"return 0.0\\\"\",\"方向类型\":\"四周扩散(随机)\",\"固定方向\":\"90.0\",\"扇形朝向\":\"45.0\",\"扇形角度\":\"90.0\",\"方向计算公式\":\"\\\"return 0.0\\\"\",\"---直角坐标模式---\":\"\",\"直角坐标整体旋转\":\"0.0\",\"X轴速度类型\":\"只初速度\",\"X轴初速度\":\"1.0\",\"X轴速度随机波动量\":\"2.0\",\"X轴加速度\":\"0.0\",\"X轴最大速度\":\"99.0\",\"X轴最小速度\":\"0.0\",\"X轴路程计算公式\":\"\\\"return 0.0\\\"\",\"Y轴速度类型\":\"只初速度\",\"Y轴初速度\":\"1.0\",\"Y轴速度随机波动量\":\"2.0\",\"Y轴加速度\":\"0.0\",\"Y轴最大速度\":\"99.0\",\"Y轴最小速度\":\"0.0\",\"Y轴路程计算公式\":\"\\\"return 0.0\\\"\"}","弹出条最大数量":"30","---粒子效果---":"","是否启用粒子效果":"false","资源-粒子":"参数条-默认粒子","粒子出现模式":"底部出现","粒子X速度":"0","粒子Y速度":"-1.5","粒子数量":"20","粒子持续时间":"20","---游标---":"","是否启用游标":"false","资源-游标":"[\"参数条-默认游标\"]","动画帧间隔":"4","是否倒放":"false","偏移-游标 X":"0","偏移-游标 Y":"0","游标显示模式":"一直显示","是否启用多段复位":"false","遮罩是否能遮挡游标":"false"}
 *
 * @param 参数条样式-40
 * @parent ---参数条样式21至40---
 * @type struct<GaugeMeter>
 * @desc 配置参数条的样式信息。
 * @default {"标签":"==地图简单魔法条==","---主体---":"","整体旋转角度":"0","资源-参数条":"地图简单生命框-魔法条","资源-参数条遮罩":"","---段---":"","段数":"1","段是否循环":"false","缩短方式":"匀速缩短","弹性缩短比":"15","匀速缩短速度":"3.5","伸长方式":"瞬间伸长","弹性伸长比":"15","匀速伸长速度":"10.5","是否使用流动效果":"true","流动方向":"从右往左","流动速度":"1.2","流动段划分模式":"三等份划分","段长度":"0","---凹槽条---":"","是否启用凹槽条":"false","资源-凹槽条":"凹槽条-默认","扣除速度":"15.0","扣除延迟":"60","连续扣除是否刷新延迟":"true","---弹出条---":"","是否启用弹出效果":"false","弹出条块模式":"白色块","弹出条弹道":"{}","弹出条最大数量":"30","---粒子效果---":"","是否启用粒子效果":"false","资源-粒子":"参数条-默认粒子","粒子出现模式":"底部出现","粒子X速度":"0","粒子Y速度":"-1.5","粒子数量":"20","粒子持续时间":"20","---游标---":"","是否启用游标":"false","资源-游标":"[\"参数条-默认游标\"]","动画帧间隔":"4","是否倒放":"false","偏移-游标 X":"0","偏移-游标 Y":"0","游标显示模式":"一直显示","是否启用多段复位":"false","遮罩是否能遮挡游标":"false"}
 *
 * @param ---参数条样式41至60---
 * @default
 *
 * @param 参数条样式-41
 * @parent ---参数条样式41至60---
 * @type struct<GaugeMeter>
 * @desc 配置参数条的样式信息。
 * @default {"标签":"==变量框标准风格-参数条==","---主体---":"","整体旋转角度":"0","资源-参数条":"变量框标准风格-段资源","资源-参数条遮罩":"","---段---":"","段数":"1","段是否循环":"true","缩短方式":"匀速缩短","弹性缩短比":"15","匀速缩短速度":"3.5","伸长方式":"匀速伸长","弹性伸长比":"15","匀速伸长速度":"10.5","是否使用流动效果":"false","流动方向":"从右往左","流动速度":"0.5","流动段划分模式":"三等份划分","段长度":"0","---凹槽条---":"","是否启用凹槽条":"false","资源-凹槽条":"凹槽条-默认","扣除速度":"15.0","扣除延迟":"60","连续扣除是否刷新延迟":"true","---弹出条---":"","是否启用弹出效果":"false","弹出条块模式":"当前参数条","弹出条弹道":"{}","弹出条最大数量":"30","---粒子效果---":"","是否启用粒子效果":"false","资源-粒子":"参数条-默认粒子","粒子出现模式":"底部出现","粒子X速度":"0","粒子Y速度":"-1.5","粒子数量":"20","粒子持续时间":"20","---游标---":"","是否启用游标":"true","资源-游标":"[\"变量框标准风格-亮光游标\"]","动画帧间隔":"4","是否倒放":"false","偏移-游标 X":"0","偏移-游标 Y":"0","游标显示模式":"变化模式","是否启用多段复位":"true","遮罩是否能遮挡游标":"false"}
 *
 * @param 参数条样式-42
 * @parent ---参数条样式41至60---
 * @type struct<GaugeMeter>
 * @desc 配置参数条的样式信息。
 * @default {"标签":"==变量框标准风格(多段)-参数条==","---主体---":"","整体旋转角度":"0","资源-参数条":"变量框标准风格-段资源2","资源-参数条遮罩":"","---段---":"","段数":"3","段是否循环":"true","缩短方式":"匀速缩短","弹性缩短比":"15","匀速缩短速度":"3.5","伸长方式":"匀速伸长","弹性伸长比":"15","匀速伸长速度":"10.5","是否使用流动效果":"false","流动方向":"从右往左","流动速度":"0.5","流动段划分模式":"三等份划分","段长度":"0","---凹槽条---":"","是否启用凹槽条":"false","资源-凹槽条":"凹槽条-默认","扣除速度":"15.0","扣除延迟":"60","连续扣除是否刷新延迟":"true","---弹出条---":"","是否启用弹出效果":"false","弹出条块模式":"当前参数条","弹出条弹道":"{}","弹出条最大数量":"30","---粒子效果---":"","是否启用粒子效果":"false","资源-粒子":"参数条-默认粒子","粒子出现模式":"底部出现","粒子X速度":"0","粒子Y速度":"-1.5","粒子数量":"20","粒子持续时间":"20","---游标---":"","是否启用游标":"true","资源-游标":"[\"变量框标准风格-游标1\",\"变量框标准风格-游标2\",\"变量框标准风格-游标3\",\"变量框标准风格-游标2\"]","动画帧间隔":"4","是否倒放":"false","偏移-游标 X":"0","偏移-游标 Y":"0","游标显示模式":"一直显示","是否启用多段复位":"true","遮罩是否能遮挡游标":"false"}
 *
 * @param 参数条样式-43
 * @parent ---参数条样式41至60---
 * @type struct<GaugeMeter>
 * @desc 配置参数条的样式信息。
 * @default {"标签":"==变量框凹槽板风格==","---主体---":"","整体旋转角度":"0","资源-参数条":"变量框凹槽板风格-段资源","资源-参数条遮罩":"变量框凹槽板风格-遮罩","---段---":"","段数":"1","段是否循环":"true","缩短方式":"瞬间缩短","弹性缩短比":"15","匀速缩短速度":"3.5","伸长方式":"瞬间伸长","弹性伸长比":"15","匀速伸长速度":"10.5","是否使用流动效果":"true","流动方向":"从右往左","流动速度":"0.5","流动段划分模式":"三等份划分","段长度":"0","---凹槽条---":"","是否启用凹槽条":"false","资源-凹槽条":"凹槽条-默认","扣除速度":"15.0","扣除延迟":"60","连续扣除是否刷新延迟":"true","---弹出条---":"","是否启用弹出效果":"false","弹出条块模式":"当前参数条","弹出条弹道":"{}","弹出条最大数量":"30","---粒子效果---":"","是否启用粒子效果":"false","资源-粒子":"参数条-默认粒子","粒子出现模式":"底部出现","粒子X速度":"0","粒子Y速度":"-1.5","粒子数量":"20","粒子持续时间":"20","---游标---":"","是否启用游标":"false","资源-游标":"[\"参数条-默认游标\"]","动画帧间隔":"4","是否倒放":"false","偏移-游标 X":"0","偏移-游标 Y":"0","游标显示模式":"一直显示","是否启用多段复位":"false","遮罩是否能遮挡游标":"false"}
 *
 * @param 参数条样式-44
 * @parent ---参数条样式41至60---
 * @type struct<GaugeMeter>
 * @desc 配置参数条的样式信息。
 * @default 
 *
 * @param 参数条样式-45
 * @parent ---参数条样式41至60---
 * @type struct<GaugeMeter>
 * @desc 配置参数条的样式信息。
 * @default 
 *
 * @param 参数条样式-46
 * @parent ---参数条样式41至60---
 * @type struct<GaugeMeter>
 * @desc 配置参数条的样式信息。
 * @default 
 *
 * @param 参数条样式-47
 * @parent ---参数条样式41至60---
 * @type struct<GaugeMeter>
 * @desc 配置参数条的样式信息。
 * @default 
 *
 * @param 参数条样式-48
 * @parent ---参数条样式41至60---
 * @type struct<GaugeMeter>
 * @desc 配置参数条的样式信息。
 * @default 
 *
 * @param 参数条样式-49
 * @parent ---参数条样式41至60---
 * @type struct<GaugeMeter>
 * @desc 配置参数条的样式信息。
 * @default 
 *
 * @param 参数条样式-50
 * @parent ---参数条样式41至60---
 * @type struct<GaugeMeter>
 * @desc 配置参数条的样式信息。
 * @default 
 *
 * @param 参数条样式-51
 * @parent ---参数条样式41至60---
 * @type struct<GaugeMeter>
 * @desc 配置参数条的样式信息。
 * @default 
 *
 * @param 参数条样式-52
 * @parent ---参数条样式41至60---
 * @type struct<GaugeMeter>
 * @desc 配置参数条的样式信息。
 * @default 
 *
 * @param 参数条样式-53
 * @parent ---参数条样式41至60---
 * @type struct<GaugeMeter>
 * @desc 配置参数条的样式信息。
 * @default 
 *
 * @param 参数条样式-54
 * @parent ---参数条样式41至60---
 * @type struct<GaugeMeter>
 * @desc 配置参数条的样式信息。
 * @default 
 *
 * @param 参数条样式-55
 * @parent ---参数条样式41至60---
 * @type struct<GaugeMeter>
 * @desc 配置参数条的样式信息。
 * @default 
 *
 * @param 参数条样式-56
 * @parent ---参数条样式41至60---
 * @type struct<GaugeMeter>
 * @desc 配置参数条的样式信息。
 * @default 
 *
 * @param 参数条样式-57
 * @parent ---参数条样式41至60---
 * @type struct<GaugeMeter>
 * @desc 配置参数条的样式信息。
 * @default 
 *
 * @param 参数条样式-58
 * @parent ---参数条样式41至60---
 * @type struct<GaugeMeter>
 * @desc 配置参数条的样式信息。
 * @default 
 *
 * @param 参数条样式-59
 * @parent ---参数条样式41至60---
 * @type struct<GaugeMeter>
 * @desc 配置参数条的样式信息。
 * @default 
 *
 * @param 参数条样式-60
 * @parent ---参数条样式41至60---
 * @type struct<GaugeMeter>
 * @desc 配置参数条的样式信息。
 * @default 
 * 
 */
/*~struct~GaugeMeter:
 * 
 * @param 标签
 * @desc 只用于方便区分查看的标签，不作用在插件中。
 * @default ==新的参数条==
 *
 * @param ---主体---
 * @desc 
 *
 * @param 整体旋转角度
 * @parent ---主体---
 * @type number
 * @min 0
 * @desc 参数条的整体旋转角度，单位角度。中心锚点在左上角。（逆时针，90度朝下，270度朝上）
 * @default 0
 *
 * @param 资源-参数条
 * @parent ---主体---
 * @desc 参数条的图片资源，注意要与你后面配置的段数吻合。
 * @default (需配置)参数条
 * @require 1
 * @dir img/Special__meter/
 * @type file
 * 
 * @param 资源-参数条遮罩
 * @parent ---主体---
 * @desc 参数条的遮罩资源。注意，如果开启了流动效果，凹槽条的长度需要适配 段长度。
 * @default 
 * @require 1
 * @dir img/Special__meter/
 * @type file
 *
 * @param ---段---
 * @desc 
 * 
 * @param 段数
 * @parent ---段---
 * @type number
 * @min 1
 * @desc 资源中对应的段的数量，系统会将参数条图片资源切成指定数量的段数。
 * @default 4
 * 
 * @param 段是否循环
 * @parent ---段---
 * @type boolean
 * @on 循环
 * @off 不循环
 * @desc 如果参数值比 段数x段上限 的值还要大。循环则从第一段继续计数，而不循环则保持满段状态。
 * @default true
 *
 * @param 缩短方式
 * @parent ---段---
 * @type select
 * @option 瞬间缩短
 * @value 瞬间缩短
 * @option 弹性缩短
 * @value 弹性缩短
 * @option 匀速缩短
 * @value 匀速缩短
 * @desc 你需要考虑 段与弹出条 的组合关系，如果有弹出条，建议设为瞬间缩短。
 * @default 匀速缩短
 *
 * @param 弹性缩短比
 * @parent 缩短方式
 * @desc 缩短方式为 弹性缩短 时，段缩短的速度。注意，弹性缩短为反比例除数，值越大，速度越慢。
 * @default 15
 *
 * @param 匀速缩短速度
 * @parent 缩短方式
 * @desc 缩短方式为 匀速缩短 时，段缩短的速度。单位为像素/帧。
 * @default 3.5
 *
 * @param 伸长方式
 * @parent ---段---
 * @type select
 * @option 瞬间伸长
 * @value 瞬间伸长
 * @option 弹性伸长
 * @value 弹性伸长
 * @option 匀速伸长
 * @value 匀速伸长
 * @desc 参数条的值增加时，参数条的伸长方式。
 * @default 瞬间伸长
 *
 * @param 弹性伸长比
 * @parent 伸长方式
 * @desc 伸长方式为 弹性伸长 时，段伸长的速度。注意，弹性伸长为反比例除数，值越大，速度越慢。
 * @default 15
 *
 * @param 匀速伸长速度
 * @parent 伸长方式
 * @desc 伸长方式为 匀速伸长 时，段伸长的速度。单位为像素/帧。
 * @default 10.5
 *
 * @param 是否使用流动效果
 * @parent ---段---
 * @type boolean
 * @on 流动
 * @off 不流动
 * @desc 注意，设置流动后，素材长度会取三分之一或按指定段长度划分。
 * @default false
 *
 * @param 流动方向
 * @parent 是否使用流动效果
 * @type select
 * @option 从右往左
 * @value 从右往左
 * @option 从左往右
 * @value 从左往右
 * @desc 流动效果的流动方向。
 * @default 从右往左
 *
 * @param 流动速度
 * @parent 是否使用流动效果
 * @desc 段 流动的速度，单位像素/帧。可为小数。
 * @default 0.5
 *
 * @param 流动段划分模式
 * @parent 是否使用流动效果
 * @type select
 * @option 三等份划分
 * @value 三等份划分
 * @option 指定段长度划分
 * @value 指定段长度划分
 * @desc 使用流动效果时，对资源的划分模式。
 * @default 三等份划分
 * 
 * @param 段长度
 * @parent 流动段划分模式
 * @type number
 * @min 0
 * @desc 流动段划分模式中 选择"指定段长度切片"时，段的实际长度。
 * @default 0
 * 
 * @param ---凹槽条---
 * @desc 
 *
 * @param 是否启用凹槽条
 * @parent ---凹槽条---
 * @type boolean
 * @on 启用
 * @off 关闭
 * @desc true - 启用，false - 关闭。
 * @default false
 *
 * @param 资源-凹槽条
 * @parent ---凹槽条---
 * @desc 凹槽条的图片资源。注意，如果开启了流动效果，凹槽条的长度需要适配 段长度。
 * @default (需配置)凹槽条
 * @require 1
 * @dir img/Special__meter/
 * @type file
 *
 * @param 扣除速度
 * @parent ---凹槽条---
 * @desc 凹槽条缩短的速度，单位像素/帧。
 * @default 15.0
 *
 * @param 扣除延迟
 * @parent ---凹槽条---
 * @type number
 * @min 0
 * @desc 凹槽条执行扣除的延迟时间，单位帧。（1秒60帧）
 * @default 60
 *
 * @param 连续扣除是否刷新延迟
 * @parent ---凹槽条---
 * @type boolean
 * @on 刷新
 * @off 不刷新
 * @desc 参数连续扣除时，比如连续受伤，会重新计算延迟时间，这时候你会看见一长条的红色凹槽条，等同于打出的伤害。
 * @default true
 * 
 * @param ---弹出条---
 * @desc 
 *
 * @param 是否启用弹出效果
 * @parent ---弹出条---
 * @type boolean
 * @on 启用
 * @off 关闭
 * @desc true - 启用，false - 关闭。
 * @default false
 *
 * @param 弹出条块模式
 * @parent ---弹出条---
 * @type select
 * @option 当前参数条
 * @value 当前参数条
 * @option 白色块
 * @value 白色块
 * @option 黑色块
 * @value 黑色块
 * @desc 弹出条的块图片模式，当前参数条是指 段的减去部分 。
 * @default 当前参数条
 *
 * @param 弹出条弹道
 * @parent ---弹出条---
 * @type struct<DrillCOGMBallistics>
 * @desc 弹出条弹道运动轨迹的详细配置信息。这里的移动时长是弹出条的持续时间。
 * @default {}
 *
 * @param 弹出条最大数量
 * @parent ---弹出条---
 * @type number
 * @min 0
 * @desc 弹出条的最大数量，一般战斗不会出现大量弹出条，而持续减少的参数量比如时间，会出现大量弹出条。
 * @default 30
 *
 * @param ---粒子效果---
 * @desc 
 *
 * @param 是否启用粒子效果
 * @parent ---粒子效果---
 * @type boolean
 * @on 启用
 * @off 关闭
 * @desc true - 启用，false - 关闭。
 * @default false
 *
 * @param 资源-粒子
 * @parent ---粒子效果---
 * @desc 生命条中的粒子效果的粒子图片资源。
 * @default (需配置)参数条粒子
 * @require 1
 * @dir img/Special__meter/
 * @type file
 *
 * @param 粒子出现模式
 * @parent ---粒子效果---
 * @type select
 * @option 随机出现
 * @value 随机出现
 * @option 左侧出现
 * @value 左侧出现
 * @option 右侧出现
 * @value 右侧出现
 * @option 顶部出现
 * @value 顶部出现
 * @option 底部出现
 * @value 底部出现
 * @desc 上下左右分别对应长方形的四个边的区域。
 * @default 底部出现
 *
 * @param 粒子X速度
 * @parent ---粒子效果---
 * @desc 粒子在x轴方向移动的速度。可为小数，可为负数。
 * @default 0
 *
 * @param 粒子Y速度
 * @parent ---粒子效果---
 * @desc 粒子在y轴方向移动的速度。可为小数，可为负数。
 * @default -1.5
 *
 * @param 粒子数量
 * @parent ---粒子效果---
 * @type number
 * @min 0
 * @desc 条中出现的粒子的数量。
 * @default 20
 *
 * @param 粒子持续时间
 * @parent ---粒子效果---
 * @type number
 * @min 1
 * @desc 粒子出现到粒子消失的时间。如果粒子离开参数条边界，则视为该粒子已经消失。
 * @default 20
 *
 * @param ---游标---
 * @desc 
 *
 * @param 是否启用游标
 * @parent ---游标---
 * @type boolean
 * @on 启用
 * @off 关闭
 * @desc true - 启用，false - 关闭。
 * @default false
 *
 * @param 资源-游标
 * @parent ---游标---
 * @desc 参数条中游标的图片资源。可以为单张，也可以为多张形成gif。
 * @default ["(需配置)参数条游标"]
 * @require 1
 * @dir img/Special__meter/
 * @type file[]
 *
 * @param 动画帧间隔
 * @parent ---游标---
 * @type number
 * @min 1
 * @desc 多帧游标的播放帧间隔，间隔越小，播放速度越快。
 * @default 4
 *
 * @param 是否倒放
 * @parent ---游标---
 * @type boolean
 * @on 倒放
 * @off 正常播放
 * @desc true - 倒放，false - 正常播放。多帧游标的播放顺序。
 * @default false
 *
 * @param 偏移-游标 X
 * @parent ---游标---
 * @desc 以游标浮动的位置为基准，x轴方向偏移，单位像素。
 * @default 0
 *
 * @param 偏移-游标 Y
 * @parent ---游标---
 * @desc 以游标浮动的位置为基准，y轴方向偏移，单位像素。
 * @default 0
 *
 * @param 游标显示模式
 * @parent ---游标---
 * @type select
 * @option 亮光模式
 * @value 亮光模式
 * @option 闪烁模式
 * @value 闪烁模式
 * @option 受伤模式
 * @value 受伤模式
 * @option 增量模式
 * @value 增量模式
 * @option 变化模式
 * @value 变化模式
 * @option 一直显示
 * @value 一直显示
 * @desc 游标的显示模式，详细介绍见文档 "1.系统 > 关于参数条.docx"中游标介绍。
 * @default 一直显示
 *
 * @param 是否启用多段复位
 * @parent ---游标---
 * @type boolean
 * @on 启用
 * @off 关闭
 * @desc 参数有多段时，游标将复位并根据多段的位置浮动。如果不复位，第一层满了，游标将一直停在满的位置。
 * @default false
 *
 * @param 遮罩是否能遮挡游标
 * @parent ---游标---
 * @type boolean
 * @on 遮挡
 * @off 不遮挡
 * @desc 如果你希望游标和参数条一样，能被遮罩遮挡，可以设置ture开启遮挡。
 * @default false
 * 
 *
 */
/*~struct~DrillCOGMBallistics:
 *
 * @param 标签
 * @desc 只用于方便区分查看的标签，不作用在插件中。
 * @default ==新的弹出条弹道==
 *
 * @param 移动时长
 * @type number
 * @min 1
 * @desc 碎片移动的持续时长，过了时间之后，碎片消失或者停止移动，单位帧。
 * @default 120
 *
 * @param 移动模式
 * @type select
 * @option 直角坐标模式
 * @value 直角坐标模式
 * @option 极坐标模式
 * @value 极坐标模式
 * @desc 描述碎片运动的模式。
 * @default 极坐标模式
 * 
 * 
 * @param ---极坐标模式---
 * @desc 
 *
 * @param 速度类型
 * @parent ---极坐标模式---
 * @type select
 * @option 只初速度
 * @value 只初速度
 * @option 初速度+波动量
 * @value 初速度+波动量
 * @option 初速度+波动量+加速度
 * @value 初速度+波动量+加速度
 * @option 初速度+波动量+加速度+最大最小
 * @value 初速度+波动量+加速度+最大最小
 * @option 路程计算公式
 * @value 路程计算公式
 * @desc 描述碎片速度的模式。
 * @default 只初速度
 * 
 * @param 初速度
 * @parent 速度类型
 * @desc 碎片的基本速度，单位 像素/帧。
 * @default 1.0
 * 
 * @param 速度随机波动量
 * @parent 速度类型
 * @desc 碎片速度上下随机浮动的量，单位 像素/帧。比如值为 5.0，则随机浮动范围为 -2.5 ~ 2.5 之间。
 * @default 2.0
 * 
 * @param 加速度
 * @parent 速度类型
 * @desc 碎片的加速度，单位 像素/帧。
 * @default 0.0
 * 
 * @param 最大速度
 * @parent 速度类型
 * @desc 碎片的最大速度，单位 像素/帧。
 * @default 99.0
 * 
 * @param 最小速度
 * @parent 速度类型
 * @desc 碎片的最小速度，单位 像素/帧。
 * @default 0.0
 * 
 * @param 路程计算公式
 * @parent 速度类型
 * @type note
 * @desc 碎片的路程计算公式。可使用 变量和常量 来设计公式，具体看看文档 "1.系统 > 关于弹道.docx"介绍。
 * @default "return 0.0"
 * 
 * @param 方向类型
 * @parent ---极坐标模式---
 * @type select
 * @option 固定方向
 * @value 固定方向
 * @option 四周扩散(线性)
 * @value 四周扩散(线性)
 * @option 四周扩散(随机)
 * @value 四周扩散(随机)
 * @option 四周扩散(抖动)
 * @value 四周扩散(抖动)
 * @option 扇形范围方向(线性)
 * @value 扇形范围方向(线性)
 * @option 扇形范围方向(随机)
 * @value 扇形范围方向(随机)
 * @option 方向计算公式
 * @value 方向计算公式
 * @desc 描述碎片速度的模式。
 * @default 四周扩散(线性)
 * 
 * @param 固定方向
 * @parent 方向类型
 * @desc 类型为"固定方向"时，固定方向的角度值。0朝右，90朝下，180朝左，270朝上。
 * @default 90.0
 * 
 * @param 扇形朝向
 * @parent 方向类型
 * @desc 类型为"扇形范围方向"时，扇形的朝向角度。0朝右，90朝下，180朝左，270朝上。
 * @default 45.0
 * 
 * @param 扇形角度
 * @parent 方向类型
 * @desc 类型为"扇形范围方向"时，扇形弧的角度数。
 * @default 90.0
 * 
 * @param 方向计算公式
 * @parent 方向类型
 * @type note
 * @desc 类型为"方向计算公式"时。可使用 变量和常量 来设计公式，具体看看文档 "1.系统 > 关于弹道.docx"介绍。
 * @default "return 0.0"
 * 
 * @param ---直角坐标模式---
 * @desc 
 * 
 * @param 直角坐标整体旋转
 * @parent ---直角坐标模式---
 * @desc 将下面设计好的xy公式，进行整体旋转，单位角度。
 * @default 0.0
 *
 * @param X轴速度类型
 * @parent ---直角坐标模式---
 * @type select
 * @option 只初速度
 * @value 只初速度
 * @option 初速度+波动量
 * @value 初速度+波动量
 * @option 初速度+波动量+加速度
 * @value 初速度+波动量+加速度
 * @option 初速度+波动量+加速度+最大最小
 * @value 初速度+波动量+加速度+最大最小
 * @option 路程计算公式
 * @value 路程计算公式
 * @desc 描述碎片速度的模式。
 * @default 只初速度
 * 
 * @param X轴初速度
 * @parent X轴速度类型
 * @desc 碎片的基本速度，单位 像素/帧。
 * @default 1.0
 * 
 * @param X轴速度随机波动量
 * @parent X轴速度类型
 * @desc 碎片速度上下随机浮动的量，单位 像素/帧。比如值为 5.0，则随机浮动范围为 -2.5 ~ 2.5 之间。
 * @default 2.0
 * 
 * @param X轴加速度
 * @parent X轴速度类型
 * @desc 碎片的加速度，单位 像素/帧。
 * @default 0.0
 * 
 * @param X轴最大速度
 * @parent X轴速度类型
 * @desc 碎片的最大速度，单位 像素/帧。
 * @default 99.0
 * 
 * @param X轴最小速度
 * @parent X轴速度类型
 * @desc 碎片的最小速度，单位 像素/帧。
 * @default 0.0
 * 
 * @param X轴路程计算公式
 * @parent X轴速度类型
 * @type note
 * @desc 碎片的路程计算公式。可使用 变量和常量 来设计公式，具体看看文档 "1.系统 > 关于弹道.docx"介绍。
 * @default "return 0.0"
 *
 * @param Y轴速度类型
 * @parent ---直角坐标模式---
 * @type select
 * @option 只初速度
 * @value 只初速度
 * @option 初速度+波动量
 * @value 初速度+波动量
 * @option 初速度+波动量+加速度
 * @value 初速度+波动量+加速度
 * @option 初速度+波动量+加速度+最大最小
 * @value 初速度+波动量+加速度+最大最小
 * @option 路程计算公式
 * @value 路程计算公式
 * @desc 描述碎片速度的模式。
 * @default 只初速度
 * 
 * @param Y轴初速度
 * @parent Y轴速度类型
 * @desc 碎片的基本速度，单位 像素/帧。
 * @default 1.0
 * 
 * @param Y轴速度随机波动量
 * @parent Y轴速度类型
 * @desc 碎片速度上下随机浮动的量，单位 像素/帧。比如值为 5.0，则随机浮动范围为 -2.5 ~ 2.5 之间。
 * @default 2.0
 * 
 * @param Y轴加速度
 * @parent Y轴速度类型
 * @desc 碎片的加速度，单位 像素/帧。
 * @default 0.0
 * 
 * @param Y轴最大速度
 * @parent Y轴速度类型
 * @desc 碎片的最大速度，单位 像素/帧。
 * @default 99.0
 * 
 * @param Y轴最小速度
 * @parent Y轴速度类型
 * @desc 碎片的最小速度，单位 像素/帧。
 * @default 0.0
 * 
 * @param Y轴路程计算公式
 * @parent Y轴速度类型
 * @type note
 * @desc 碎片的路程计算公式。可使用 变量和常量 来设计公式，具体看看文档 "1.系统 > 关于弹道.docx"介绍。
 * @default "return 0.0"
 * 
 * 
 * 
 */
 
//<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
//		插件简称：		COGM (Core_Of_Gauge_Meter)
//		临时全局变量	DrillUp.g_COGM_xxx
//		临时局部变量	this._drill_COGM_xxx
//		存储数据变量	无
//		全局存储变量	无
//		覆盖重写方法	无
//
//<<<<<<<<性能记录<<<<<<<<
//
//		★工作类型		持续执行
//		★时间复杂度		o(n^4)*o(参数条数)*o(贴图处理) 每帧
//		★性能测试因素	可视化管理层、战斗界面
//		★性能测试消耗	22.52ms （菜单界面 38.83ms）
//		★最坏情况		大量弹出条滥用
//		★备注			可视化管理层平均fps14，同时开6个参数条，帧数无明显下降，大概fps12。
//						只有缓冲时间条+弹出条时，fps直接降到1。
//		
//		★优化记录
//			2022-10-30优化：
//				这里把 参数条 的代码全部重新整理，划分结构，去掉了分割图片功能。
//
//<<<<<<<<插件记录<<<<<<<<
//
//		★功能结构树：
//			->☆提示信息
//			->☆变量获取
//			
//			->☆临时变量初始化
//				->资源提前预加载
//			->参数条【Drill_COGM_MeterSprite】
//				->标准模块
//					->显示/隐藏【标准函数】
//					->是否就绪【标准函数】
//					->销毁【标准函数】
//					->修改变化因子【标准函数】
//					->修改段上限【标准函数】
//					->初始化数据【标准默认值】
//				->A主体
//				->B分段条
//				->C追逐值
//				->D流动效果
//				->E凹槽条
//				->F弹出条
//				->G粒子
//				->H游标
//				->I加满动画
//			->弹出条【Drill_COGM_SpringSprite】
//			->加满动画遮罩【Drill_COGM_MeterSpriteMask】
//			
//			
//		★家谱：
//			无
//		
//		★插件私有类：
//			* 参数条【Drill_COGM_MeterSprite】
//			* 弹出条【Drill_COGM_SpringSprite】
//			* 加满动画遮罩【Drill_COGM_MeterSpriteMask】
//		
//		★核心说明：
//			1.整个核心只提供了一个封装好的【Sprite独立子类】。
//			  具体见类的说明。
//		
//		★必要注意事项：
//			1.参数条只分两层，内容层 和 外层。两层级可以通过zIndex排序。
//			2.切换菜单时，参数条会重刷所有数据。你需要手动控制一些数据。
//			  其中包括：加满动画（刷后会出现）、粒子（刷后不出现）、弹出条（刷后不出现）。
//
//		★其它说明细节：
//			1.参数条是高度对象化的sprite大类。具体在类说明中有解释。		
//			2.弹出条局限性：
//				目前的弹出条，只是根据以存在的bitmap来画，而不是sprite转换后的效果来画。比如粒子效果，是加不进弹出条的。
//				（bitmap是引擎自己写的类，经过图片存储等中介转换，最后给texture来渲染。）
//				（如果我要把sprite转成图片，必须先建立一个stage，然后addchild，再然后render，生成的图片还是有黑底的canvas。）
//				（主要还是渲染消耗性能问题，render一次会费许多性能）
//			3.参数条的中心锚点最后还是需要通过数学来锁定，因为有一定旋转角度时，参数条的位置会乱。
//		
//		★存在的问题：
//			1.同时开7个缓冲时间条，并且弹出条开启。210个弹出条同时运动时，地图界面会卡爆。
//		

//=============================================================================
// ** ☆提示信息
//=============================================================================
	//==============================
	// * 提示信息 - 参数
	//==============================
	var DrillUp = DrillUp || {}; 
	DrillUp.g_COGM_PluginTip_curName = "Drill_CoreOfGaugeMeter.js 系统-参数条核心";
	DrillUp.g_COGM_PluginTip_baseList = ["Drill_CoreOfBallistics.js 系统-弹道核心"];
	//==============================
	// * 提示信息 - 报错 - 缺少基础插件
	//			
	//			说明：	此函数只提供提示信息，不校验真实的插件关系。
	//==============================
	DrillUp.drill_COGM_getPluginTip_NoBasePlugin = function(){
		if( DrillUp.g_COGM_PluginTip_baseList.length == 0 ){ return ""; }
		var message = "【" + DrillUp.g_COGM_PluginTip_curName + "】\n缺少基础插件，去看看下列插件是不是 未添加 / 被关闭 / 顺序不对：";
		for(var i=0; i < DrillUp.g_COGM_PluginTip_baseList.length; i++){
			message += "\n- ";
			message += DrillUp.g_COGM_PluginTip_baseList[i];
		}
		return message;
	};
	//==============================
	// * 提示信息 - 报错 - 找不到数据
	//==============================
	DrillUp.drill_COGM_getPluginTip_DataNotFind = function( index ){
		return "【" + DrillUp.g_COGM_PluginTip_curName + "】\n未找到id为"+ (index+1) +"的参数条样式配置。";
	};
	//==============================
	// * 提示信息 - 报错 - 底层版本过低
	//==============================
	DrillUp.drill_COGM_getPluginTip_LowVersion = function(){
		return "【" + DrillUp.g_COGM_PluginTip_curName + "】\n游戏底层版本过低，插件基本功能无法执行。\n你可以去看\"rmmv软件版本（必看）.docx\"中的 \"旧工程升级至1.6版本\" 章节，来升级你的游戏底层版本。";
	};
	
	
//=============================================================================
// ** ☆变量获取
//=============================================================================
　　var Imported = Imported || {};
　　Imported.Drill_CoreOfGaugeMeter = true;
　　var DrillUp = DrillUp || {}; 
    DrillUp.parameters = PluginManager.parameters('Drill_CoreOfGaugeMeter');
	
	
	//==============================
	// * 变量获取 - 弹出条弹道样式
	//				（~struct~DrillCOGMBallistics）
	//
	//			说明：	函数未定义白色括号中的参数，为默认值。
	//==============================
	DrillUp.drill_COGM_ballisticsInit = function( dataFrom ) {
		var data = {};
		
		//   移动（movement）
		//		data['movementNum']【数量】
		//		data['movementDelay']【延迟时间】
		data['movementTime'] = Number( dataFrom["移动时长"] || 0);
		data['movementMode'] = String( dataFrom["移动模式"] || "极坐标模式" );
		
		//   极坐标（polar）
		data['polarSpeedType'] = String( dataFrom["速度类型"] || "只初速度" );
		data['polarSpeedBase'] = Number( dataFrom["初速度"] || 0.0);
		data['polarSpeedRandom'] = Number( dataFrom["速度随机波动量"] || 0.0);
		data['polarSpeedInc'] = Number( dataFrom["加速度"] || 0);
		data['polarSpeedMax'] = Number( dataFrom["最大速度"] || 0);
		data['polarSpeedMin'] = Number( dataFrom["最小速度"] || 0);
		var temp_str = String( dataFrom["路程计算公式"] || "\"return 0\"" );
		temp_str = temp_str.substring(1,temp_str.length-1);
		temp_str = temp_str.replace(/\\n/g,"\n");
		temp_str = temp_str.replace(/\\\\/g,"\\");
		data['polarDistanceFormula'] = temp_str;
		data['polarDirType'] = String( dataFrom["方向类型"] || "只初速度" );
		data['polarDirFixed'] = Number( dataFrom["固定方向"] || 0);
		data['polarDirSectorFace'] = Number( dataFrom["扇形朝向"] || 0);
		data['polarDirSectorDegree'] = Number( dataFrom["扇形角度"] || 0);
		temp_str = String( dataFrom["方向计算公式"] || "\"return 0\"" );
		temp_str = temp_str.substring(1,temp_str.length-1);
		temp_str = temp_str.replace(/\\n/g,"\n");
		temp_str = temp_str.replace(/\\\\/g,"\\");
		data['polarDirFormula'] = temp_str;
		
		//   直角坐标（cartesian）
		data['cartRotation'] = Number( dataFrom["直角坐标整体旋转"] || 0.0);
		data['cartXSpeedType'] = String( dataFrom["X轴速度类型"] || "只初速度" );
		data['cartXSpeedBase'] = Number( dataFrom["X轴初速度"] || 0.0);
		data['cartXSpeedRandom'] = Number( dataFrom["X轴速度随机波动量"] || 0.0);
		data['cartXSpeedInc'] = Number( dataFrom["X轴加速度"] || 0);
		data['cartXSpeedMax'] = Number( dataFrom["X轴最大速度"] || 0);
		data['cartXSpeedMin'] = Number( dataFrom["X轴最小速度"] || 0);
		temp_str = String( dataFrom["X轴路程计算公式"] || "return 0" );
		temp_str = temp_str.substring(1,temp_str.length-1);
		temp_str = temp_str.replace(/\\n/g,"\n");
		temp_str = temp_str.replace(/\\\\/g,"\\");
		data['cartXDistanceFormula'] = temp_str;
		data['cartYSpeedType'] = String( dataFrom["Y轴速度类型"] || "只初速度" );
		data['cartYSpeedBase'] = Number( dataFrom["Y轴初速度"] || 0.0);
		data['cartYSpeedRandom'] = Number( dataFrom["Y轴速度随机波动量"] || 0.0);
		data['cartYSpeedInc'] = Number( dataFrom["Y轴加速度"] || 0);
		data['cartYSpeedMax'] = Number( dataFrom["Y轴最大速度"] || 0);
		data['cartYSpeedMin'] = Number( dataFrom["Y轴最小速度"] || 0);
		temp_str = String( dataFrom["Y轴路程计算公式"] || "return 0" );
		temp_str = temp_str.substring(1,temp_str.length-1);
		temp_str = temp_str.replace(/\\n/g,"\n");
		temp_str = temp_str.replace(/\\\\/g,"\\");
		data['cartYDistanceFormula'] = temp_str;
		
		//   轨道锚点（track） （关闭）
		//   两点式（twoPoint）（关闭）
		
		return data;
	}
	
	//==============================
	// * 变量获取 - 参数条样式
	//				（~struct~GaugeMeter）
	//
	//				说明：函数未定义白色括号中的参数，需要子插件定义。若不定义则为默认值。
	//==============================
	DrillUp.drill_COGM_initStyle = function( dataFrom ) {
		var data = {};
		
		// > 主体
		//		data['x']【平移x（非实时赋值）】
		//		data['y']【平移y（非实时赋值）】
		//		data['anchor_x']【中心锚点x（非实时赋值）】
		//		data['anchor_y']【中心锚点y（非实时赋值）】
		//		data['visible']【可见】
		data['rotation'] = Number( dataFrom["整体旋转角度"] || 0 );
		data['meter_src'] = String( dataFrom["资源-参数条"] || "" );
		data['meter_src_file'] = "img/Special__meter/";
		data['meter_src_mask'] = String( dataFrom["资源-参数条遮罩"] || "" );
		
		// > 分段条（段）
		//		data['level_max']【段上限】
		data['level_count'] = Number( dataFrom["段数"] || 1);
		data['level_isLoop'] = String( dataFrom["段是否循环"] || "true") === "true";
		data['shorten_mode'] = String( dataFrom["缩短方式"] || "匀速缩短");
		data['shorten_speed'] = Math.abs( Number( dataFrom["缩短速度"] || Number( dataFrom["匀速缩短速度"] || 2.5)) );
		data['shorten_ratio'] = Math.abs( Number( dataFrom["弹性缩短比"] || 10 ));
		data['lengthen_mode'] = String( dataFrom["伸长方式"] || "瞬间伸长");
		data['lengthen_speed'] = Math.abs( Number( dataFrom["匀速伸长速度"] ) );
		data['lengthen_ratio'] = Math.abs( Number( dataFrom["弹性伸长比"] || 10 ));
		data['flow_enable'] = String( dataFrom["是否使用流动效果"] || "true") === "true";
		data['flow_dir'] = String( dataFrom["流动方向"] || "从右往左");
		data['flow_speed'] = Number( dataFrom["流动速度"] || 1.0 );
		data['flow_srcMode'] = String( dataFrom["流动段划分模式"] || "三等份划分");
		data['flow_levelLength'] = Number( dataFrom["段长度"] || 0 );
		
		// > 凹槽条
		data['leak_enable'] = String( dataFrom["是否启用凹槽条"] || "true") === "true";
		data['leak_src'] = String( dataFrom["资源-凹槽条"] || "" );
		data['leak_src_file'] = "img/Special__meter/";
		data['leak_speed'] = Number( dataFrom["扣除速度"] || 15.0 );
		data['leak_delay'] = Number( dataFrom["扣除延迟"] || 0 );
		data['leak_delayRefresh'] = String( dataFrom["连续扣除是否刷新延迟"] || "true") === "true";
		
		// > 弹出条
		data['spring_enable'] = String( dataFrom["是否启用弹出效果"] || "true") === "true";
		data['spring_type'] = String( dataFrom["弹出条块模式"] || "当前参数条");
		data['spring_maxNum'] = Number( dataFrom["弹出条最大数量"] || 30 );
		if( dataFrom["弹出条弹道"] != undefined && dataFrom["弹出条弹道"] != "" ){
			data['spring_ballistics'] = DrillUp.drill_COGM_ballisticsInit( JSON.parse( dataFrom["弹出条弹道"] ));		
		}else{
			data['spring_ballistics'] = {};		
		}
		
		// > 粒子
		data['par_enable'] = String( dataFrom["是否启用粒子效果"] || "true") === "true";
		data['par_src'] = String( dataFrom["资源-粒子"] || "" );
		data['par_src_file'] = "img/Special__meter/";
		data['par_mode'] = String( dataFrom["粒子出现模式"] || "底部出现");
		data['par_speedX'] = Number( dataFrom["粒子X速度"] || 0);
		data['par_speedY'] = Number( dataFrom["粒子Y速度"] || -1.5);
		data['par_count'] = Number( dataFrom["粒子数量"] || 20);
		data['par_life'] = Number( dataFrom["粒子持续时间"] || 20);
		
		// > 游标
		data['vernier_enable'] = String( dataFrom["是否启用游标"] || "false") === "true";
		if( dataFrom["资源-游标"] != undefined && dataFrom["资源-游标"] != "" ){
			data['vernier_src'] = JSON.parse( dataFrom["资源-游标"] );
		}else{
			data['vernier_src'] = [];
		}
		data['vernier_src_file'] = "img/Special__meter/";
		data['vernier_gif_interval'] = Number( dataFrom["动画帧间隔"] || 0);
		data['vernier_gif_backrun'] = String( dataFrom["是否倒放"] || "true") === "true";
		data['vernier_x'] = Number( dataFrom["偏移-游标 X"] || 0);
		data['vernier_y'] = Number( dataFrom["偏移-游标 Y"] || 0);
		data['vernier_mode'] = String( dataFrom["游标显示模式"] || "一直显示");
		data['vernier_reset'] = String( dataFrom["是否启用多段复位"] || "false") === "true";
		data['vernier_maskCover'] = String( dataFrom["遮罩是否能遮挡游标"] || "false") === "true";
		
		// > 加满动画
		//		data['filling_enable']【启用】
		//		data['filling_mode']【加满方式】
		//		data['filling_time']【持续时间】
		//		data['filling_delay']【动画延迟】
		
		return data;
	};
	
	/*-----------------杂项------------------*/
	DrillUp.g_COGM_preloadEnabled = String(DrillUp.parameters["是否启用预加载"] || "true") === "true";	
	
	/*-----------------参数条样式（配置）------------------*/
	DrillUp.g_COGM_list_length = 60;
	DrillUp.g_COGM_list = [];
	for (var i = 0; i < DrillUp.g_COGM_list_length; i++) {
		if( DrillUp.parameters["参数条样式-" + String(i+1) ] != undefined &&
			DrillUp.parameters["参数条样式-" + String(i+1) ] != "" ){
			DrillUp.g_COGM_list[i] = JSON.parse(DrillUp.parameters["参数条样式-" + String(i+1) ]);
			DrillUp.g_COGM_list[i] = DrillUp.drill_COGM_initStyle( DrillUp.g_COGM_list[i] );
		}else{
			DrillUp.g_COGM_list[i] = {};
		}
	}

	//==============================
	// * 数据获取 - 参数条样式（接口）
	//	
	//			说明：	与直接获取 "DrillUp.g_COGM_list[i]" 一样，只是加了一道过滤提示网。
	//==============================
	DrillUp.drill_COGM_getCopyedData = function( index ){
		var data = DrillUp.g_COGM_list[ index ];
		if( data == undefined ||
			data['level_count'] == undefined ){
			alert( DrillUp.drill_COGM_getPluginTip_DataNotFind( index ) );
			return {};
		}
		return JSON.parse(JSON.stringify( data ));
	}
	
	
//=============================================================================
// * >>>>基于插件检测>>>>
//=============================================================================
if( Imported.Drill_CoreOfBallistics ){
	
	
//=============================================================================
// ** ☆临时变量初始化
//
//			说明：	> 用过的bitmap，全部标记不删除，防止刷菜单时重建导致浪费资源。
//					（插件完整的功能目录去看看：功能结构树）
//=============================================================================
if( DrillUp.g_COGM_preloadEnabled == true ){
	//==============================
	// * 临时变量 - 初始化
	//==============================
	var _drill_COGM_temp_initialize = Game_Temp.prototype.initialize;
	Game_Temp.prototype.initialize = function() {
		_drill_COGM_temp_initialize.call(this);
		this.drill_COGM_preloadInit();
	}
	//==============================
	// * 临时变量 - 预加载 版本校验
	//==============================
	if( Utils.generateRuntimeId == undefined ){
		alert( DrillUp.drill_COGM_getPluginTip_LowVersion() );
	}
	//==============================
	// * 临时变量 - 资源提前预加载
	//
	//			说明：	遍历全部资源，提前预加载标记过的资源。
	//==============================
	Game_Temp.prototype.drill_COGM_preloadInit = function() {
		this._drill_COGM_cacheId = Utils.generateRuntimeId();	//资源缓存id
		this._drill_COGM_preloadTank = [];						//bitmap容器
		for( var i = 0; i < DrillUp.g_COGM_list.length; i++ ){
			var temp_data = DrillUp.g_COGM_list[i];
			if( temp_data == undefined ){ continue; }
			if( temp_data['meter_src'] == undefined ){ continue; }
			
			this._drill_COGM_preloadTank.push( 
				ImageManager.reserveBitmap( temp_data['meter_src_file'], temp_data['meter_src'], 0, true ) 
			);
			this._drill_COGM_preloadTank.push( 
				ImageManager.reserveBitmap( temp_data['meter_src_file'], temp_data['meter_src_mask'], 0, true ) 
			);
			
			if( temp_data['leak_enable'] == true ){
				this._drill_COGM_preloadTank.push( 
					ImageManager.reserveBitmap( temp_data['leak_src_file'], temp_data['leak_src'], 0, true ) 
				);
			}
			
			if( temp_data['par_enable'] == true ){
				this._drill_COGM_preloadTank.push( 
					ImageManager.reserveBitmap( temp_data['par_src_file'], temp_data['par_src'], 0, true ) 
				);
			}
			
			if( temp_data['vernier_enable'] == true ){
				for(var j=0; j < temp_data['vernier_src'].length; j++){
					this._drill_COGM_preloadTank.push( 
						ImageManager.reserveBitmap( temp_data['vernier_src_file'], temp_data['vernier_src'][j], 0, true ) 
					);
				}
			}
		}
	}

}


//=============================================================================
// ** 参数条【Drill_COGM_MeterSprite】
// **		
// **		索引：	COGM（可从子插件搜索到函数、类用法）
// **		来源：	继承于Sprite
// **		实例：	> 可见 Drill_GaugeOfBufferTimeBar插件 的 _drill_meterSprite 成员
// **		应用：	> 可见 Drill_GaugeOfBufferTimeBar插件 的 drill_createMeter 函数
// **				（应用中除了参数条贴图，还包括背景层和前景层，组合形成 缓冲时间条）
// **		
// **		作用域：	地图界面、战斗界面、菜单界面
// **		主功能：	> 定义一个贴图组合体，根据预设定义，得到一个参数条贴图。
// **					> 具体功能见 "1.系统 > 关于参数条.docx"。
// **		子功能：	->贴图
// **						->显示/隐藏
// **						->是否就绪
// **						->优化策略
// **						->销毁
// **						->帧刷新
// **						->初始化数据
// **						->初始化对象
// **						->延迟初始化
// **					->接口
// **						->修改变化因子
// **						->修改段上限
// **					->A主体
// **						->层级
// **							->外层
// **							->内容层
// **								->内容层遮罩
// **								->上段
// **								->凹槽条
// **								->下段
// **						->旋转角度
// **						->锚点锁定
// **						->资源宽度
// **						->资源高度
// **					->B分段条
// **						->资源bitmap
// **						->概念
// **							->段（level）
// **							->段上限（level_max）
// **							->上段/下段（section）
// **						->框架起点
// **						->框架终点
// **						->分段条宽度（开放接口）
// **						->分段条高度（开放接口）
// **					->C追逐值
// **						->伸长方式
// **						->缩短方式
// **						->帧刷新 当前值
// **						->帧刷新 当前层级
// **						->帧刷新 框架值
// **						->帧刷新 框架层级
// **							->段循环
// **						->是否被阻塞（开放接口）
// **						->上段的宽度（开放接口）
// **					->D流动效果
// **						->概念
// **							->头段/尾段
// **							->段长度
// **						->帧刷新 - 流动值
// **						->帧刷新 - 框架值
// **					->E凹槽条
// **						->滞留标记
// **						->延迟扣除
// **							->扣除速度
// **						->■凹槽条阻塞
// **						->帧刷新 数值
// **						->帧刷新 框架
// **					->F弹出条
// **						->块框架
// **						->弹道轨迹
// **					->G粒子
// **						->边沿出现
// **					->H游标
// **						->多段复位
// **						->所在层级（遮罩遮挡）
// **						->显示模式
// **					->I加满动画
// **						->播放结束后销毁
// **
// **		说明：	> sprite贴在任意地方都可以。
// **			 	> 【temp_data配置参数】都在drill_initData中，其他的都为私有参数。
// **		 		  你可以先取【DrillUp.g_COGM_list样式数据】再赋值各个额外属性，也可以【直接new】全参数自己建立控制。
// **		 		  其中，"level_max"段上限 由于其特殊性，是会贯穿于所有子插件的。
// **				> 需要实时调用函数.drill_COGM_reflashValue(value)改变参数条的值。
// **				> 值减少时，凹槽条、弹出条会产生效果，值增加不会。
// **
// **		代码：	> 范围 - 该类只对 具体的数值 提供可视化参数条显示。
// **				> 结构 - [ ●合并 /分离/混乱] 贴图与数据合并，主要靠接口控制 当前值 和 段上限。
// **				> 数量 - [单个/ ●多个 ] 
// **				> 创建 - [一次性/ ●自延迟 /外部延迟] 需要等分段条加载完毕后，才进行切割划分。
// **				> 销毁 - [不考虑/自销毁/ ●外部销毁 ] 如果外部要换样式，先将贴图销毁，然后重建即可。
// **				> 样式 - [ ●不可修改 /自变化/外部变化] 
// **
// **		调用方法：	// > 参数条 数据初始化
// **					//  （完整数据 默认值 见函数drill_initData）
// **						var meter_id = 1;
// **						var temp_data = DrillUp.drill_COGM_getCopyedData( meter_id );	//深拷贝数据
// **						temp_data['level_max'] = 200;				//段上限
// **						temp_data['anchor_x'] = 0.5;				//中心锚点x
// **						temp_data['anchor_y'] = 0.5;				//中心锚点y	
// **					// > 参数条 贴图初始化
// **						var temp_sprite = new Drill_COGM_MeterSprite( temp_data );
// **						this.addChild( temp_sprite );
//=============================================================================
//==============================
// * 参数条 - 定义
//==============================
function Drill_COGM_MeterSprite() {
	this.initialize.apply(this, arguments);
}
Drill_COGM_MeterSprite.prototype = Object.create(Sprite.prototype);
Drill_COGM_MeterSprite.prototype.constructor = Drill_COGM_MeterSprite;
//==============================
// * 参数条 - 初始化
//==============================
Drill_COGM_MeterSprite.prototype.initialize = function( data ) {
	Sprite.prototype.initialize.call(this);
	this._drill_data = JSON.parse(JSON.stringify( data ));	//深拷贝数据
	this.drill_initData();									//初始化数据
	this.drill_initSprite();								//初始化对象
}
//==============================
// * 参数条 - 帧刷新
//==============================
Drill_COGM_MeterSprite.prototype.update = function() {
	if( this.drill_COGM_isReady() == false ){ return; }
	if( this.drill_COGM_isOptimizationPassed() == false ){ return; }
	Sprite.prototype.update.call(this);
	this.drill_updateDelayingInit();				//帧刷新 - 延迟初始化
	
	this.drill_updateSpringShowing();				//帧刷新 - F弹出条（在上段下段切换前，要捕获bitmap对象）
	
	this.drill_updateLevel_FrameStart();			//帧刷新 - B分段条 - 框架起点
	this.drill_updateChase_Value();					//帧刷新 - C追逐值 - 当前值
	this.drill_updateChase_Level(); 				//帧刷新 - C追逐值 - 当前层级
	this.drill_updateChase_FrameValue();			//帧刷新 - C追逐值 - 框架值
	this.drill_updateChase_FrameLevel(); 			//帧刷新 - C追逐值 - 框架层级
	this.drill_updateFlow_Value(); 					//帧刷新 - D流动效果 - 流动值
	this.drill_updateFlow_Frame(); 					//帧刷新 - D流动效果 - 框架值
	this.drill_updateLevel_FrameEnd();				//帧刷新 - B分段条 - 框架终点
	
	this.drill_updateLeaking_Value();				//帧刷新 - E凹槽条 - 数值
	this.drill_updateLeaking_Frame(); 				//帧刷新 - E凹槽条 - 框架
	this.drill_updateLeaking_Block(); 				//帧刷新 - E凹槽条 - 阻塞
	
	this.drill_updateParticle(); 					//帧刷新 - G粒子
	this.drill_updateVernier(); 					//帧刷新 - H游标
	this.drill_updateFilling(); 					//帧刷新 - I加满动画
	
	this._drill_cur_value = this._drill_new_value;	//帧刷新 - 变化因子
}
//==============================
// * 参数条 - 帧刷新 - 延迟初始化
//==============================
Drill_COGM_MeterSprite.prototype.drill_updateDelayingInit = function() {
	var data = this._drill_data;
	
	// > I加满动画
	if( this._drill_filling_needInit == true ){	
		this._drill_filling_needInit = false;
		this.drill_delayingInitFilling();
	}
	
	// > A主体
	if( this._drill_attr_needInit == true ){
		this._drill_attr_needInit = false;
		this.drill_delayingInitAttr();
	}
	
	// > B分段条（无）
	
	// > C追逐值（无）
	
	// > D流动效果（无）
	
	// > E凹槽条（无）
	
	// > F弹出条
	if( this._drill_spring_needInit == true ){	
		this._drill_spring_needInit = false;
		this.drill_delayingInitSpring();
	}
	
	// > G粒子
	if( this._drill_par_bitmap != undefined && 
		this._drill_par_bitmap.isReady() && 
		this._drill_par_needInit == true ){	
		this._drill_par_needInit = false;
		this.drill_delayingInitParticle();
	}
	
	// > H游标
	if( this._drill_vernier_bitmaps.length != 0 && 
		this._drill_vernier_bitmaps[0].isReady() && 
		this._drill_vernier_needInit == true ){	
		this._drill_vernier_needInit = false;
		this.drill_delayingInitVernier();
	}
	
	// > 显示
	if( this.visible != data['visible'] ){
		this.visible = data['visible'];
	}
}
//##############################
// * 参数条 - 显示/隐藏【标准函数】
//
//			参数：	> visible 布尔（是否显示）
//			返回：	> 无
//			
//			说明：	> 可放在帧刷新函数中实时调用。
//##############################
Drill_COGM_MeterSprite.prototype.drill_COGM_setVisible = function( visible ){
	var data = this._drill_data;
	data['visible'] = visible;
}
//##############################
// * 参数条 - 是否就绪【标准函数】
//
//			参数：	> 无
//			返回：	> visible 布尔
//			
//			说明：	> 可放在帧刷新函数中实时调用。
//##############################
Drill_COGM_MeterSprite.prototype.drill_COGM_isReady = function(){
	if( this.drill_isSrcReady() == false ){ return false; }
	return true;
}
//##############################
// * 参数条 - 优化策略【标准函数】
//			
//			参数：	> 无
//			返回：	> 布尔（是否通过）
//			
//			说明：	> 通过时，正常帧刷新；未通过时，不执行帧刷新。
//##############################
Drill_COGM_MeterSprite.prototype.drill_COGM_isOptimizationPassed = function(){
    return true;	//（暂无策略）
};
//##############################
// * 参数条 - 销毁【标准函数】
//
//			参数：	> 无
//			返回：	> 无
//			
//			说明：	> 如果需要重建时。
//##############################
Drill_COGM_MeterSprite.prototype.drill_COGM_destroy = function(){
	this.drill_COGM_destroy_Private();
};
//##############################
// * 参数条 - 修改变化因子【标准函数】
//
//			参数：	> value 数字（变化因子值）
//			返回：	> 无
//			
//			说明：	> 可放在帧刷新函数中实时调用。
//					> 该插件只提供变换因子的参数条显示效果，且 变化因子 可以超出 段上限，也可以为负数。
//##############################
Drill_COGM_MeterSprite.prototype.drill_COGM_reflashValue = function( value ){
	this._drill_new_value = value;
}
//##############################
// * 参数条 - 修改段上限【标准函数】
//
//			参数：	> level_max 数字（段上限）
//			返回：	> 无
//			
//			说明：	> 可放在帧刷新函数中实时调用。
//##############################
Drill_COGM_MeterSprite.prototype.drill_COGM_setLevelMax = function( level_max ){
	var data = this._drill_data;
	data['level_max'] = level_max;
}
//##############################
// * 参数条 - 初始化数据【标准默认值】
//
//			参数：	> 无
//			返回：	> 无
//			
//			说明：	> data 动态参数对象（来自类初始化）
//					  该对象包含 类所需的所有默认值。
//					> 其中 DrillUp.drill_COGM_initStyle 提供了部分数据库设置的样式数据，
//					  样式数据中注释的部分，仍然需要子插件根据自身情况来进行赋值。
//##############################
Drill_COGM_MeterSprite.prototype.drill_initData = function() {
	var data = this._drill_data;
	
	// > 标准默认值
	data['enable'] = true;	
	if( data['x'] == undefined ){ data['x'] = 0 };													//A主体 - 平移x（非实时赋值）
	if( data['y'] == undefined ){ data['y'] = 0 };													//A主体 - 平移y（非实时赋值）
	if( data['anchor_x'] == undefined ){ data['anchor_x'] = 0 };									//A主体 - 中心锚点x（非实时赋值）
	if( data['anchor_y'] == undefined ){ data['anchor_y'] = 0 };									//A主体 - 中心锚点y（非实时赋值）
	if( data['rotation'] == undefined ){ data['rotation'] = 0 };									//A主体 - 旋转（非实时赋值）
	if( data['visible'] == undefined ){ data['visible'] = true };									//A主体 - 可见
	
	if( data['meter_src'] == undefined ){ data['meter_src'] = "" };									//B分段条 - 资源
	if( data['meter_src_file'] == undefined ){ data['meter_src_file'] = "img/Special__meter/" };	//B分段条 - 资源文件夹
	if( data['meter_src_mask'] == undefined ){ data['meter_src_mask'] = "" };						//B分段条 - 遮罩
	if( data['level_max'] == undefined ){ data['level_max'] = 100 };								//B分段条 - 单段最大值（段上限）
	if( data['level_count'] == undefined ){ data['level_count'] = 1 };								//B分段条 - 段数量
	if( data['level_isLoop'] == undefined ){ data['level_isLoop'] = false };						//B分段条 - 段是否循环
	
	if( data['shorten_mode'] == undefined ){ data['shorten_mode'] = "匀速缩短" };					//C追逐值 - 缩短方式
	if( data['shorten_ratio'] == undefined ){ data['shorten_ratio'] = 15.0 };						//C追逐值 - 缩短方式 - 弹性缩短比
	if( data['shorten_speed'] == undefined ){ data['shorten_speed'] = 2.5 };						//C追逐值 - 缩短方式 - 匀速缩短速度
	if( data['lengthen_mode'] == undefined ){ data['lengthen_mode'] = "瞬间伸长" };					//C追逐值 - 伸长方式
	if( data['lengthen_ratio'] == undefined ){ data['lengthen_ratio'] = 15.0 };						//C追逐值 - 伸长方式 - 弹性伸长比
	if( data['lengthen_speed'] == undefined ){ data['lengthen_speed'] = 10.5 };						//C追逐值 - 伸长方式 - 匀速伸长速度
	
	if( data['flow_enable'] == undefined ){ data['flow_enable'] = true };							//D流动效果 - 是否流动
	if( data['flow_dir'] == undefined ){ data['flow_dir'] = "从右往左" };							//D流动效果 - 流动方向
	if( data['flow_speed'] == undefined ){ data['flow_speed'] = 0.5 };								//D流动效果 - 流动速度
	if( data['flow_srcMode'] == undefined ){ data['flow_srcMode'] = "三等份划分" };					//D流动效果 - 流动段划分模式
	if( data['flow_levelLength'] == undefined ){ data['flow_levelLength'] = 0 };					//D流动效果 - 段长度
	
	if( data['leak_enable'] == undefined ){ data['leak_enable'] = false };							//E凹槽条 - 启用
	if( data['leak_src'] == undefined ){ data['leak_src'] = "" };									//E凹槽条 - 资源
	if( data['leak_src_file'] == undefined ){ data['leak_src_file'] = "img/Special__meter/" };		//E凹槽条 - 资源文件夹
	if( data['leak_speed'] == undefined ){ data['leak_speed'] = 4.0 };								//E凹槽条 - 扣除速度
	if( data['leak_delay'] == undefined ){ data['leak_delay'] = 0.0 };								//E凹槽条 - 扣除延迟
	if( data['leak_delayRefresh'] == undefined ){ data['leak_delayRefresh'] = true };				//E凹槽条 - 连续受伤是否刷新延迟
	
	if( data['spring_enable'] == undefined ){ data['spring_enable'] = false };						//F弹出条 - 启用
	if( data['spring_type'] == undefined ){ data['spring_type'] = "当前参数条" };					//F弹出条 - 块模式
	if( data['spring_ballistics'] == undefined ){ data['spring_ballistics'] = {} };					//F弹出条 - 弹道
	if( data['spring_maxNum'] == undefined ){ data['spring_maxNum'] = 30 };							//F弹出条 - 最大数量
	
	if( data['par_enable'] == undefined ){ data['par_enable'] = false };							//G粒子 - 启用
	if( data['par_src'] == undefined ){ data['par_src'] = "" };										//G粒子 - 资源
	if( data['par_src_file'] == undefined ){ data['par_src_file'] = "img/Special__meter/" };		//G粒子 - 资源文件夹
	if( data['par_mode'] == undefined ){ data['par_mode'] = "底部出现" };							//G粒子 - 出现模式	
	if( data['par_speedX'] == undefined ){ data['par_speedX'] = 0 };								//G粒子 - X速度
	if( data['par_speedY'] == undefined ){ data['par_speedY'] = -1.5 };								//G粒子 - Y速度
	if( data['par_count'] == undefined ){ data['par_count'] = 20 };									//G粒子 - 数量
	if( data['par_life'] == undefined ){ data['par_life'] = 20 };									//G粒子 - 持续时间
	
	if( data['vernier_enable'] == undefined ){ data['vernier_enable'] = false };					//H游标 - 启用
	if( data['vernier_src'] == undefined ){ data['vernier_src'] = [] };								//H游标 - 资源
	if( data['vernier_src_file'] == undefined ){ data['vernier_src_file'] = "img/Special__meter/" };//H游标 - 资源文件夹
	if( data['vernier_gif_interval'] == undefined ){ data['vernier_gif_interval'] = 4 };			//H游标 - 动画帧间隔
	if( data['vernier_gif_backrun'] == undefined ){ data['vernier_gif_backrun'] = false };			//H游标 - 是否倒放
	if( data['vernier_x'] == undefined ){ data['vernier_x'] = 0 };									//H游标 - x
	if( data['vernier_y'] == undefined ){ data['vernier_y'] = 0 };									//H游标 - y
	if( data['vernier_mode'] == undefined ){ data['vernier_mode'] = "一直显示" };					//H游标 - 显示模式
	if( data['vernier_reset'] == undefined ){ data['vernier_reset'] = false };						//H游标 - 多层重置
	if( data['vernier_maskCover'] == undefined ){ data['vernier_maskCover'] = false };				//H游标 - 遮罩遮挡
	
	if( data['filling_enable'] == undefined ){ data['filling_enable'] = false };					//I加满动画 - 启用
	if( data['filling_mode'] == undefined ){ data['filling_mode'] = "匀速加满" };					//I加满动画 - 加满方式
	if( data['filling_time'] == undefined ){ data['filling_time'] = 60 };							//I加满动画 - 持续时间
	if( data['filling_delay'] == undefined ){ data['filling_delay'] = 10 };							//I加满动画 - 动画延迟
};
//==============================
// * 参数条 - 初始化对象
//==============================
Drill_COGM_MeterSprite.prototype.drill_initSprite = function() {
	this._drill_new_value = 0;				//变化因子 - 新变化参数【使用时只读】
	this._drill_cur_value = 0;				//变化因子 - 当前参数【使用时只读】
	
	this.drill_initAttr();					//初始化对象 - A主体
	this.drill_initSection();				//初始化对象 - B分段条
	this.drill_initChase();					//初始化对象 - C追逐值
	this.drill_initFlow();					//初始化对象 - D流动效果
	this.drill_initLeak();					//初始化对象 - E凹槽条
	this.drill_initSpring();				//初始化对象 - F弹出条
	this.drill_initParticle();				//初始化对象 - G粒子
	this.drill_initVernier();				//初始化对象 - H游标
	this.drill_initFilling();				//初始化对象 - I加满动画
}
//==============================
// * 参数条 - 销毁（私有）
//==============================
Drill_COGM_MeterSprite.prototype.drill_COGM_destroy_Private = function() {
	this.visible = false;
	
	// > 销毁 - A主体
	this.drill_COGM_removeChildConnect( this._layer_context );	//断开联系
	this.drill_COGM_removeChildConnect( this._layer_outer );
	this._layer_outer = null;
	this._layer_context = null;
	this._layer_contextMask = null;
	this._drill_attr_needInit = false;
	
	// > 销毁 - B分段条
	this._drill_section_bitmap = null;
	this._drill_sectionUp_sprite = null;
	this._drill_sectionDown_sprite = null;
	
	// > 销毁 - C追逐值（无）
	
	// > 销毁 - D流动效果（无）
	
	// > 销毁 - E凹槽条
	this._drill_leak_sprite = null;
	
	// > 销毁 - F弹出条
	this._drill_spring_needInit = false;
	this._drill_spring_tank.length = 0;
	
	// > 销毁 - G粒子
	this._drill_par_needInit = false;
	this._drill_par_spriteTank.length = 0;
	this._drill_par_bitmap = null;
	
	// > 销毁 - H游标
	this._drill_vernier_needInit = false;
	this._drill_vernier_sprite = null;
	this._drill_vernier_bitmaps.length = 0;
	
	// > 销毁 - I加满动画
	this.removeChild(this._drill_filling_mask);
	this._drill_filling_needInit = false;
	this._drill_filling_mask = null;
}
//==============================
// * 参数条 - 销毁 - 递归断开连接（私有）
//==============================
Drill_COGM_MeterSprite.prototype.drill_COGM_removeChildConnect = function( parent_sprite ){
	if( parent_sprite == undefined ){ return; }
	var sprite_list = parent_sprite.children;
	if( sprite_list == undefined ){ return; }
	for( var i = sprite_list.length-1; i >= 0; i-- ){
		var sprite = sprite_list[i];
		if( sprite == undefined ){ continue; }
		parent_sprite.removeChild( sprite );
		this.drill_COGM_removeChildConnect( sprite );
	}
};
//==============================
// * 参数条 - 资源宽度
//==============================
Drill_COGM_MeterSprite.prototype.drill_srcWidth = function(){
	if( !this._drill_section_bitmap.isReady() ){ return 0; } 
	return this._drill_section_bitmap.width;
};
//==============================
// * 参数条 - 资源高度
//==============================
Drill_COGM_MeterSprite.prototype.drill_srcHeight = function(){
	if( !this._drill_section_bitmap.isReady() ){ return 0; } 
	return this._drill_section_bitmap.height;
};
//==============================
// * 参数条 - 资源是否准备就绪
//==============================
Drill_COGM_MeterSprite.prototype.drill_isSrcReady = function() {
	if( this._drill_section_bitmap.isReady() == false ){ return false; }
	return true;
}


//==============================
// * A主体 - 初始化对象
//==============================
Drill_COGM_MeterSprite.prototype.drill_initAttr = function() {
	this._drill_radian = 0;								//A主体 - 整体旋转弧度
	this._layer_outer = null;							//A主体 - 外层
	this._layer_context = null;							//A主体 - 内容层
	this._layer_contextMask = null;						//A主体 - 内容层遮罩
	this._drill_attr_needInit = true;
	
	// > 主体属性
	var data = this._drill_data;
	this._drill_radian = data['rotation']/180*Math.PI;
	this.x = 0;	
	this.y = 0;	
	this.anchor.x = data['anchor_x'];	
	this.anchor.y = data['anchor_y'];	
	this.rotation = this._drill_radian;	
	this.visible = false;
	
	// > 层级初始化
	this._layer_contextMask = new Sprite();			//内容层遮罩
	this._layer_context = new Sprite();				//内容层
	this.addChild(this._layer_context);				//
	this._layer_outer = new Sprite();				//外层
	this.addChild(this._layer_outer);				//
	
	// > 内容层遮罩初始化
	if( data['meter_src_mask'] != "" ){
		var bitmap = ImageManager.loadBitmap( data['meter_src_file'], data['meter_src_mask'], 0, true);
		this._layer_contextMask.bitmap = bitmap;
		
		this._layer_context.addChild(this._layer_contextMask);
		this._layer_context.mask = this._layer_contextMask;
	}
};
//==============================
// * A主体 - 延迟初始化
//==============================
Drill_COGM_MeterSprite.prototype.drill_delayingInitAttr = function() {
	
	// > 设置初始位置
	var data = this._drill_data;
	var ww = this.drill_levelWidth();
	var hh = this.drill_levelHeight();
	var point = $gameTemp.drill_COGM_getFixPointInAnchor(
					0.0, 0.0,
					data['anchor_x'], data['anchor_y'],
					ww, hh,
					this._drill_radian,
					1.0 , 1.0 
				);
	this.x = data['x'] + point.x - ww*data['anchor_x'];
	this.y = data['y'] + point.y - hh*data['anchor_y'];
};
//==============================
// * A主体 - 层级排序
//==============================
Drill_COGM_MeterSprite.prototype.drill_COGM_sortByZIndex = function() {
   this._layer_context.children.sort(function(a, b){return a.zIndex-b.zIndex});		//内容层
   this._layer_outer.children.sort(function(a, b){return a.zIndex-b.zIndex});		//外层
};


//==============================
// * B分段条 - 初始化对象
//
//			说明：	> 该部分只提供 上段、下段 的创建，基本的数据函数，框架帧刷新。
//					> 高级操作由其他模块执行。
//==============================
Drill_COGM_MeterSprite.prototype.drill_initSection = function() {
	this._drill_section_bitmap = null;					//B分段条 - 资源bitmap
	this._drill_sectionUp_sprite = null;				//B分段条 - 上段
	this._drill_sectionDown_sprite = null;				//B分段条 - 下段
	this._drill_sectionUpFrame_x = 0;					//B分段条 - 上段框架x
	this._drill_sectionUpFrame_y = 0;					//B分段条 - 上段框架y
	this._drill_sectionUpFrame_w = 0;					//B分段条 - 上段框架w
	this._drill_sectionUpFrame_h = 0;					//B分段条 - 上段框架h
	this._drill_sectionDownFrame_x = 0;					//B分段条 - 下段框架x
	this._drill_sectionDownFrame_y = 0;					//B分段条 - 下段框架y
	this._drill_sectionDownFrame_w = 0;					//B分段条 - 下段框架w
	this._drill_sectionDownFrame_h = 0;					//B分段条 - 下段框架h
	this._drill_level_width = 0;						//B分段条 - 分段条宽度
	this._drill_level_height = 0;						//B分段条 - 分段条高度
	
	// > 资源bitmap
	var data = this._drill_data;
	this._drill_section_bitmap = ImageManager.loadBitmap( data['meter_src_file'], data['meter_src'], 0, true);
	
	// > 分段条初始化 - 上段
	this._drill_sectionUp_sprite = new Sprite();
	this._drill_sectionUp_sprite.bitmap = this._drill_section_bitmap;
	this._drill_sectionUp_sprite.zIndex = 30;
	this._layer_context.addChild(this._drill_sectionUp_sprite);
	
	// > 分段条初始化 - 下段
	this._drill_sectionDown_sprite = new Sprite();
	this._drill_sectionDown_sprite.bitmap = this._drill_section_bitmap;
	this._drill_sectionDown_sprite.zIndex = 10;
	this._layer_context.addChild(this._drill_sectionDown_sprite);
	
	// > 层级排序
	this.drill_COGM_sortByZIndex();
};
//==============================
// * B分段条 - 帧刷新 框架起点
//==============================
Drill_COGM_MeterSprite.prototype.drill_updateLevel_FrameStart = function() {
	this._drill_sectionUpFrame_x = 0;
	this._drill_sectionUpFrame_y = 0;
	this._drill_sectionUpFrame_w = 0;
	this._drill_sectionUpFrame_h = 0;
	this._drill_sectionDownFrame_x = 0;
	this._drill_sectionDownFrame_y = 0;
	this._drill_sectionDownFrame_w = 0;
	this._drill_sectionDownFrame_h = 0;
};
//==============================
// * B分段条 - 帧刷新 框架终点
//==============================
Drill_COGM_MeterSprite.prototype.drill_updateLevel_FrameEnd = function() {
	
	// > 上段
	this._drill_sectionUp_sprite.drill_COGM_setFrame(
		this._drill_sectionUpFrame_x,
		this._drill_sectionUpFrame_y,
		this._drill_sectionUpFrame_w,
		this._drill_sectionUpFrame_h
	);
	
	// > 下段
	this._drill_sectionDown_sprite.drill_COGM_setFrame(
		this._drill_sectionDownFrame_x,
		this._drill_sectionDownFrame_y,
		this._drill_sectionDownFrame_w,
		this._drill_sectionDownFrame_h
	);
};
//==============================
// * B分段条 - 数据 - 分段条宽度（开放接口）
//==============================
Drill_COGM_MeterSprite.prototype.drill_levelWidth = function(){
	if( this._drill_level_width == 0 ){
		this.drill_initLevelWidthAndHeight();	//（高宽初始化）
	}
	return this._drill_level_width;
};
//==============================
// * B分段条 - 数据 - 分段条高度（开放接口）
//==============================
Drill_COGM_MeterSprite.prototype.drill_levelHeight = function(){
	if( this._drill_level_height == 0 ){
		this.drill_initLevelWidthAndHeight();	//（高宽初始化）
	}
	return this._drill_level_height;
};
//==============================
// * B分段条 - 数据 - 高宽初始化（私有）
//==============================
Drill_COGM_MeterSprite.prototype.drill_initLevelWidthAndHeight = function(){
	var data = this._drill_data;
	var ww = this._drill_section_bitmap.width;
	var hh = this._drill_section_bitmap.height;
	
	// > D流动效果
	if( data['flow_enable'] == true ){
		
		// > 三等份划分
		ww = Math.ceil( ww/3 );
		
		// > 指定段长度划分
		if( data['flow_srcMode'] == "指定段长度划分" && data['flow_levelLength'] != 0 ){
			ww = data['flow_levelLength'];
		}
	}
	
	// > 段数
	hh = Math.ceil( hh / data['level_count'] );
	
	this._drill_level_width = ww;
	this._drill_level_height = hh;
};


//==============================
// * C追逐值 - 初始化对象
//==============================
Drill_COGM_MeterSprite.prototype.drill_initChase = function() {
	this._drill_chase_curValue = 0;					//C追逐值 - 当前值
	this._drill_chase_curLevel = -1;				//C追逐值 - 当前层级
}
//==============================
// * C追逐值 - 帧刷新 当前值
//
//			说明：	当前函数只给 this._drill_chase_curValue 赋值。
//==============================
Drill_COGM_MeterSprite.prototype.drill_updateChase_Value = function() {
	var data = this._drill_data;
	
	// > 当前值 阻塞
	//		（被阻塞时，值不变，等待释放）
	if( this.drill_isChaseBlock() == true ){ return; }
	
	
	// > C追逐值
	var cur_value = this._drill_chase_curValue;
	var new_value = this._drill_new_value;
	var diff_value = Math.abs( cur_value - new_value );
	var vv = this._drill_chase_curValue;
	
	// > C追逐值 - 伸长方式
	if( cur_value < new_value ){
		
		// > 伸长方式 - 瞬间
		if( data['lengthen_mode'] == "瞬间伸长" || data['lengthen_speed'] == 0 || data['lengthen_ratio'] == 0 ){
			vv = new_value;
		
		// > 伸长方式 - 弹性
		}else if( data['lengthen_mode'] == "弹性伸长" ){
			var speed = Math.max( diff_value/data['lengthen_ratio'], 1 );
			vv += speed;
			if( vv > new_value ){
				vv = new_value;
			}
			
		// > 伸长方式 - 匀速
		}else if( data['lengthen_mode'] == "匀速伸长" ){
			var ww = this.drill_srcWidth();
			var speed = data['level_max'] / ww * data['lengthen_speed'];
			if( diff_value > data['level_max'] ){
				speed = speed * diff_value/data['level_max'];		//（如果差值远远大于匀速缩短，则加大缩短倍率）
			}
			vv += speed;
			if( vv > new_value ){
				vv = new_value;
			}
		}
		
	// > C追逐值 - 缩短方式
	}else if( cur_value > new_value ){
		
		// > 缩短方式 - 瞬间
		if( data['shorten_mode'] == "瞬间缩短" || data['shorten_speed'] == 0 || data['shorten_ratio'] == 0 ){
			vv = new_value;
			
		// > 缩短方式 - 弹性
		}else if( data['shorten_mode'] == "弹性缩短" ){
			var speed = Math.max( diff_value/data['shorten_ratio'], 1 );
			vv -= speed;
			if( vv < new_value ){
				vv = new_value;
			}
			
		// > 缩短方式 - 匀速
		}else if( data['shorten_mode'] == "匀速缩短" ){
			var ww = this.drill_srcWidth();
			var speed = data['level_max'] / ww * data['shorten_speed'];
			if( diff_value > data['level_max'] ){
				speed = speed * diff_value/data['level_max'];		//（如果差值远远大于匀速缩短，则加大缩短倍率）
			}
			vv -= speed;
			if( vv < new_value ){
				vv = new_value;
			}
		}
	}
	this._drill_chase_curValue = vv;
}
//==============================
// * C追逐值 - 帧刷新 当前层级
//
//			说明：	> 当前函数给 this._drill_chase_curLevel 赋值。
//					> 你可以在 减少时 添加其他功能的阻塞设置。
//==============================
Drill_COGM_MeterSprite.prototype.drill_updateChase_Level = function() {
	var data = this._drill_data;
	
	// > 当前层级 阻塞
	//		（被阻塞时，值不变，等待释放）
	if( this.drill_isChaseBlock() == true ){ return; }
	
	
	// > 真实值结果
	var a1 = Math.floor(this._drill_chase_curValue / data['level_max']);
	var a2 = Math.floor(this._drill_chase_curValue % data['level_max']);
	if( this._drill_chase_curLevel == a1 ){ return; }
	
	
	// > 当前层级 - 减少时
	if( this._drill_chase_curLevel > a1 ){
		this._drill_chase_curLevel -= 1;
		
		// > ■凹槽条阻塞
		//		（每次小于层级时，E凹槽条 都阻塞一次）
		if( data['leak_enable'] == true ){
			this._drill_leak_isBlocked = true;
		}
	}
	
	
	// > 当前层级 - 增加时
	if( this._drill_chase_curLevel < a1 ){
		this._drill_chase_curLevel += 1;
	}
}
//==============================
// * C追逐值 - 帧刷新 框架值
//
//			说明：	此函数只操作 分段条 框架。
//==============================
Drill_COGM_MeterSprite.prototype.drill_updateChase_FrameValue = function() {
	var data = this._drill_data;
	
	// > B分段条
	var a1 = Math.floor(this._drill_chase_curValue / data['level_max']);
	var a2 = Math.floor(this._drill_chase_curValue % data['level_max']);
	var ww = this.drill_levelWidth() *a2/data['level_max'];
	if( a2 == data['level_max'] -1 ){
		ww += 1;	//（799这种差一点点的参数值，补满条）
	}
	
	// > B分段条 - 宽度
	this._drill_sectionUpFrame_w = ww;
	this._drill_sectionDownFrame_w = this.drill_levelWidth();
}
//==============================
// * C追逐值 - 帧刷新 框架层级
//
//			说明：	此函数只操作 分段条 框架。
//==============================
Drill_COGM_MeterSprite.prototype.drill_updateChase_FrameLevel = function() {
	var data = this._drill_data;
	var cur_count = this._drill_chase_curLevel;
	
	// > B分段条
	var hh = this.drill_levelHeight();
	var u_yy = cur_count *hh;
	var u_hh = hh;
	var d_yy = (cur_count-1) *hh;
	var d_hh = hh;
	
	// > B分段条 - 最后一层时
	if( cur_count == 0 ){
		u_yy = 0 *hh;
		u_hh = hh;
		d_yy = 0;
		d_hh = 0;
	}
	
	// > B分段条 - 超过最大层时
	if( cur_count >= data['level_count'] ){
		
		// > 段循环 - 循环色
		if( data['level_isLoop'] == true ){
			var cc = cur_count % data['level_count'];
			if( cc == 0 ){
				u_yy = 0 *hh;
				u_hh = hh;
				d_yy = (data['level_count'] - 1) *hh;
				d_hh = hh;
			}else{
				u_yy = cc *hh;
				u_hh = hh;
				d_yy = (cc-1) *hh;
				d_hh = hh;
			}
			
		// > 段循环 - 不循环色
		}else{
			u_yy = (data['level_count'] - 1) *hh;
			u_hh = hh;
			d_yy = (data['level_count'] - 1) *hh;
			d_hh = hh;
		}
	}
	
	// > B分段条 - Y位置与高度
	this._drill_sectionUpFrame_y = u_yy;
	this._drill_sectionUpFrame_h = u_hh;
	this._drill_sectionDownFrame_y = d_yy;
	this._drill_sectionDownFrame_h = d_hh;
}
//==============================
// * C追逐值 - 是否被阻塞（开放接口）
//==============================
Drill_COGM_MeterSprite.prototype.drill_isChaseBlock = function(){
	if( this._drill_leak_isBlocked == true ){ return true; }
	return false;
}
//==============================
// * C追逐值 - 上段的宽度（开放接口）
//==============================
Drill_COGM_MeterSprite.prototype.drill_chaseUpWidth = function(){
	var data = this._drill_data;
	var a1 = Math.floor(this._drill_chase_curValue / data['level_max']);
	var a2 = Math.floor(this._drill_chase_curValue % data['level_max']);
	
	// > 大于一层时
	if( a1 > 0 ){
		return this.drill_levelWidth();
		
	// > 只剩最后一层时
	}else{
		return this.drill_levelWidth() *a2/data['level_max'];
	}
};


//==============================
// * D流动效果 - 初始化对象
//==============================
Drill_COGM_MeterSprite.prototype.drill_initFlow = function() {
	this._drill_flow_upMove = 0;				//D流动效果 - 上段框架x偏移值
	this._drill_flow_downMove = 0;				//D流动效果 - 下段框架x偏移值
}
//==============================
// * D流动效果 - 帧刷新 - 流动值
//==============================
Drill_COGM_MeterSprite.prototype.drill_updateFlow_Value = function() {
	var data = this._drill_data;
	
	// > 不流动
	//	（不操作）
	
	// > 流动（增量累加速度）
	var s_ww = Math.floor(this.drill_srcWidth());
	var ww = Math.floor(this.drill_levelWidth());
	if( data['flow_enable'] == true ){
		var f_speed = Math.abs( data['flow_speed'] );
		if( data['flow_dir'] == "从左往右" ){ f_speed = -1 * f_speed; }
		if( f_speed > 0){
			this._drill_flow_upMove += f_speed;
			if( this._drill_flow_upMove >= s_ww-ww ){
				this._drill_flow_upMove = 0 ;
			}
		}else if( f_speed < 0){
			this._drill_flow_upMove += f_speed;
			if( this._drill_flow_upMove <= 0 ){
				this._drill_flow_upMove = s_ww-ww ;
			}
		}
		if( f_speed > 0){
			this._drill_flow_downMove += f_speed;
			if( this._drill_flow_downMove >= s_ww-ww ){
				this._drill_flow_downMove = 0 ;
			}
		}else if( f_speed < 0){
			this._drill_flow_downMove += f_speed;
			if( this._drill_flow_downMove <= 0 ){
				this._drill_flow_downMove = s_ww-ww ;
			}
		}
	}
}
//==============================
// * D流动效果 - 帧刷新 - 框架值
//==============================
Drill_COGM_MeterSprite.prototype.drill_updateFlow_Frame = function() {
	
	// > B分段条 - X位置
	this._drill_sectionUpFrame_x += this._drill_flow_upMove;
	this._drill_sectionDownFrame_x += this._drill_flow_downMove;
}


//==============================
// * E凹槽条 - 初始化对象
//
//			说明：	凹槽条由于需要长期滞留，因此必要时需要 阻塞 C追逐值 。
//==============================
Drill_COGM_MeterSprite.prototype.drill_initLeak = function() {
	this._drill_leak_sprite = null;				//E凹槽条 - 贴图
	this._drill_leak_curValue = 0;				//E凹槽条 - 当前值
	this._drill_leak_isRecording = false;		//E凹槽条 - 记录标记
	this._drill_leak_delay = 0;					//E凹槽条 - 延时时间
	this._drill_leak_isBlocked = false;			//E凹槽条 - ■凹槽条阻塞（每次层级降1时自动阻塞）
	
	// > 资源bitmap
	var data = this._drill_data;
	if( data['leak_enable'] != true ){ return };
	var bitmap = ImageManager.loadBitmap( data['leak_src_file'], data['leak_src'], 0, true);
	
	// > 创建贴图
	this._drill_leak_sprite = new Sprite();
	this._drill_leak_sprite.bitmap = bitmap;
	this._drill_leak_sprite.zIndex = 20;
	this._drill_leak_sprite.drill_COGM_setFrame(0,0,0,0);
	this._layer_context.addChild(this._drill_leak_sprite);
	
	// > 层级排序
	this.drill_COGM_sortByZIndex();
}
//==============================
// * E凹槽条 - 帧刷新 数值
//==============================
Drill_COGM_MeterSprite.prototype.drill_updateLeaking_Value = function() {
	var data = this._drill_data;
	if( data['leak_enable'] != true ){ return };
	
	// > 滞留标记
	//		（小于追逐值时，直接设为追逐值）
	if( this._drill_leak_curValue <= this._drill_chase_curValue ){
		this._drill_leak_curValue = this._drill_chase_curValue;
		this._drill_leak_isRecording = false;
	}
	
	// > 滞留标记
	//		（大于追逐值时，开始滞留）
	if( this._drill_leak_isRecording == false ){
		if( this._drill_leak_curValue > this._drill_chase_curValue ){
			this._drill_leak_isRecording = true;
			this._drill_leak_delay = data['leak_delay'];
		}
	}
	if( this._drill_leak_isRecording == false ){ return; }
	
	
	// > 延迟 - 连续受伤时，刷新延迟
	if( data['leak_delayRefresh'] == true && this._drill_new_value != this._drill_cur_value ){
		this._drill_leak_delay = data['leak_delay'];
	}
	
	// > 延迟 - ■凹槽条阻塞
	//		（阻塞时，凹槽条不再等待，立即缩短）
	if( this._drill_leak_isBlocked == true ){
		this._drill_leak_delay = 0;
	}
	
	// > 延迟 - 开始扣除
	this._drill_leak_delay -= 1;
	if( this._drill_leak_delay <= 0 ){
		
		// > 扣除速度
		if( this._drill_leak_curValue > this._drill_chase_curValue ){
			this._drill_leak_curValue -= ( data['level_max'] *data['leak_speed']/this.drill_levelWidth() );
			if( this._drill_leak_curValue < this._drill_chase_curValue ){
				this._drill_leak_curValue = this._drill_chase_curValue;
			}
		}
	}
}
//==============================
// * E凹槽条 - 帧刷新 框架
//==============================
Drill_COGM_MeterSprite.prototype.drill_updateLeaking_Frame = function() {
	var data = this._drill_data;
	if( data['leak_enable'] != true ){ return };
	
	// > 凹槽条长度
	var l_a1 = Math.floor(this._drill_leak_curValue / data['level_max']);
	var l_a2 = Math.floor(this._drill_leak_curValue % data['level_max']);
	var ww = this.drill_levelWidth() * l_a2 / data['level_max'];
	this._drill_leak_sprite.drill_COGM_setFrame( 0, 0, ww, this.drill_levelHeight() );
}
//==============================
// * E凹槽条 - 帧刷新 阻塞
//
//			说明：	层级下降一层时，等待凹槽条流逝完毕的状态，该状态显示的分段条会卡在最大值位置。
//==============================
Drill_COGM_MeterSprite.prototype.drill_updateLeaking_Block = function() {
	var data = this._drill_data;
	if( data['leak_enable'] != true ){ return; };
	
	// > ■凹槽条阻塞
	//		（凹槽条 流逝完毕后 才结束阻塞）
	if( this._drill_leak_curValue <= this._drill_chase_curValue ){
		this._drill_leak_isBlocked = false;
		this._drill_leak_isRecording = false;
	}
	
	// > ■凹槽条阻塞
	//		（阻塞时，层级顺序变化）
	var a1 = Math.floor(this._drill_chase_curValue / data['level_max']);
	var l_a1 = Math.floor(this._drill_leak_curValue / data['level_max']);
	if( this._drill_leak_isBlocked == true ){
		if( a1 < l_a1 ){
			if( this._drill_leak_sprite.zIndex != 40 ){
				this._drill_leak_sprite.zIndex = 40;
				this.drill_COGM_sortByZIndex();
			}
		}else{
			if( this._drill_leak_sprite.zIndex != 20 ){
				this._drill_leak_sprite.zIndex = 20;
				this.drill_COGM_sortByZIndex();
			}
		}
		
		// > ■凹槽条阻塞
		//		（阻塞时，条为满状态）
		this._drill_sectionUpFrame_w = this.drill_levelWidth();
		this._drill_sectionDownFrame_w = this.drill_levelWidth();
	}
	
	// > 未阻塞时，保持夹在 上段/下段 的中间
	if( this._drill_leak_isBlocked == false && 
		this._drill_leak_sprite.zIndex != 20 ){
		this._drill_leak_sprite.zIndex = 20;
		this.drill_COGM_sortByZIndex();
	}
}


//==============================
// * F弹出条 - 初始化对象
//==============================
Drill_COGM_MeterSprite.prototype.drill_initSpring = function() {
	this._drill_spring_needInit = true;					//F弹出条 - 初始化 锁
	this._drill_spring_tank = [];						//F弹出条 - 贴图容器
	this._drill_spring_cur_tankIndex = 0;				//F弹出条 - 索引下标
	this._drill_spring_curValue = 0;					//F弹出条 - 缓冲值
}
//==============================
// * F弹出条 - 延迟初始化
//==============================
Drill_COGM_MeterSprite.prototype.drill_delayingInitSpring = function() {
	var data = this._drill_data;
	if( data['spring_enable'] == false ){ return; }
	this._drill_spring_tank = [];
	for(var j=0; j < data['spring_maxNum']; j++){
		
		// > 弹出条初始化（与分段条宽度高度一样）
		var cut_height = this.drill_srcHeight() / data['level_count'];
		var temp_sprite = new Drill_COGM_SpringSprite( this.drill_srcWidth(), cut_height );
		this._drill_spring_tank[j] = temp_sprite;
		this._layer_outer.addChild(temp_sprite);
	}
}
//==============================
// * F弹出条 - 帧刷新
//==============================
Drill_COGM_MeterSprite.prototype.drill_updateSpringShowing = function() {
	var data = this._drill_data;
	if( data['spring_enable'] == false ){ return; }
	
	// > 参数增加
	if( this._drill_spring_curValue < this._drill_new_value ){
		// ... 暂不操作
	}
	
	// > 参数减少
	if( this._drill_spring_curValue > this._drill_new_value ){
		
		var w1 = Math.floor(this._drill_spring_curValue % data['level_max']);		//原切点（参数值）
		var w2 = Math.floor(this._drill_new_value % data['level_max']);				//新切点（参数值）
		w1 = this.drill_levelWidth() / data['level_max'] * w1;						//原切点（长度值）
		w2 = this.drill_levelWidth() / data['level_max'] * w2;						//新切点（长度值）
		
		// > 当前段减少
		if( w1 > w2 ){
			var spring_bitmap = this._drill_section_bitmap;
			var spring_data = {};		//重新组装必要参数
			spring_data['pos_x'] = w2;
			spring_data['pos_y'] = 0;
			spring_data['xx'] = this._drill_sectionUpFrame_x;
			spring_data['yy'] = this._drill_sectionUpFrame_y;
			spring_data['ww'] = w1-w2;	//弹出条宽度
			spring_data['hh'] = this.drill_levelHeight();
			spring_data['type'] = data['spring_type'];
			spring_data['ballistics'] = data['spring_ballistics'];
			var spring_sprite = this._drill_spring_tank[ this._drill_spring_cur_tankIndex ];
			spring_sprite.drill_resetData( spring_data, spring_bitmap );
			
			this._drill_spring_cur_tankIndex += 1;
			this._drill_spring_cur_tankIndex %= data['spring_maxNum'];
			
		// > 段直接减少到下一段
		} else if( w2 > w1 ){
			var spring_bitmap = this._drill_section_bitmap;
			var spring_data = {};		//头部的碎片
			spring_data['pos_x'] = 0;
			spring_data['pos_y'] = 0;
			spring_data['xx'] = this._drill_sectionUpFrame_x;
			spring_data['yy'] = this._drill_sectionUpFrame_y;
			spring_data['ww'] = w1;
			spring_data['hh'] = this.drill_levelHeight();
			spring_data['type'] = data['spring_type'];
			spring_data['ballistics'] = data['spring_ballistics'];
			var spring_sprite = this._drill_spring_tank[ this._drill_spring_cur_tankIndex ];
			spring_sprite.drill_resetData( spring_data, spring_bitmap );
			this._drill_spring_cur_tankIndex += 1;
			this._drill_spring_cur_tankIndex %= data['spring_maxNum'];
			
			var spring_bitmap = this._drill_section_bitmap;
			var spring_data = {};		//尾部的碎片
			spring_data['pos_x'] = w2;
			spring_data['pos_y'] = 0;
			spring_data['xx'] = this._drill_sectionDownFrame_x;
			spring_data['yy'] = this._drill_sectionDownFrame_y;
			spring_data['ww'] = this.drill_levelWidth() - w2;
			spring_data['hh'] = this.drill_levelHeight();
			spring_data['type'] = data['spring_type'];
			spring_data['ballistics'] = data['spring_ballistics'];
			var spring_sprite = this._drill_spring_tank[ this._drill_spring_cur_tankIndex ];
			spring_sprite.drill_resetData( spring_data, spring_bitmap );
			this._drill_spring_cur_tankIndex += 1;
			this._drill_spring_cur_tankIndex %= data['spring_maxNum'];
		}
	}
	
	// > 控制参数
	if( this._drill_spring_curValue != this._drill_new_value ){
		this._drill_spring_curValue = this._drill_new_value;
	}
}


//==============================
// * G粒子 - 初始化对象
//==============================
Drill_COGM_MeterSprite.prototype.drill_initParticle = function() {
	this._drill_par_needInit = true;					//G粒子 - 初始化 锁
	this._drill_par_spriteTank = [];					//G粒子 - 贴图容器
	this._drill_par_bitmap = null;						//G粒子 - bitmap
	
	// > 资源bitmap
	var data = this._drill_data;
	if( data['par_enable'] == false ){ return; }
	this._drill_par_bitmap = ImageManager.loadBitmap( data['par_src_file'], data['par_src'], 0, true);
}
//==============================
// * G粒子 - 延迟初始化
//==============================
Drill_COGM_MeterSprite.prototype.drill_delayingInitParticle = function() {
	var data = this._drill_data;
	if( data['par_enable'] == false ){ return; }
	
	// > 创建粒子
	for( var j = 0; j < data['par_count'] ; j++ ){
		var temp_sprite = new Sprite( this._drill_par_bitmap );
		temp_sprite.anchor.x = 0.5;
		temp_sprite.anchor.y = 0.5;
		temp_sprite.opacity = 0;
		temp_sprite.cur_life = Math.random() * data['par_life'];
		temp_sprite.cur_random = 0.3*(Math.random()-0.5)	//随机因子
		temp_sprite.zIndex = 40;
		
		this._drill_par_spriteTank.push(temp_sprite);
		this._layer_context.addChild(temp_sprite);	
	}
}
//==============================
// * G粒子 - 帧刷新
//==============================
Drill_COGM_MeterSprite.prototype.drill_updateParticle = function() {
	var data = this._drill_data;
	if( data['par_enable'] == false ){ return; }
	if( this._drill_par_bitmap == undefined ){ return; }
	if( this._drill_par_bitmap.isReady() == false ){ return; }
	
	var pw = this._drill_par_bitmap.width;
	var ph = this._drill_par_bitmap.height;
	
	for(var i= 0; i< this._drill_par_spriteTank.length; i++){
		var par = this._drill_par_spriteTank[i];
		
		if( par.x < 0 - pw ||		 //超出边界判定
			par.x > this.drill_chaseUpWidth() + pw ||
			par.y < 0 - ph ||
			par.y > this.drill_levelHeight() + ph ){
			par.cur_life -= par.cur_life/3;
		}
		
		par.x += (data['par_speedX'] + par.cur_random);
		par.y += (data['par_speedY'] + par.cur_random);
		par.cur_life -= 1;
		if( data['par_mode'] == "随机出现" ||
			data['par_mode'] == "左侧出现" ){	//随机出现有先显示后消失的过程
			var q = data['par_life']/4;
			if( par.cur_life < q ){
				par.opacity = 255 * ( par.cur_life / q) ;
			}else if( par.cur_life > q*3 ){
				par.opacity = 255 * (( par.cur_life-q*3)/ q) ;
			}
		}else{
			par.opacity = 255 * ( par.cur_life / data['par_life']);
		}
		
		if( par.cur_life <= 0 ){	//重新出现粒子
			par.cur_life = data['par_life'] + Math.random()*0.4*data['par_life'];
			if( data['par_mode'] == "底部出现" ){
				par.x = this.drill_chaseUpWidth() * Math.random();
				par.y = this.drill_levelHeight() + ph/2;
			}else if( data['par_mode'] == "顶部出现" ){
				par.x = this.drill_chaseUpWidth() * Math.random();
				par.y = 0 - ph/2;
			}else if( data['par_mode'] == "左侧出现" ){
				par.opacity = 0;
				par.x = 0 - pw/2 * Math.random();
				par.y = this.drill_levelHeight() * Math.random();
			}else if( data['par_mode'] == "右侧出现" ){
				par.x = this.drill_chaseUpWidth() + pw/2;
				par.y = this.drill_levelHeight() * Math.random();
			}else{
				par.opacity = 0;		//随机出现
				par.x = this.drill_chaseUpWidth() * Math.random();
				par.y = this.drill_levelHeight() * Math.random();
			}
		}
	}
}

//==============================
// * H游标 - 初始化对象
//==============================
Drill_COGM_MeterSprite.prototype.drill_initVernier = function() {
	this._drill_vernier_needInit = true;				//H游标 - 初始化 锁
	this._drill_vernier_sprite = null;					//H游标 - 贴图
	this._drill_vernier_bitmaps = [];					//H游标 - bitmap
	this._drill_vernier_flicker = 1;					//H游标 - 闪烁方向
	this._drill_vernier_time = 0;						//H游标 - 当前时间
	this._drill_vernier_cur = 0;						//H游标 - 当前动画帧
	
	// > 资源bitmap
	var data = this._drill_data;
	if( data['vernier_enable'] == false ){ return; }
	this._drill_vernier_bitmaps = [];
	for(var j=0; j < data['vernier_src'].length; j++){
		var bitmap = ImageManager.loadBitmap( data['vernier_src_file'], data['vernier_src'][j], 0, true);
		this._drill_vernier_bitmaps[j] = bitmap;
	}
}
//==============================
// * H游标 - 延迟初始化
//==============================
Drill_COGM_MeterSprite.prototype.drill_delayingInitVernier = function() {
	var data = this._drill_data;
	if( data['vernier_enable'] == false ){ return; }
	
	// > 游标贴图
	var temp_sprite = new Sprite( this._drill_vernier_bitmaps[0] );
	temp_sprite.anchor.x = 0.5;
	temp_sprite.anchor.y = 0.5;
	temp_sprite.opacity = 0;
	this._drill_vernier_sprite = temp_sprite;
	
	// > 所在层级
	if( data['vernier_maskCover'] == true ){
		temp_sprite.zIndex = 50;
		this._layer_context.addChild( temp_sprite );	//（处于内容层，会被遮挡）
	}else{
		temp_sprite.zIndex = 10;
		this._layer_outer.addChild( temp_sprite );		//（游标位于弹出层）
	}
}
//==============================
// * H游标 - 帧刷新
//==============================
Drill_COGM_MeterSprite.prototype.drill_updateVernier = function() {
	var data = this._drill_data;
	if( data['vernier_enable'] == false ){ return; }
	if( this._drill_vernier_sprite == null ){ return; }
	
	// > 游标位置
	var temp_sprite = this._drill_vernier_sprite;
	var float_pos;
	var float_cur = this._drill_chase_curValue;
	var float_max = data['level_max'];
	if( data['vernier_reset'] ){		//多段复位
		var a2 = Math.floor( float_cur % float_max);
		float_pos = this.drill_levelWidth() * a2 / float_max;
	}else{
		float_pos = this.drill_chaseUpWidth(); 
	}
	temp_sprite.x = float_pos; 
	temp_sprite.y = this.drill_levelHeight()/2;
	temp_sprite.x += data['vernier_x'];
	temp_sprite.y += data['vernier_y'];
	
	// > 动画帧
	this._drill_vernier_time += 1;
	if( this._drill_vernier_time > data['vernier_gif_interval'] ){
		this._drill_vernier_time = 0;
		
		this._drill_vernier_cur += 1;
		var len = this._drill_vernier_bitmaps.length;
		if(this._drill_vernier_cur >= len ){
			this._drill_vernier_cur = 0;
		}
		if( data['vernier_gif_backrun'] ){
			temp_sprite.bitmap = this._drill_vernier_bitmaps[len - 1 - this._drill_vernier_cur];
		}else{
			temp_sprite.bitmap = this._drill_vernier_bitmaps[this._drill_vernier_cur];
		}
	}
	
	// > 游标模式
	if( data['vernier_mode'] == "亮光模式" ){
		var a2 = Math.floor( float_cur % float_max);
		var tar_o ;
		if( a2 < float_max/3){
			tar_o = 255 * a2/(float_max/3);
		}
		if( a2 > float_max/3*2){
			tar_o = 255 - 255 * (a2 - float_max/3*2)/(float_max/3);
		}
		if(temp_sprite.opacity > tar_o){
			temp_sprite.opacity -= 3;
			if(temp_sprite.opacity < tar_o){
				temp_sprite.opacity = tar_o;
			}
		}
		if(temp_sprite.opacity < tar_o){
			temp_sprite.opacity += 3;
			if(temp_sprite.opacity > tar_o){
				temp_sprite.opacity = tar_o;
			}
		}
	}else if( data['vernier_mode'] == "闪烁模式" ){
		temp_sprite.opacity += this._drill_vernier_flicker * 5;
		if(temp_sprite.opacity == 255){
			this._drill_vernier_flicker = -1;
		}
		if(temp_sprite.opacity == 0){
			this._drill_vernier_flicker = 1;
		}
	}else if( data['vernier_mode'] == "受伤模式" ){
		temp_sprite.opacity -= 5;
		if( this._drill_cur_value > this._drill_new_value ){
			temp_sprite.opacity = 255;
		}
	}else if( data['vernier_mode'] == "增量模式" ){
		temp_sprite.opacity -= 5;
		if( this._drill_cur_value < this._drill_new_value ){
			temp_sprite.opacity = 255;
		}
	}else if( data['vernier_mode'] == "变化模式" ){
		temp_sprite.opacity -= 5;
		if( this._drill_cur_value > this._drill_new_value ){
			temp_sprite.opacity = 255;
		}
		if( this._drill_cur_value < this._drill_new_value ){
			temp_sprite.opacity = 255;
		}
	}else{
		temp_sprite.opacity = 255;
	}
}


//==============================
// * I加满动画 - 初始化对象
//==============================
Drill_COGM_MeterSprite.prototype.drill_initFilling = function() {
	this._drill_filling_needInit = true;				//I加满动画 - 初始化 锁
	this._drill_filling_mask = null;					//I加满动画 - 贴图
}
//==============================
// * I加满动画 - 延迟初始化
//==============================
Drill_COGM_MeterSprite.prototype.drill_delayingInitFilling = function() {
	var data = this._drill_data;
	if( data['filling_enable'] == false ){ return; }
	
	var filling_data = {};
	filling_data['x'] = 0;
	filling_data['y'] = 0;
	filling_data['w'] = this.drill_levelWidth();
	filling_data['h'] = this.drill_levelHeight();
	filling_data['mode'] = data['filling_mode'];
	filling_data['time'] = data['filling_time'];
	filling_data['delay'] = data['filling_delay'];
	
	var temp_mask = new Drill_COGM_MeterSpriteMask( filling_data );
	this.mask = temp_mask;
	this.addChild(temp_mask);
	this._drill_filling_mask = temp_mask;
}
//==============================
// * I加满动画 - 帧刷新
//==============================
Drill_COGM_MeterSprite.prototype.drill_updateFilling = function() {
	var data = this._drill_data;
	if( data['filling_enable'] == false ){ return; }
	if( this._drill_filling_mask == null ){ return; }
	
	// > 时间流逝
	this._drill_filling_mask._drill_cur_time += 1;
	
	// > 播放结束后销毁（防止遮罩挡住弹出条）
	if( this._drill_filling_mask.drill_isPlaying() == false ){
		this.mask = null;
		this.removeChild( this._drill_filling_mask );
		this._drill_filling_mask = null;
	}
}
//=============================================================================
// * 数学 - 锁定锚点
//			
//			参数：	> org_anchor_x 数字    （原贴图锚点X）
//					> org_anchor_y 数字    （原贴图锚点Y）
//					> target_anchor_x 数字 （新的锚点X）
//					> target_anchor_y 数字 （新的锚点Y）
//					> width 数字           （贴图宽度）
//					> height 数字          （贴图高度）
//					> rotation 数字        （旋转度数，弧度）
//					> scale_x,scale_y 数字 （缩放比例XY，默认1.00）
//			返回：	> { x:0, y:0 }         （偏移的坐标）
//			
//			说明：	修正 旋转+缩放 的坐标，使其看起来像是在绕着 新的锚点 变换。
//					旋转值和缩放值可为负数。
//=============================================================================
Game_Temp.prototype.drill_COGM_getFixPointInAnchor = function( 
					org_anchor_x,org_anchor_y,			//原贴图中心锚点 
					target_anchor_x,target_anchor_y, 	//新的中心锚点 
					width, height,						//贴图高宽
					rotation, scale_x, scale_y ) {		//变换的值（旋转弧度+缩放）
	
	var ww = width * ( target_anchor_x - org_anchor_x );
	var hh = height * ( target_anchor_y - org_anchor_y );
	var xx = 0;
	var yy = 0;
	if( ww == 0 && hh == 0){ return { "x":0, "y":0 }; }
	if( ww == 0 ){ ww = 0.0001; }
	
	// > 先缩放
	var sww = ww*scale_x;
	var shh = hh*scale_y;
	
	// > 后旋转
	var r = Math.sqrt( Math.pow(sww,2) + Math.pow(shh,2) );
	var p_degree = Math.atan(shh/sww);	
	p_degree = Math.PI - p_degree;
	if( sww < 0 ){
		p_degree = Math.PI + p_degree;
	}
	
	// > 变换的偏移量
	xx += r*Math.cos( rotation - p_degree);		//圆公式 (x-a)²+(y-b)²=r²
	yy += r*Math.sin( rotation - p_degree);		//圆极坐标 x=ρcosθ,y=ρsinθ
	
	// > 锚点偏移量
	xx += ww;
	yy += hh;
	
	return { "x":xx, "y":yy };
}
//=============================================================================
// * 优化 - 浮点数过滤
//
//			说明：	用floor防止 浮点数 比较时，造成frame的反复刷新。
//=============================================================================
Sprite.prototype.drill_COGM_setFrame = function( x, y, width, height ){
	this.setFrame( Math.floor(x), Math.floor(y), Math.floor(width), Math.floor(height) );
}




//=============================================================================
// ** 弹出条【Drill_COGM_SpringSprite】
// ** 
// **		接口：	见drill_resetData函数中的默认值。
// **		说明：	1.参数条专用。
// ** 				2.先初始化，再重刷。初始化时一定要赋值高宽，确定最基本的图片属性。
// ** 				3.使用的与 上段 相同的bitmap，然后进行setFrame。
//=============================================================================
//==============================
// * 弹出条 - 定义
//==============================
function Drill_COGM_SpringSprite() {
	this.initialize.apply(this, arguments);
}
Drill_COGM_SpringSprite.prototype = Object.create(Sprite.prototype);
Drill_COGM_SpringSprite.prototype.constructor = Drill_COGM_SpringSprite;
//==============================
// * 弹出条 - 初始化
//==============================
Drill_COGM_SpringSprite.prototype.initialize = function( width, height ){
	Sprite.prototype.initialize.call(this);
	
	this._drill_cur_type = "";			//当前模式
	this._drill_width = width;			//宽度
	this._drill_height = height;		//高度
	this._drill_life = 0;				//持续时间
	this._drill_time = 0;				//当前时间
	this._drill_inited = false;			//初始化
	
	this._drill_bitmap = null;
	this._drill_bitmap_white = new Bitmap( this._drill_width, this._drill_height );
	this._drill_bitmap_white.fillAll("#ffffff");
	this._drill_bitmap_black = new Bitmap( this._drill_width, this._drill_height );
	this._drill_bitmap_black.fillAll("#000000");
}
//==============================
// * 弹出条 - 帧刷新
//==============================
Drill_COGM_SpringSprite.prototype.update = function() {
	Sprite.prototype.update.call(this);
	if( this._drill_inited == false ){ return; }
	
	this.drill_updateSprite();			//帧刷新对象
}
//==============================
// * 弹出条 - 重刷全部数据
//==============================
Drill_COGM_SpringSprite.prototype.drill_resetData = function( data, parent_bitmap_obj ){
	
	// > 校验
	var life_time = data['ballistics']['movementTime'];
	if( life_time <= 0 ){ return; }
	
	// > 数据初始化
	this._drill_data = JSON.parse(JSON.stringify( data ));	//深拷贝数据
	this._drill_bitmap = parent_bitmap_obj;					//父类参数条bitmap
	this._drill_inited = true;
	var data = this._drill_data;
	
	// > 默认值
	if( data['pos_x'] == undefined ){ data['pos_x'] = 0 };					//位置x
	if( data['pos_y'] == undefined ){ data['pos_y'] = 0 };					//位置y
	if( data['xx'] == undefined ){ data['xx'] = 0 };						//框架x
	if( data['yy'] == undefined ){ data['yy'] = 0 };						//框架y
	if( data['ww'] == undefined ){ data['ww'] = 1 };						//框架w
	if( data['hh'] == undefined ){ data['hh'] = 1 };						//框架h
	if( data['type'] == undefined ){ data['type'] = "当前参数条" };			//类型
	if( data['ballistics'] == undefined ){ data['ballistics'] = {} };		//弹道
	data['ww'] = Math.max( 1, Math.ceil(data['ww']) );						//宽度可能<1情况
	data['hh'] = Math.max( 1, Math.ceil(data['hh']) );						//高度可能<1情况
	
	// > 私有对象初始化
	this._drill_life = life_time;							//持续时间
	this._drill_time = 0;									//计时器
	
	// > 弹道核心 - 初始化 + 推演
	$gameTemp.drill_COBa_setBallisticsMove( data['ballistics'] );
	$gameTemp.drill_COBa_preBallisticsMove( this, 0, data['pos_x'], data['pos_y'] );
	
	// > 设置bitmap
	var temp_bitmap = null;
	if( data['type'] == "白色块"){
		this.bitmap = this._drill_bitmap_white;
	}else if( data['type'] == "黑色块"){
		this.bitmap = this._drill_bitmap_black;
	}else{	// 当前参数条
		this.bitmap = this._drill_bitmap;
	}
	
	// > 锁定长方形
	this.drill_COGM_setFrame( data['pos_x']+data['xx'], data['yy'], data['ww'], data['hh'] );
}
//==============================
// * 弹出条 - 帧刷新
//==============================
Drill_COGM_SpringSprite.prototype.drill_updateSprite = function() {
	this._drill_time += 1;
	
	// > 显示控制
	if( this._drill_time > this._drill_life ){
		this.visible = false;
		return;
	}else{
		this.visible = true;
	}
	
	// > 轨迹移动（自身只有一个sprite，与粒子群sprite不一样）
	var time = this._drill_time;
	if( time < 0 ){ time = 0; }
	if( time > this['_drill_COBa_x'].length-1 ){
		time = this['_drill_COBa_x'].length-1;
	}
	this.x = this['_drill_COBa_x'][time];		//播放弹道轨迹
	this.y = this['_drill_COBa_y'][time];
	
	// > 抛物线透明
	var v1 = 1 / this._drill_life *2;
	var a = 1 / this._drill_life / this._drill_life;
	var t = this._drill_time;
	this.opacity = 255 * (1- (v1*t - 0.5*a*t*t));
}



//=============================================================================
// ** 加满动画遮罩【Drill_COGM_MeterSpriteMask】
// **
// **		接口：	见maskInit函数。
// **		说明：	1.参数条专用。
// **				2.初始化后即用，不需要其他函数。要有高度宽度数据。
// **				3.黑色部分和透明部分都表示遮挡。最初的状态是全遮挡的。该遮罩可以挡住弹出条。
//=============================================================================
//==============================
// * 加满遮罩 - 定义
//==============================
function Drill_COGM_MeterSpriteMask() {
	this.initialize.apply(this, arguments);
}
Drill_COGM_MeterSpriteMask.prototype = Object.create(Sprite.prototype);
Drill_COGM_MeterSpriteMask.prototype.constructor = Drill_COGM_MeterSpriteMask;
//==============================
// * 加满遮罩 - 初始化
//==============================
Drill_COGM_MeterSpriteMask.prototype.initialize = function( data ) {
	Sprite.prototype.initialize.call(this);
	this._drill_data = JSON.parse(JSON.stringify( data ));	//深拷贝数据
	this.drill_initData();									//初始化数据
	this.drill_initSprite();								//初始化对象
}
//==============================
// * 加满遮罩 - 帧刷新
//==============================
Drill_COGM_MeterSpriteMask.prototype.update = function() {
	Sprite.prototype.update.call(this);
	this.drill_updateSprite();			//帧刷新对象
}
//==============================
// * 加满遮罩 - 初始化数据
//==============================
Drill_COGM_MeterSpriteMask.prototype.drill_initData = function(){
	var data = this._drill_data;
	
	// > 默认值
	if( data['x'] == undefined ){ data['x'] = 0 };						//x
	if( data['y'] == undefined ){ data['y'] = 0 };						//y
	if( data['w'] == undefined ){ data['w'] = 1 };						//宽度
	if( data['h'] == undefined ){ data['h'] = 1 };						//高度
	if( data['mode'] == undefined ){ data['mode'] = "匀速加满" };		//加满方式
	if( data['time'] == undefined ){ data['time'] = 30 };				//加满时长
	if( data['delay'] == undefined ){ data['delay'] = 10 };				//加满延迟
};
//==============================
// * 加满遮罩 - 初始化对象
//==============================
Drill_COGM_MeterSpriteMask.prototype.drill_initSprite = function(){
	var data = this._drill_data;
	
	// > 私有对象初始化
	this._drill_cur_time = 0;								//当前时间
	this._drill_bitmap_white = new Bitmap( data['w'], data['h'] );
	this._drill_bitmap_white.fillAll("#ffffff");
	this.bitmap = this._drill_bitmap_white;
	
	// > 主体属性
	this.x = data['x'];
	this.y = data['y'];
	this.drill_COGM_setFrame( 0, 0, 0, data['h'] );
}
//==============================
// * 加满遮罩 - 帧刷新对象
//==============================
Drill_COGM_MeterSpriteMask.prototype.drill_updateSprite = function() {
	var data = this._drill_data;
	
	// > 加满动画
	var p_time = this._drill_cur_time - data['delay'];
	p_time = Math.max( 0, p_time );
	if( data['mode'] == "匀速加满" ){
		var ww = Math.ceil( data['w'] * p_time / data['time'] );
		var hh = data['h'];
		this.drill_COGM_setFrame( 0, 0, ww, hh );
	}
	if( data['mode'] == "弹性加满" ){
		var a = 2 * data['w'] / data['time'] / data['time'];
		var c_time = data['time'] - p_time;
		var ww = 0.5 * a * c_time * c_time ;		//抛物线减速
		var hh = data['h'];
		ww = Math.ceil( data['w'] - ww );
		this.drill_COGM_setFrame( 0, 0, ww, hh );
	}
}
//==============================
// * 加满遮罩 - 播放情况
//==============================
Drill_COGM_MeterSpriteMask.prototype.drill_isPlaying = function() {
	var data = this._drill_data;
	return this._drill_cur_time <= data['delay'] + data['time'];
}


//=============================================================================
// * <<<<基于插件检测<<<<
//=============================================================================
}else{
		Imported.Drill_CoreOfGaugeMeter = false;
		var pluginTip = DrillUp.drill_COGM_getPluginTip_NoBasePlugin();
		alert( pluginTip );
}


