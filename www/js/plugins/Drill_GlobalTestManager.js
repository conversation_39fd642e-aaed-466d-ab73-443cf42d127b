//=============================================================================
// Drill_GlobalTestManager.js
//=============================================================================

/*:
 * @plugindesc [v1.1]        管理器 - 调试管理器
 * <AUTHOR>
 * 
 * 
 * @help  
 * =============================================================================
 * +++ Drill_GlobalTestManager +++
 * 作者：Drill_up
 * 如果你有兴趣，也可以来看看更多我写的drill插件哦ヽ(*。>Д<)o゜
 * https://rpg.blue/thread-409713-1-1.html
 * =============================================================================
 * 该插件提供直接调用内置调试器的指令，方便测试游戏。
 * 
 * -----------------------------------------------------------------------------
 * ----插件扩展
 * 该插件可以单独使用。
 * 
 * -----------------------------------------------------------------------------
 * ----设定注意事项
 * 1.插件的作用域：菜单界面、地图界面、战斗界面。
 *   作用于内置的调试器。
 * 2.使用插件指令可以直接打开调试相关功能，
 *   方便在游戏发布后手机端上无法按F12了解情况时用。
 * 
 * -----------------------------------------------------------------------------
 * ----激活条件
 * 你可以使用下面插件指令：
 * （冒号两边都有一个空格）
 * 
 * 插件指令：>调试管理器 : 开启fps框
 * 插件指令：>调试管理器 : 开启fps框(延迟时间部分)
 * 插件指令：>调试管理器 : 隐藏fps框
 * 
 * 插件指令：>调试管理器 : 进入排错界面
 * 
 * 1.按键盘F2，能够"开启fps框"，也可以通过插件指令开启。
 * 2.按键盘F9，能够"进入排错界面"，部署的游戏中按F9无效。
 *   通过插件指令，将会强制进入排错界面，部署游戏的也能进。
 * 
 * -----------------------------------------------------------------------------
 * ----插件性能
 * 测试仪器：   4G 内存，Intel Core i5-2520M CPU 2.5GHz 处理器
 *              Intel(R) HD Graphics 3000 集显 的垃圾笔记本
 *              (笔记本的3dmark综合分：571，鲁大师综合分：48456)
 * 总时段：     20000.00ms左右
 * 对照表：     0.00ms  - 40.00ms （几乎无消耗）
 *              40.00ms - 80.00ms （低消耗）
 *              80.00ms - 120.00ms（中消耗）
 *              120.00ms以上      （高消耗）
 * 工作类型：   单次执行
 * 时间复杂度： o(n)
 * 测试方法：   开启插件，进行相应的性能测试。
 * 测试结果：   战斗界面中，平均消耗为：【5ms以下】
 *              地图界面中，平均消耗为：【5ms以下】
 *              菜单界面中，平均消耗为：【5ms以下】
 * 
 * 1.插件只在自己作用域下工作消耗性能，在其它作用域下是不工作的。
 *   测试结果并不是精确值，范围在给定值的10ms范围内波动。
 *   更多性能介绍，去看看 "0.性能测试报告 > 关于插件性能.docx"。
 * 
 * -----------------------------------------------------------------------------
 * ----更新日志
 * [v1.0]
 * 完成插件ヽ(*。>Д<)o゜
 * [v1.1]
 * 修改了插件分类。
 * 
 * 
 * @param 初始是否开启fps框
 * @type boolean
 * @on 开启
 * @off 关闭
 * @desc 进入游戏前，自动开启fps框。
 * @default false
 * 
 */
 
//<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
//		插件简称：		GTM (Global_Test_Manager)
//		临时全局变量	无
//		临时局部变量	无
//		存储数据变量	无
//		全局存储变量	无
//		覆盖重写方法	无
//
//<<<<<<<<性能记录<<<<<<<<
//
//		★工作类型		单次执行
//		★时间复杂度		o(n)
//		★性能测试因素	无
//		★性能测试消耗	无
//		★最坏情况		无
//		★备注			只提供插件指令。
//		
//		★优化记录		暂无
//
//<<<<<<<<插件记录<<<<<<<<
//
//		★功能结构树：
//			调试管理器：
//				->开启fps
//				->开启调试界面
//		
//		★家谱：
//			无
//		
//		★脚本文档：
//			无
//		
//		★插件私有类：
//			无
//		
//		★必要注意事项：
//			暂无
//
//		★其它说明细节：
//			暂无
//
//		★存在的问题：
//			暂无
//

//=============================================================================
// ** 提示信息
//=============================================================================
	//==============================
	// * 提示信息 - 参数
	//==============================
	var DrillUp = DrillUp || {}; 
	DrillUp.g_GTM_PluginTip_curName = "Drill_GlobalTestManager.js 管理器-调试管理器";
	DrillUp.g_GTM_PluginTip_baseList = [];
	
	
//=============================================================================
// ** 静态数据
//=============================================================================
　　var Imported = Imported || {};
　　Imported.Drill_GlobalTestManager = true;
　　var DrillUp = DrillUp || {}; 
    DrillUp.parameters = PluginManager.parameters('Drill_GlobalTestManager');
	
	
	/*-----------------杂项------------------*/
	DrillUp.g_GTM_showFPS = String(DrillUp.parameters["初始是否开启fps框"] || "false") === "true";	
	

//=============================================================================
// ** 插件指令
//=============================================================================
var _drill_GTM_pluginCommand = Game_Interpreter.prototype.pluginCommand;
Game_Interpreter.prototype.pluginCommand = function(command, args) {
	_drill_GTM_pluginCommand.call(this, command, args);
	if( command === ">调试管理器" ){
		if( args.length == 2 ){
			var type = String(args[1]);
			if( type == "开启fps框" ){
				Graphics.showFps();
				Graphics._fpsMeter.showFps();
				Graphics._fpsMeterToggled = false;
			}
			if( type == "开启fps框(延迟时间部分)" ){
				Graphics._fpsMeter.showDuration();
				Graphics._fpsMeterToggled = true;
			}
			if( type == "隐藏fps框" ){
				Graphics.hideFps();
			}
			if( type == "进入排错界面" ){
				SceneManager.push(Scene_Debug);
			}
		}
	}
}

//=============================================================================
// * 功能部件 - 初始是否开启fps框
//=============================================================================
var _drill_GTM__createAllElements = Graphics._createAllElements;
Graphics._createAllElements = function(){
	_drill_GTM__createAllElements.call(this);
	if( DrillUp.g_GTM_showFPS == true ){
		this.showFps();
		this._fpsMeter.showFps();
        this._fpsMeterToggled = false;
	}
};