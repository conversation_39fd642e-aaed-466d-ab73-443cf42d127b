//=============================================================================
// Drill_LayerIllumination.js
//=============================================================================

/*:
 * @plugindesc [v1.9]        地图 - 自定义照明效果
 * <AUTHOR>
 * 
 * @Drill_LE_param "光源-%d"
 * @Drill_LE_parentKey "---光源组%d至%d---"
 * @Drill_LE_var "DrillUp.g_LIl_light_length"
 * 
 * 
 * @help 
 * =============================================================================
 * +++ Drill_LayerIllumination +++
 * 作者：Drill_up
 * 如果你有兴趣，也可以来看看更多我写的drill插件哦ヽ(*。>Д<)o゜
 * https://rpg.blue/thread-409713-1-1.html
 * =============================================================================
 * 你可以用自己画的照明资源图片，然后绑定到玩家、事件身上。
 * 
 * -----------------------------------------------------------------------------
 * ----插件扩展
 * 该插件 不能 单独使用。
 * 必须基于核心插件才能运行。也可以作用于其他插件。
 * 基于：
 *   - Drill_CoreOfDynamicMask     系统-动态遮罩核心★★v1.2及以上★★
 * 作用于：
 *   - Drill_MouseIllumination     鼠标-自定义照明效果★★v1.1及以上★★
 *     使得鼠标也能够具备照明效果。
 *   - Drill_BombCore              炸弹人-游戏核心
 *     使得炸弹人的炸弹能够具备照明效果。
 * 
 * -----------------------------------------------------------------------------
 * ----设定注意事项
 * 1.插件的作用域：地图界面。
 *   作用于玩家、事件。
 * 2.建议先了解 "0.基本定义 > 显示与透明度.docx"。
 *   详细内容可以去看看 "6.地图 > 关于自定义照明效果.docx"。
 * 黑暗层：
 *   (1.黑暗层可以放在地图层级的 上层、图片层、最顶层。
 *      一般为上层，因为再往上的层级可以挡住ui和图片。
 *   (2.黑暗层的底层原理是滤镜，所以不能修改混合模式。
 *      黑暗层默认是固定黑色"#000000"。
 * 黑暗层开关：
 *   (1.黑暗层通过插件指令进行开关。
 *      开启/关闭后将会执行一段过渡时间，类似于昼夜更替。
 *   (2.地图备注 会临时锁定该地图的黑暗层设置，
 *      离开该地图后恢复原黑暗层设置。
 *   (3.插件指令无法影响 锁定地图 的黑暗层。但是可以影响默认的黑暗层。
 *      比如，屋内地图添加 锁定注释，屋外地图 无注释。
 *      那么，在屋内执行插件指令，屋内不会变黑，去了屋外，会发现已经变黑。
 *   (4.地图备注主要用于 黑暗层 不受外界影响变化的地图，
 *      比如，屋内、车厢、山洞、鬼屋、过场剧情地图 等。
 * 自画资源：
 *   (1.所有照明的形状、大小都需要你自己画照明素材来提供。
 *      通常为白色和透明为主。
 *   (2.单个图块的像素是48x48。所需光照素材的大小通常较大，
 *      你也可以修改光源配置的 缩放比例 来放大光源。
 * 多种颜色：
 *   (1.资源图片的颜色默认都是纯白与透明。
 *      你可以设置其它颜色，可以产生不同效果，但要注意区分。
 *   (2.插件与纯色滤镜的功能相似。
 *      光的三原色是：红、绿、蓝。 
 *      黄=红+绿。紫=红+蓝。青=蓝+绿。白=红+绿+蓝。
 *   (3.黑暗层默认是纯黑色，如果你设置纯蓝，地图界面将会看见蓝色光线。
 *      不要用纯白色，因为什么光线都过滤不了。
 * 物体照明：
 *   (1.物体照明的注释 跨事件页，不关会长期存在。
 *      如果要关闭照明，需要添加"关闭照明"的注释。
 *      插件指令设置只在当前地图有效，离开地图失效。
 *      但是玩家的照明设置不会失效。
 *   (2.每个事件只能绑定一个照明效果。
 *      并且这个照明效果可以随着事件的朝向而转向。
 *   (3.当你切换进入菜单后，立刻离开，你会发现光源会闪一下。
 *      这属于正常现象，因为切换时，地图必须重新扫描加载全部光源。
 * 限时动态照明：
 *   (1.动态照明只能存在一段时间，时间结束后会被清除。
 *   (2.动态照明不能转向。
 * 设计：
 *   (1.你可以在地图注释中，设置颜色、透明度、开关等。
 *      可以实现不同的地图有不同的黑暗效果。
 *   (2.光源是以图片资源或GIF的模式展现的，你可以制作gif动画的光源效果。
 *      简单的方形、圆形光源，可以直接修改 缩放比例 来快速设置。
 * 旧版本：
 *   (1.注意，v1.3以前版本的黑暗层指令歧义很大，容易误解。
 *      新版本已经不支持旧指令，你需要重新设置指令。
 * 
 * -----------------------------------------------------------------------------
 * ----关联文件
 * 资源路径：img/Map__illumination （Map后面有两个下划线）
 * 先确保项目img文件夹下是否有Map__illumination文件夹！
 * 要查看所有关联资源文件的插件，可以去看看"插件清单.xlsx"。
 * 如果没有，需要自己建立。需要配置资源文件：
 * 
 * 光源-1 资源-光源GIF
 * 光源-2 资源-光源GIF
 * 光源-3 资源-光源GIF
 * ……
 * 
 * 所有素材都放在Map__illumination文件夹下。
 * 
 * -----------------------------------------------------------------------------
 * ----激活条件 - 黑暗层
 * 你可以通过插件指令手动控制黑暗层：
 * 
 * 插件指令：>自定义照明 : 黑暗层 : 执行开启
 * 插件指令：>自定义照明 : 黑暗层 : 执行关闭
 * 插件指令：>自定义照明 : 黑暗层 : 修改黑暗层透明度 : 155
 * 插件指令：>自定义照明 : 黑暗层 : 修改黑暗层过渡时间 : 60
 * 插件指令：>自定义照明 : 黑暗层 : 修改黑暗层颜色 : #00ff00
 * 
 * 1."黑暗层透明度[0]"的值变为0时，照明效果将会自动关闭。
 *   只要透明度的值大于0，就表示启用了黑暗层，就会持续消耗并工作。
 *   注意，这里的黑暗层设置，不作用于 锁定 的地图。
 * 2."黑暗层过渡时间"单位为帧，1秒60帧。
 * 3.黑暗层与纯色滤镜的功能相似。颜色控制相应的过滤。
 *   不要用纯白色，因为什么光线都过滤不了。
 * 
 * -----------------------------------------------------------------------------
 * ----激活条件 - 临时锁定 黑暗层
 * 你可以通过插件指令手动控制黑暗层：
 * 
 * 地图备注：=>自定义照明:临时锁定:开启
 * 地图备注：=>自定义照明:临时锁定:关闭
 * 地图备注：=>自定义照明:临时锁定:黑暗层透明度:155
 * 地图备注：=>自定义照明:临时锁定:黑暗层颜色:#00ff00
 * 
 * 1.注意，这里是地图备注，在地图的备注中添加。
 * 2.地图备注 会临时锁定该地图的黑暗层设置，离开该地图后恢复原黑暗层设置。
 *   插件指令无法影响 锁定地图 的黑暗层。但是可以影响默认的黑暗层。
 *   比如，屋内地图添加 锁定注释，屋外地图 无注释。
 *   那么，在屋内执行插件指令，屋内不会变黑，去了屋外，会发现已经变黑。
 * 3.地图备注主要用于 黑暗层 不受外界影响变化的地图，
 *   比如，屋内、车厢、山洞、鬼屋、过场剧情地图 等。
 * 
 * -----------------------------------------------------------------------------
 * ----激活条件 - 事件照明
 * 你可以通过插件指令手动控制事件照明：
 * 
 * 事件注释：=>自定义照明 : 物体照明 : 照明[1]
 * 事件注释：=>自定义照明 : 物体照明 : 关闭照明
 * 
 * 插件指令：>自定义照明 : 物体照明 : 玩家 : 照明[1]
 * 插件指令：>自定义照明 : 物体照明 : 本事件 : 照明[1]
 * 插件指令：>自定义照明 : 物体照明 : 事件[10] : 照明[1]
 * 插件指令：>自定义照明 : 物体照明 : 事件变量[21] : 照明[1]
 * 插件指令：>自定义照明 : 物体照明 : 批量事件[10,11] : 照明[1]
 * 插件指令：>自定义照明 : 物体照明 : 批量事件变量[21,22] : 照明[1]
 * 
 * 插件指令：>自定义照明 : 物体照明 : 玩家 : 照明[1]
 * 插件指令：>自定义照明 : 物体照明 : 玩家 : 关闭照明
 * 插件指令：>自定义照明 : 物体照明 : 玩家 : 修改图片层级[0]
 * 
 * 1.前面部分（玩家）和后面设置（照明[1]）可以随意组合。
 *   一共有6*3种组合方式。
 * 2."照明[1]"对应配置的第1个光源，光源和照明是一样的意思。
 * 3.事件注释的物体照明会长期存在，且跨事件页。
 *   如果要关闭照明，需要添加"关闭照明"的注释。
 *   而插件指令设置只在当前地图有效，离开地图失效。
 * 
 * -----------------------------------------------------------------------------
 * ----可选设定 - 高级照明
 * 你可以通过插件指令创建设置高级照明：
 * 
 * 插件指令：>自定义照明 : 高级照明[2] : 创建 : 样式[3]
 * 插件指令：>自定义照明 : 高级照明[2] : 清除
 * 
 * 插件指令：>自定义照明 : 高级照明[2] : 设置生命 : 持续时间[180]
 * 插件指令：>自定义照明 : 高级照明[2] : 暂停生命流逝
 * 插件指令：>自定义照明 : 高级照明[2] : 继续生命流逝
 * 
 * 插件指令：>自定义照明 : 高级照明[2] : 绑定到 : 玩家
 * 插件指令：>自定义照明 : 高级照明[2] : 绑定到 : 本事件
 * 插件指令：>自定义照明 : 高级照明[2] : 绑定到 : 事件[10]
 * 插件指令：>自定义照明 : 高级照明[2] : 绑定到 : 事件变量[10]
 * 插件指令：>自定义照明 : 高级照明[2] : 绑定到 : 鼠标
 * 插件指令：>自定义照明 : 高级照明[2] : 绑定到 : 图片[10]
 * 插件指令：>自定义照明 : 高级照明[2] : 绑定到 : 图片变量[10]
 * 插件指令：>自定义照明 : 高级照明[2] : 位置归零
 * 
 * 1."设置生命"是指 高级照明 在持续时间结束后，会被自动清除。
 *   多用于临时安排设置的透视镜效果。
 * 2.高级照明能够跨 地图 存在，并且能跨越 地图界面和战斗界面 。
 *   如果暂时不用，要记得关闭，避免透视镜长期滞留。
 * 
 * -----------------------------------------------------------------------------
 * ----可选设定 - 高级照明变量
 * 你可以通过插件指令创建设置临时高级照明：
 * 
 * 插件指令：>自定义照明 : 高级照明变量[21] : 创建 : 样式[3]
 * 插件指令：>自定义照明 : 高级照明变量[21] : 清除
 * 插件指令：>自定义照明 : 获取未创建的高级照明编号[100-200] : 变量[21]
 * 
 * 插件指令：>自定义照明 : 高级照明变量[2] : 设置生命 : 持续时间[180]
 * 插件指令：>自定义照明 : 高级照明变量[2] : 暂停生命流逝
 * 插件指令：>自定义照明 : 高级照明变量[2] : 继续生命流逝
 * 
 * 1."高级照明变量[21]"均能适配可选设定中
 *   "高级照明[2]"的 绑定、移动、变化 等的用法。
 * 2."编号[100-200]"指从id为100至200的范围中，找出一个未创建的编号。
 * 3.使用变量获取一个未使用的自动编号，然后创建 高级照明，
 *   创建后设置该 高级照明 的生命，实现时效结束后自动清除。
 *   通过上述流程，可以使得 永久有效的高级照明 变成临时的照明功能。
 * 4.由于生命结束后自动销毁，下一次获取自动编号时，
 *   可以获取到销毁空出来的那个编号。
 * 
 * -----------------------------------------------------------------------------
 * ----可选设定 - 移动
 * 你可以通过插件指令控制高级照明移动：
 * 
 * 插件指令：>自定义照明 : 高级照明[2] : 瞬间移动 : 位置[100,200]
 * 插件指令：>自定义照明 : 高级照明[2] : 匀速移动 : 位置[100,200] : 时间[20]
 * 插件指令：>自定义照明 : 高级照明[2] : 增减速移动 : 位置[100,200] : 时间[20]
 * 插件指令：>自定义照明 : 高级照明[2] : 弹性移动 : 位置[100,200] : 时间[20]
 * 插件指令：>自定义照明 : 高级照明[2] : 抛物线移动 : 位置[100,200] : 时间[20]
 * 
 * 插件指令：>自定义照明 : 高级照明[2] : 匀速移动 : 位置[100,200] : 时间[20]
 * 插件指令：>自定义照明 : 高级照明[2] : 匀速移动 : 位置变量[25,26] : 时间[20]
 * 插件指令：>自定义照明 : 高级照明[2] : 匀速移动 : 相对位置[-100,0] : 时间[20]
 * 插件指令：>自定义照明 : 高级照明[2] : 匀速移动 : 相对位置变量[25,26] : 时间[20]
 * 
 * 1.前面部分（瞬间移动）和后面设置（位置[10,12]）可以随意组合。
 *   一共有5*4种组合方式。
 * 2.注意，如果高级照明已经绑定了 事件或图片，那么该透视镜会与 移动的坐标量 叠加。
 * 
 * -----------------------------------------------------------------------------
 * ----可选设定 - 缩放、透明度、旋转变化
 * 你可以通过插件指令控制高级照明缩放变化：
 * 
 * 插件指令：>自定义照明 : 高级照明[2] : 瞬间变化 : 缩放X[1.2]
 * 插件指令：>自定义照明 : 高级照明[2] : 匀速变化 : 缩放X[1.2] : 时间[20]
 * 插件指令：>自定义照明 : 高级照明[2] : 增减速变化 : 缩放X[1.2] : 时间[20]
 * 插件指令：>自定义照明 : 高级照明[2] : 弹性变化 : 缩放X[1.2] : 时间[20]
 * 
 * 插件指令：>自定义照明 : 高级照明[2] : 瞬间变化 : 缩放Y[1.2]
 * 插件指令：>自定义照明 : 高级照明[2] : 匀速变化 : 缩放Y[1.2] : 时间[20]
 * 插件指令：>自定义照明 : 高级照明[2] : 增减速变化 : 缩放Y[1.2] : 时间[20]
 * 插件指令：>自定义照明 : 高级照明[2] : 弹性变化 : 缩放Y[1.2] : 时间[20]
 * 
 * 插件指令：>自定义照明 : 高级照明[2] : 瞬间变化 : 透明度[255]
 * 插件指令：>自定义照明 : 高级照明[2] : 匀速变化 : 透明度[255] : 时间[20]
 * 插件指令：>自定义照明 : 高级照明[2] : 增减速变化 : 透明度[255] : 时间[20]
 * 插件指令：>自定义照明 : 高级照明[2] : 弹性变化 : 透明度[255] : 时间[20]
 * 
 * 插件指令：>自定义照明 : 高级照明[2] : 瞬间变化 : 旋转角度[90]
 * 插件指令：>自定义照明 : 高级照明[2] : 匀速变化 : 旋转角度[90] : 时间[20]
 * 插件指令：>自定义照明 : 高级照明[2] : 增减速变化 : 旋转角度[90] : 时间[20]
 * 插件指令：>自定义照明 : 高级照明[2] : 弹性变化 : 旋转角度[90] : 时间[20]
 * 
 * 1.注意，上述指令 和 移动 用法相似，但是指令不一样，注意区分。
 * 
 * -----------------------------------------------------------------------------
 * ----可选设定 - 限时动态照明
 * 你可以通过插件指令添加限时动态照明：
 * 
 * 插件指令：>自定义照明 : 限时动态照明 : 逐渐淡去 : 持续时间[180] : 玩家 : 照明[1]
 * 插件指令：>自定义照明 : 限时动态照明 : 逐渐淡去 : 持续时间[180] : 本事件 : 照明[1]
 * 插件指令：>自定义照明 : 限时动态照明 : 逐渐淡去 : 持续时间[180] : 事件[10] : 照明[1]
 * 插件指令：>自定义照明 : 限时动态照明 : 逐渐淡去 : 持续时间[180] : 事件变量[21] : 照明[1]
 * 
 * 插件指令：>自定义照明 : 限时动态照明 : 逐渐淡去 : 持续时间[180] : 本事件 : 照明[17]
 * 插件指令：>自定义照明 : 限时动态照明 : 逐渐显现 : 持续时间[180] : 本事件 : 照明[17]
 * 插件指令：>自定义照明 : 限时动态照明 : 保持亮度 : 持续时间[10] : 本事件 : 照明[17]
 * 
 * 1.限时动态照明在持续时间结束后，会被清除。多用于临时效果。
 * 2.限时动态照明本质上就是 预设的高级照明 。
 *   该预设 会从100-200中获取未创建的编号，然后创建一个高级照明，
 *   并设置生命时间、绑定对象、透明度变化。
 * 
 * -----------------------------------------------------------------------------
 * ----插件性能
 * 测试仪器：   4G 内存，Intel Core i5-2520M CPU 2.5GHz 处理器
 *              Intel(R) HD Graphics 3000 集显 的垃圾笔记本
 *              (笔记本的3dmark综合分：571，鲁大师综合分：48456)
 * 总时段：     20000.00ms左右
 * 对照表：     0.00ms  - 40.00ms （几乎无消耗）
 *              40.00ms - 80.00ms （低消耗）
 *              80.00ms - 120.00ms（中消耗）
 *              120.00ms以上      （高消耗）
 * 工作类型：   持续执行
 * 时间复杂度： o(n^2)*o(贴图处理)*o(遮罩渲染) 每帧
 * 测试方法：   在光源管理层进行性能测试。
 * 测试结果：   200个事件的地图中，平均消耗为：【175.44ms】
 *              100个事件的地图中，平均消耗为：【138.23ms】
 *               50个事件的地图中，平均消耗为：【92.16ms】
 *               20个事件的地图中，平均消耗为：【76.23ms】
 * 
 * 1.插件只在自己作用域下工作消耗性能，在其它作用域下是不工作的。
 *   测试结果并不是精确值，范围在给定值的 20ms 范围内波动。
 *   更多性能介绍，去看看 "0.性能测试报告 > 关于插件性能.docx"。
 * 2.经过数次优化，光源插件的性能还是比较难压下去，因为主要消耗GPU
 *   的能力，黑暗层和光源是在整个地图图层的基础上，再绘制一层遮罩。
 *   客户端打开的游戏没有性能问题，而用浏览器进行游戏会比较吃力。
 * 
 * -----------------------------------------------------------------------------
 * ----更新日志
 * [v1.0]
 * 完成插件ヽ(*。>Д<)o゜
 * [v1.1]
 * 修复了没有插件指令设置后未及时变色的bug。
 * 修复了添加动态光源时，gif和旋转角度重置的bug。
 * [v1.2]
 * 修复了菜单中地图截图在没有黑暗的情况下变黑的bug。
 * [v1.3]
 * 重新整理了 黑暗层开关 与 地图注释锁定 的关系。
 * 注意，旧版本的指令不再有效。
 * [v1.4]
 * 修复了部分特殊情况下，黑暗层不显示的bug。
 * [v1.5]
 * 修复了插件指令透明度的过渡过程。
 * [v1.6]
 * 改进了部分配置，以及插件指令内容。
 * [v1.7]
 * 区分了物体照明和高级照明，并强化了 高级照明 的各项功能和插件指令。
 * [v1.8]
 * 修复了1像素抖动的问题。
 * [v1.9]
 * 优化了旧存档的识别与兼容。
 * 
 * 
 * 
 * @param ---黑暗层---
 * @default
 *
 * @param 初始是否开启黑暗层
 * @parent ---黑暗层---
 * @type boolean
 * @on 启用
 * @off 关闭
 * @desc true - 启用，false - 关闭，后续只能通过插件指令开关黑暗层。
 * @default false
 * 
 * @param 黑暗层过渡时间
 * @parent ---黑暗层---
 * @type number
 * @min 1
 * @desc 黑暗层开启/关闭时，显示/消失的过渡时间。
 * @default 60
 * 
 * @param 黑暗层透明度
 * @parent ---黑暗层---
 * @type number
 * @min 0
 * @max 255
 * @desc 0为完全透明，255为完全不透明。
 * @default 255
 * 
 * @param 黑暗层颜色
 * @parent ---黑暗层---
 * @desc 填入颜色代码，比如#000000黑、#0000FF纯蓝。黑暗层与纯色滤镜的功能相似。颜色控制相应的过滤。你也可以通过插件指令修改。
 * @default #000000
 *
 * @param 黑暗层层级
 * @parent ---黑暗层---
 * @type select
 * @option 上层
 * @value 上层
 * @option 图片层
 * @value 图片层
 * @option 最顶层
 * @value 最顶层
 * @desc 黑暗层的地图层级。
 * @default 上层
 * 
 * @param ---光源组 1至20---
 * @default
 *
 * @param 光源-1
 * @parent ---光源组 1至20---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default {"标签":"==圆形60x60==","--贴图--":"","资源-光源GIF":"[\"自定义照明-圆形60x60\"]","帧间隔":"4","是否倒放":"false","平移-光源 X":"0","平移-光源 Y":"0","缩放 X":"1.0","缩放 Y":"1.0","--朝向--":"","旋转模式":"不旋转","自旋转速度":"-5.0","根据事件转向类型":"瞬间转向","根据事件转向速度":"5.0","--透明度--":"","透明度模式":"固定透明度","固定透明度":"255","透明度波动范围":"150","透明度波动周期":"120"}
 *
 * @param 光源-2
 * @parent ---光源组 1至20---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default {"标签":"==圆形90x90==","--贴图--":"","资源-光源GIF":"[\"自定义照明-圆形90x90\"]","帧间隔":"4","是否倒放":"false","平移-光源 X":"0","平移-光源 Y":"0","缩放 X":"1.0","缩放 Y":"1.0","--朝向--":"","旋转模式":"不旋转","自旋转速度":"-5.0","根据事件转向类型":"瞬间转向","根据事件转向速度":"5.0","--透明度--":"","透明度模式":"固定透明度","固定透明度":"255","透明度波动范围":"150","透明度波动周期":"120"}
 *
 * @param 光源-3
 * @parent ---光源组 1至20---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default {"标签":"==圆形120x120==","--贴图--":"","资源-光源GIF":"[\"自定义照明-圆形120x120\"]","帧间隔":"4","是否倒放":"false","平移-光源 X":"0","平移-光源 Y":"0","缩放 X":"1.0","缩放 Y":"1.0","--朝向--":"","旋转模式":"不旋转","自旋转速度":"-5.0","根据事件转向类型":"瞬间转向","根据事件转向速度":"5.0","--透明度--":"","透明度模式":"固定透明度","固定透明度":"255","透明度波动范围":"150","透明度波动周期":"120"}
 *
 * @param 光源-4
 * @parent ---光源组 1至20---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default {"标签":"==圆形180x180==","--贴图--":"","资源-光源GIF":"[\"自定义照明-圆形180x180\"]","帧间隔":"4","是否倒放":"false","平移-光源 X":"0","平移-光源 Y":"0","缩放 X":"1.0","缩放 Y":"1.0","--朝向--":"","旋转模式":"不旋转","自旋转速度":"-5.0","根据事件转向类型":"瞬间转向","根据事件转向速度":"5.0","--透明度--":"","透明度模式":"固定透明度","固定透明度":"255","透明度波动范围":"150","透明度波动周期":"120"}
 *
 * @param 光源-5
 * @parent ---光源组 1至20---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default {"标签":"==方形60x60==","--贴图--":"","资源-光源GIF":"[\"自定义照明-方形60x60\"]","帧间隔":"4","是否倒放":"false","平移-光源 X":"0","平移-光源 Y":"0","缩放 X":"1.0","缩放 Y":"1.0","--朝向--":"","旋转模式":"不旋转","自旋转速度":"-5.0","根据事件转向类型":"瞬间转向","根据事件转向速度":"5.0","--透明度--":"","透明度模式":"固定透明度","固定透明度":"255","透明度波动范围":"150","透明度波动周期":"120"}
 *
 * @param 光源-6
 * @parent ---光源组 1至20---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default {"标签":"==方形90x90==","--贴图--":"","资源-光源GIF":"[\"自定义照明-方形90x90\"]","帧间隔":"4","是否倒放":"false","平移-光源 X":"0","平移-光源 Y":"0","缩放 X":"1.0","缩放 Y":"1.0","--朝向--":"","旋转模式":"不旋转","自旋转速度":"-5.0","根据事件转向类型":"瞬间转向","根据事件转向速度":"5.0","--透明度--":"","透明度模式":"固定透明度","固定透明度":"255","透明度波动范围":"150","透明度波动周期":"120"}
 *
 * @param 光源-7
 * @parent ---光源组 1至20---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default {"标签":"==方形120x120==","--贴图--":"","资源-光源GIF":"[\"自定义照明-方形120x120\"]","帧间隔":"4","是否倒放":"false","平移-光源 X":"0","平移-光源 Y":"0","缩放 X":"1.0","缩放 Y":"1.0","--朝向--":"","旋转模式":"不旋转","自旋转速度":"-5.0","根据事件转向类型":"瞬间转向","根据事件转向速度":"5.0","--透明度--":"","透明度模式":"固定透明度","固定透明度":"255","透明度波动范围":"150","透明度波动周期":"120"}
 *
 * @param 光源-8
 * @parent ---光源组 1至20---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default {"标签":"==方形180x180==","--贴图--":"","资源-光源GIF":"[\"自定义照明-方形180x180\"]","帧间隔":"4","是否倒放":"false","平移-光源 X":"0","平移-光源 Y":"0","缩放 X":"1.0","缩放 Y":"1.0","--朝向--":"","旋转模式":"不旋转","自旋转速度":"-5.0","根据事件转向类型":"瞬间转向","根据事件转向速度":"5.0","--透明度--":"","透明度模式":"固定透明度","固定透明度":"255","透明度波动范围":"150","透明度波动周期":"120"}
 *
 * @param 光源-9
 * @parent ---光源组 1至20---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default {"标签":"==渐变圆形60x60==","--贴图--":"","资源-光源GIF":"[\"自定义照明-渐变圆形60x60\"]","帧间隔":"4","是否倒放":"false","平移-光源 X":"0","平移-光源 Y":"0","缩放 X":"1.0","缩放 Y":"1.0","--朝向--":"","旋转模式":"不旋转","自旋转速度":"-5.0","根据事件转向类型":"瞬间转向","根据事件转向速度":"5.0","--透明度--":"","透明度模式":"固定透明度","固定透明度":"255","透明度波动范围":"150","透明度波动周期":"120"}
 *
 * @param 光源-10
 * @parent ---光源组 1至20---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default {"标签":"==渐变圆形90x90==","--贴图--":"","资源-光源GIF":"[\"自定义照明-渐变圆形90x90\"]","帧间隔":"4","是否倒放":"false","平移-光源 X":"0","平移-光源 Y":"0","缩放 X":"1.0","缩放 Y":"1.0","--朝向--":"","旋转模式":"不旋转","自旋转速度":"-5.0","根据事件转向类型":"瞬间转向","根据事件转向速度":"5.0","--透明度--":"","透明度模式":"固定透明度","固定透明度":"255","透明度波动范围":"150","透明度波动周期":"120"}
 * 
 * @param 光源-11
 * @parent ---光源组 1至20---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default {"标签":"==渐变圆形120x120==","--贴图--":"","资源-光源GIF":"[\"自定义照明-渐变圆形120x120\"]","帧间隔":"4","是否倒放":"false","平移-光源 X":"0","平移-光源 Y":"0","缩放 X":"1.0","缩放 Y":"1.0","--朝向--":"","旋转模式":"不旋转","自旋转速度":"-5.0","根据事件转向类型":"瞬间转向","根据事件转向速度":"5.0","--透明度--":"","透明度模式":"固定透明度","固定透明度":"255","透明度波动范围":"150","透明度波动周期":"120"}
 *
 * @param 光源-12
 * @parent ---光源组 1至20---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default {"标签":"==渐变圆形180x180==","--贴图--":"","资源-光源GIF":"[\"自定义照明-渐变圆形180x180\"]","帧间隔":"4","是否倒放":"false","平移-光源 X":"0","平移-光源 Y":"0","缩放 X":"1.0","缩放 Y":"1.0","--朝向--":"","旋转模式":"不旋转","自旋转速度":"-5.0","根据事件转向类型":"瞬间转向","根据事件转向速度":"5.0","--透明度--":"","透明度模式":"固定透明度","固定透明度":"255","透明度波动范围":"150","透明度波动周期":"120"}
 *
 * @param 光源-13
 * @parent ---光源组 1至20---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default {"标签":"==渐变方形60x60==","--贴图--":"","资源-光源GIF":"[\"自定义照明-渐变方形60x60\"]","帧间隔":"4","是否倒放":"false","平移-光源 X":"0","平移-光源 Y":"0","缩放 X":"1.0","缩放 Y":"1.0","--朝向--":"","旋转模式":"不旋转","自旋转速度":"-5.0","根据事件转向类型":"瞬间转向","根据事件转向速度":"5.0","--透明度--":"","透明度模式":"固定透明度","固定透明度":"255","透明度波动范围":"150","透明度波动周期":"120"}
 *
 * @param 光源-14
 * @parent ---光源组 1至20---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default {"标签":"==渐变方形90x90==","--贴图--":"","资源-光源GIF":"[\"自定义照明-渐变方形90x90\"]","帧间隔":"4","是否倒放":"false","平移-光源 X":"0","平移-光源 Y":"0","缩放 X":"1.0","缩放 Y":"1.0","--朝向--":"","旋转模式":"不旋转","自旋转速度":"-5.0","根据事件转向类型":"瞬间转向","根据事件转向速度":"5.0","--透明度--":"","透明度模式":"固定透明度","固定透明度":"255","透明度波动范围":"150","透明度波动周期":"120"}
 *
 * @param 光源-15
 * @parent ---光源组 1至20---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default {"标签":"==渐变方形120x120==","--贴图--":"","资源-光源GIF":"[\"自定义照明-渐变方形120x120\"]","帧间隔":"4","是否倒放":"false","平移-光源 X":"0","平移-光源 Y":"0","缩放 X":"1.0","缩放 Y":"1.0","--朝向--":"","旋转模式":"不旋转","自旋转速度":"-5.0","根据事件转向类型":"瞬间转向","根据事件转向速度":"5.0","--透明度--":"","透明度模式":"固定透明度","固定透明度":"255","透明度波动范围":"150","透明度波动周期":"120"}
 *
 * @param 光源-16
 * @parent ---光源组 1至20---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default {"标签":"==渐变方形180x180==","--贴图--":"","资源-光源GIF":"[\"自定义照明-渐变方形180x180\"]","帧间隔":"4","是否倒放":"false","平移-光源 X":"0","平移-光源 Y":"0","缩放 X":"1.0","缩放 Y":"1.0","--朝向--":"","旋转模式":"不旋转","自旋转速度":"-5.0","根据事件转向类型":"瞬间转向","根据事件转向速度":"5.0","--透明度--":"","透明度模式":"固定透明度","固定透明度":"255","透明度波动范围":"150","透明度波动周期":"120"}
 *
 * @param 光源-17
 * @parent ---光源组 1至20---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default {"标签":"==大圆照明==","--贴图--":"","资源-光源GIF":"[\"自定义照明-大圆照明\"]","帧间隔":"4","是否倒放":"false","平移-光源 X":"0","平移-光源 Y":"0","缩放 X":"1.0","缩放 Y":"1.0","--朝向--":"","旋转模式":"不旋转","自旋转速度":"-5.0","根据事件转向类型":"瞬间转向","根据事件转向速度":"5.0","--透明度--":"","透明度模式":"固定透明度","固定透明度":"255","透明度波动范围":"150","透明度波动周期":"120"}
 *
 * @param 光源-18
 * @parent ---光源组 1至20---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default {"标签":"==超大圆照明==","--贴图--":"","资源-光源GIF":"[\"自定义照明-大圆照明\"]","帧间隔":"4","是否倒放":"false","平移-光源 X":"0","平移-光源 Y":"0","缩放 X":"2.0","缩放 Y":"2.0","--朝向--":"","旋转模式":"不旋转","自旋转速度":"-5.0","根据事件转向类型":"瞬间转向","根据事件转向速度":"5.0","--透明度--":"","透明度模式":"固定透明度","固定透明度":"255","透明度波动范围":"150","透明度波动周期":"120"}
 *
 * @param 光源-19
 * @parent ---光源组 1至20---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default {"标签":"==炸弹人-炸弹火苗==","--贴图--":"","资源-光源GIF":"[\"自定义照明-灯光-闪烁1\",\"自定义照明-灯光-闪烁1\",\"自定义照明-灯光-闪烁1\",\"自定义照明-灯光-闪烁1\",\"自定义照明-灯光-闪烁2\",\"自定义照明-灯光-闪烁2\",\"自定义照明-灯光-闪烁1\",\"自定义照明-灯光-闪烁1\",\"自定义照明-灯光-闪烁2\",\"自定义照明-灯光-闪烁1\",\"自定义照明-灯光-闪烁2\",\"自定义照明-灯光-闪烁2\",\"自定义照明-灯光-闪烁2\"]","帧间隔":"5","是否倒放":"false","平移-光源 X":"0","平移-光源 Y":"0","缩放 X":"0.4","缩放 Y":"0.4","--朝向--":"","旋转模式":"不旋转","自旋转速度":"-5.0","根据事件转向类型":"瞬间转向","根据事件转向速度":"5.0","--透明度--":"","透明度模式":"固定透明度","固定透明度":"155","透明度波动范围":"150","透明度波动周期":"120"}
 *
 * @param 光源-20
 * @parent ---光源组 1至20---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default {"标签":"==炸弹人-爆炸光亮==","--贴图--":"","资源-光源GIF":"[\"自定义照明-渐变圆形180x180\"]","帧间隔":"4","是否倒放":"false","平移-光源 X":"0","平移-光源 Y":"0","缩放 X":"3.2","缩放 Y":"3.2","--朝向--":"","旋转模式":"不旋转","自旋转速度":"-5.0","根据事件转向类型":"瞬间转向","根据事件转向速度":"5.0","--透明度--":"","透明度模式":"固定透明度","固定透明度":"255","透明度波动范围":"150","透明度波动周期":"120"}
 *
 * @param ---光源组21至40---
 * @default
 *
 * @param 光源-21
 * @parent ---光源组21至40---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default {"标签":"==手电筒-瞬间转向==","--贴图--":"","资源-光源GIF":"[\"自定义照明-手电筒\"]","帧间隔":"4","是否倒放":"false","平移-光源 X":"0","平移-光源 Y":"0","缩放 X":"1.0","缩放 Y":"1.0","--朝向--":"","旋转模式":"根据事件朝向转向","自旋转速度":"-5.0","根据事件转向类型":"瞬间转向","根据事件转向速度":"5.0","--透明度--":"","透明度模式":"固定透明度","固定透明度":"255","透明度波动范围":"150","透明度波动周期":"120"}
 *
 * @param 光源-22
 * @parent ---光源组21至40---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default {"标签":"==手电筒-匀速转向==","--贴图--":"","资源-光源GIF":"[\"自定义照明-手电筒\"]","帧间隔":"4","是否倒放":"false","平移-光源 X":"0","平移-光源 Y":"0","缩放 X":"1.0","缩放 Y":"1.0","--朝向--":"","旋转模式":"根据事件朝向转向","自旋转速度":"-5.0","根据事件转向类型":"匀速转向","根据事件转向速度":"5.0","--透明度--":"","透明度模式":"固定透明度","固定透明度":"255","透明度波动范围":"150","透明度波动周期":"120"}
 *
 * @param 光源-23
 * @parent ---光源组21至40---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default {"标签":"==手电筒-弹性转向==","--贴图--":"","资源-光源GIF":"[\"自定义照明-手电筒\"]","帧间隔":"4","是否倒放":"false","平移-光源 X":"0","平移-光源 Y":"0","缩放 X":"1.0","缩放 Y":"1.0","--朝向--":"","旋转模式":"根据事件朝向转向","自旋转速度":"-5.0","根据事件转向类型":"弹性转向","根据事件转向速度":"5.0","--透明度--":"","透明度模式":"固定透明度","固定透明度":"255","透明度波动范围":"150","透明度波动周期":"120"}
 *
 * @param 光源-24
 * @parent ---光源组21至40---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default {"标签":"==路灯照明-微弱闪烁==","--贴图--":"","资源-光源GIF":"[\"自定义照明-灯光-闪烁1\",\"自定义照明-灯光-闪烁1\",\"自定义照明-灯光-闪烁1\",\"自定义照明-灯光-闪烁1\",\"自定义照明-灯光-闪烁2\",\"自定义照明-灯光-闪烁2\",\"自定义照明-灯光-闪烁1\",\"自定义照明-灯光-闪烁1\",\"自定义照明-灯光-闪烁2\",\"自定义照明-灯光-闪烁1\",\"自定义照明-灯光-闪烁2\",\"自定义照明-灯光-闪烁2\",\"自定义照明-灯光-闪烁2\"]","帧间隔":"6","是否倒放":"false","平移-光源 X":"0","平移-光源 Y":"0","缩放 X":"1.0","缩放 Y":"1.0","--朝向--":"","旋转模式":"不旋转","自旋转速度":"-5.0","根据事件转向类型":"瞬间转向","根据事件转向速度":"5.0","--透明度--":"","透明度模式":"固定透明度","固定透明度":"255","透明度波动范围":"150","透明度波动周期":"120"}
 *
 * @param 光源-25
 * @parent ---光源组21至40---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default {"标签":"==路灯照明-短路闪烁==","--贴图--":"","资源-光源GIF":"[\"自定义照明-灯光-闪烁1\",\"自定义照明-灯光-闪烁1\",\"自定义照明-灯光-闪烁1\",\"自定义照明-灯光-闪烁1\",\"\",\"\",\"自定义照明-灯光-闪烁1\",\"自定义照明-灯光-闪烁1\",\"自定义照明-灯光-闪烁1\",\"\",\"自定义照明-灯光-闪烁1\",\"\",\"自定义照明-灯光-闪烁1\",\"自定义照明-灯光-闪烁1\"]","帧间隔":"6","是否倒放":"false","平移-光源 X":"0","平移-光源 Y":"0","缩放 X":"1.0","缩放 Y":"1.0","--朝向--":"","旋转模式":"不旋转","自旋转速度":"-5.0","根据事件转向类型":"瞬间转向","根据事件转向速度":"5.0","--透明度--":"","透明度模式":"固定透明度","固定透明度":"255","透明度波动范围":"150","透明度波动周期":"120"}
 *
 * @param 光源-26
 * @parent ---光源组21至40---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default {"标签":"==路灯照明-靠墙==","--贴图--":"","资源-光源GIF":"[\"自定义照明-灯光-靠墙\"]","帧间隔":"4","是否倒放":"false","平移-光源 X":"0","平移-光源 Y":"0","缩放 X":"1.0","缩放 Y":"1.0","--朝向--":"","旋转模式":"不旋转","自旋转速度":"-5.0","根据事件转向类型":"瞬间转向","根据事件转向速度":"5.0","--透明度--":"","透明度模式":"固定透明度","固定透明度":"255","透明度波动范围":"150","透明度波动周期":"120"}
 *
 * @param 光源-27
 * @parent ---光源组21至40---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default {"标签":"==路灯照明-红灯==","--贴图--":"","资源-光源GIF":"[\"自定义照明-灯光-红\"]","帧间隔":"4","是否倒放":"false","平移-光源 X":"0","平移-光源 Y":"0","缩放 X":"1.0","缩放 Y":"1.0","--朝向--":"","旋转模式":"不旋转","自旋转速度":"-5.0","根据事件转向类型":"瞬间转向","根据事件转向速度":"5.0","--透明度--":"","透明度模式":"固定透明度","固定透明度":"255","透明度波动范围":"150","透明度波动周期":"120"}
 *
 * @param 光源-28
 * @parent ---光源组21至40---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default {"标签":"==路灯照明-波动变化==","--贴图--":"","资源-光源GIF":"[\"自定义照明-灯光-闪烁1\"]","帧间隔":"4","是否倒放":"false","平移-光源 X":"0","平移-光源 Y":"0","缩放 X":"1.0","缩放 Y":"1.0","--朝向--":"","旋转模式":"不旋转","自旋转速度":"-5.0","根据事件转向类型":"瞬间转向","根据事件转向速度":"5.0","--透明度--":"","透明度模式":"波动透明度","固定透明度":"255","透明度波动范围":"150","透明度波动周期":"120"}
 *
 * @param 光源-29
 * @parent ---光源组21至40---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default {"标签":"==路灯照明-探照灯==","--贴图--":"","资源-光源GIF":"[\"自定义照明-灯光-探照灯\"]","帧间隔":"4","是否倒放":"false","平移-光源 X":"0","平移-光源 Y":"0","缩放 X":"1.0","缩放 Y":"1.0","--朝向--":"","旋转模式":"无限自旋转","自旋转速度":"-3.0","根据事件转向类型":"瞬间转向","根据事件转向速度":"5.0","--透明度--":"","透明度模式":"固定透明度","固定透明度":"255","透明度波动范围":"150","透明度波动周期":"120"}
 *
 * @param 光源-30
 * @parent ---光源组21至40---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default {"标签":"==形状gif变化==","--贴图--":"","资源-光源GIF":"[\"自定义照明-方形120x120\",\"自定义照明-圆形120x120\",\"自定义照明-六边形120x120\"]","帧间隔":"75","是否倒放":"false","平移-光源 X":"0","平移-光源 Y":"0","缩放 X":"1.0","缩放 Y":"1.0","--朝向--":"","旋转模式":"不旋转","自旋转速度":"-2.5","根据事件转向类型":"瞬间转向","根据事件转向速度":"5.0","--透明度--":"","透明度模式":"固定透明度","固定透明度":"255","透明度波动范围":"150","透明度波动周期":"120"}
 * 
 * @param 光源-31
 * @parent ---光源组21至40---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-32
 * @parent ---光源组21至40---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-33
 * @parent ---光源组21至40---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-34
 * @parent ---光源组21至40---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-35
 * @parent ---光源组21至40---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-36
 * @parent ---光源组21至40---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-37
 * @parent ---光源组21至40---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-38
 * @parent ---光源组21至40---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-39
 * @parent ---光源组21至40---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-40
 * @parent ---光源组21至40---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param ---光源组41至60---
 * @default
 *
 * @param 光源-41
 * @parent ---光源组41至60---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-42
 * @parent ---光源组41至60---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-43
 * @parent ---光源组41至60---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-44
 * @parent ---光源组41至60---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-45
 * @parent ---光源组41至60---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-46
 * @parent ---光源组41至60---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-47
 * @parent ---光源组41至60---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-48
 * @parent ---光源组41至60---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-49
 * @parent ---光源组41至60---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-50
 * @parent ---光源组41至60---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-51
 * @parent ---光源组41至60---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-52
 * @parent ---光源组41至60---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-53
 * @parent ---光源组41至60---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-54
 * @parent ---光源组41至60---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-55
 * @parent ---光源组41至60---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-56
 * @parent ---光源组41至60---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-57
 * @parent ---光源组41至60---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-58
 * @parent ---光源组41至60---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-59
 * @parent ---光源组41至60---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-60
 * @parent ---光源组41至60---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param ---光源组61至80---
 * @default
 *
 * @param 光源-61
 * @parent ---光源组61至80---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-62
 * @parent ---光源组61至80---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-63
 * @parent ---光源组61至80---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-64
 * @parent ---光源组61至80---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-65
 * @parent ---光源组61至80---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-66
 * @parent ---光源组61至80---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-67
 * @parent ---光源组61至80---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-68
 * @parent ---光源组61至80---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-69
 * @parent ---光源组61至80---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-70
 * @parent ---光源组61至80---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-71
 * @parent ---光源组61至80---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-72
 * @parent ---光源组61至80---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-73
 * @parent ---光源组61至80---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-74
 * @parent ---光源组61至80---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-75
 * @parent ---光源组61至80---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-76
 * @parent ---光源组61至80---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-77
 * @parent ---光源组61至80---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-78
 * @parent ---光源组61至80---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-79
 * @parent ---光源组61至80---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-80
 * @parent ---光源组61至80---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param ---光源组81至100---
 * @default
 *
 * @param 光源-81
 * @parent ---光源组81至100---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-82
 * @parent ---光源组81至100---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-83
 * @parent ---光源组81至100---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-84
 * @parent ---光源组81至100---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-85
 * @parent ---光源组81至100---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-86
 * @parent ---光源组81至100---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-87
 * @parent ---光源组81至100---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-88
 * @parent ---光源组81至100---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-89
 * @parent ---光源组81至100---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-90
 * @parent ---光源组81至100---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-91
 * @parent ---光源组81至100---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-92
 * @parent ---光源组81至100---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-93
 * @parent ---光源组81至100---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-94
 * @parent ---光源组81至100---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-95
 * @parent ---光源组81至100---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-96
 * @parent ---光源组81至100---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-97
 * @parent ---光源组81至100---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-98
 * @parent ---光源组81至100---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-99
 * @parent ---光源组81至100---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-100
 * @parent ---光源组81至100---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param ---光源组101至120---
 * @default
 *
 * @param 光源-101
 * @parent ---光源组101至120---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-102
 * @parent ---光源组101至120---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-103
 * @parent ---光源组101至120---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-104
 * @parent ---光源组101至120---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-105
 * @parent ---光源组101至120---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-106
 * @parent ---光源组101至120---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-107
 * @parent ---光源组101至120---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-108
 * @parent ---光源组101至120---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-109
 * @parent ---光源组101至120---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-110
 * @parent ---光源组101至120---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-111
 * @parent ---光源组101至120---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-112
 * @parent ---光源组101至120---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-113
 * @parent ---光源组101至120---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-114
 * @parent ---光源组101至120---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-115
 * @parent ---光源组101至120---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-116
 * @parent ---光源组101至120---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-117
 * @parent ---光源组101至120---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-118
 * @parent ---光源组101至120---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-119
 * @parent ---光源组101至120---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-120
 * @parent ---光源组101至120---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param ---光源组121至140---
 * @default
 *
 * @param 光源-121
 * @parent ---光源组121至140---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-122
 * @parent ---光源组121至140---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-123
 * @parent ---光源组121至140---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-124
 * @parent ---光源组121至140---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-125
 * @parent ---光源组121至140---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-126
 * @parent ---光源组121至140---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-127
 * @parent ---光源组121至140---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-128
 * @parent ---光源组121至140---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-129
 * @parent ---光源组121至140---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-130
 * @parent ---光源组121至140---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-131
 * @parent ---光源组121至140---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-132
 * @parent ---光源组121至140---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-133
 * @parent ---光源组121至140---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-134
 * @parent ---光源组121至140---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-135
 * @parent ---光源组121至140---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-136
 * @parent ---光源组121至140---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-137
 * @parent ---光源组121至140---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-138
 * @parent ---光源组121至140---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-139
 * @parent ---光源组121至140---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-140
 * @parent ---光源组121至140---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param ---光源组141至160---
 * @default
 *
 * @param 光源-141
 * @parent ---光源组141至160---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-142
 * @parent ---光源组141至160---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-143
 * @parent ---光源组141至160---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-144
 * @parent ---光源组141至160---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-145
 * @parent ---光源组141至160---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-146
 * @parent ---光源组141至160---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-147
 * @parent ---光源组141至160---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-148
 * @parent ---光源组141至160---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-149
 * @parent ---光源组141至160---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-150
 * @parent ---光源组141至160---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-151
 * @parent ---光源组141至160---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-152
 * @parent ---光源组141至160---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-153
 * @parent ---光源组141至160---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-154
 * @parent ---光源组141至160---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-155
 * @parent ---光源组141至160---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-156
 * @parent ---光源组141至160---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-157
 * @parent ---光源组141至160---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-158
 * @parent ---光源组141至160---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-159
 * @parent ---光源组141至160---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-160
 * @parent ---光源组141至160---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param ---光源组161至180---
 * @default
 *
 * @param 光源-161
 * @parent ---光源组161至180---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-162
 * @parent ---光源组161至180---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-163
 * @parent ---光源组161至180---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-164
 * @parent ---光源组161至180---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-165
 * @parent ---光源组161至180---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-166
 * @parent ---光源组161至180---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-167
 * @parent ---光源组161至180---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-168
 * @parent ---光源组161至180---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-169
 * @parent ---光源组161至180---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-170
 * @parent ---光源组161至180---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-171
 * @parent ---光源组161至180---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-172
 * @parent ---光源组161至180---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-173
 * @parent ---光源组161至180---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-174
 * @parent ---光源组161至180---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-175
 * @parent ---光源组161至180---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-176
 * @parent ---光源组161至180---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-177
 * @parent ---光源组161至180---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-178
 * @parent ---光源组161至180---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-179
 * @parent ---光源组161至180---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-180
 * @parent ---光源组161至180---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param ---光源组181至200---
 * @default
 *
 * @param 光源-181
 * @parent ---光源组181至200---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-182
 * @parent ---光源组181至200---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-183
 * @parent ---光源组181至200---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-184
 * @parent ---光源组181至200---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-185
 * @parent ---光源组181至200---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-186
 * @parent ---光源组181至200---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-187
 * @parent ---光源组181至200---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-188
 * @parent ---光源组181至200---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-189
 * @parent ---光源组181至200---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-190
 * @parent ---光源组181至200---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-191
 * @parent ---光源组181至200---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-192
 * @parent ---光源组181至200---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-193
 * @parent ---光源组181至200---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-194
 * @parent ---光源组181至200---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-195
 * @parent ---光源组181至200---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-196
 * @parent ---光源组181至200---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-197
 * @parent ---光源组181至200---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-198
 * @parent ---光源组181至200---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-199
 * @parent ---光源组181至200---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 * @param 光源-200
 * @parent ---光源组181至200---
 * @type struct<LIlLight>
 * @desc 光源照明设置的详细配置信息。
 * @default 
 *
 */
/*~struct~LIlLight:
 * 
 * @param 标签
 * @desc 只用于方便区分查看的标签，不作用在插件中。
 * @default ==新的地图光源==
 * 
 * 
 * @param ---贴图---
 * @desc 
 *
 * @param 资源-光源GIF
 * @parent ---贴图---
 * @desc png图片资源组，多张构成gif。也可以只是单张图片。
 * @default []
 * @require 1
 * @dir img/Map__illumination/
 * @type file[]
 *
 * @param 帧间隔
 * @parent ---贴图---
 * @type number
 * @min 1
 * @desc gif每帧播放间隔时间，单位帧。（1秒60帧）
 * @default 4
 *
 * @param 是否倒放
 * @parent ---贴图---
 * @type boolean
 * @on 倒放
 * @off 不倒放
 * @desc true - 倒放，false - 不倒放
 * @default false
 * 
 * @param 平移-光源 X
 * @parent ---贴图---
 * @desc x轴方向平移，单位像素。0表示光源中心贴在事件中心。正数向右，负数向左。
 * @default 0
 *
 * @param 平移-光源 Y
 * @parent ---贴图---
 * @desc y轴方向平移，单位像素。0表示光源中心贴在事件中心。正数向下，负数向上。
 * @default 0
 * 
 * @param 缩放 X
 * @parent ---贴图---
 * @desc 魔法圈的缩放X值，默认比例1.0。缩放将会使得魔法圈看起来旋转具有一定透视。
 * @default 1.0
 * 
 * @param 缩放 Y
 * @parent ---贴图---
 * @desc 魔法圈的缩放Y值，默认比例1.0。缩放将会使得魔法圈看起来旋转具有一定透视。
 * @default 1.0
 *
 * @param 图片层级
 * @parent ---贴图---
 * @type number
 * @min 1
 * @desc 多个光源之间的先后顺序层级。
 * @default 2
 * 
 * @param ---朝向---
 * @desc 
 *
 * @param 旋转模式
 * @parent ---朝向---
 * @type select
 * @option 不旋转
 * @value 不旋转
 * @option 无限自旋转
 * @value 无限自旋转
 * @option 根据事件朝向转向
 * @value 根据事件朝向转向
 * @option 始终朝向鼠标位置
 * @value 始终朝向鼠标位置
 * @desc 光源旋转的模式。
 * @default 不旋转
 *
 * @param 自旋转速度
 * @parent ---朝向---
 * @desc 旋转模式为"无限自旋转"时，则单位为角度/帧。正数逆时针旋转，负数顺时针旋转。
 * @default -5.0
 *
 * @param 根据事件转向类型
 * @parent ---朝向---
 * @type select
 * @option 瞬间转向
 * @value 瞬间转向
 * @option 匀速转向
 * @value 匀速转向
 * @option 弹性转向
 * @value 弹性转向
 * @desc 旋转模式为"根据事件朝向转向"时，初始的移动方式。
 * @default 瞬间转向
 *
 * @param 根据事件转向速度
 * @parent 根据事件转向类型
 * @desc 如果为"匀速转向"，则单位为角度/帧。如果为"弹性转向"，则值为比例除数。
 * @default 5.0
 * 
 * @param ---透明度---
 * @desc 
 *
 * @param 透明度模式
 * @parent ---透明度---
 * @type select
 * @option 固定透明度
 * @value 固定透明度
 * @option 波动透明度
 * @value 波动透明度
 * @desc 透明度的变化模式。
 * @default 固定透明度
 * 
 * @param 固定透明度
 * @parent ---透明度---
 * @type number
 * @min 0
 * @max 255
 * @desc 0为完全透明，255为完全不透明。
 * @default 255
 * 
 * @param 波动透明度最小值
 * @parent ---透明度---
 * @type number
 * @min 0
 * @max 255
 * @desc 为"波动透明度"模式时，透明度波动的最小值。
 * @default 150
 * 
 * @param 波动透明度最大值
 * @parent ---透明度---
 * @type number
 * @min 0
 * @max 255
 * @desc 为"波动透明度"模式时，透明度波动的最大值。
 * @default 255
 * 
 * @param 透明度波动周期
 * @parent ---透明度---
 * @type number
 * @min 2
 * @desc 为"波动透明度"模式时，透明度波动的周期时长。单位帧，1秒60帧。
 * @default 120
 * 
 *
 */
 
//<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
//		插件简称		LIl（Layer_Illumination）
//		临时全局变量	DrillUp.g_LIl_xxx
//		临时局部变量	this._drill_LIl_xxx
//		存储数据变量	$gameSystem._drill_LIl_xxx
//		全局存储变量	无
//		覆盖重写方法	无
//
//<<<<<<<<性能记录<<<<<<<<
//
//		★工作类型		持续执行
//		★时间复杂度		o(n^2)*o(贴图处理)*o(遮罩渲染) 每帧
//		★性能测试因素	光源管理层
//		★性能测试消耗	138.23ms
//		★最坏情况		无
//		★备注			无
//		
//		★优化记录		暂无
//
//<<<<<<<<插件记录<<<<<<<<
//
//		★功能结构树：
//			自定义照明效果：
//				->物体照明
//					->gif光源
//					->根据事件自旋转
//					->波动光源
//					->旋转、gif时间不被重刷
//				->多颜色光源
//				->限时动态照明
//					->突然爆炸的闪亮光源
//		
//		
//		★家谱：
//			大家族-动态遮罩
//		
//		★脚本文档：
//			无
//		
//		★插件私有类：
//			* 遮罩渲染器【Drill_LIl_Renderer】
//			* 黑暗层贴图【Drill_LIl_MaskSprite】
//		
//		★必要注意事项：
//			1.插件的图片层级与多个插件共享。【必须自写 层级排序 函数】
//			2.相似的功能称呼：
//				光源 -> 透视镜样式
//				物体照明 -> 简单透视镜
//				高级照明 -> 高级照明
//
//		★其它说明细节：
//			1.  2020-4-28 随着对pixi的深入，我发现了许多坏消息。
//				>最初，我发现了mask只对当前的sprite有效果，所有child根本不起作用。
//				 可能是mask只识别bitmap的问题，后来使用额外的渲染器和画布，直接绘制了一张新bitmap。
//				>但是bitmap无论怎么变，mask都不会改变。mask只认最初的那一个贴图材质。
//				 这里极有可能是进行了颜色矩阵的缓存，因为 赋值不同的bitmap、强行赋值mask、赋值不同的sprite、设置null 都不管用。
//				 为了测试可行性，花了半个下午。
//				>后来发现renderable有效，但是renderable会破坏颜色矩阵，将父类层级完全变成白色，并且变不回来。
//				 setBlendColor、setColorTone、blendMode 都没有用。
//				>思考了很久。最后，改变策略，既然光源要求黑布，那么就用 blendMode = 2 滤镜板来实现吧。
//				 滤镜板套子类会造成白色无效，于是，建一个画布直接对滤镜板的bitmap进行绘制，终于生效了。
//			2.简单说说结论：
//				1). mask 的child无效
//				2). mask + 绘制bitmap 不刷新
//				3). mask + 绘制bitmap + renderable 会导致颜色矩阵失真，只能全白，还不能变色。
//				4). mask + renderable 没有遮罩效果
//				5). mask + renderable + blendMode 没有遮罩效果
//				6). blendMode 的child，白色不能叠加
//				7). blendMode + 绘制bitmap
//				8). blendMode + 绘制texture 最终成型方案（texture比bitmap快一点，不过也没快多少）
//			  		pixi所给的类，有很大的局限性，比如 循环sprite、mask、container等，都没有单纯的sprite那么灵活，
//			  		可能也是基于硬件的限制，功能受限。
//				9). mask + 绘制texture  新的可用方案（做成核心）
//					前一段时间，我注释了bitmap作为中间过渡层，发现bitmap并没有那么完美。
//					遇到底层渲染时，需要直接操作texture，因为 赋值和画图 都被bitmap过滤了，这严重影响了对texture的操作。
//					但是经过该组合，竟然成功实现了 【贴图相减】 的功能。
//			3.限时动态照明 是建立了一个假事件，这个事件用于缓冲到gamemap中，
//			  在游戏保存，切菜单时，都不会因为贴图被清而消失。
//				
//		★存在的问题：
//			1.pixi底层的部分功能有限，且难以修改，只能基于该渲染器作额外扩充。
//

//=============================================================================
// ** 提示信息
//=============================================================================
	//==============================
	// * 提示信息 - 参数
	//==============================
	var DrillUp = DrillUp || {}; 
	DrillUp.g_LIl_PluginTip_curName = "Drill_LayerIllumination.js 地图-自定义照明效果";
	DrillUp.g_LIl_PluginTip_baseList = ["Drill_CoreOfDynamicMask.js 系统-动态遮罩核心"];
	//==============================
	// * 提示信息 - 报错 - 缺少基础插件
	//			
	//			说明：	此函数只提供提示信息，不校验真实的插件关系。
	//==============================
	DrillUp.drill_LIl_getPluginTip_NoBasePlugin = function(){
		if( DrillUp.g_LIl_PluginTip_baseList.length == 0 ){ return ""; }
		var message = "【" + DrillUp.g_LIl_PluginTip_curName + "】\n缺少基础插件，去看看下列插件是不是 未添加 / 被关闭 / 顺序不对：";
		for(var i=0; i < DrillUp.g_LIl_PluginTip_baseList.length; i++){
			message += "\n- ";
			message += DrillUp.g_LIl_PluginTip_baseList[i];
		}
		return message;
	};
	//==============================
	// * 提示信息 - 报错 - 找不到事件
	//==============================
	DrillUp.drill_LIl_getPluginTip_EventNotFind = function( e_id ){
		return "【" + DrillUp.g_LIl_PluginTip_curName + "】\n插件指令错误，当前地图并不存在id为"+e_id+"的事件。";
	};
	//==============================
	// * 提示信息 - 报错 - 找不到图片
	//==============================
	DrillUp.drill_LIl_getPluginTip_PictureNotFind = function( pic_id ){
		return "【" + DrillUp.g_LIl_PluginTip_curName + "】\n插件指令错误，id为"+pic_id+"的图片还没被创建。\n你可能需要将指令放在'显示图片'事件指令之后。";
	};
	//==============================
	// * 提示信息 - 报错 - 找不到样式配置
	//==============================
	DrillUp.drill_LIl_getPluginTip_StyleDataNotFind = function( data_id ){
		return "【" + DrillUp.g_LIl_PluginTip_curName + "】\n插件指令错误，不存在id为"+data_id+"的光源配置。";
	};
	//==============================
	// * 提示信息 - 报错 - 照明未创建
	//==============================
	DrillUp.drill_LIl_getPluginTip_DataNotCreate = function( data_id ){
		return "【" + DrillUp.g_LIl_PluginTip_curName + "】\n插件指令错误，id为"+data_id+"的高级照明未创建，需要创建再使用。";
	};
	//==============================
	// * 提示信息 - 报错 - 插件指令过载
	//==============================
	DrillUp.drill_LIl_getPluginTip_Overdrive = function( ch_str ){
		return "【" + DrillUp.g_LIl_PluginTip_curName + "】\n插件指令过载，控制\""+ch_str+"\"的指令在3秒内重复执行了50次以上！\n请重新检查你写的事件，注意不要把指令放入并行事件反复执行！";
	};
	//==============================
	// * 提示信息 - 报错 - 黑暗层透明度
	//==============================
	DrillUp.drill_LIl_getPluginTip_OpacityWrong = function(){
		return "【" + DrillUp.g_LIl_PluginTip_curName + "】\n提示：你将临时锁定设为开启，又将当前地图的透明度设为了0。\n由于黑暗层是0为全亮，255为全黑，所以这样设置将没有任何效果，建议直接关闭。";
	};
	
	
//=============================================================================
// ** 变量获取
//=============================================================================
　　var Imported = Imported || {};
　　Imported.Drill_LayerIllumination = true;
　　var DrillUp = DrillUp || {}; 
    DrillUp.parameters = PluginManager.parameters('Drill_LayerIllumination');

	//==============================
	// * 变量获取 - 光源
	//				（~struct~LIlLight）
	//==============================
	DrillUp.drill_LIl_initLight = function( dataFrom ) {
		var data = {};
		
		// > 贴图
		if( dataFrom["资源-光源GIF"] != "" &&
			dataFrom["资源-光源GIF"] != undefined ){
			data['gif_src'] = JSON.parse( dataFrom["资源-光源GIF"] );
		}else{
			data['gif_src'] = [];
		}
		data['gif_src_file'] = "img/Map__illumination/"
		data['gif_interval'] = Number( dataFrom["帧间隔"] || 4);
		data['gif_back_run'] = String( dataFrom["是否倒放"] || "false") == "true";
		data['offsetX'] = Number( dataFrom["平移-光源 X"] || 0);
		data['offsetY'] = Number( dataFrom["平移-光源 Y"] || 0);
		data['scale_x'] = Number( dataFrom["缩放 X"] || 1.0);
		data['scale_y'] = Number( dataFrom["缩放 Y"] || 1.0);
		data['zIndex'] = Number( dataFrom["图片层级"] || 2);
		
		// > 朝向
		data['dir_mode'] = String( dataFrom["旋转模式"] || "根据事件朝向转向");
		data['dir_selfSpeed'] = Number( dataFrom["自旋转速度"] || 5.0);
		data['dir_evType'] = String( dataFrom["根据事件转向类型"] || "瞬间转向");
		data['dir_evSpeed'] = Math.abs( Number( dataFrom["根据事件转向速度"] || 0) );
		
		// > 透明度
		data['opacity_mode'] = String( dataFrom["透明度模式"] || "固定透明度");
		data['opacity_fix'] = Number( dataFrom["固定透明度"] || 255);
		data['opacity_waveMin'] = Number( dataFrom["波动透明度最小值"] || 150);
		data['opacity_waveMax'] = Number( dataFrom["波动透明度最大值"] || 255);
		data['opacity_period'] = Number( dataFrom["透明度波动周期"] || 120);
		
		return data;
	}
	
	/*-----------------黑暗层------------------*/
	DrillUp.g_LIl_enable = String(DrillUp.parameters["初始是否开启黑暗层"] || "false") == "true" ;
	DrillUp.g_LIl_sustainTime = Number(DrillUp.parameters["黑暗层过渡时间"] || 60) ;
	DrillUp.g_LIl_opacity = Number(DrillUp.parameters["黑暗层透明度"] || 255) ;
	DrillUp.g_LIl_layerColor = String(DrillUp.parameters["黑暗层颜色"] || "#000000") ;
	DrillUp.g_LIl_layer = String(DrillUp.parameters["黑暗层层级"] || "上层") ;
	
	/*-----------------光源------------------*/
	DrillUp.g_LIl_light_length = 200;
	DrillUp.g_LIl_light = [];	
	for (var i = 0; i < DrillUp.g_LIl_light_length; i++) {
		if( DrillUp.parameters["光源-" + String(i+1) ] != "" &&
			DrillUp.parameters["光源-" + String(i+1) ] != undefined ){
			var data = JSON.parse(DrillUp.parameters["光源-" + String(i+1) ]);
			DrillUp.g_LIl_light[i] = DrillUp.drill_LIl_initLight( data );
		}else{
			DrillUp.g_LIl_light[i] = null;
		}
	}
	
	
//=============================================================================
// * >>>>基于插件检测>>>>
//=============================================================================
if( Imported.Drill_CoreOfDynamicMask ){


//=============================================================================
// * 插件指令
//=============================================================================
var _drill_LIl_pluginCommand = Game_Interpreter.prototype.pluginCommand;
Game_Interpreter.prototype.pluginCommand = function(command, args) {
	_drill_LIl_pluginCommand.call(this, command, args);
	if( command === ">自定义照明" ){
		
		/*-----------------黑暗层------------------*/
		if(args.length == 4){
			var type = String(args[1]);
			var temp1 = String(args[3]);
			if( type == "黑暗层" ){
				if( temp1 == "执行开启" ){
					$gameSystem.drill_LIl_setNewTargetOpacity( DrillUp.g_LIl_opacity );
					return;
				}
				if( temp1 == "执行关闭" ){
					$gameSystem.drill_LIl_setNewTargetOpacity( 0 );
					return;
				}
			}
		}
		if(args.length == 6){
			var type = String(args[1]);
			var temp1 = String(args[3]);
			var temp2 = String(args[5]);
			if( type == "黑暗层" ){
				if( temp1 == "修改黑暗层过渡时间" ){
					$gameSystem._drill_LIl['tar_time'] = Math.max( 1, Number(temp2) );
					return;
				}
				if( temp1 == "修改黑暗层透明度" ){
					$gameSystem.drill_LIl_setNewTargetOpacity( Number(temp2) );
					return;
				}
				if( temp1 == "修改黑暗层颜色" ){
					$gameSystem._drill_LIl['layerColor'] = temp2;
					return;
				}
			}
		}
		
		// > 如果黑暗层未开，则插件指令无效
		if( $gameTemp.drill_LIl_isDarkMaskEnabled() == false ){ return; }
		
		/*-----------------物体照明 - 对象组获取------------------*/
		var chars = null;
		if(args.length == 6){
			var type = String(args[1]);
			var obj_str = String(args[3]);
			var temp2 = String(args[5]);
			if( type == "物体照明" ){
				if( chars == null && obj_str == "本事件" ){
					chars = [];
					chars.push( this._eventId );
				}
				if( chars == null && obj_str.indexOf("批量事件[") != -1 ){
					obj_str = obj_str.replace("批量事件[","");
					obj_str = obj_str.replace("]","");
					var temp_arr = obj_str.split(/[,，]/);
					chars = [];
					for( var k=0; k < temp_arr.length; k++ ){
						var e_id = Number(temp_arr[j]);
						if( $gameMap.drill_LIl_isEventExist( e_id ) == false ){ continue; }
						chars.push( e_id );
					}
				}
				if( chars == null && obj_str.indexOf("批量事件变量[") != -1 ){
					obj_str = obj_str.replace("批量事件变量[","");
					obj_str = obj_str.replace("]","");
					var temp_arr = obj_str.split(/[,，]/);
					chars = [];
					for( var k=0; k < temp_arr.length; k++ ){
						var e_id = $gameVariables.value(Number(temp_arr[k]));
						if( $gameMap.drill_LIl_isEventExist( e_id ) == false ){ continue; }
						chars.push( e_id );
					}
				}
				if( chars == null && obj_str.indexOf("事件[") != -1 ){
					obj_str = obj_str.replace("事件[","");
					obj_str = obj_str.replace("]","");
					var e_id = Number(obj_str);
					if( $gameMap.drill_LIl_isEventExist( e_id ) == false ){ return; }
					chars = [ e_id ];
				}
				if( chars == null && obj_str.indexOf("事件变量[") != -1 ){
					obj_str = obj_str.replace("事件变量[","");
					obj_str = obj_str.replace("]","");
					var e_id = $gameVariables.value(Number(obj_str));
					if( $gameMap.drill_LIl_isEventExist( e_id ) == false ){ return; }
					chars = [ e_id ];
				}
				if( chars == null && obj_str == "玩家" ){
					chars = [ -2 ];
				}
			}
		}
		/*-----------------物体照明 - 执行指令------------------*/
		if(args.length == 6){
			var type = String(args[1]);
			var temp2 = String(args[5]);
			if( type == "物体照明" ){
				
				// > 事件/玩家
				if( chars != null && chars.length > 0 ){
					if( temp2.indexOf("照明[") != -1 ){
						temp2 = temp2.replace("照明[","");
						temp2 = temp2.replace("]","");
						temp2 = Number(temp2);
						for( var j=0; j < chars.length; j++ ){
							var ch_id = chars[j];
							$gameMap.drill_LIl_addSimplePerspect_characterId( ch_id, Number(temp2)-1 );
						}
					}
					else if( temp2 == "关闭照明" ){
						for( var j=0; j < chars.length; j++ ){
							var ch_id = chars[j];
							$gameMap.drill_LIl_removeSimplePerspect_characterId( ch_id );
						}
					}
					else if( temp2.indexOf("修改图片层级[") != -1 ){
						temp2 = temp2.replace("修改图片层级[","");
						temp2 = temp2.replace("]","");
						temp2 = Number(temp2);
						for( var j=0; j < chars.length; j++ ){
							var ch_id = chars[j];
							$gameMap.drill_LIl_changeSimplePerspectZIndex_characterId( ch_id, Number(temp2) );
						}
					}
				}
				
				// 鼠标（在子插件中）
				
			}
		}
		
		/*-----------------高级照明 - 创建------------------*/
		if( args.length == 6 ){
			var temp1 = String(args[1]);
			var temp2 = String(args[3]);
			var temp3 = String(args[5]);
			if( temp1.indexOf("高级照明[") != -1 && temp2 == "创建" ){
				temp1 = temp1.replace("高级照明[","");
				temp1 = temp1.replace("]","");
				temp1 = Number(temp1);
				temp3 = temp3.replace("样式[","");
				temp3 = temp3.replace("]","");
				temp3 = Number(temp3)-1;
				$gameMap.drill_LIl_addSeniorPerspect( temp1, temp3 );
				return;
			
			}else if( temp1.indexOf("高级照明变量[") != -1 && temp2 == "创建" ){
				temp1 = temp1.replace("高级照明变量[","");
				temp1 = temp1.replace("]","");
				temp1 = $gameVariables.value( Number(temp1) );
				temp3 = temp3.replace("样式[","");
				temp3 = temp3.replace("]","");
				temp3 = Number(temp3)-1;
				$gameMap.drill_LIl_addSeniorPerspect( temp1, temp3 );
				return;
			}
		}
		if( args.length == 4 ){
			var temp1 = String(args[1]);
			var temp2 = String(args[3]);
			if( temp1.indexOf("高级照明[") != -1 && temp2 == "清除" ){
				temp1 = temp1.replace("高级照明[","");
				temp1 = temp1.replace("]","");
				temp1 = Number(temp1);
				$gameMap.drill_LIl_removeSeniorPerspect( temp1 );
				return;
			
			}else if( temp1.indexOf("高级照明变量[") != -1 && temp2 == "清除" ){
				temp1 = temp1.replace("高级照明变量[","");
				temp1 = temp1.replace("]","");
				temp1 = $gameVariables.value( Number(temp1) );
				$gameMap.drill_LIl_removeSeniorPerspect( temp1 );
				return;
			
			}else if( temp1.indexOf("获取未创建的高级照明编号[") != -1 && temp2.indexOf("变量[") != -1 ){
				temp1 = temp1.replace("获取未创建的高级照明编号[","");
				temp1 = temp1.replace("]","");
				var temp_arr = temp1.split("-");
				if( temp_arr.length >= 2 ){
					var id = $gameSystem._drill_LIl_container.drill_CODM_getEmptyId( Number(temp_arr[0]), Number(temp_arr[1]) );
					temp2 = temp2.replace("变量[","");
					temp2 = temp2.replace("]","");
					temp2 = Number(temp2);
					$gameVariables.setValue( temp2, id );
				}
				return;
			}
		}
		/*-----------------高级照明 - 对象组获取------------------*/
		var marker = null;
		if( args.length >= 2 ){
			var temp1 = String(args[1]);
			if( temp1.indexOf("高级照明[") != -1 ){
				temp1 = temp1.replace("高级照明[","");
				temp1 = temp1.replace("]","");
				temp1 = Number(temp1);
				marker = $gameSystem._drill_LIl_container.drill_CODM_getSeniorMarkerById( temp1 );
				if( marker == undefined ){
					alert( DrillUp.drill_LIl_getPluginTip_DataNotCreate( temp1 ) );
				}
				
			}else if( temp1.indexOf("高级照明变量[") != -1 ){
				temp1 = temp1.replace("高级照明变量[","");
				temp1 = temp1.replace("]","");
				temp1 = $gameVariables.value( Number(temp1) );
				marker = $gameSystem._drill_LIl_container.drill_CODM_getSeniorMarkerById( temp1 );
				if( marker == undefined ){
					alert( DrillUp.drill_LIl_getPluginTip_DataNotCreate( temp1 ) );
				}
			}
		}
		/*-----------------高级照明 - 生命周期------------------*/
		if( marker != null && args.length == 6 ){
			var type = String(args[3]);
			var temp1 = String(args[5]);
			if( type == "设置生命" ){
				temp1 = temp1.replace("持续时间[","");
				temp1 = temp1.replace("]","");
				temp1 = Number(temp1);
				marker.drill_setLifeTime( temp1 );
				return;
			}
		}
		if( marker != null && args.length == 4 ){
			var type = String(args[3]);
			if( type == "暂停生命流逝" ){
				marker.drill_setLifePause( temp1 );
				return;
			}
			if( type == "继续生命流逝" ){
				marker.drill_setLifePause( temp1 );
				return;
			}
		}
		/*-----------------高级照明 - 绑定到------------------*/
		if( marker != null && args.length == 6 ){
			var type = String(args[3]);
			var obj_str = String(args[5]);
			if( type == "绑定到" ){
				
				if( obj_str == "玩家" ){
					marker.drill_setBindingCharacterId( -2 );
					return;
				}
				if( obj_str == "本事件" ){
					marker.drill_setBindingCharacterId( this._eventId );
					return;
				}
				if( obj_str.indexOf("事件[") != -1 ){
					obj_str = obj_str.replace("事件[","");
					obj_str = obj_str.replace("]","");
					var e_id = Number(obj_str);
					if( $gameMap.drill_LIl_isEventExist( e_id ) == false ){ return; }
					marker.drill_setBindingCharacterId( e_id );
					return;
				}
				if( obj_str.indexOf("事件变量[") != -1 ){
					obj_str = obj_str.replace("事件变量[","");
					obj_str = obj_str.replace("]","");
					var e_id = $gameVariables.value( Number(obj_str) );
					if( $gameMap.drill_LIl_isEventExist( e_id ) == false ){ return; }
					marker.drill_setBindingCharacterId( e_id );
					return;
				}
				if( obj_str == "鼠标" ){
					marker.drill_setBindingMouse( true );
					return;
				}
				if( obj_str.indexOf("图片变量[") != -1 ){
					obj_str = obj_str.replace("图片变量[","");
					obj_str = obj_str.replace("]","");
					var pic_id = $gameVariables.value( Number(obj_str) );
					if( $gameScreen.drill_LIl_isPictureExist( pic_id ) == false ){ return; }
					marker.drill_setBindingPictureId( pic_id );
					return;
				}
				if( obj_str.indexOf("图片[") != -1 ){
					obj_str = obj_str.replace("图片[","");
					obj_str = obj_str.replace("]","");
					var pic_id = Number(obj_str);
					if( $gameScreen.drill_LIl_isPictureExist( pic_id ) == false ){ return; }
					marker.drill_setBindingPictureId( pic_id );
					return;
				}
			}
		}
		if( marker != null && args.length == 4 ){
			var type = String(args[3]);
			if( type == "位置归零" ){
				var m_data = {
					"x": 0,
					"y": 0,
					"time": 1,
					"type": "瞬间移动",
				}
				$gameSystem.drill_LIl_moveTo( marker.drill_id(), m_data );
			}
		}
		/*-----------------高级照明 - 移动------------------*/
		if( marker != null && (args.length == 6 || args.length == 8) ){
			var type = String(args[3]);
			var temp1 = String(args[5]);
			var temp2 = String(args[7] || "1");
			if( type == "瞬间移动" || type == "匀速移动" || type == "增减速移动" || type == "弹性移动" || type == "抛物线移动" ){
				temp2 = temp2.replace("时间[","");
				temp2 = temp2.replace("]","");
				temp2 = Number(temp2);
				
				var pos = [];
				if( temp1.indexOf("相对位置变量[") != -1 ){
					temp1 = temp1.replace("相对位置变量[","");
					temp1 = temp1.replace("]","");
					temp1 = temp1.split(/[,，]/);
					pos = [ $gameVariables.value(Number(temp1[0])), 
							$gameVariables.value(Number(temp1[1])) ];
					var m_data = {
						"x": data['x'] + Number(pos[0]),
						"y": data['y'] + Number(pos[1]),
						"time":temp2,
						"type":type,
					}
					$gameSystem.drill_LIl_moveTo( marker.drill_id(), m_data );
					
				}else if( temp1.indexOf("相对位置[") != -1 ){
					temp1 = temp1.replace("相对位置[","");
					temp1 = temp1.replace("]","");
					temp1 = temp1.split(/[,，]/);
					pos = [ Number(temp1[0]), 
							Number(temp1[1]) ];
					var m_data = {
						"x": data['x'] + Number(pos[0]),
						"y": data['y'] + Number(pos[1]),
						"time":temp2,
						"type":type,
					}
					$gameSystem.drill_LIl_moveTo( marker.drill_id(), m_data );
					
				}else if( temp1.indexOf("位置变量[") != -1 ){
					temp1 = temp1.replace("位置变量[","");
					temp1 = temp1.replace("]","");
					temp1 = temp1.split(/[,，]/);
					pos = [ $gameVariables.value(Number(temp1[0])), 
							$gameVariables.value(Number(temp1[1])) ];
					var m_data = {
						"x":Number(pos[0]),
						"y":Number(pos[1]),
						"time":temp2,
						"type":type,
					}
					$gameSystem.drill_LIl_moveTo( marker.drill_id(), m_data );
				}
				else if( temp1.indexOf("位置[") != -1 ){
					temp1 = temp1.replace("位置[","");
					temp1 = temp1.replace("]","");
					temp1 = temp1.split(/[,，]/);
					pos = [ Number(temp1[0]), 
							Number(temp1[1]) ];
					var m_data = {
						"x":Number(pos[0]),
						"y":Number(pos[1]),
						"time":temp2,
						"type":type,
					}
					$gameSystem.drill_LIl_moveTo( marker.drill_id(), m_data );
				}
			}
		}
		/*-----------------高级照明 - 缩放变化------------------*/
		if( marker != null && (args.length == 6 || args.length == 8) ){
			var type = String(args[3]);
			var temp1 = String(args[5]);
			var temp2 = String(args[7] || "1");
			if( type == "瞬间变化" || type == "匀速变化" || type == "增减速变化" || type == "弹性变化" ){
				temp2 = temp2.replace("时间[","");
				temp2 = temp2.replace("]","");
				temp2 = Number(temp2);
				
				if( temp1.indexOf("缩放X[") != -1 ){
					temp1 = temp1.replace("缩放X[","");
					temp1 = temp1.replace("]","");
					var o_data = {
						"scaleX":Number(temp1),
						"time":temp2,
						"type":type,
					}
					$gameSystem.drill_LIl_scaleXTo( marker.drill_id(), o_data );
				}
				if( temp1.indexOf("缩放Y[") != -1 ){
					temp1 = temp1.replace("缩放Y[","");
					temp1 = temp1.replace("]","");
					var o_data = {
						"scaleY":Number(temp1),
						"time":temp2,
						"type":type,
					}
					$gameSystem.drill_LIl_scaleYTo( marker.drill_id(), o_data );
				}
			}
		}
		/*-----------------高级照明 - 透明度变化------------------*/
		if( marker != null && (args.length == 6 || args.length == 8) ){
			var type = String(args[3]);
			var temp1 = String(args[5]);
			var temp2 = String(args[7] || "1");
			if( type == "瞬间变化" || type == "匀速变化" || type == "增减速变化" || type == "弹性变化" ){
				temp2 = temp2.replace("时间[","");
				temp2 = temp2.replace("]","");
				temp2 = Number(temp2);
				
				if( temp1.indexOf("透明度[") != -1 ){
					temp1 = temp1.replace("透明度[","");
					temp1 = temp1.replace("]","");
					var o_data = {
						"opacity":Number(temp1),
						"time":temp2,
						"type":type,
					}
					$gameSystem.drill_LIl_opacityTo( marker.drill_id(), o_data );
				}
			}
		}
		/*-----------------高级照明 - 旋转变化------------------*/
		if( marker != null && (args.length == 6 || args.length == 8) ){
			var type = String(args[3]);
			var temp1 = String(args[5]);
			var temp2 = String(args[7] || "1");
			if( type == "瞬间变化" || type == "匀速变化" || type == "增减速变化" || type == "弹性变化" ){
				temp2 = temp2.replace("时间[","");
				temp2 = temp2.replace("]","");
				temp2 = Number(temp2);
				
				if( temp1.indexOf("旋转角度[") != -1 ){
					temp1 = temp1.replace("旋转角度[","");
					temp1 = temp1.replace("]","");
					var o_data = {
						"rotate":Number(temp1),
						"time":temp2,
						"type":type,
					}
					$gameSystem.drill_LIl_rotateTo( marker.drill_id(), o_data );
				}
			}
		}
		
		/*-----------------限时动态照明------------------*/
		var marker_lim = null;
		if( args.length == 10 ){
			var type = String(args[1]);
			var temp1 = String(args[3]);
			var temp2 = String(args[5]);
			var temp3 = String(args[7]);
			var temp4 = String(args[9]);
			if( type == "限时动态照明" && temp3 != "鼠标" ){
				temp2 = temp2.replace("持续时间[","");
				temp2 = temp2.replace("]","");
				temp2 = Number(temp2);
				temp4 = temp4.replace("照明[","");
				temp4 = temp4.replace("]","");
				temp4 = Number(temp4)-1;
				
				// > 获取编号
				var id = $gameSystem._drill_LIl_container.drill_CODM_getEmptyId( 100, 200 );
				
				// > 创建
				var marker_lim = $gameMap.drill_LIl_addSeniorPerspect( id, temp4 );
				if( marker_lim == undefined ){ return; }
				
				// > 设置生命
				marker_lim.drill_setLifeTime( temp2 );
				
				// > 绑定对象
				if( temp3 == "玩家" ){
					marker_lim.drill_setBindingCharacterId( -2 );
				}
				if( temp3 == "本事件" ){
					marker_lim.drill_setBindingCharacterId( this._eventId );
				}
				if( temp3.indexOf("事件[") != -1 ){
					temp3 = temp3.replace("事件[","");
					temp3 = temp3.replace("]","");
					var e_id = Number(temp3);
					if( $gameMap.drill_LIl_isEventExist( e_id ) == false ){ return; }
					marker_lim.drill_setBindingCharacterId( e_id );
				}
				if( temp3.indexOf("事件变量[") != -1 ){
					temp3 = temp3.replace("事件变量[","");
					temp3 = temp3.replace("]","");
					var e_id = $gameVariables.value( Number(temp3) );
					if( $gameMap.drill_LIl_isEventExist( e_id ) == false ){ return; }
					marker_lim.drill_setBindingCharacterId( e_id );
				}
				
				// > 透明度变化
				if( temp1 == "逐渐淡去" ){
					marker_lim._opacity = 255;		//（强行修改初始化时透明度）
					var o_data = {
						"opacity":0,
						"time":temp2,
						"type":"匀速变化",
					}
					$gameSystem.drill_LIl_opacityTo( marker_lim.drill_id(), o_data );
				}
				if( temp1 == "逐渐显现" ){
					marker_lim._opacity = 0;
					var o_data = {
						"opacity":255,
						"time":temp2,
						"type":"匀速变化",
					}
					$gameSystem.drill_LIl_opacityTo( marker_lim.drill_id(), o_data );
				}
				if( temp1 == "保持亮度" ){
					marker_lim._opacity = 255;
					var o_data = {
						"opacity":255,
						"time":1,
						"type":"匀速变化",
					}
					$gameSystem.drill_LIl_opacityTo( marker_lim.drill_id(), o_data );
				}
			}
		}
	}
};
//==============================
// ** 插件指令 - 事件检查
//==============================
Game_Map.prototype.drill_LIl_isEventExist = function( e_id ){
	if( e_id == 0 ){ return false; }
	
	var e = this.event( e_id );
	if( e == undefined ){
		alert( DrillUp.drill_LIl_getPluginTip_EventNotFind( e_id ) );
		return false;
	}
	return true;
};
//==============================
// ** 插件指令 - 图片检查
//==============================
Game_Screen.prototype.drill_LIl_isPictureExist = function( pic_id ){
	if( pic_id == 0 ){ return false; }
	
	var pic = this.picture( pic_id );
	if( pic == undefined ){
		alert( DrillUp.drill_LIl_getPluginTip_PictureNotFind( pic_id ) );
		return false;
	}
	return true;
};


//#############################################################################
// ** 【标准模块】存储数据
//#############################################################################
//##############################
// * 存储数据 - 参数存储 开关
//          
//			说明：	> 如果该插件开放了用户可以修改的参数，就注释掉。
//##############################
DrillUp.g_LIl_saveEnabled = true;
//##############################
// * 存储数据 - 初始化
//          
//			说明：	> 下方为固定写法，不要动。
//##############################
var _drill_LIl_sys_initialize = Game_System.prototype.initialize;
Game_System.prototype.initialize = function() {
    _drill_LIl_sys_initialize.call(this);
	this.drill_LIl_initSysData();
};
//##############################
// * 存储数据 - 载入存档
//          
//			说明：	> 下方为固定写法，不要动。
//##############################
var _drill_LIl_sys_extractSaveContents = DataManager.extractSaveContents;
DataManager.extractSaveContents = function( contents ){
	_drill_LIl_sys_extractSaveContents.call( this, contents );
	
	// > 参数存储 启用时（检查数据）
	if( DrillUp.g_LIl_saveEnabled == true ){	
		$gameSystem.drill_LIl_checkSysData();
		
	// > 参数存储 关闭时（直接覆盖）
	}else{
		$gameSystem.drill_LIl_initSysData();
	}
};
//##############################
// * 存储数据 - 初始化数据【标准函数】
//			
//			参数：	> 无
//			返回：	> 无
//          
//			说明：	> 强行规范的接口，执行数据初始化，并存入存档数据中。
//##############################
Game_System.prototype.drill_LIl_initSysData = function() {
	this.drill_LIl_initSysData_Private();
};
//##############################
// * 存储数据 - 载入存档时检查数据【标准函数】
//			
//			参数：	> 无
//			返回：	> 无
//          
//			说明：	> 强行规范的接口，载入存档时执行的数据检查操作。
//##############################
Game_System.prototype.drill_LIl_checkSysData = function() {
	this.drill_LIl_checkSysData_Private();
};
//=============================================================================
// ** 存储数据（接口实现）
//=============================================================================
//==============================
// * 存储数据 - 初始化数据（私有）
//==============================
Game_System.prototype.drill_LIl_initSysData_Private = function() {
	
	// > 黑暗层数据
	this._drill_LIl = {};
	this._drill_LIl['cur_time'] = 0;									//黑暗层 - 当前时间
	this._drill_LIl['tar_time'] = DrillUp.g_LIl_sustainTime;			//黑暗层 - 过渡时间
	this._drill_LIl['cur_opacity'] = 0;									//黑暗层 - 当前透明度（实时变化）
	this._drill_LIl['last_opacity'] = 0;								//黑暗层 - 变化前透明度
	this._drill_LIl['next_opacity'] = 0;								//黑暗层 - 下一个透明度
	this._drill_LIl['layerColor'] = DrillUp.g_LIl_layerColor;			//黑暗层 - 颜色
	
	// > 初始开启黑暗层
	if( DrillUp.g_LIl_enable == true ){		//（设为指定透明度）
		this._drill_LIl['last_opacity'] = DrillUp.g_LIl_opacity;	
		this._drill_LIl['next_opacity'] = DrillUp.g_LIl_opacity;
	}
};
//==============================
// * 存储数据 - 载入存档时检查数据（私有）
//==============================
Game_System.prototype.drill_LIl_checkSysData_Private = function() {
	
	// > 旧存档数据自动补充
	if( this._drill_LIl == undefined ){
		this.drill_LIl_initSysData();
	}
	
};
//==============================
// * 存储数据 - 设置黑暗层透明度
//==============================
Game_System.prototype.drill_LIl_setNewTargetOpacity = function( opacity ){
	var l_data = this._drill_LIl;
	l_data['last_opacity'] = l_data['last_opacity'] + (l_data['next_opacity'] - l_data['last_opacity']) * l_data['cur_time'] / l_data['tar_time'];
	l_data['cur_time'] = 0;		//（从透明度A迈向透明度B）
	l_data['next_opacity'] = Math.min( 255, Math.max( 0, Number(opacity) ));
	
	//alert(l_data['cur_time']);
	//alert(l_data['tar_time']);
	//alert(l_data['last_opacity']);
	//alert(l_data['next_opacity']);
}



//=============================================================================
// ** 遮罩渲染器【Drill_LIl_Renderer】
//			
//			说明：	> 该类为静态类，单独定义一个渲染器结构。
//					> 该渲染器与 主游戏界面 完全并行渲染场景。
//					> 此功能可类比 Drill_CODM_Renderer 遮罩渲染器
//=============================================================================
//==============================
// * 渲染器 - 定义
//==============================
function Drill_LIl_Renderer() {
	this.initialize.apply(this, arguments);
}
//==============================
// * 渲染器 - 初始化
//==============================
Drill_LIl_Renderer.initialize = function(){
	this.drill_LIl_createCanvas();			//创建 - 画布
	this.drill_LIl_createRenderer();		//创建 - 渲染器
};
//==============================
// * 渲染器 - 刷新（非帧）
//==============================
Drill_LIl_Renderer.update = function() {
	this.drill_LIl_updateCanvas();			//刷新 - 画布
	this.drill_LIl_updateRenderer();		//刷新 - 渲染器
}
//==============================
// * 创建 - 画布
//==============================
Drill_LIl_Renderer.drill_LIl_createCanvas = function() {
	this._drill_LIl_canvas = document.createElement('canvas');		//（canvas是非常基础的对象，一个texture/bitmap就有一个canvas）
	this._drill_LIl_canvas.id = 'drill_LIl_canvas';

	this.drill_LIl_updateCanvas();		//（创建后刷新）
};
//==============================
// * 创建 - 渲染器
//==============================
Drill_LIl_Renderer.drill_LIl_createRenderer = function() {
	PIXI.dontSayHello = true;
	var width = Graphics.boxWidth;
	var height = Graphics.boxHeight;
	var options = { view: this._drill_LIl_canvas };
	try {
		switch( Graphics._rendererType ){
			
		// > canvas渲染器
		case 'canvas':
			this._drill_LIl_renderer = new PIXI.CanvasRenderer(width, height, options);
			break;
			
		// > webgl渲染器
		case 'webgl':
			this._drill_LIl_renderer = new PIXI.WebGLRenderer(width, height, options);
			break;
			
		// > 自动渲染器（在canvas和webgl选一）
		default:
			this._drill_LIl_renderer = PIXI.autoDetectRenderer(width, height, options);
			break;
		}
	
		// > webgl渲染器 的材质缓存数
		if( this._drill_LIl_renderer && this._drill_LIl_renderer.textureGC ){
			this._drill_LIl_renderer.textureGC.maxIdle = 600;		//（最大缓存值：600）
		}
	}catch( e ){
		this._drill_LIl_renderer = null;
	}
};
//==============================
// * 刷新 - 画布
//==============================
Drill_LIl_Renderer.drill_LIl_updateCanvas = function() {
	this._drill_LIl_canvas.style.width = Graphics.boxWidth;		//（保持窗口高宽）
	this._drill_LIl_canvas.style.height = Graphics.boxHeight;
	this._drill_LIl_canvas.style.zIndex = 0;
};
//==============================
// * 刷新 - 渲染器
//==============================
Drill_LIl_Renderer.drill_LIl_updateRenderer = function() {
	if( this._drill_LIl_renderer == undefined ){ return; }			//（保持窗口高宽）
	this._drill_LIl_renderer.resize( Graphics.boxWidth, Graphics.boxHeight );
};
//==============================
// * 渲染器 - 执行渲染（接口）
//==============================
Drill_LIl_Renderer.drill_LIl_doRender = function( stage ){
	if( stage ){
		this._drill_LIl_renderer.render(stage);
		if (this._drill_LIl_renderer.gl && this._drill_LIl_renderer.gl.flush) {
			this._drill_LIl_renderer.gl.flush();
		}
	}
};
//==============================
// * 渲染器 - 初始化（绑定）
//==============================
var _drill_LIl_createAllElements = Graphics._createAllElements;
Graphics._createAllElements = function() {
	_drill_LIl_createAllElements.call(this);
	Drill_LIl_Renderer.initialize();		//渲染器初始化
}
//==============================
// * 渲染器 - 刷新（非帧）
//==============================
var _drill_LIl_updateAllElements = Graphics._updateAllElements;
Graphics._updateAllElements = function() {
	_drill_LIl_updateAllElements.call(this);
	Drill_LIl_Renderer.update();			//渲染器刷新
}


//=============================================================================
// ** 黑暗层贴图【Drill_LIl_MaskSprite】
//			
//			
//			主功能：	定义一个贴图，能够容纳光源，并实现遮挡效果。
//			子功能：	
//						->渲染
//							->低帧优化
//							->遮罩高宽
//							->渲染材质（PIXI.BaseTexture）
//						->绘制层
//							->场景容器
//							->添加光源（可嵌套）
//							->移除光源
//							->贴图底色
//
//	 		代码：	> 范围 - 该类显示单独的黑暗层。
//					> 结构 - [ ●合并 /分离/混乱] 数据与贴图合并。
//					> 数量 - [ ●单个 /多个] 
//					> 创建 - [ ●一次性 /自延迟/外部延迟] 
//					> 销毁 - [ ●不考虑 /自销毁/外部销毁 ] 
//					> 样式 - [不可修改/ ●自变化 /外部变化] 根据地图注释配置样式，创建后样式根据$gameSystem的部分参数变化。
//					
//			说明：	此功能可类比 Drill_CODM_MaskSprite 动态遮罩贴图。
//=============================================================================
//==============================
// * 黑暗层贴图 - 定义
//==============================
function Drill_LIl_MaskSprite() {
	this.initialize.apply(this, arguments);
}
Drill_LIl_MaskSprite.prototype = Object.create(Sprite_Base.prototype);
Drill_LIl_MaskSprite.prototype.constructor = Drill_LIl_MaskSprite;
//==============================
// * 黑暗层贴图 - 初始化
//==============================
Drill_LIl_MaskSprite.prototype.initialize = function( width, height ){
	Sprite_Base.prototype.initialize.call(this);
	
	// > 私有属性初始化
	this._drill_time = 0;					//低帧优化
	this._drill_width = width;				//遮罩宽度
	this._drill_height = height;			//遮罩高度
	
	this._drill_stage = null;				//场景容器
	this._drill_main_layer = null;			//主绘制层
	this._drill_curColor = null;			//当前贴图底色
	this.blendMode = 2;						//混合模式（关键控制，固定乘积混合）
	
	// > 自定义渲染器 - 材质
	var source = Drill_LIl_Renderer._drill_LIl_canvas;
	this.__baseTexture = new PIXI.BaseTexture(source);
    this.__baseTexture.mipmap = false;
    this.__baseTexture.width = source.width;
    this.__baseTexture.height = source.height;
	
	// > 渲染材质
	this._texture = new PIXI.Texture(this.__baseTexture);
	this.texture = this._texture;
	
	this.drill_createStage();			//场景容器初始化
};
//==============================
// * 黑暗层贴图 - 帧刷新
//==============================
Drill_LIl_MaskSprite.prototype.update = function() {
	Sprite_Base.prototype.update.call(this);
	
	// > 关闭时，不工作
	var temp_visible = true;
	temp_visible = $gameTemp.drill_LIl_isDarkMaskEnabled();
	if( SceneManager._scene.constructor.name != "Scene_Map" ){ temp_visible = false; }
	
	// > 可见
	this.visible = temp_visible;
	if( temp_visible == false ){ return; }
	
	// > fps控制
	this._drill_time += 1;
	var fps = 1000 / Graphics._fpsMeter.duration;
	if( fps < 10 ){
		if( this._drill_time * 3 != 0 ){ return; }	//低帧数减少刷新
	}
	
	// > 绘制子类
	if( this._drill_stage.isReady() ){
		
		this._drill_stage.update();										//手动刷新
		Drill_LIl_Renderer.drill_LIl_doRender(this._drill_stage);		//手动渲染
		
		// > 画到texture中
		this.__baseTexture.update();
		
		// > 画到bitmap中（速度慢）
		//this.bitmap._context.drawImage( DrillUp.g_LIl_canvas, 0, 0);
		//this.bitmap._setDirty();
		
		// > 强制渲染
		//this.renderable = true;
	}
	
	// > 主绘制层 - 变色控制
	if( $gameMap._drill_LIl_lock['enableLocked'] == true ){
		if( this._drill_curColor != $gameMap._drill_LIl_lock['layerColor'] ){
			this._drill_curColor = $gameMap._drill_LIl_lock['layerColor'];
			this._drill_main_layer.bitmap.fillAll( this._drill_curColor );		
		}
	}else{
		if( this._drill_curColor != $gameSystem._drill_LIl['layerColor'] ){	
			this._drill_curColor = $gameSystem._drill_LIl['layerColor'];
			this._drill_main_layer.bitmap.fillAll( this._drill_curColor );	
		}
	}
};
//==============================
// * 黑暗层贴图 - 场景容器初始化
//==============================
Drill_LIl_MaskSprite.prototype.drill_createStage = function(){
	
	// > 场景容器
	this._drill_stage = new Scene_Base();
	this._drill_stage.start();
	this._drill_stage.create();
	
	// > 主绘制层
	this._drill_main_layer = new Sprite();
	this._drill_stage.addChild(this._drill_main_layer);	
	this._drill_main_layer.bitmap = new Bitmap( this._drill_width, this._drill_height );
	
	// > 主绘制层 - 绘制底色
	if( $gameMap._drill_LIl_lock['enableLocked'] == true ){
		this._drill_curColor = $gameMap._drill_LIl_lock['layerColor'];
		this._drill_main_layer.bitmap.fillAll( this._drill_curColor );		
	}else{
		this._drill_curColor = $gameSystem._drill_LIl['layerColor'];
		this._drill_main_layer.bitmap.fillAll( this._drill_curColor );		
	}
}
//==============================
// * 黑暗层贴图 - 添加光源（接口）
//==============================
Drill_LIl_MaskSprite.prototype.drill_LIl_addMaskChild = function( temp_sprite ) {
	this._drill_main_layer.addChild( temp_sprite );
}
//==============================
// * 黑暗层贴图 - 移除光源（接口）
//==============================
Drill_LIl_MaskSprite.prototype.drill_LIl_removeMaskChild = function( temp_sprite ) {
	this._drill_main_layer.removeChild( temp_sprite );
}
//==============================
// * 黑暗层贴图 - 判断是否开启
//==============================
Game_Temp.prototype.drill_LIl_isDarkMaskEnabled = function(){
	
	// > 临时锁定 为关闭状态，则表示长期未开
	if( $gameMap._drill_LIl_lock['enableLocked'] == true && $gameMap._drill_LIl_lock['enable'] == false ){ return false; }
	
	// > 未锁定，且透明度为0，也表示长期未开
	if( $gameMap._drill_LIl_lock['enableLocked'] == false && $gameSystem._drill_LIl['cur_opacity'] == 0 ){ return false; }
	
	return true;
}


//=============================================================================
// ** 黑暗层数据（地图备注）
//=============================================================================
//==============================
// * 地图备注 - 初始化
//==============================
var _drill_LIl_map_setup = Game_Map.prototype.setup;
Game_Map.prototype.setup = function(mapId) {
	_drill_LIl_map_setup.call(this, mapId);
	this.drill_LIl_setupIllumination();			//黑暗层初始化
};
//==============================
// * 地图备注 - 黑暗层初始化
//==============================
Game_Map.prototype.drill_LIl_setupIllumination = function() {
	
	// > 地图锁定初始化
	this._drill_LIl_lock = {};
	this._drill_LIl_lock['enableLocked'] = false;
	this._drill_LIl_lock['enable'] = false;									//黑暗层锁定 - 开关
	this._drill_LIl_lock['sustainTime'] = DrillUp.g_LIl_sustainTime;		//黑暗层锁定 - 过渡时间
	this._drill_LIl_lock['targetOpacity'] = DrillUp.g_LIl_opacity;			//黑暗层锁定 - 透明度
	this._drill_LIl_lock['layerColor'] = DrillUp.g_LIl_layerColor;			//黑暗层锁定 - 颜色
	
	$dataMap.note.split(/[\r\n]+/).forEach(function(note) {
		var args = note.split(':');
		var command = args.shift();
		if( command == "=>自定义照明"){
			if(args.length == 2){
				var temp1 = String(args[0]);
				var temp2 = String(args[1]);
				if( temp1 == "临时锁定"){
					if( temp2 == "启用" || temp2 == "开启" || temp2 == "打开" || temp2 == "启动" ){
						this._drill_LIl_lock['enableLocked'] = true;
						this._drill_LIl_lock['enable'] = true;
					}
					if( temp2 == "关闭" || temp2 == "禁用" ){
						this._drill_LIl_lock['enableLocked'] = true;
						this._drill_LIl_lock['enable'] = false;
					}
				}
			}
			if(args.length == 3){
				var temp1 = String(args[0]);
				var type = String(args[1]);
				var temp2 = String(args[2]);
				if( temp1 == "临时锁定"){
					if( type == "黑暗层过渡时间"){		//（这个参数没有意义，地图切换不需要过渡。不过先放着）
						this._drill_LIl_lock['sustainTime'] = Math.max( 1, Number(temp2) );
					}
					if( type == "黑暗层透明度"){
						temp2 = Number(temp2);
						if( temp2 == 0 && this._drill_LIl_lock['enable'] == true ){
							alert( DrillUp.drill_LIl_getPluginTip_OpacityWrong() );
						}
						this._drill_LIl_lock['targetOpacity'] = temp2;
					}
					if( type == "黑暗层颜色"){
						this._drill_LIl_lock['layerColor'] = temp2;
					}
				}
			}
		}
	},this);
};

//=============================================================================
// ** 黑暗层贴图（地图层级）
//=============================================================================
//==============================
// ** 上层
//==============================
var _drill_LIl_layer_createDestination = Spriteset_Map.prototype.createDestination;
Spriteset_Map.prototype.createDestination = function() {
	_drill_LIl_layer_createDestination.call(this);	//鼠标目的地 < 上层 < 天气层
	if( !this._drill_mapUpArea ){
		this._drill_mapUpArea = new Sprite();
		this._baseSprite.addChild(this._drill_mapUpArea);	
	}
}
//==============================
// ** 图片层
//==============================
var _drill_LIl_layer_createPictures = Spriteset_Map.prototype.createPictures;
Spriteset_Map.prototype.createPictures = function() {
	_drill_LIl_layer_createPictures.call(this);		//图片对象层 < 图片层 < 对话框集合
	if( !this._drill_mapPicArea ){
		this._drill_mapPicArea = new Sprite();
		this.addChild(this._drill_mapPicArea);	
	}
}
//==============================
// ** 最顶层
//==============================
var _drill_LIl_layer_createAllWindows = Scene_Map.prototype.createAllWindows;
Scene_Map.prototype.createAllWindows = function() {
	_drill_LIl_layer_createAllWindows.call(this);	//对话框集合 < 最顶层
	if( !this._drill_SenceTopArea ){
		this._drill_SenceTopArea = new Sprite();
		this.addChild(this._drill_SenceTopArea);	
	}
}
//==============================
// ** 黑暗层层级排序
//==============================
Scene_Map.prototype.drill_LIl_sortByZIndex = function() {
	this._spriteset._drill_mapUpArea.children.sort(function(a, b){return a.zIndex-b.zIndex});
	this._spriteset._drill_mapPicArea.children.sort(function(a, b){return a.zIndex-b.zIndex});
	this._drill_SenceTopArea.children.sort(function(a, b){return a.zIndex-b.zIndex});
};
//=============================================================================
// ** 黑暗层贴图（创建）
//=============================================================================
//==============================
// * 地图界面 - 创建
//==============================
var _drill_LIl_Scene_createAllWindows = Scene_Map.prototype.createAllWindows;
Scene_Map.prototype.createAllWindows = function() {
	_drill_LIl_Scene_createAllWindows.call(this);
	this.drill_LIl_createDarkLayer();			//创建黑暗层
};
//==============================
// * 地图界面 - 创建黑暗层
//==============================
Scene_Map.prototype.drill_LIl_createDarkLayer = function() {
	$gameSystem.drill_LIl_checkSysData_Private();	//（参数检查）
	
	// > 黑暗层
	var temp_sprite = new Drill_LIl_MaskSprite(Graphics.boxWidth, Graphics.boxHeight);
	temp_sprite.visible = false;
	temp_sprite.zIndex = 100;
	
	if( DrillUp.g_LIl_layer == "上层" ){
		this._spriteset._drill_mapUpArea.addChild(temp_sprite);
	}
	if( DrillUp.g_LIl_layer == "图片层" ){
		this._spriteset._drill_mapPicArea.addChild(temp_sprite);
	}
	if( DrillUp.g_LIl_layer == "最顶层" ){
		this._drill_SenceTopArea.addChild(temp_sprite);
	}
	this._drill_LIl_darkSprite = temp_sprite;
	this.drill_LIl_sortByZIndex();		//黑暗层排序
}
//==============================
// * 地图界面 - 帧刷新
//
//			说明：	要继承updateMain，而不是update，否则会有1像素抖动问题。
//==============================
var _drill_LIl_scene_updateMain = Scene_Map.prototype.updateMain;
Scene_Map.prototype.updateMain = function() {	
	_drill_LIl_scene_updateMain.call(this);
	
	if( this.isActive() ){
		this.drill_LIl_updateDarkLayer();		//黑暗层帧刷新
		this.drill_LIl_updateLightCheck();		//光源容器帧刷新
		if( $gameTemp._drill_LIl_needSort == true ){	//光源排序
			$gameTemp._drill_LIl_needSort = false;
			this.drill_LIl_sortLightByZIndex();
		}
	}
};
//==============================
// * 地图界面 - 黑暗层帧刷新
//==============================
Scene_Map.prototype.drill_LIl_updateDarkLayer = function() {
	
	// > 地图锁定时，黑暗层无法变化
	if( $gameMap._drill_LIl_lock['enableLocked'] == true ){ 		
		this._drill_LIl_darkSprite.opacity = $gameMap._drill_LIl_lock['targetOpacity'];
		return;
	}
	
	// > 旧版本兼容刷新
	if( $gameSystem._drill_LIl['cur_time'] == undefined ){ $gameSystem.drill_LIl_darkInit(); }
	var l_data = $gameSystem._drill_LIl;
	
	// > 透明度控制	
	l_data['cur_time'] += 1;
	if( l_data['cur_time'] >= l_data['tar_time'] ){
		l_data['cur_time'] = l_data['tar_time'];
		
		// > 完成变换时
		l_data['last_opacity'] = l_data['next_opacity'];
		l_data['cur_opacity'] = l_data['next_opacity'];
		this._drill_LIl_darkSprite.opacity = l_data['cur_opacity'];
		return;
	}
	
	// > 变换过程
	l_data['cur_opacity'] = l_data['last_opacity'] + (l_data['next_opacity'] - l_data['last_opacity']) * l_data['cur_time'] / l_data['tar_time'];
	this._drill_LIl_darkSprite.opacity = l_data['cur_opacity'];
};
//==============================
// * 地图界面 - 光源层级排序
//==============================
Scene_Map.prototype.drill_LIl_sortLightByZIndex = function(){
	this._drill_LIl_darkSprite._drill_main_layer.children.sort(function(a, b){return a._drill_marker.zIndex-b._drill_marker.zIndex});	//比较器
}




//=============================================================================
// ** 事件
//=============================================================================
//==============================
// * 事件 - 注释初始化
//==============================
var _drill_LIl_setupPage = Game_Event.prototype.setupPage;
Game_Event.prototype.setupPage = function() {
	_drill_LIl_setupPage.call(this);
    this.drill_LIl_setup();
};
Game_Event.prototype.drill_LIl_setup = function() {		

	if( !this._erased && this.page() ){ this.list().forEach(function( l ){
		if( l.code === 108 ){
			var args = l.parameters[0].split(' ');
			var command = args.shift();
			if( command == "=>自定义照明" ){
				if(args.length == 2){		//=>自定义照明 : 照明[1]
					var temp1 = String(args[1]);
					if( temp1 == "关闭照明" ){
						$gameMap.drill_LIl_removeSimplePerspect_characterId( this._eventId );
					}
					if( temp1.indexOf("照明[") != -1 ){
						temp1 = temp1.replace("照明[","");
						temp1 = temp1.replace("]","");
						$gameMap.drill_LIl_addSimplePerspect_characterId( this._eventId, Number(temp1)-1 );
					}
				}
				if(args.length == 4){		//=>自定义照明 : 物体照明 : 照明[1]
					var type = String(args[1]);
					var temp1 = String(args[3]);
					if( type == "物体照明" ){
						if( temp1 == "关闭照明" ){
							$gameMap.drill_LIl_removeSimplePerspect_characterId( this._eventId );
						}
						if( temp1.indexOf("照明[") != -1 ){
							temp1 = temp1.replace("照明[","");
							temp1 = temp1.replace("]","");
							$gameMap.drill_LIl_addSimplePerspect_characterId( this._eventId, Number(temp1)-1 );
						}
					}
				}
			};
		};
	}, this);};
};

//=============================================================================
// ** 存储数据（透视镜物体容器）
//=============================================================================
//==============================
// ** 存储数据 - 初始化
//==============================
var _drill_LIl_initSysData2 = Game_System.prototype.drill_LIl_initSysData;
Game_System.prototype.drill_LIl_initSysData = function() {
    _drill_LIl_initSysData2.call(this);
	this._drill_LIl_container = new Drill_CODM_PerspectiveMarkerContainer();	//（创建容器）
};
//==============================
// ** 存储数据 - 存档检查
//==============================
var _drill_LIl_checkSysData2 = Game_System.prototype.drill_LIl_checkSysData;
Game_System.prototype.drill_LIl_checkSysData = function() {
    _drill_LIl_checkSysData2.call(this);
	if( this._drill_LIl_container == undefined ){
		this._drill_LIl_container = new Drill_CODM_PerspectiveMarkerContainer();	//（创建容器）
	}
};
//==============================
// * 位置 - 移动设置
//==============================
Game_System.prototype.drill_LIl_moveTo = function( marker_id, m_data ){
	var marker = this._drill_LIl_container.drill_CODM_getSeniorMarkerById( marker_id );
	//if( marker == undefined ){	//（如果还没来得及创建，则放入变化容器中）
	//	this._drill_LIl_command_moveBuffer[ marker_id ] = m_data;
	//	return;
	//}
	
	var org_x = marker.drill_getBallisticsX();
	var org_y = marker.drill_getBallisticsY();
	marker.drill_resetBallisticsMovingTime();		//（重设时间）
	
	//   移动（movement）
	m_data['movementNum'] = 1;									//对象数量
	m_data['movementTime'] = m_data["time"];					//时长
	m_data['movementMode'] = "两点式";							//移动模式
	//   两点式（twoPoint）
	m_data['twoPointType'] = m_data["type"];					//两点式 - 类型（匀速移动/弹性移动/…）
	m_data['twoPointDifferenceX'] = m_data["x"] - org_x;		//两点式 - 距离差值x
	m_data['twoPointDifferenceY'] = m_data["y"] - org_y;		//两点式 - 距离差值y
	
	// > 弹道（坐标）
	$gameTemp.drill_COBa_setBallisticsMove( m_data );							//弹道核心 - 坐标初始化
	$gameTemp.drill_COBa_preBallisticsMove( marker, 0 , org_x, org_y );			//弹道核心 - 推演
	
};
//==============================
// * 位置 - 缩放X设置
//==============================
Game_System.prototype.drill_LIl_scaleXTo = function( marker_id, o_data ){
	var marker = this._drill_LIl_container.drill_CODM_getSeniorMarkerById( marker_id );
	
	var org_sx = marker.drill_getBallisticsScaleX();
	marker.drill_resetBallisticsScaleXTime();		//（重设时间）
	
	//   缩放X（scaleX）
	o_data['scaleXNum'] = 1;									//对象数量
	o_data['scaleXTime'] = o_data["time"];						//时长
	o_data['scaleXMode'] = "目标值模式";						//移动模式
	//   目标值模式（target）
	o_data['targetType'] = o_data["type"];						//目标值模式 - 类型（匀速变化/弹性变化/…）
	o_data['targetDifference'] = o_data["scaleX"] - org_sx;		//目标值模式 - 距离差值
	
	// > 弹道（缩放X）
	$gameTemp.drill_COBa_setBallisticsScaleX( o_data );					//弹道核心 - 缩放X初始化
	$gameTemp.drill_COBa_preBallisticsScaleX( marker, 0 , org_sx );		//弹道核心 - 推演
	
};
//==============================
// * 位置 - 缩放Y设置
//==============================
Game_System.prototype.drill_LIl_scaleYTo = function( marker_id, o_data ){
	var marker = this._drill_LIl_container.drill_CODM_getSeniorMarkerById( marker_id );
	
	var org_sy = marker.drill_getBallisticsScaleY();
	marker.drill_resetBallisticsScaleYTime();		//（重设时间）
	
	//   缩放Y（scaleY）
	o_data['scaleYNum'] = 1;									//对象数量
	o_data['scaleYTime'] = o_data["time"];						//时长
	o_data['scaleYMode'] = "目标值模式";						//移动模式
	//   目标值模式（target）
	o_data['targetType'] = o_data["type"];						//目标值模式 - 类型（匀速变化/弹性变化/…）
	o_data['targetDifference'] = o_data["scaleY"] - org_sy;		//目标值模式 - 距离差值
	
	// > 弹道（缩放Y）
	$gameTemp.drill_COBa_setBallisticsScaleY( o_data );					//弹道核心 - 缩放Y初始化
	$gameTemp.drill_COBa_preBallisticsScaleY( marker, 0 , org_sy );		//弹道核心 - 推演
	
};
//==============================
// * 位置 - 透明度设置
//==============================
Game_System.prototype.drill_LIl_opacityTo = function( marker_id, o_data ){
	var marker = this._drill_LIl_container.drill_CODM_getSeniorMarkerById( marker_id );
	
	var org_opacity = marker.drill_getBallisticsOpacity();
	marker.drill_resetBallisticsOpacityTime();		//（重设时间）
	
	//   透明度（opacity）
	o_data['opacityNum'] = 1;									//对象数量
	o_data['opacityTime'] = o_data["time"];						//时长
	o_data['opacityMode'] = "目标值模式";						//移动模式
	//   目标值模式（target）
	o_data['targetType'] = o_data["type"];								//目标值模式 - 类型（匀速变化/弹性变化/…）
	o_data['targetDifference'] = o_data["opacity"] - org_opacity;		//目标值模式 - 距离差值
	
	// > 弹道（透明度）
	$gameTemp.drill_COBa_setBallisticsOpacity( o_data );					//弹道核心 - 透明度初始化
	$gameTemp.drill_COBa_preBallisticsOpacity( marker, 0 , org_opacity );	//弹道核心 - 推演
	
};
//==============================
// * 位置 - 旋转设置
//==============================
Game_System.prototype.drill_LIl_rotateTo = function( marker_id, o_data ){
	var marker = this._drill_LIl_container.drill_CODM_getSeniorMarkerById( marker_id );
	
	var org_rotate = marker.drill_getBallisticsRotate();
	marker.drill_resetBallisticsRotateTime();		//（重设时间）
	
	//   旋转（rotate，单位角度）
	o_data['rotateNum'] = 1;									//对象数量
	o_data['rotateTime'] = o_data["time"];						//时长
	o_data['rotateMode'] = "目标值模式";						//移动模式
	//   目标值模式（target）
	o_data['targetType'] = o_data["type"];							//目标值模式 - 类型（匀速变化/弹性变化/…）
	o_data['targetDifference'] = o_data["rotate"] - org_rotate;		//目标值模式 - 距离差值
	
	// > 弹道（旋转）
	$gameTemp.drill_COBa_setBallisticsRotate( o_data );					//弹道核心 - 旋转初始化
	$gameTemp.drill_COBa_preBallisticsRotate( marker, 0 , org_rotate );	//弹道核心 - 推演
	
};


//=============================================================================
// ** 物体照明容器
//			
//			主功能：	> 专门控制该插件 动态遮罩板 的 物体照明 的容器。
//			子功能：	
//						->物体照明（简单透视镜）
//							> 绑定事件
//							> 绑定鼠标
//							> 绑定图片
//						->高级照明（高级透视镜）
//
//			说明：	直接使用父类的容器【Drill_CODM_PerspectiveMarkerContainer】，添加数据。
//=============================================================================
//==============================
// * 物体容器 - 地图初始化
//==============================
var _drill_LIl_map_setupEvents = Game_Map.prototype.setupEvents;
Game_Map.prototype.setupEvents = function(){
	$gameSystem.drill_LIl_checkSysData();
	$gameSystem._drill_LIl_container.drill_CODM_clearSimple();	//（清理物体照明）
	this._drill_LIl_commandLock = {};							//（指令过度重复检测锁）
	_drill_LIl_map_setupEvents.call( this );
}
//==============================
// * 物体容器 - 地图帧刷新
//
//			说明：	要继承updateMain，而不是update，否则会有1像素抖动问题。
//==============================
var _drill_LIl_scene_updateMain2 = Scene_Map.prototype.updateMain;
Scene_Map.prototype.updateMain = function(){
	_drill_LIl_scene_updateMain2.call( this );
	if( this.isActive() ){
		$gameSystem.drill_LIl_checkSysData();
		$gameSystem._drill_LIl_container.update();		//（容器帧刷新）
	}
	if( Graphics.frameCount % 180 == 0 ){
		$gameMap._drill_LIl_commandLock = {};				//（指令过度重复检测锁）
	}
};
//==============================
// * 物体容器 - 添加简单透视镜（事件/玩家）
//==============================
Game_Map.prototype.drill_LIl_addSimplePerspect_characterId = function( character_id, style_id ){	
	var data = DrillUp.g_LIl_light[ style_id ];				//（参数准备）
	if( data == undefined ){
		alert( DrillUp.drill_LIl_getPluginTip_StyleDataNotFind(style_id+1) );
		return;
	}
	
	// > 指令过度重复检测
	if( this._drill_LIl_commandLock[character_id] == undefined ){
		this._drill_LIl_commandLock[character_id] = 0;
	}
	this._drill_LIl_commandLock[character_id] += 1;
	if( this._drill_LIl_commandLock[character_id] >= 50 ){
		this._drill_LIl_commandLock[character_id] = -1000;
		var ch_str = "事件["+character_id+"]";
		if( character_id == -2 ){ ch_str = "玩家"; }
		alert( DrillUp.drill_LIl_getPluginTip_Overdrive( ch_str ) );
		return;
	}
	
	var marker = new Drill_CODM_PerspectiveMarker( data );			//（创建透视镜）
	marker.drill_setBindingCharacterId( character_id );				//（绑定对象）
	marker.drill_setSimple( true );									//（简单透视镜标记）
	$gameSystem._drill_LIl_container.drill_CODM_addOne( marker );	//（添加到容器）
}
//==============================
// * 物体容器 - 去除简单透视镜（事件/玩家）
//==============================
Game_Map.prototype.drill_LIl_removeSimplePerspect_characterId = function( character_id ){
	var marker = $gameSystem._drill_LIl_container.drill_CODM_getSimpleMarkerByCharacterId( character_id );
	if( marker == undefined ){ return; }
	marker.drill_destroy();											//（清除透视镜）
}
//==============================
// * 物体容器 - 修改图片层级（事件/玩家）
//==============================
Game_Map.prototype.drill_LIl_changeSimplePerspectZIndex_characterId = function( character_id, zindex ){
	var marker = $gameSystem._drill_LIl_container.drill_CODM_getSimpleMarkerByCharacterId( character_id );
	if( marker == undefined ){ return; }
	marker.zIndex = zindex;
	$gameTemp._drill_LIl_needSort = true;
}
//==============================
// * 物体容器 - 添加简单透视镜（鼠标）
//==============================
Game_Map.prototype.drill_LIl_addSimplePerspect_mouse = function( style_id ){
	var data = DrillUp.g_LIl_light[ style_id ];				//（参数准备）
	if( data == undefined ){
		alert( DrillUp.drill_LIl_getPluginTip_StyleDataNotFind(style_id+1) );
		return;
	}
	var marker = new Drill_CODM_PerspectiveMarker( data );			//（创建透视镜）
	marker.drill_setBindingMouse( true );							//（绑定对象）
	marker.drill_setSimple( true );									//（简单透视镜标记）
	$gameSystem._drill_LIl_container.drill_CODM_addOne( marker );	//（添加到容器）
}
//==============================
// * 物体容器 - 去除简单透视镜（鼠标）
//==============================
Game_Map.prototype.drill_LIl_removeSimplePerspect_mouse = function(){
	var marker = $gameSystem._drill_LIl_container.drill_CODM_getSimpleMarkerByMouse();
	if( marker == undefined ){ return; }
	marker.drill_destroy();											//（清除透视镜）
}
//==============================
// * 物体容器 - 添加简单透视镜（图片）
//==============================
Game_Map.prototype.drill_LIl_addSimplePerspect_picId = function( pic_id, style_id ){
	var data = DrillUp.g_LIl_light[ style_id ];				//（参数准备）
	if( data == undefined ){
		alert( DrillUp.drill_LIl_getPluginTip_StyleDataNotFind(style_id+1) );
		return;
	}
	var marker = new Drill_CODM_PerspectiveMarker( data );			//（创建透视镜）
	marker.drill_setBindingPictureId( pic_id );						//（绑定对象）
	marker.drill_setSimple( true );									//（简单透视镜标记）
	$gameSystem._drill_LIl_container.drill_CODM_addOne( marker );	//（添加到容器）
}
//==============================
// * 物体容器 - 去除简单透视镜（图片）
//==============================
Game_Map.prototype.drill_LIl_removeSimplePerspect_picId = function( pic_id ){
	var marker = $gameSystem._drill_LIl_container.drill_CODM_getSimpleMarkerByPictureId( pic_id );
	if( marker == undefined ){ return; }
	marker.drill_destroy();											//（清除透视镜）
}
//==============================
// * 物体容器 - 添加高级透视镜（默认）
//==============================
Game_Map.prototype.drill_LIl_addSeniorPerspect = function( marker_id, style_id ){
	var data = DrillUp.g_LIl_light[ style_id ];				//（参数准备）
	if( data == undefined ){
		alert( DrillUp.drill_LIl_getPluginTip_StyleDataNotFind(style_id+1) );
		return;
	}
	var marker = new Drill_CODM_PerspectiveMarker( data );			//（创建透视镜）
	marker.drill_setId( marker_id );								//（设置id）
	$gameSystem._drill_LIl_container.drill_CODM_addOne( marker );	//（添加到容器）
	return marker;
}
//==============================
// * 物体容器 - 去除高级透视镜
//			
//			说明：	直接找到对象设置 drill_destroy 即可。
//==============================
Game_Map.prototype.drill_LIl_removeSeniorPerspect = function( marker_id ){
	var marker = $gameSystem._drill_LIl_container.drill_CODM_getSeniorMarkerById( marker_id );
	if( marker == undefined ){ return; }
	marker.drill_destroy();											//（清除透视镜）
}



//=============================================================================
// ** 贴图容器
//
//			说明：	根据 透视镜物体容器，添加/删除相应的贴图。
//=============================================================================
//==============================
// * 贴图容器 - 初始化
//==============================
var _drill_LIl_temp_initialize = Game_Temp.prototype.initialize;
Game_Temp.prototype.initialize = function() {	
	_drill_LIl_temp_initialize.call(this);
	this._drill_LIl_sprites = [];				//缓冲池 - 鼠标贴图
	this._drill_LIl_needSort = true;			//排序标记
	this._drill_LIl_opened = false;				//容器开关
};
//==============================
// * 贴图容器 - 切换地图时
//==============================
var _drill_LIl_gmap_setup = Game_Map.prototype.setup;
Game_Map.prototype.setup = function( mapId ){
	$gameTemp._drill_LIl_sprites = [];	
	$gameTemp._drill_LIl_opened = false;		//容器开关
	_drill_LIl_gmap_setup.call( this,mapId );
}
//==============================
// * 贴图容器 - 切换贴图时（菜单界面刷新）
//==============================
var _drill_LIl_smap_createCharacters = Spriteset_Map.prototype.createCharacters;
Spriteset_Map.prototype.createCharacters = function() {
	$gameTemp._drill_LIl_sprites = [];	
	_drill_LIl_smap_createCharacters.call(this);
}
//==============================
// * 贴图容器 - 获取透视镜贴图（根据透视镜物体）
//==============================
Game_Temp.prototype.drill_LIl_getPerspectiveSpriteByMarker = function( marker ){
	for(var i=0; i < this._drill_LIl_sprites.length; i++ ){
		var temp_sprite = this._drill_LIl_sprites[i];
		if( temp_sprite._drill_marker == marker ){
			return temp_sprite;
		}
	}
	return null;
}
//==============================
// * 贴图容器 - 帧刷新 创建/删除 贴图
//==============================
Scene_Map.prototype.drill_LIl_updateLightCheck = function() {
	
	// > 删除贴图
	for(var i = $gameTemp._drill_LIl_sprites.length-1; i >= 0; i--){
		var temp_sprite = $gameTemp._drill_LIl_sprites[i];
		if( temp_sprite.drill_isDestroyed() ){
			this._drill_LIl_darkSprite.drill_LIl_removeMaskChild( temp_sprite );		//（调用 动态遮罩板 接口，删除贴图）
			$gameTemp._drill_LIl_sprites.splice( i, 1 );
		}
	}
	
	// > 创建贴图
	var tank = $gameSystem._drill_LIl_container.drill_CODM_getTank();
	if( tank.length > $gameTemp._drill_LIl_sprites.length ){
		
		for(var i=0; i < tank.length; i++ ){		//（从物体列表中，依次找贴图对应的物体）
			var temp_marker = tank[i];
			var temp_sprite = $gameTemp.drill_LIl_getPerspectiveSpriteByMarker( temp_marker );
			if( temp_sprite != undefined ){ continue; }
			
			var new_sprite = new Drill_CODM_PerspectiveSprite( temp_marker );
			this._drill_LIl_darkSprite.drill_LIl_addMaskChild( new_sprite );			//（调用 动态遮罩板 接口，添加贴图）
			$gameTemp._drill_LIl_sprites.push( new_sprite );
			if( tank.length == $gameTemp._drill_LIl_sprites.length ){
				break;
			}
		}
		$gameTemp._drill_LIl_needSort = true;
	}
	
}


//=============================================================================
// * <<<<基于插件检测<<<<
//=============================================================================
}else{
		Imported.Drill_LayerIllumination = false;
		var pluginTip = DrillUp.drill_LIl_getPluginTip_NoBasePlugin();
		alert( pluginTip );
}
