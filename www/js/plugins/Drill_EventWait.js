//=============================================================================
// Drill_EventWait.js
//=============================================================================

/*:
 * @plugindesc [v1.0]        物体 - 等待指令
 * <AUTHOR>
 * 
 * 
 * @help
 * =============================================================================
 * +++ Drill_EventWait +++
 * 作者：Drill_up
 * 如果你有兴趣，也可以来看看更多我写的drill插件哦ヽ(*。>Д<)o゜
 * https://rpg.blue/thread-409713-1-1.html
 * =============================================================================
 * 你可以通过插件指令自定义等待时间。
 * 
 * -----------------------------------------------------------------------------
 * ----插件扩展
 * 该插件可以单独使用。
 * 
 * -----------------------------------------------------------------------------
 * ----设定注意事项
 * 1.插件的作用域：地图界面。
 *   作用于事件指令。
 * 细节：
 *   (1.默认等待指令最大为999帧，并且不支持变量。
 *      此插件用于改善此问题。
 * 
 * -----------------------------------------------------------------------------
 * ----激活条件
 * 你可以通过插件指令修改当前的声音：
 * 
 * 插件指令：>等待指令 : 执行等待 : 时间[120]
 * 插件指令：>等待指令 : 执行等待 : 时间变量[21]
 * 
 * 1.等待的时间单位为帧，1秒60帧。
 * 2.等待指令为一次性设置，比如设置时变量[21]的值为120，设置后则等待120帧。
 *   变量值的修改不会影响当前等待的时长。
 * 
 * -----------------------------------------------------------------------------
 * ----插件性能
 * 测试仪器：   4G 内存，Intel Core i5-2520M CPU 2.5GHz 处理器
 *              Intel(R) HD Graphics 3000 集显 的垃圾笔记本
 *              (笔记本的3dmark综合分：571，鲁大师综合分：48456)
 * 总时段：     20000.00ms左右
 * 对照表：     0.00ms  - 40.00ms （几乎无消耗）
 *              40.00ms - 80.00ms （低消耗）
 *              80.00ms - 120.00ms（中消耗）
 *              120.00ms以上      （高消耗）
 * 工作类型：   单次执行
 * 时间复杂度： o(n)
 * 测试方法：   在地图界面中进行测试。
 * 测试结果：   地图界面中，平均消耗为：【5ms以下】
 * 
 * 1.插件只在自己作用域下工作消耗性能，在其它作用域下是不工作的。
 *   测试结果并不是精确值，范围在给定值的10ms范围内波动。
 *   更多性能介绍，去看看 "0.性能测试报告 > 关于插件性能.docx"。
 * 2.插件只单次执行等待指令。
 *
 * -----------------------------------------------------------------------------
 * ----更新日志
 * [v1.0]
 * 完成插件ヽ(*。>Д<)o゜
 * 
 */
 
//<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
//		插件简称		EWa（Event_Wait）
//		临时全局变量	无
//		临时局部变量	无
//		存储数据变量	无
//		全局存储变量	无
//		覆盖重写方法	无
//
//<<<<<<<<性能记录<<<<<<<<
//
//		★工作类型		单次执行
//		★时间复杂度		o(n)
//		★性能测试因素	
//		★性能测试消耗	
//		★最坏情况		暂无
//		★备注			暂无
//		
//		★优化记录		暂无
//
//<<<<<<<<插件记录<<<<<<<<
//
//		★功能结构树：
//			->☆提示信息
//			->☆变量获取
//			->☆插件指令
//			
//		★家谱：
//			无
//		
//		★插件私有类：
//			无
//		
//		★必要注意事项：
//			暂无
//
//		★其它说明细节：
//			暂无
//			
//		★存在的问题：
//			暂无
//		

//=============================================================================
// ** ☆提示信息
//=============================================================================
	//==============================
	// * 提示信息 - 参数
	//==============================
	var DrillUp = DrillUp || {}; 
	DrillUp.g_EWa_PluginTip_curName = "Drill_EventWait.js 物体-等待指令";
	DrillUp.g_EWa_PluginTip_baseList = [];
	
	
//=============================================================================
// ** ☆变量获取
//=============================================================================
　　var Imported = Imported || {};
　　Imported.Drill_EventWait = true;
　　var DrillUp = DrillUp || {}; 
	DrillUp.parameters = PluginManager.parameters('Drill_EventWait');
	
	
//=============================================================================
// ** ☆插件指令
//=============================================================================
var _drill_EWa_pluginCommand = Game_Interpreter.prototype.pluginCommand;
Game_Interpreter.prototype.pluginCommand = function(command, args) {
	_drill_EWa_pluginCommand.call(this, command, args);
	if( command === ">等待指令" ){
		
		if(args.length == 4){
			var type = String(args[1]);
			var temp1 = String(args[3]);
			if( type == "执行等待" ){
				if( temp1.indexOf("时间[") != -1 ){
					temp1 = temp1.replace("时间[","");
					temp1 = temp1.replace("]","");
					this.wait( Number(temp1) );
				}
				if( temp1.indexOf("时间变量[") != -1 ){
					temp1 = temp1.replace("时间变量[","");
					temp1 = temp1.replace("]","");
					this.wait( $gameVariables.value( Number(temp1) ) );
				}
			}
		}
	}
};


