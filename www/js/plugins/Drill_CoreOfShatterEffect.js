//=============================================================================
// Drill_CoreOfShatterEffect.js
//=============================================================================

/*:
 * @plugindesc [v1.7]        系统 - 方块粉碎核心
 * <AUTHOR>
 * 
 * @Drill_LE_param "方块粉碎-%d"
 * @Drill_LE_parentKey "---方块粉碎组%d至%d---"
 * @Drill_LE_var "DrillUp.g_COSE_style_list_length"
 * 
 * 
 * @help  
 * =============================================================================
 * +++ Drill_CoreOfShatterEffect +++
 * 作者：Drill_up
 * 如果你有兴趣，也可以来看看更多我写的drill插件哦ヽ(*。>Д<)o゜
 * https://rpg.blue/thread-409713-1-1.html
 * =============================================================================
 * 该核心可将贴图按网格方块的形式分割成大量碎片，从而播放粉碎散开动画。
 * ★★尽量放在最靠上的位置★★
 * 
 * -----------------------------------------------------------------------------
 * ----插件扩展
 * 该插件为基础核心，单独使用没有效果。
 * 插件可以作用于各个粉碎效果子插件，但是要基于核心才能运行。
 * 基于：
 *   - Drill_CoreOfBallistics       系统-弹道核心★★v2.1及以上★★
 * 作用于：
 *   - Drill_BattleShatterEffect    战斗-方块粉碎效果
 *   - Drill_LayerShatterEffect     地图-方块粉碎效果
 *   - Drill_EventShatterEffect     行走图-方块粉碎效果
 *   - Drill_PictureShatterEffect   图片-方块粉碎效果
 *   - Drill_DialogShatterEffect    对话框-方块粉碎效果
 * 
 * -----------------------------------------------------------------------------
 * ----设定注意事项
 * 1.插件的作用域：地图界面、战斗界面。
 *   作用于贴图。
 * 2.想要更多了解方块粉碎，去看看 "1.系统 > 大家族-方块粉碎.docx"。
 * 弹道：
 *   (1.碎片的弹道支持情况如下：
 *        极坐标模式    √
 *        直角坐标模式  √
 *        轨道锚点模式  x  (不适合多碎片)
 *        两点式        x  (不适合多碎片)
 *   (2.碎片的发射、扩散轨迹完全可以通过弹道设置进行设计。
 *      你还可以设置使用插件指令控制反向弹道。
 *      具体配置方式可以看看 "1.系统 > 关于弹道.docx"。
 * 碎片：
 *   (1.碎片的数量 = 切割矩阵列数 x 切割矩阵行数。
 *      数量太多可能会轻微影响性能。
 *   (2.原理为：将指定的图片根据行数列数切割成n块碎片。
 *      碎片编号为0至n-1，依次赋予弹道。
 *      如果弹道的随机值差异不大，则碎片散开的差异也不会很大。
 * 
 * -----------------------------------------------------------------------------
 * ----插件性能
 * 测试仪器：   4G 内存，Intel Core i5-2520M CPU 2.5GHz 处理器
 *              Intel(R) HD Graphics 3000 集显 的垃圾笔记本
 *              (笔记本的3dmark综合分：571，鲁大师综合分：48456)
 * 总时段：     20000.00ms左右
 * 对照表：     0.00ms  - 40.00ms （几乎无消耗）
 *              40.00ms - 80.00ms （低消耗）
 *              80.00ms - 120.00ms（中消耗）
 *              120.00ms以上      （高消耗）
 * 工作类型：   持续执行
 * 时间复杂度： o(n^2)*o(贴图处理) 每帧
 * 测试方法：   根据子插件运行，看看在各界面的效果。
 * 测试结果：   地图界面中，平均消耗为：【63.34ms】
 *              战斗界面中，平均消耗为：【45.39ms】
 *
 * 1.插件只在自己作用域下工作消耗性能，在其它作用域下是不工作的。
 *   测试结果并不是精确值，范围在给定值的 20ms 范围内波动。
 *   更多性能介绍，去看看 "0.性能测试报告 > 关于插件性能.docx"。
 * 2.方块粉碎是性能消耗大户，因为粉碎后图片实际上被分成了m*n块新贴图碎片。
 *   性能测试中并不能准确找到该插件的消耗量，只能通过update总消耗量相减来
 *   进行估算。所以误差会比较大。
 * 
 * -----------------------------------------------------------------------------
 * ----更新日志
 * [v1.0]
 * 完成插件ヽ(*。>Д<)o゜
 * [v1.1]
 * 修复了一些细节bug。
 * [v1.2]
 * 添加了碎片 比例扩散 的功能。
 * [v1.3]
 * 优化了内部接口的结构。
 * [v1.4]
 * 修复了弹道的多行 自定义公式 中无法执行且出错的bug。
 * [v1.5]
 * 优化了内部结构。
 * [v1.6]
 * 修复了空碎片数据的bug。
 * [v1.7]
 * 优化了弹道的支持。
 * 
 * 
 * @param ---方块粉碎组 1至20---
 * @desc 
 * 
 * @param 方块粉碎-1
 * @parent ---方块粉碎组 1至20---
 * @type struct<DrillCOSEShatter>
 * @desc GIF的详细配置信息。
 * @default {"标签":"==小图-规则圆扩散==","切割矩阵行数":"7","切割矩阵列数":"6","碎片速度是否分比例":"false","最小速度比例":"0.3","碎片弹道":"{\"标签\":\"==扩散弹道==\",\"移动时长\":\"80\",\"移动模式\":\"极坐标模式\",\"---极坐标模式---\":\"\",\"速度类型\":\"初速度+波动量\",\"初速度\":\"3.0\",\"速度随机波动量\":\"0.5\",\"加速度\":\"0.0\",\"最大速度\":\"99.0\",\"最小速度\":\"0.0\",\"路程计算公式\":\"\\\"return 0.0\\\"\",\"方向类型\":\"四周扩散(线性)\",\"固定方向\":\"90.0\",\"扇形朝向\":\"45.0\",\"扇形角度\":\"90.0\",\"方向计算公式\":\"\\\"return 0.0\\\"\",\"---直角坐标模式---\":\"\",\"直角坐标整体旋转\":\"0.0\",\"X轴速度类型\":\"只初速度\",\"X轴初速度\":\"1.0\",\"X轴速度随机波动量\":\"2.0\",\"X轴加速度\":\"0.0\",\"X轴最大速度\":\"99.0\",\"X轴最小速度\":\"0.0\",\"X轴路程计算公式\":\"\\\"return 0.0\\\"\",\"Y轴速度类型\":\"只初速度\",\"Y轴初速度\":\"1.0\",\"Y轴速度随机波动量\":\"2.0\",\"Y轴加速度\":\"0.0\",\"Y轴最大速度\":\"99.0\",\"Y轴最小速度\":\"0.0\",\"Y轴路程计算公式\":\"\\\"return 0.0\\\"\"}"}
 *
 * @param 方块粉碎-2
 * @parent ---方块粉碎组 1至20---
 * @type struct<DrillCOSEShatter>
 * @desc GIF的详细配置信息。
 * @default {"标签":"==小图-规则半圆扩散==","切割矩阵行数":"7","切割矩阵列数":"6","碎片速度是否分比例":"false","最小速度比例":"0.2","碎片弹道":"{\"标签\":\"==扩散弹道==\",\"移动时长\":\"80\",\"移动模式\":\"极坐标模式\",\"---极坐标模式---\":\"\",\"速度类型\":\"初速度+波动量\",\"初速度\":\"2.5\",\"速度随机波动量\":\"0.5\",\"加速度\":\"0.0\",\"最大速度\":\"99.0\",\"最小速度\":\"0.0\",\"路程计算公式\":\"\\\"return 0.0\\\"\",\"方向类型\":\"扇形范围方向(线性)\",\"固定方向\":\"90.0\",\"扇形朝向\":\"-90.0\",\"扇形角度\":\"180.0\",\"方向计算公式\":\"\\\"return 0.0\\\"\",\"---直角坐标模式---\":\"\",\"直角坐标整体旋转\":\"0.0\",\"X轴速度类型\":\"只初速度\",\"X轴初速度\":\"1.0\",\"X轴速度随机波动量\":\"2.0\",\"X轴加速度\":\"0.0\",\"X轴最大速度\":\"99.0\",\"X轴最小速度\":\"0.0\",\"X轴路程计算公式\":\"\\\"return 0.0\\\"\",\"Y轴速度类型\":\"只初速度\",\"Y轴初速度\":\"1.0\",\"Y轴速度随机波动量\":\"2.0\",\"Y轴加速度\":\"0.0\",\"Y轴最大速度\":\"99.0\",\"Y轴最小速度\":\"0.0\",\"Y轴路程计算公式\":\"\\\"return 0.0\\\"\"}"}
 *
 * @param 方块粉碎-3
 * @parent ---方块粉碎组 1至20---
 * @type struct<DrillCOSEShatter>
 * @desc GIF的详细配置信息。
 * @default {"标签":"==小图-普通扩散==","切割矩阵行数":"7","切割矩阵列数":"6","碎片速度是否分比例":"false","最小速度比例":"0.3","碎片弹道":"{\"标签\":\"==扩散弹道==\",\"移动时长\":\"90\",\"移动模式\":\"极坐标模式\",\"---极坐标模式---\":\"\",\"速度类型\":\"初速度+波动量\",\"初速度\":\"1.8\",\"速度随机波动量\":\"3.0\",\"加速度\":\"0.0\",\"最大速度\":\"99.0\",\"最小速度\":\"0.0\",\"路程计算公式\":\"\\\"return 0.0\\\"\",\"方向类型\":\"四周扩散(随机)\",\"固定方向\":\"90.0\",\"扇形朝向\":\"45.0\",\"扇形角度\":\"90.0\",\"方向计算公式\":\"\\\"return 0.0\\\"\",\"---直角坐标模式---\":\"\",\"直角坐标整体旋转\":\"0.0\",\"X轴速度类型\":\"只初速度\",\"X轴初速度\":\"1.0\",\"X轴速度随机波动量\":\"2.0\",\"X轴加速度\":\"0.0\",\"X轴最大速度\":\"99.0\",\"X轴最小速度\":\"0.0\",\"X轴路程计算公式\":\"\\\"return 0.0\\\"\",\"Y轴速度类型\":\"只初速度\",\"Y轴初速度\":\"1.0\",\"Y轴速度随机波动量\":\"2.0\",\"Y轴加速度\":\"0.0\",\"Y轴最大速度\":\"99.0\",\"Y轴最小速度\":\"0.0\",\"Y轴路程计算公式\":\"\\\"return 0.0\\\"\"}"}
 *
 * @param 方块粉碎-4
 * @parent ---方块粉碎组 1至20---
 * @type struct<DrillCOSEShatter>
 * @desc GIF的详细配置信息。
 * @default {"标签":"==小图-普通半圆扩散==","切割矩阵行数":"7","切割矩阵列数":"6","碎片速度是否分比例":"false","最小速度比例":"0.3","碎片弹道":"{\"标签\":\"==扩散弹道==\",\"移动时长\":\"90\",\"移动模式\":\"极坐标模式\",\"---极坐标模式---\":\"\",\"速度类型\":\"初速度+波动量\",\"初速度\":\"1.8\",\"速度随机波动量\":\"3.0\",\"加速度\":\"0.0\",\"最大速度\":\"99.0\",\"最小速度\":\"0.0\",\"路程计算公式\":\"\\\"return 0.0\\\"\",\"方向类型\":\"扇形范围方向(随机)\",\"固定方向\":\"90.0\",\"扇形朝向\":\"-90.0\",\"扇形角度\":\"180.0\",\"方向计算公式\":\"\\\"return 0.0\\\"\",\"---直角坐标模式---\":\"\",\"直角坐标整体旋转\":\"0.0\",\"X轴速度类型\":\"只初速度\",\"X轴初速度\":\"1.0\",\"X轴速度随机波动量\":\"2.0\",\"X轴加速度\":\"0.0\",\"X轴最大速度\":\"99.0\",\"X轴最小速度\":\"0.0\",\"X轴路程计算公式\":\"\\\"return 0.0\\\"\",\"Y轴速度类型\":\"只初速度\",\"Y轴初速度\":\"1.0\",\"Y轴速度随机波动量\":\"2.0\",\"Y轴加速度\":\"0.0\",\"Y轴最大速度\":\"99.0\",\"Y轴最小速度\":\"0.0\",\"Y轴路程计算公式\":\"\\\"return 0.0\\\"\"}"}
 *
 * @param 方块粉碎-5
 * @parent ---方块粉碎组 1至20---
 * @type struct<DrillCOSEShatter>
 * @desc GIF的详细配置信息。
 * @default {"标签":"==小图-抖动扩散==","切割矩阵行数":"5","切割矩阵列数":"5","碎片速度是否分比例":"false","最小速度比例":"0.2","碎片弹道":"{\"标签\":\"==扩散弹道==\",\"移动时长\":\"80\",\"移动模式\":\"极坐标模式\",\"---极坐标模式---\":\"\",\"速度类型\":\"初速度+波动量\",\"初速度\":\"1.0\",\"速度随机波动量\":\"1.6\",\"加速度\":\"0.0\",\"最大速度\":\"99.0\",\"最小速度\":\"0.0\",\"路程计算公式\":\"\\\"return 0.0\\\"\",\"方向类型\":\"混乱bug扩散\",\"固定方向\":\"90.0\",\"扇形朝向\":\"45.0\",\"扇形角度\":\"90.0\",\"方向计算公式\":\"\\\"return 0.0\\\"\",\"---直角坐标模式---\":\"\",\"X轴速度类型\":\"只初速度\",\"X轴初速度\":\"1.0\",\"X轴速度随机波动量\":\"2.0\",\"X轴加速度\":\"0.0\",\"X轴最大速度\":\"99.0\",\"X轴最小速度\":\"0.0\",\"X轴路程计算公式\":\"\\\"return 0.0\\\"\",\"Y轴速度类型\":\"只初速度\",\"Y轴初速度\":\"1.0\",\"Y轴速度随机波动量\":\"2.0\",\"Y轴加速度\":\"0.0\",\"Y轴最大速度\":\"99.0\",\"Y轴最小速度\":\"0.0\",\"Y轴路程计算公式\":\"\\\"return 0.0\\\"\"}"}
 *
 * @param 方块粉碎-6
 * @parent ---方块粉碎组 1至20---
 * @type struct<DrillCOSEShatter>
 * @desc GIF的详细配置信息。
 * @default {"标签":"==小图-碎片抖动==","切割矩阵行数":"6","切割矩阵列数":"6","碎片速度是否分比例":"false","最小速度比例":"0.2","碎片弹道":"{\"标签\":\"==随机抖动==\",\"移动时长\":\"120\",\"移动模式\":\"极坐标模式\",\"---极坐标模式---\":\"\",\"速度类型\":\"路程计算公式\",\"初速度\":\"1.0\",\"速度随机波动量\":\"2.0\",\"加速度\":\"0.0\",\"最大速度\":\"99.0\",\"最小速度\":\"0.0\",\"路程计算公式\":\"\\\"return Math.random() * 4\\\"\",\"方向类型\":\"四周扩散(线性)\",\"固定方向\":\"90.0\",\"扇形朝向\":\"45.0\",\"扇形角度\":\"90.0\",\"方向计算公式\":\"\\\"return 0.0\\\"\",\"---直角坐标模式---\":\"\",\"直角坐标整体旋转\":\"0.0\",\"X轴速度类型\":\"只初速度\",\"X轴初速度\":\"1.0\",\"X轴速度随机波动量\":\"2.0\",\"X轴加速度\":\"0.0\",\"X轴最大速度\":\"99.0\",\"X轴最小速度\":\"0.0\",\"X轴路程计算公式\":\"\\\"return 0.0\\\"\",\"Y轴速度类型\":\"只初速度\",\"Y轴初速度\":\"1.0\",\"Y轴速度随机波动量\":\"2.0\",\"Y轴加速度\":\"0.0\",\"Y轴最大速度\":\"99.0\",\"Y轴最小速度\":\"0.0\",\"Y轴路程计算公式\":\"\\\"return 0.0\\\"\"}"}
 *
 * @param 方块粉碎-7
 * @parent ---方块粉碎组 1至20---
 * @type struct<DrillCOSEShatter>
 * @desc GIF的详细配置信息。
 * @default {"标签":"==小图-减速扩散-小==","切割矩阵行数":"7","切割矩阵列数":"6","碎片速度是否分比例":"false","最小速度比例":"0.2","碎片弹道":"{\"标签\":\"==扩散弹道==\",\"移动时长\":\"120\",\"移动模式\":\"极坐标模式\",\"---极坐标模式---\":\"\",\"速度类型\":\"初速度+波动量+加速度+最大最小\",\"初速度\":\"4.0\",\"速度随机波动量\":\"1.8\",\"加速度\":\"-0.08\",\"最大速度\":\"99.0\",\"最小速度\":\"0.0\",\"路程计算公式\":\"\\\"return 0.0\\\"\",\"方向类型\":\"四周扩散(随机)\",\"固定方向\":\"90.0\",\"扇形朝向\":\"45.0\",\"扇形角度\":\"90.0\",\"方向计算公式\":\"\\\"return 0.0\\\"\",\"---直角坐标模式---\":\"\",\"直角坐标整体旋转\":\"0.0\",\"X轴速度类型\":\"只初速度\",\"X轴初速度\":\"1.0\",\"X轴速度随机波动量\":\"2.0\",\"X轴加速度\":\"0.0\",\"X轴最大速度\":\"99.0\",\"X轴最小速度\":\"0.0\",\"X轴路程计算公式\":\"\\\"return 0.0\\\"\",\"Y轴速度类型\":\"只初速度\",\"Y轴初速度\":\"1.0\",\"Y轴速度随机波动量\":\"2.0\",\"Y轴加速度\":\"0.0\",\"Y轴最大速度\":\"99.0\",\"Y轴最小速度\":\"0.0\",\"Y轴路程计算公式\":\"\\\"return 0.0\\\"\"}"}
 *
 * @param 方块粉碎-8
 * @parent ---方块粉碎组 1至20---
 * @type struct<DrillCOSEShatter>
 * @desc GIF的详细配置信息。
 * @default {"标签":"==小图-减速扩散-大==","切割矩阵行数":"7","切割矩阵列数":"6","碎片速度是否分比例":"false","最小速度比例":"0.2","碎片弹道":"{\"标签\":\"==扩散弹道==\",\"移动时长\":\"120\",\"移动模式\":\"极坐标模式\",\"---极坐标模式---\":\"\",\"速度类型\":\"初速度+波动量+加速度+最大最小\",\"初速度\":\"8.5\",\"速度随机波动量\":\"3.8\",\"加速度\":\"-0.09\",\"最大速度\":\"99.0\",\"最小速度\":\"0.0\",\"路程计算公式\":\"\\\"return 0.0\\\"\",\"方向类型\":\"四周扩散(随机)\",\"固定方向\":\"90.0\",\"扇形朝向\":\"45.0\",\"扇形角度\":\"90.0\",\"方向计算公式\":\"\\\"return 0.0\\\"\",\"---直角坐标模式---\":\"\",\"直角坐标整体旋转\":\"0.0\",\"X轴速度类型\":\"只初速度\",\"X轴初速度\":\"1.0\",\"X轴速度随机波动量\":\"2.0\",\"X轴加速度\":\"0.0\",\"X轴最大速度\":\"99.0\",\"X轴最小速度\":\"0.0\",\"X轴路程计算公式\":\"\\\"return 0.0\\\"\",\"Y轴速度类型\":\"只初速度\",\"Y轴初速度\":\"1.0\",\"Y轴速度随机波动量\":\"2.0\",\"Y轴加速度\":\"0.0\",\"Y轴最大速度\":\"99.0\",\"Y轴最小速度\":\"0.0\",\"Y轴路程计算公式\":\"\\\"return 0.0\\\"\"}"}
 *
 * @param 方块粉碎-9
 * @parent ---方块粉碎组 1至20---
 * @type struct<DrillCOSEShatter>
 * @desc GIF的详细配置信息。
 * @default 
 *
 * @param 方块粉碎-10
 * @parent ---方块粉碎组 1至20---
 * @type struct<DrillCOSEShatter>
 * @desc GIF的详细配置信息。
 * @default {"标签":"==小图-抛物线掉落==","切割矩阵行数":"6","切割矩阵列数":"6","碎片速度是否分比例":"false","最小速度比例":"0.2","碎片弹道":"{\"标签\":\"==抛物线弹道==\",\"移动时长\":\"120\",\"移动模式\":\"直角坐标模式\",\"---极坐标模式---\":\"\",\"速度类型\":\"只初速度\",\"初速度\":\"1.0\",\"速度随机波动量\":\"2.0\",\"加速度\":\"0.0\",\"最大速度\":\"99.0\",\"最小速度\":\"0.0\",\"路程计算公式\":\"\\\"return 0.0\\\"\",\"方向类型\":\"四周扩散(线性)\",\"固定方向\":\"90.0\",\"扇形朝向\":\"45.0\",\"扇形角度\":\"90.0\",\"方向计算公式\":\"\\\"return 0.0\\\"\",\"---直角坐标模式---\":\"\",\"直角坐标整体旋转\":\"0.0\",\"X轴速度类型\":\"初速度+波动量\",\"X轴初速度\":\"0.0\",\"X轴速度随机波动量\":\"3.0\",\"X轴加速度\":\"0.0\",\"X轴最大速度\":\"99.0\",\"X轴最小速度\":\"0.0\",\"X轴路程计算公式\":\"\\\"return 0.0\\\"\",\"Y轴速度类型\":\"初速度+波动量+加速度\",\"Y轴初速度\":\"-5.0\",\"Y轴速度随机波动量\":\"2.0\",\"Y轴加速度\":\"0.18\",\"Y轴最大速度\":\"99.0\",\"Y轴最小速度\":\"0.0\",\"Y轴路程计算公式\":\"\\\"return 0.0\\\"\"}"}
 *
 * @param 方块粉碎-11
 * @parent ---方块粉碎组 1至20---
 * @type struct<DrillCOSEShatter>
 * @desc GIF的详细配置信息。
 * @default {"标签":"==小图-蛇线粉碎==","切割矩阵行数":"6","切割矩阵列数":"6","碎片速度是否分比例":"false","最小速度比例":"0.2","碎片弹道":"{\"标签\":\"==抛物线弹道==\",\"移动时长\":\"120\",\"移动模式\":\"直角坐标模式\",\"---极坐标模式---\":\"\",\"速度类型\":\"只初速度\",\"初速度\":\"1.0\",\"速度随机波动量\":\"2.0\",\"加速度\":\"0.0\",\"最大速度\":\"99.0\",\"最小速度\":\"0.0\",\"路程计算公式\":\"\\\"return 0.0\\\"\",\"方向类型\":\"四周扩散(线性)\",\"固定方向\":\"90.0\",\"扇形朝向\":\"45.0\",\"扇形角度\":\"90.0\",\"方向计算公式\":\"\\\"return 0.0\\\"\",\"---直角坐标模式---\":\"\",\"直角坐标整体旋转\":\"0.0\",\"X轴速度类型\":\"路程计算公式\",\"X轴初速度\":\"4.5\",\"X轴速度随机波动量\":\"3.0\",\"X轴加速度\":\"0.0\",\"X轴最大速度\":\"99.0\",\"X轴最小速度\":\"0.0\",\"X轴路程计算公式\":\"\\\"var result = p.ran * p.time;\\\\nresult += 35 * Math.sin( 3 * p.v0 * p.time /180*Math.PI );\\\\nreturn result;\\\"\",\"Y轴速度类型\":\"初速度+波动量+加速度\",\"Y轴初速度\":\"1.4\",\"Y轴速度随机波动量\":\"0.3\",\"Y轴加速度\":\"0.02\",\"Y轴最大速度\":\"99.0\",\"Y轴最小速度\":\"0.0\",\"Y轴路程计算公式\":\"\\\"return 0.0\\\"\"}"}
 *
 * @param 方块粉碎-12
 * @parent ---方块粉碎组 1至20---
 * @type struct<DrillCOSEShatter>
 * @desc GIF的详细配置信息。
 * @default 
 *
 * @param 方块粉碎-13
 * @parent ---方块粉碎组 1至20---
 * @type struct<DrillCOSEShatter>
 * @desc GIF的详细配置信息。
 * @default {"标签":"==小图-比例扩散==","切割矩阵行数":"9","切割矩阵列数":"8","碎片速度是否分比例":"true","最小速度比例":"0.15","碎片弹道":"{\"标签\":\"==扩散弹道==\",\"移动时长\":\"80\",\"移动模式\":\"极坐标模式\",\"---极坐标模式---\":\"\",\"速度类型\":\"初速度+波动量\",\"初速度\":\"5.0\",\"速度随机波动量\":\"0.5\",\"加速度\":\"0.0\",\"最大速度\":\"99.0\",\"最小速度\":\"0.0\",\"路程计算公式\":\"\\\"return 0.0\\\"\",\"方向类型\":\"四周扩散(线性)\",\"固定方向\":\"90.0\",\"扇形朝向\":\"45.0\",\"扇形角度\":\"90.0\",\"方向计算公式\":\"\\\"return 0.0\\\"\",\"---直角坐标模式---\":\"\",\"直角坐标整体旋转\":\"0.0\",\"X轴速度类型\":\"只初速度\",\"X轴初速度\":\"1.0\",\"X轴速度随机波动量\":\"2.0\",\"X轴加速度\":\"0.0\",\"X轴最大速度\":\"99.0\",\"X轴最小速度\":\"0.0\",\"X轴路程计算公式\":\"\\\"return 0.0\\\"\",\"Y轴速度类型\":\"只初速度\",\"Y轴初速度\":\"1.0\",\"Y轴速度随机波动量\":\"2.0\",\"Y轴加速度\":\"0.0\",\"Y轴最大速度\":\"99.0\",\"Y轴最小速度\":\"0.0\",\"Y轴路程计算公式\":\"\\\"return 0.0\\\"\"}"}
 *
 * @param 方块粉碎-14
 * @parent ---方块粉碎组 1至20---
 * @type struct<DrillCOSEShatter>
 * @desc GIF的详细配置信息。
 * @default {"标签":"==小图-比例半圆扩散==","切割矩阵行数":"9","切割矩阵列数":"8","碎片速度是否分比例":"true","最小速度比例":"0.15","碎片弹道":"{\"标签\":\"==扩散弹道==\",\"移动时长\":\"80\",\"移动模式\":\"极坐标模式\",\"---极坐标模式---\":\"\",\"速度类型\":\"初速度+波动量\",\"初速度\":\"4.5\",\"速度随机波动量\":\"0.2\",\"加速度\":\"0.0\",\"最大速度\":\"99.0\",\"最小速度\":\"0.0\",\"路程计算公式\":\"\\\"return 0.0\\\"\",\"方向类型\":\"扇形范围方向(线性)\",\"固定方向\":\"90.0\",\"扇形朝向\":\"-90.0\",\"扇形角度\":\"180.0\",\"方向计算公式\":\"\\\"return 0.0\\\"\",\"---直角坐标模式---\":\"\",\"直角坐标整体旋转\":\"0.0\",\"X轴速度类型\":\"只初速度\",\"X轴初速度\":\"1.0\",\"X轴速度随机波动量\":\"2.0\",\"X轴加速度\":\"0.0\",\"X轴最大速度\":\"99.0\",\"X轴最小速度\":\"0.0\",\"X轴路程计算公式\":\"\\\"return 0.0\\\"\",\"Y轴速度类型\":\"只初速度\",\"Y轴初速度\":\"1.0\",\"Y轴速度随机波动量\":\"2.0\",\"Y轴加速度\":\"0.0\",\"Y轴最大速度\":\"99.0\",\"Y轴最小速度\":\"0.0\",\"Y轴路程计算公式\":\"\\\"return 0.0\\\"\"}"}
 *
 * @param 方块粉碎-15
 * @parent ---方块粉碎组 1至20---
 * @type struct<DrillCOSEShatter>
 * @desc GIF的详细配置信息。
 * @default {"标签":"==小图-比例抛物线==","切割矩阵行数":"9","切割矩阵列数":"8","碎片速度是否分比例":"true","最小速度比例":"0.15","碎片弹道":"{\"标签\":\"==抛物线弹道==\",\"移动时长\":\"120\",\"移动模式\":\"直角坐标模式\",\"---极坐标模式---\":\"\",\"速度类型\":\"只初速度\",\"初速度\":\"1.0\",\"速度随机波动量\":\"2.0\",\"加速度\":\"0.0\",\"最大速度\":\"99.0\",\"最小速度\":\"0.0\",\"路程计算公式\":\"\\\"return 0.0\\\"\",\"方向类型\":\"四周扩散(线性)\",\"固定方向\":\"90.0\",\"扇形朝向\":\"45.0\",\"扇形角度\":\"90.0\",\"方向计算公式\":\"\\\"return 0.0\\\"\",\"---直角坐标模式---\":\"\",\"直角坐标整体旋转\":\"0.0\",\"X轴速度类型\":\"初速度+波动量\",\"X轴初速度\":\"0.0\",\"X轴速度随机波动量\":\"3.0\",\"X轴加速度\":\"0.0\",\"X轴最大速度\":\"99.0\",\"X轴最小速度\":\"0.0\",\"X轴路程计算公式\":\"\\\"return 0.0\\\"\",\"Y轴速度类型\":\"初速度+波动量+加速度\",\"Y轴初速度\":\"-6.0\",\"Y轴速度随机波动量\":\"2.0\",\"Y轴加速度\":\"0.28\",\"Y轴最大速度\":\"99.0\",\"Y轴最小速度\":\"0.0\",\"Y轴路程计算公式\":\"\\\"return 0.0\\\"\"}"}
 *
 * @param 方块粉碎-16
 * @parent ---方块粉碎组 1至20---
 * @type struct<DrillCOSEShatter>
 * @desc GIF的详细配置信息。
 * @default 
 *
 * @param 方块粉碎-17
 * @parent ---方块粉碎组 1至20---
 * @type struct<DrillCOSEShatter>
 * @desc GIF的详细配置信息。
 * @default 
 *
 * @param 方块粉碎-18
 * @parent ---方块粉碎组 1至20---
 * @type struct<DrillCOSEShatter>
 * @desc GIF的详细配置信息。
 * @default 
 *
 * @param 方块粉碎-19
 * @parent ---方块粉碎组 1至20---
 * @type struct<DrillCOSEShatter>
 * @desc GIF的详细配置信息。
 * @default 
 *
 * @param 方块粉碎-20
 * @parent ---方块粉碎组 1至20---
 * @type struct<DrillCOSEShatter>
 * @desc GIF的详细配置信息。
 * @default 
 * 
 * @param ---方块粉碎组21至40---
 * @desc 
 * 
 * @param 方块粉碎-21
 * @parent ---方块粉碎组21至40---
 * @type struct<DrillCOSEShatter>
 * @desc GIF的详细配置信息。
 * @default {"标签":"==大图-规则圆扩散==","切割矩阵行数":"16","切割矩阵列数":"24","碎片速度是否分比例":"false","最小速度比例":"0.2","碎片弹道":"{\"标签\":\"==扩散弹道==\",\"移动时长\":\"90\",\"移动模式\":\"极坐标模式\",\"---极坐标模式---\":\"\",\"速度类型\":\"初速度+波动量\",\"初速度\":\"15.0\",\"速度随机波动量\":\"1.5\",\"加速度\":\"0.0\",\"最大速度\":\"99.0\",\"最小速度\":\"0.0\",\"路程计算公式\":\"\\\"return 0.0\\\"\",\"方向类型\":\"四周扩散(线性)\",\"固定方向\":\"90.0\",\"扇形朝向\":\"45.0\",\"扇形角度\":\"90.0\",\"方向计算公式\":\"\\\"return 0.0\\\"\",\"---直角坐标模式---\":\"\",\"直角坐标整体旋转\":\"0.0\",\"X轴速度类型\":\"只初速度\",\"X轴初速度\":\"1.0\",\"X轴速度随机波动量\":\"2.0\",\"X轴加速度\":\"0.0\",\"X轴最大速度\":\"99.0\",\"X轴最小速度\":\"0.0\",\"X轴路程计算公式\":\"\\\"return 0.0\\\"\",\"Y轴速度类型\":\"只初速度\",\"Y轴初速度\":\"1.0\",\"Y轴速度随机波动量\":\"2.0\",\"Y轴加速度\":\"0.0\",\"Y轴最大速度\":\"99.0\",\"Y轴最小速度\":\"0.0\",\"Y轴路程计算公式\":\"\\\"return 0.0\\\"\"}"}
 *
 * @param 方块粉碎-22
 * @parent ---方块粉碎组21至40---
 * @type struct<DrillCOSEShatter>
 * @desc GIF的详细配置信息。
 * @default {"标签":"==大图-减速扩散==","切割矩阵行数":"16","切割矩阵列数":"24","碎片速度是否分比例":"false","最小速度比例":"0.2","碎片弹道":"{\"标签\":\"==扩散弹道==\",\"移动时长\":\"110\",\"移动模式\":\"极坐标模式\",\"---极坐标模式---\":\"\",\"速度类型\":\"初速度+波动量+加速度+最大最小\",\"初速度\":\"12.0\",\"速度随机波动量\":\"4.8\",\"加速度\":\"-0.12\",\"最大速度\":\"99.0\",\"最小速度\":\"0.0\",\"路程计算公式\":\"\\\"return 0.0\\\"\",\"方向类型\":\"四周扩散(随机)\",\"固定方向\":\"90.0\",\"扇形朝向\":\"45.0\",\"扇形角度\":\"90.0\",\"方向计算公式\":\"\\\"return 0.0\\\"\",\"---直角坐标模式---\":\"\",\"直角坐标整体旋转\":\"0.0\",\"X轴速度类型\":\"只初速度\",\"X轴初速度\":\"1.0\",\"X轴速度随机波动量\":\"2.0\",\"X轴加速度\":\"0.0\",\"X轴最大速度\":\"99.0\",\"X轴最小速度\":\"0.0\",\"X轴路程计算公式\":\"\\\"return 0.0\\\"\",\"Y轴速度类型\":\"只初速度\",\"Y轴初速度\":\"1.0\",\"Y轴速度随机波动量\":\"2.0\",\"Y轴加速度\":\"0.0\",\"Y轴最大速度\":\"99.0\",\"Y轴最小速度\":\"0.0\",\"Y轴路程计算公式\":\"\\\"return 0.0\\\"\"}"}
 *
 * @param 方块粉碎-23
 * @parent ---方块粉碎组21至40---
 * @type struct<DrillCOSEShatter>
 * @desc GIF的详细配置信息。
 * @default {"标签":"==大图-抛物线掉落==","切割矩阵行数":"16","切割矩阵列数":"24","碎片速度是否分比例":"false","最小速度比例":"0.2","碎片弹道":"{\"标签\":\"==抛物线弹道==\",\"移动时长\":\"150\",\"移动模式\":\"直角坐标模式\",\"---极坐标模式---\":\"\",\"速度类型\":\"只初速度\",\"初速度\":\"1.0\",\"速度随机波动量\":\"2.0\",\"加速度\":\"0.0\",\"最大速度\":\"99.0\",\"最小速度\":\"0.0\",\"路程计算公式\":\"\\\"return 0.0\\\"\",\"方向类型\":\"四周扩散(线性)\",\"固定方向\":\"90.0\",\"扇形朝向\":\"45.0\",\"扇形角度\":\"90.0\",\"方向计算公式\":\"\\\"return 0.0\\\"\",\"---直角坐标模式---\":\"\",\"直角坐标整体旋转\":\"0.0\",\"X轴速度类型\":\"初速度+波动量\",\"X轴初速度\":\"0.0\",\"X轴速度随机波动量\":\"12.0\",\"X轴加速度\":\"0.0\",\"X轴最大速度\":\"99.0\",\"X轴最小速度\":\"0.0\",\"X轴路程计算公式\":\"\\\"return 0.0\\\"\",\"Y轴速度类型\":\"初速度+波动量+加速度\",\"Y轴初速度\":\"-14.0\",\"Y轴速度随机波动量\":\"10.0\",\"Y轴加速度\":\"0.38\",\"Y轴最大速度\":\"99.0\",\"Y轴最小速度\":\"0.0\",\"Y轴路程计算公式\":\"\\\"return 0.0\\\"\"}"}
 *
 * @param 方块粉碎-24
 * @parent ---方块粉碎组21至40---
 * @type struct<DrillCOSEShatter>
 * @desc GIF的详细配置信息。
 * @default 
 *
 * @param 方块粉碎-25
 * @parent ---方块粉碎组21至40---
 * @type struct<DrillCOSEShatter>
 * @desc GIF的详细配置信息。
 * @default 
 *
 * @param 方块粉碎-26
 * @parent ---方块粉碎组21至40---
 * @type struct<DrillCOSEShatter>
 * @desc GIF的详细配置信息。
 * @default 
 *
 * @param 方块粉碎-27
 * @parent ---方块粉碎组21至40---
 * @type struct<DrillCOSEShatter>
 * @desc GIF的详细配置信息。
 * @default 
 *
 * @param 方块粉碎-28
 * @parent ---方块粉碎组21至40---
 * @type struct<DrillCOSEShatter>
 * @desc GIF的详细配置信息。
 * @default 
 *
 * @param 方块粉碎-29
 * @parent ---方块粉碎组21至40---
 * @type struct<DrillCOSEShatter>
 * @desc GIF的详细配置信息。
 * @default 
 *
 * @param 方块粉碎-30
 * @parent ---方块粉碎组21至40---
 * @type struct<DrillCOSEShatter>
 * @desc GIF的详细配置信息。
 * @default 
 * 
 * @param 方块粉碎-31
 * @parent ---方块粉碎组21至40---
 * @type struct<DrillCOSEShatter>
 * @desc GIF的详细配置信息。
 * @default 
 *
 * @param 方块粉碎-32
 * @parent ---方块粉碎组21至40---
 * @type struct<DrillCOSEShatter>
 * @desc GIF的详细配置信息。
 * @default 
 *
 * @param 方块粉碎-33
 * @parent ---方块粉碎组21至40---
 * @type struct<DrillCOSEShatter>
 * @desc GIF的详细配置信息。
 * @default 
 *
 * @param 方块粉碎-34
 * @parent ---方块粉碎组21至40---
 * @type struct<DrillCOSEShatter>
 * @desc GIF的详细配置信息。
 * @default 
 *
 * @param 方块粉碎-35
 * @parent ---方块粉碎组21至40---
 * @type struct<DrillCOSEShatter>
 * @desc GIF的详细配置信息。
 * @default 
 *
 * @param 方块粉碎-36
 * @parent ---方块粉碎组21至40---
 * @type struct<DrillCOSEShatter>
 * @desc GIF的详细配置信息。
 * @default 
 *
 * @param 方块粉碎-37
 * @parent ---方块粉碎组21至40---
 * @type struct<DrillCOSEShatter>
 * @desc GIF的详细配置信息。
 * @default 
 *
 * @param 方块粉碎-38
 * @parent ---方块粉碎组21至40---
 * @type struct<DrillCOSEShatter>
 * @desc GIF的详细配置信息。
 * @default 
 *
 * @param 方块粉碎-39
 * @parent ---方块粉碎组21至40---
 * @type struct<DrillCOSEShatter>
 * @desc GIF的详细配置信息。
 * @default 
 *
 * @param 方块粉碎-40
 * @parent ---方块粉碎组21至40---
 * @type struct<DrillCOSEShatter>
 * @desc GIF的详细配置信息。
 * @default 
 * 
 * @param ---方块粉碎组41至60---
 * @desc 
 * 
 * @param 方块粉碎-41
 * @parent ---方块粉碎组41至60---
 * @type struct<DrillCOSEShatter>
 * @desc GIF的详细配置信息。
 * @default 
 *
 * @param 方块粉碎-42
 * @parent ---方块粉碎组41至60---
 * @type struct<DrillCOSEShatter>
 * @desc GIF的详细配置信息。
 * @default 
 *
 * @param 方块粉碎-43
 * @parent ---方块粉碎组41至60---
 * @type struct<DrillCOSEShatter>
 * @desc GIF的详细配置信息。
 * @default 
 *
 * @param 方块粉碎-44
 * @parent ---方块粉碎组41至60---
 * @type struct<DrillCOSEShatter>
 * @desc GIF的详细配置信息。
 * @default 
 *
 * @param 方块粉碎-45
 * @parent ---方块粉碎组41至60---
 * @type struct<DrillCOSEShatter>
 * @desc GIF的详细配置信息。
 * @default 
 *
 * @param 方块粉碎-46
 * @parent ---方块粉碎组41至60---
 * @type struct<DrillCOSEShatter>
 * @desc GIF的详细配置信息。
 * @default 
 *
 * @param 方块粉碎-47
 * @parent ---方块粉碎组41至60---
 * @type struct<DrillCOSEShatter>
 * @desc GIF的详细配置信息。
 * @default 
 *
 * @param 方块粉碎-48
 * @parent ---方块粉碎组41至60---
 * @type struct<DrillCOSEShatter>
 * @desc GIF的详细配置信息。
 * @default 
 *
 * @param 方块粉碎-49
 * @parent ---方块粉碎组41至60---
 * @type struct<DrillCOSEShatter>
 * @desc GIF的详细配置信息。
 * @default 
 *
 * @param 方块粉碎-50
 * @parent ---方块粉碎组41至60---
 * @type struct<DrillCOSEShatter>
 * @desc GIF的详细配置信息。
 * @default 
 * 
 * @param 方块粉碎-51
 * @parent ---方块粉碎组41至60---
 * @type struct<DrillCOSEShatter>
 * @desc GIF的详细配置信息。
 * @default 
 *
 * @param 方块粉碎-52
 * @parent ---方块粉碎组41至60---
 * @type struct<DrillCOSEShatter>
 * @desc GIF的详细配置信息。
 * @default 
 *
 * @param 方块粉碎-53
 * @parent ---方块粉碎组41至60---
 * @type struct<DrillCOSEShatter>
 * @desc GIF的详细配置信息。
 * @default 
 *
 * @param 方块粉碎-54
 * @parent ---方块粉碎组41至60---
 * @type struct<DrillCOSEShatter>
 * @desc GIF的详细配置信息。
 * @default 
 *
 * @param 方块粉碎-55
 * @parent ---方块粉碎组41至60---
 * @type struct<DrillCOSEShatter>
 * @desc GIF的详细配置信息。
 * @default 
 *
 * @param 方块粉碎-56
 * @parent ---方块粉碎组41至60---
 * @type struct<DrillCOSEShatter>
 * @desc GIF的详细配置信息。
 * @default 
 *
 * @param 方块粉碎-57
 * @parent ---方块粉碎组41至60---
 * @type struct<DrillCOSEShatter>
 * @desc GIF的详细配置信息。
 * @default 
 *
 * @param 方块粉碎-58
 * @parent ---方块粉碎组41至60---
 * @type struct<DrillCOSEShatter>
 * @desc GIF的详细配置信息。
 * @default 
 *
 * @param 方块粉碎-59
 * @parent ---方块粉碎组41至60---
 * @type struct<DrillCOSEShatter>
 * @desc GIF的详细配置信息。
 * @default 
 *
 * @param 方块粉碎-60
 * @parent ---方块粉碎组41至60---
 * @type struct<DrillCOSEShatter>
 * @desc GIF的详细配置信息。
 * @default 
 *
 */
/*~struct~DrillCOSEShatter:
 *
 * @param 标签
 * @desc 只用于方便区分查看的标签，不作用在插件中。
 * @default ==新的粉碎设置==
 * 
 * @param ---碎片弹道---
 * @desc 
 *
 * @param 碎片弹道
 * @parent ---碎片弹道---
 * @type struct<DrillCOSEBallistic>
 * @desc 碎片弹道运动轨迹的详细配置信息。
 * @default {}
 * 
 * @param 碎片持续时长
 * @parent ---碎片弹道---
 * @type number
 * @min 4
 * @desc 碎片的持续时长，单位帧。（1秒60帧）
 * @default 120
 * 
 * @param 碎片依次延迟间隔
 * @parent ---碎片弹道---
 * @desc 碎片依次延迟粉碎的间隔，单位帧。可为负数，正数表示第一块碎片最先粉碎，负数表示最后一块碎片最先粉碎。
 * @default 0
 * 
 * @param 碎片速度倍率
 * @parent ---碎片弹道---
 * @desc 中心的碎片的速度最小，最边缘碎片的速度的最大。
 * @default 1.00
 *
 * @param 碎片速度是否分比例
 * @parent ---碎片弹道---
 * @type boolean
 * @on 分比例
 * @off 速度一致
 * @desc true - 分比例，false - 速度一致
 * @default false
 * 
 * @param 最小速度比例
 * @parent 碎片速度是否分比例
 * @desc 中心的碎片的速度最小，最边缘碎片的速度的最大。
 * @default 0.2
 * 
 * @param ---碎片切割---
 * @desc 
 *
 * @param 碎片切割方式
 * @parent ---碎片切割---
 * @type select
 * @option 切割矩阵
 * @value 切割矩阵
 * @option 固定大小
 * @value 固定大小
 * @desc 碎片切割的模式。
 * @default 切割矩阵
 * 
 * @param 切割矩阵行数
 * @parent 碎片切割方式
 * @type number
 * @min 1
 * @desc 切割模式为 切割矩阵 时，指定贴图切割的行数。碎片数 = 行数 x 列数。
 * @default 6
 * 
 * @param 切割矩阵列数
 * @parent 碎片切割方式
 * @type number
 * @min 1
 * @desc 切割模式为 切割矩阵 时，指定贴图切割的行数。碎片数 = 行数 x 列数。
 * @default 6
 * 
 * @param 固定大小的碎片宽度
 * @parent 碎片切割方式
 * @type number
 * @min 4
 * @desc 切割模式为 固定大小 时，指定碎片的宽度，单位像素。
 * @default 36
 * 
 * @param 固定大小的碎片高度
 * @parent 碎片切割方式
 * @type number
 * @min 4
 * @desc 切割模式为 固定大小 时，指定碎片的高度，单位像素。
 * @default 36
 * 
 */
/*~struct~DrillCOSEBallistic:
 *
 * @param 标签
 * @desc 只用于方便区分查看的标签，不作用在插件中。
 * @default ==新的运动模式==
 *
 * @param 移动模式
 * @type select
 * @option 直角坐标模式
 * @value 直角坐标模式
 * @option 极坐标模式
 * @value 极坐标模式
 * @desc 描述碎片运动的模式。
 * @default 极坐标模式
 * 
 * 
 * @param ---极坐标模式---
 * @desc 
 *
 * @param 速度类型
 * @parent ---极坐标模式---
 * @type select
 * @option 只初速度
 * @value 只初速度
 * @option 初速度+波动量
 * @value 初速度+波动量
 * @option 初速度+波动量+加速度
 * @value 初速度+波动量+加速度
 * @option 初速度+波动量+加速度+最大最小
 * @value 初速度+波动量+加速度+最大最小
 * @option 路程计算公式
 * @value 路程计算公式
 * @desc 描述碎片速度的模式。
 * @default 只初速度
 * 
 * @param 初速度
 * @parent 速度类型
 * @desc 碎片的基本速度，单位 像素/帧。
 * @default 1.0
 * 
 * @param 速度随机波动量
 * @parent 速度类型
 * @desc 碎片速度上下随机浮动的量，单位 像素/帧。比如值为 5.0，则随机浮动范围为 -2.5 ~ 2.5 之间。
 * @default 2.0
 * 
 * @param 加速度
 * @parent 速度类型
 * @desc 碎片的加速度，单位 像素/帧。
 * @default 0.0
 * 
 * @param 最大速度
 * @parent 速度类型
 * @desc 碎片的最大速度，单位 像素/帧。
 * @default 99.0
 * 
 * @param 最小速度
 * @parent 速度类型
 * @desc 碎片的最小速度，单位 像素/帧。
 * @default 0.0
 * 
 * @param 路程计算公式
 * @parent 速度类型
 * @type note
 * @desc 碎片的路程计算公式。可使用 变量和常量 来设计公式，具体看看文档 "1.系统 > 关于弹道.docx"介绍。
 * @default "return 0.0"
 * 
 * @param 方向类型
 * @parent ---极坐标模式---
 * @type select
 * @option 固定方向
 * @value 固定方向
 * @option 四周扩散(线性)
 * @value 四周扩散(线性)
 * @option 四周扩散(随机)
 * @value 四周扩散(随机)
 * @option 四周扩散(抖动)
 * @value 四周扩散(抖动)
 * @option 扇形范围方向(线性)
 * @value 扇形范围方向(线性)
 * @option 扇形范围方向(随机)
 * @value 扇形范围方向(随机)
 * @option 方向计算公式
 * @value 方向计算公式
 * @desc 描述碎片速度的模式。
 * @default 四周扩散(线性)
 * 
 * @param 固定方向
 * @parent 方向类型
 * @desc 类型为"固定方向"时，固定方向的角度值。0朝右，90朝下，180朝左，270朝上。
 * @default 90.0
 * 
 * @param 扇形朝向
 * @parent 方向类型
 * @desc 类型为"扇形范围方向"时，扇形的朝向角度。0朝右，90朝下，180朝左，270朝上。
 * @default 45.0
 * 
 * @param 扇形角度
 * @parent 方向类型
 * @desc 类型为"扇形范围方向"时，扇形弧的角度数。
 * @default 90.0
 * 
 * @param 方向计算公式
 * @parent 方向类型
 * @type note
 * @desc 类型为"方向计算公式"时。可使用 变量和常量 来设计公式，具体看看文档 "1.系统 > 关于弹道.docx"介绍。
 * @default "return 0.0"
 * 
 * @param ---直角坐标模式---
 * @desc 
 * 
 * @param 直角坐标整体旋转
 * @parent ---直角坐标模式---
 * @desc 将下面设计好的xy公式，进行整体旋转，单位角度。
 * @default 0.0
 *
 * @param X轴速度类型
 * @parent ---直角坐标模式---
 * @type select
 * @option 只初速度
 * @value 只初速度
 * @option 初速度+波动量
 * @value 初速度+波动量
 * @option 初速度+波动量+加速度
 * @value 初速度+波动量+加速度
 * @option 初速度+波动量+加速度+最大最小
 * @value 初速度+波动量+加速度+最大最小
 * @option 路程计算公式
 * @value 路程计算公式
 * @desc 描述碎片速度的模式。
 * @default 只初速度
 * 
 * @param X轴初速度
 * @parent X轴速度类型
 * @desc 碎片的基本速度，单位 像素/帧。
 * @default 1.0
 * 
 * @param X轴速度随机波动量
 * @parent X轴速度类型
 * @desc 碎片速度上下随机浮动的量，单位 像素/帧。比如值为 5.0，则随机浮动范围为 -2.5 ~ 2.5 之间。
 * @default 2.0
 * 
 * @param X轴加速度
 * @parent X轴速度类型
 * @desc 碎片的加速度，单位 像素/帧。
 * @default 0.0
 * 
 * @param X轴最大速度
 * @parent X轴速度类型
 * @desc 碎片的最大速度，单位 像素/帧。
 * @default 99.0
 * 
 * @param X轴最小速度
 * @parent X轴速度类型
 * @desc 碎片的最小速度，单位 像素/帧。
 * @default 0.0
 * 
 * @param X轴路程计算公式
 * @parent X轴速度类型
 * @type note
 * @desc 碎片的路程计算公式。可使用 变量和常量 来设计公式，具体看看文档 "1.系统 > 关于弹道.docx"介绍。
 * @default "return 0.0"
 *
 * @param Y轴速度类型
 * @parent ---直角坐标模式---
 * @type select
 * @option 只初速度
 * @value 只初速度
 * @option 初速度+波动量
 * @value 初速度+波动量
 * @option 初速度+波动量+加速度
 * @value 初速度+波动量+加速度
 * @option 初速度+波动量+加速度+最大最小
 * @value 初速度+波动量+加速度+最大最小
 * @option 路程计算公式
 * @value 路程计算公式
 * @desc 描述碎片速度的模式。
 * @default 只初速度
 * 
 * @param Y轴初速度
 * @parent Y轴速度类型
 * @desc 碎片的基本速度，单位 像素/帧。
 * @default 1.0
 * 
 * @param Y轴速度随机波动量
 * @parent Y轴速度类型
 * @desc 碎片速度上下随机浮动的量，单位 像素/帧。比如值为 5.0，则随机浮动范围为 -2.5 ~ 2.5 之间。
 * @default 2.0
 * 
 * @param Y轴加速度
 * @parent Y轴速度类型
 * @desc 碎片的加速度，单位 像素/帧。
 * @default 0.0
 * 
 * @param Y轴最大速度
 * @parent Y轴速度类型
 * @desc 碎片的最大速度，单位 像素/帧。
 * @default 99.0
 * 
 * @param Y轴最小速度
 * @parent Y轴速度类型
 * @desc 碎片的最小速度，单位 像素/帧。
 * @default 0.0
 * 
 * @param Y轴路程计算公式
 * @parent Y轴速度类型
 * @type note
 * @desc 碎片的路程计算公式。可使用 变量和常量 来设计公式，具体看看文档 "1.系统 > 关于弹道.docx"介绍。
 * @default "return 0.0"
 * 
 * 
 * 
 */
 
//<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
//		插件简称		COSE（Core_Of_Shatter_Effect）
//		临时全局变量	无
//		临时局部变量	this._drill_COSE_xxx
//		存储数据变量	无
//		全局存储变量	无
//		覆盖重写方法	无
//
//<<<<<<<<性能记录<<<<<<<<
//
//		★工作类型		持续执行
//		★时间复杂度		o(n^2)*o(贴图处理) 每帧
//		★性能测试因素	无
//		★性能测试消耗	1163.34ms（由于覆写了sprite.update函数，结果全算到这个插件上了）
//		★最坏情况		不可估计
//		★备注			可以确定的是，消耗要比滤镜的多一点。
//		
//		★优化记录		
//			2022-7-16优化：
//				这次主要优化存储数据对象，数据类保存后，每个 方块粉碎控制器 的数据占2k的字节容量。
//				以Drill_LayerShatterEffect为例，存储数据 与 不存数据的存档，差了 12k 的大小区别。
//			2022-10-5优化：
//				drill_updateShatterMove 在没任何贴图的情况下默认工作。已加上标记。
//				不可见的时候，碎片移动也完全不需要执行。
//
//<<<<<<<<插件记录<<<<<<<<
//
//		★功能结构树：
//			方块粉碎核心：
//				->控制器
//					->标准模块 模板
//						->帧刷新【标准函数】
//						->重设数据【标准函数】
//						->显示/隐藏【标准函数】
//						->初始化数据【标准默认值】
//					->标准模块
//						->是否正在播放【标准函数】
//						->播放粉碎过程【标准函数】
//						->倒放粉碎过程【标准函数】
//						->立即复原【标准函数】
//						->暂停粉碎过程【标准函数】
//						->继续粉碎过程【标准函数】
//					->弹道参数
//						->弹道（坐标）
//						->弹道（透明度）
//					->帧刷新
//						->碎片可见
//						->碎片时间流逝
//				->贴图
//					->标准模块 模板
//						->设置控制器【标准函数】
//						->是否就绪【标准函数】
//						->是否需要销毁【标准函数】
//						->销毁【标准函数】
//					->标准模块
//						->父贴图是否显示【标准函数】
//					->粉碎贴图
//						->重刷时机
//						->重建 碎片贴图
//						->重建 资源对象
//						->重建 弹道
//						->优化浮点数过滤
//
//		★家谱：
//			大家族-方块粉碎
//			核心
//		
//		★插件私有类：
//			无
//		
//		★必要注意事项：
//			1.这里引用了弹道核心的：坐标、透明度 功能。
//			  这里的 透明度 是自定义的配置内容。
//			2.该核心的粉碎效果不会隐藏图片本体，需要在子插件手动隐藏。
//			
//		★其它说明细节：
//			1.碎片滞留、父贴图框架问题、父贴图隐藏问题、控制器数据管理问题等问题，
//			  此核心一概不管，全部由 子插件 说明、处理。
//		
//		★核心说明：
//			1.整个核心提供 一个粉碎控制器数据对象 和 一个粉碎贴图。
//		
//		★存在的问题：
//			1.参数过多，拆解极其麻烦。概念上，一直在纠结碎片行数列数是否要下发到子插件中设置。
//			虽然这里完全封装成了一个单一函数接口。（2022/7/20 这里经过了完整重构，结构思路已经很清晰了）
//

//=============================================================================
// ** 提示信息
//=============================================================================
	//==============================
	// * 提示信息 - 参数
	//==============================
	var DrillUp = DrillUp || {}; 
	DrillUp.g_COSE_PluginTip_curName = "Drill_CoreOfShatterEffect.js 系统-方块粉碎核心";
	DrillUp.g_COSE_PluginTip_baseList = ["Drill_CoreOfBallistics.js 系统-弹道核心"];
	//==============================
	// * 提示信息 - 报错 - 缺少基础插件
	//			
	//			说明：	此函数只提供提示信息，不校验真实的插件关系。
	//==============================
	DrillUp.drill_COSE_getPluginTip_NoBasePlugin = function(){
		if( DrillUp.g_COSE_PluginTip_baseList.length == 0 ){ return ""; }
		var message = "【" + DrillUp.g_COSE_PluginTip_curName + "】\n缺少基础插件，去看看下列插件是不是 未添加 / 被关闭 / 顺序不对：";
		for(var i=0; i < DrillUp.g_COSE_PluginTip_baseList.length; i++){
			message += "\n- ";
			message += DrillUp.g_COSE_PluginTip_baseList[i];
		}
		return message;
	};
	//==============================
	// * 提示信息 - 报错 - 未配置的参数
	//==============================
	DrillUp.drill_COSE_getPluginTip_DataNotFind = function( data_id ){
		return "【" + DrillUp.g_COSE_PluginTip_curName + "】\n没有找到编号为"+data_id+"的方块粉碎配置，请查看插件参数的配置内容。";
	};
	//==============================
	// * 提示信息 - 报错 - 未配置的参数
	//==============================
	DrillUp.drill_COSE_getPluginTip_ErrorOpacityType = function( opacity_type ){
		return "【" + DrillUp.g_COSE_PluginTip_curName + "】\n透明度类型错误，没有类型'"+opacity_type+"'。";
	};
	//==============================
	// * 提示信息 - 报错 - 宽度高度不为零
	//==============================
	DrillUp.drill_COSE_getPluginTip_NoZero = function(){
		return "【" + DrillUp.g_COSE_PluginTip_curName + "】\n碎片参数错误，出现了宽度或高度为零的碎片。";
	};
	
	
//=============================================================================
// ** 变量获取
//=============================================================================
　　var Imported = Imported || {};
　　Imported.Drill_CoreOfShatterEffect = true;
　　var DrillUp = DrillUp || {}; 
    DrillUp.parameters = PluginManager.parameters('Drill_CoreOfShatterEffect');
	
	
	//==============================
	// * 变量获取 - 弹道样式
	//				（~struct~DrillCOSEBallistic）
	//				
	//				说明：函数未定义白色括号中的参数，需要子插件定义。若不定义则为默认值。
	//==============================
	DrillUp.drill_COSE_ballisticsInit = function( dataFrom ){
		var data = {};
		// > 移动（movement）
		//		data['movementNum']     【碎片数量】
		//		data['movementTime']    【移动时长】
		//		data['movementDelay']    开始前延迟时间
		//		data['movementEndDelay'] 到终点后延迟时间
		data['movementTime'] = Number( dataFrom["移动时长"] || -1 );	//（兼容旧配置）
		data['movementMode'] = String( dataFrom["移动模式"] || "极坐标模式" );
		//   极坐标（polar）
		data['polarSpeedType'] = String( dataFrom["速度类型"] || "只初速度" );
		data['polarSpeedBase'] = Number( dataFrom["初速度"] || 0.0);
		data['polarSpeedRandom'] = Number( dataFrom["速度随机波动量"] || 0.0);
		data['polarSpeedInc'] = Number( dataFrom["加速度"] || 0);
		data['polarSpeedMax'] = Number( dataFrom["最大速度"] || 0);
		data['polarSpeedMin'] = Number( dataFrom["最小速度"] || 0);
		var temp_str = String( dataFrom["路程计算公式"] || "\"return 0\"" );
		temp_str = temp_str.substring(1,temp_str.length-1);
		temp_str = temp_str.replace(/\\n/g,"\n");
		temp_str = temp_str.replace(/\\\\/g,"\\");
		data['polarDistanceFormula'] = temp_str;
		data['polarDirType'] = String( dataFrom["方向类型"] || "固定方向" );
		data['polarDirFixed'] = Number( dataFrom["固定方向"] || 0);
		data['polarDirSectorFace'] = Number( dataFrom["扇形朝向"] || 0);
		data['polarDirSectorDegree'] = Number( dataFrom["扇形角度"] || 0);
		temp_str = String( dataFrom["方向计算公式"] || "\"return 0\"" );
		temp_str = temp_str.substring(1,temp_str.length-1);
		temp_str = temp_str.replace(/\\n/g,"\n");
		temp_str = temp_str.replace(/\\\\/g,"\\");
		data['polarDirFormula'] = temp_str;
		//   直角坐标（cartesian）
		data['cartRotation'] = Number( dataFrom["直角坐标整体旋转"] || 0.0);
		data['cartXSpeedType'] = String( dataFrom["X轴速度类型"] || "只初速度" );
		data['cartXSpeedBase'] = Number( dataFrom["X轴初速度"] || 0.0);
		data['cartXSpeedRandom'] = Number( dataFrom["X轴速度随机波动量"] || 0.0);
		data['cartXSpeedInc'] = Number( dataFrom["X轴加速度"] || 0);
		data['cartXSpeedMax'] = Number( dataFrom["X轴最大速度"] || 0);
		data['cartXSpeedMin'] = Number( dataFrom["X轴最小速度"] || 0);
		temp_str = String( dataFrom["X轴路程计算公式"] || "return 0" );
		temp_str = temp_str.substring(1,temp_str.length-1);
		temp_str = temp_str.replace(/\\n/g,"\n");
		temp_str = temp_str.replace(/\\\\/g,"\\");
		data['cartXDistanceFormula'] = temp_str;
		data['cartYSpeedType'] = String( dataFrom["Y轴速度类型"] || "只初速度" );
		data['cartYSpeedBase'] = Number( dataFrom["Y轴初速度"] || 0.0);
		data['cartYSpeedRandom'] = Number( dataFrom["Y轴速度随机波动量"] || 0.0);
		data['cartYSpeedInc'] = Number( dataFrom["Y轴加速度"] || 0);
		data['cartYSpeedMax'] = Number( dataFrom["Y轴最大速度"] || 0);
		data['cartYSpeedMin'] = Number( dataFrom["Y轴最小速度"] || 0);
		temp_str = String( dataFrom["Y轴路程计算公式"] || "return 0" );
		temp_str = temp_str.substring(1,temp_str.length-1);
		temp_str = temp_str.replace(/\\n/g,"\n");
		temp_str = temp_str.replace(/\\\\/g,"\\");
		data['cartYDistanceFormula'] = temp_str;
		//   轨道锚点（track） （关闭）
		//   两点式（twoPoint）（关闭）
		return data;
	}
	//==============================
	// * 变量获取 - 方块粉碎
	//				（~struct~DrillCOSEShatter）
	//==============================
	DrillUp.drill_COSE_styleInit = function( dataFrom ){
		var data = {};
		
		// > 碎片弹道
		if( dataFrom["碎片弹道"] != undefined &&
			dataFrom["碎片弹道"] != "" ){
			var temp = JSON.parse( dataFrom["碎片弹道"] || [] );
			data['ballistics'] = DrillUp.drill_COSE_ballisticsInit( temp );
		}else{
			data['ballistics'] = DrillUp.drill_COSE_ballisticsInit( {} );
		}
		data['sustain'] = Number( dataFrom["碎片持续时长"] || 120);
		data['orderDelay'] = Number( dataFrom["碎片依次延迟间隔"] || 0);
		if( data['ballistics']['movementTime'] > 0 ){ data['sustain'] = data['ballistics']['movementTime']; }
		data['speedFactor'] = Number( dataFrom["碎片速度倍率"] || 1.0);
		data['speedPer'] = String( dataFrom["碎片速度是否分比例"] || "false" ) == "true";
		data['speedPerMin'] = Number( dataFrom["最小速度比例"] || 0.2);
		
		// > 碎片切割
		data['splitMode'] = String( dataFrom["碎片切割方式"] || "切割矩阵" );
		data['splitRowCount'] = Number( dataFrom["切割矩阵行数"] || 5);
		data['splitColCount'] = Number( dataFrom["切割矩阵列数"] || 5);
		data['splitWidth'] = Number( dataFrom["固定大小的碎片宽度"] || 36);
		data['splitHeight'] = Number( dataFrom["固定大小的碎片高度"] || 36);
		//data['rotation'] = Number( dataFrom["碎片自旋转速度"] || 0);
		
		return data;
	}
	
	/*-----------------方块粉碎（配置）------------------*/
	DrillUp.g_COSE_style_list_length = 60;
	DrillUp.g_COSE_style_list = [];
	for( var i = 0; i < DrillUp.g_COSE_style_list_length; i++ ){
		if( DrillUp.parameters['方块粉碎-' + String(i+1) ] != "" &&
			DrillUp.parameters['方块粉碎-' + String(i+1) ] != undefined ){
			var data = JSON.parse(DrillUp.parameters['方块粉碎-' + String(i+1)] );
			DrillUp.g_COSE_style_list[i] = DrillUp.drill_COSE_styleInit( data );
		}else{
			DrillUp.g_COSE_style_list[i] = null;
		}
	};
	
	
	
//=============================================================================
// * >>>>基于插件检测>>>>
//=============================================================================
if( Imported.Drill_CoreOfBallistics ){
	
	
//=============================================================================
// ** 方块粉碎控制器【Drill_COSE_Controller】
// **			
// **		索引：	COSE（可从子插件搜索到函数、类用法）
// **		来源：	独立数据
// **		实例：	> Drill_EventShatterEffect 插件中 Game_CharacterBase 对象的 ._drill_ESE_controller 成员
// **		应用：	> Drill_EventShatterEffect 插件中 Game_CharacterBase 对象的 drill_COSE_update 帧刷新。
// **		
// **		作用域：	地图界面、战斗界面、菜单界面
// **		主功能：	> 定义一个专门控制粉碎的数据类。
// **		子功能：	->帧刷新
// **						->重设数据
// **						->显示/隐藏
// **						->暂停/继续
// **					->粉碎过程
// **						->播放/倒放
// **						->立即复原
// **					->资源模式
// **						> 指定资源名
// **						> 关闭资源控制
// **						->切割框架
// **					->碎片
// **						->碎片时间流逝
// **						->透明度类型
// **						->弹道初始化（坐标）
// **						->弹道初始化（透明度）
// **					
// **		说明：	> 该类不能单独使用，必须结合 方块粉碎贴图 对象类。
// **				> 创建后固定为暂停状态，需要手动执行碎片播放。
// **				> 注意，此控制器，使用了 随机因子 （保存后再读取能复原碎片样子），但是没用 随机迭代次数。
//=============================================================================
//==============================
// * 控制器 - 定义
//==============================
function Drill_COSE_Controller() {
	this.initialize.apply(this, arguments);
}
//==============================
// * 控制器 - 初始化
//==============================
Drill_COSE_Controller.prototype.initialize = function( data ){
	this._drill_data = {};
	this._drill_controllerSerial = new Date().getTime() + Math.random();	//（生成一个不重复的序列号）
    this.drill_initData();													//初始化数据
    this.drill_initPrivateData();											//私有数据初始化
	if( data == undefined ){ data = {}; }
    this.drill_COSE_resetData( data );
}
//##############################
// * 控制器 - 帧刷新【标准函数】
//			
//			参数：	> 无
//			返回：	> 无
//			
//			说明：	> 此函数必须在 帧刷新 中手动调用执行。
//##############################
Drill_COSE_Controller.prototype.drill_COSE_update = function(){
	this.drill_updateVisible();			//帧刷新 - 可见
	this.drill_updateTime();			//帧刷新 - 时间流逝
}
//##############################
// * 控制器 - 重设数据【标准函数】
//			
//			参数：	> data 动态参数对象
//			返回：	> 无
//			
//			说明：	> 通过此函数，你不需要再重新创建一个数据对象，并且贴图能直接根据此数据来变化。
//					> 参数对象中的参数【可以缺项】，只要的参数项不一样，就刷新；参数项一样，则不变化。
//					> 此函数遇到 指定资源名+空名称 的碎片设置时，不变化。
//##############################
Drill_COSE_Controller.prototype.drill_COSE_resetData = function( data ){
	this.drill_COSE_resetData_Private( data );
};
//##############################
// * 控制器 - 显示/隐藏【标准函数】
//
//			参数：	> visible 布尔（是否显示）
//			返回：	> 无
//			
//			说明：	> 可放在帧刷新函数中实时调用。
//##############################
Drill_COSE_Controller.prototype.drill_COSE_setVisible = function( visible ){
	var data = this._drill_data;
	data['visible'] = visible;
};
//##############################
// * 控制器 - 是否正在播放【标准函数】
//
//			参数：	> 无
//			返回：	> 布尔（是否显示）
//			
//			说明：	> 注意，粉碎播放完后，会返回false。
//					  如果是正向播放，碎片会保持消失状态，父贴图也不会显示。
//##############################
Drill_COSE_Controller.prototype.drill_COSE_isPlaying = function(){
	return this.drill_COSE_isPlaying_Private();
}
//##############################
// * 控制器 - 播放粉碎过程【标准函数】
//
//			参数：	> 无
//			返回：	> 无
//			
//			说明：	> 开始播放 粉碎过程。若之前处于暂停状态，则会解除暂停。
//##############################
Drill_COSE_Controller.prototype.drill_COSE_runShatter = function(){
    this.drill_COSE_runShatter_Private();
}
//##############################
// * 控制器 - 倒放粉碎过程【标准函数】
//
//			参数：	> 无
//			返回：	> 无
//			
//			说明：	> 开始倒放 粉碎过程。若之前处于暂停状态，则会解除暂停。
//##############################
Drill_COSE_Controller.prototype.drill_COSE_backrunShatter = function(){
    this.drill_COSE_backrunShatter_Private();
}
//##############################
// * 控制器 - 立即复原【标准函数】
//
//			参数：	> 无
//			返回：	> 无
//			
//			说明：	> 隐藏碎片贴图，恢复初始状态。
//##############################
Drill_COSE_Controller.prototype.drill_COSE_restoreShatter = function(){
    this.drill_COSE_restoreShatter_Private();
}
//##############################
// * 控制器 - 暂停粉碎过程【标准函数】
//
//			参数：	> 无
//			返回：	> 无
//##############################
Drill_COSE_Controller.prototype.drill_COSE_pause = function(){
    this.drill_COSE_pause_Private();
}
//##############################
// * 控制器 - 继续粉碎过程【标准函数】
//
//			参数：	> 无
//			返回：	> 无
//##############################
Drill_COSE_Controller.prototype.drill_COSE_continue = function(){
    this.drill_COSE_continue_Private();
}
//##############################
// * 控制器 - 初始化数据【标准默认值】
//
//			参数：	> 无
//			返回：	> 无
//			
//			说明：	> data 动态参数对象（来自类初始化）
//					  该对象包含 类所需的所有默认值。
//##############################
Drill_COSE_Controller.prototype.drill_initData = function(){
	var data = this._drill_data;
	
	// > 默认值
	if( data['visible'] == undefined ){ data['visible'] = true };				//可见情况
	
	if( data['frameX'] == undefined ){ data['frameX'] = 0 };					//切割框架X
	if( data['frameY'] == undefined ){ data['frameY'] = 0 };					//切割框架Y
	if( data['frameW'] == undefined ){ data['frameW'] = 100 };					//切割框架宽度
	if( data['frameH'] == undefined ){ data['frameH'] = 100 };					//切割框架高度
	
	if( data['src_mode'] == undefined ){ data['src_mode'] = "指定资源名" };		//资源模式（指定资源名/关闭资源控制）
	if( data['src_img'] == undefined ){ data['src_img'] = "" };					//资源文件
	if( data['src_file'] == undefined ){ data['src_file'] = "img/system/" };	//资源路径
	//（"指定资源名"情况下，数据能够保存，如果为"关闭资源控制"，那么数据不存，并且你还需要在贴图创建后，赋值bitmap，见 drill_COSE_setUncontroledBitmap 。）
	
	if( data['shatter_id'] == undefined ){ data['shatter_id'] = 0 };							//碎片 - 方块粉碎id
	if( data['shatter_opacityType'] == undefined ){ data['shatter_opacityType'] = "线性消失" };	//碎片 - 透明度类型
	if( data['shatter_hasParent'] == undefined ){ data['shatter_hasParent'] = true };			//碎片 - 父贴图标记（如果没有父贴图，那么碎片复原后会保持显示）
}
//==============================
// * 初始化 - 私有数据初始化
//==============================
Drill_COSE_Controller.prototype.drill_initPrivateData = function(){
	var data = this._drill_data;
	
	// > 检查
	var style_data = DrillUp.g_COSE_style_list[ data['shatter_id'] ];
	if( style_data == null ){
		alert( DrillUp.drill_COSE_getPluginTip_DataNotFind( data['shatter_id']+1 ) );
		return;
	}
	
	// > 不接受零高宽情况
	if( data['frameW'] == 0 || data['frameH'] == 0 ){
		//alert( DrillUp.drill_COSE_getPluginTip_NoZero() );
		return;
	}
	
	
	// > 初始化 - 私有变量
	this._drill_pause = true;							//暂停标记（注意，创建后固定为暂停状态，需要手动执行碎片播放）
	this._drill_backrun = false;						//倒放标记
	this._drill_visible = false;						//可见情况
	this._drill_curTime = 0;							//当前时间进度
	this._drill_playingTime = 0;						//动画总时间
	this._drill_needDestroy = false;					//销毁标记（暂未用到）
	
	// > 初始化 - 碎片切割方式 - 固定大小
	if( style_data['splitMode'] == "固定大小" ){
		this._drill_spriteWidth = style_data['splitWidth'];
		this._drill_spriteHeight = style_data['splitHeight'];
		this._drill_spriteRowCount = Math.ceil( data['frameH'] / style_data['splitHeight'] );
		this._drill_spriteColCount = Math.ceil( data['frameW'] / style_data['splitWidth'] );
		this._drill_spriteNum = this._drill_spriteRowCount * this._drill_spriteColCount;
		
	// > 初始化 - 碎片切割方式 - 切割矩阵
	}else{
		this._drill_spriteNum = style_data['splitRowCount'] * style_data['splitColCount'];
		this._drill_spriteWidth = Math.ceil( data['frameW'] / style_data['splitColCount'] );
		this._drill_spriteHeight = Math.ceil( data['frameH'] / style_data['splitRowCount'] );
		this._drill_spriteRowCount = style_data['splitRowCount'];
		this._drill_spriteColCount = style_data['splitColCount'];
	}
	
	
	// > 碎片群弹道 - 随机因子
	this._drill_randomFactor_speed = Math.random();
	this._drill_randomFactor_dir = Math.random();
	this._drill_randomFactor_xSpeed = Math.random();
	this._drill_randomFactor_ySpeed = Math.random();
	this._drill_randomFactor_opacity = Math.random();
	
	// > 碎片群弹道 - 弹道数据
	this._drill_COSE_ballistics_move = {};
	this._drill_COSE_ballistics_opacity = {};
    this.drill_initBallisticsMove( data, style_data['ballistics'], style_data['sustain'], style_data['orderDelay'] );	//弹道初始化（坐标）
    this.drill_initBallisticsOpacity( data, style_data['sustain'], style_data['orderDelay'] );							//弹道初始化（透明度）
	this._drill_playingTime = $gameTemp.drill_COBa_getBallisticsMove_TotalTime();										//碎片持续时长
}
//==============================
// * 碎片群弹道 - 弹道初始化（坐标）
//
//			说明：	> 只存 弹道配置，不存 实际弹道。包括随机因子，但不含随机迭代次数。
//					> 实际弹道只在贴图中进行推演并使用。
//==============================
Drill_COSE_Controller.prototype.drill_initBallisticsMove = function( data, b_data, sustain, orderDelay ){
	
	// > 弹道初始化（坐标）
	var temp_b_move = {}
	
	//   移动（movement）
	temp_b_move['movementNum'] = this._drill_spriteNum;							//碎片数量
	temp_b_move['movementTime'] = sustain;										//时长
	temp_b_move['movementDelay'] = 0;											//延迟
	temp_b_move['movementEndDelay'] = 0;										//延迟
	temp_b_move['movementOrderDelay'] = orderDelay;								//依次延迟时间
	temp_b_move['movementMode'] = b_data["movementMode"];						//移动模式
	//   极坐标（polar）
	temp_b_move['polarSpeedType'] = b_data["polarSpeedType"];					//极坐标 - 速度 - 类型
	temp_b_move['polarSpeedBase'] = b_data["polarSpeedBase"];					//极坐标 - 速度 - 初速度
	temp_b_move['polarSpeedRandom'] = b_data["polarSpeedRandom"];				//极坐标 - 速度 - 速度随机波动量
	temp_b_move['polarSpeedInc'] = b_data["polarSpeedInc"];						//极坐标 - 速度 - 加速度
	temp_b_move['polarSpeedMax'] = b_data["polarSpeedMax"];						//极坐标 - 速度 - 最大速度
	temp_b_move['polarSpeedMin'] = b_data["polarSpeedMin"];						//极坐标 - 速度 - 最小速度
	temp_b_move['polarDistanceFormula'] = b_data["polarDistanceFormula"];		//极坐标 - 速度 - 路程计算公式
	temp_b_move['polarDirType'] = b_data["polarDirType"];						//极坐标 - 方向 - 类型
	temp_b_move['polarDirFixed'] = b_data["polarDirFixed"];						//极坐标 - 方向 - 固定方向
	temp_b_move['polarDirSectorFace'] = b_data["polarDirSectorFace"];			//极坐标 - 方向 - 扇形朝向
	temp_b_move['polarDirSectorDegree'] = b_data["polarDirSectorDegree"];		//极坐标 - 方向 - 扇形角度
	temp_b_move['polarDirFormula'] = b_data["polarDirFormula"];					//极坐标 - 方向 - 方向计算公式
	//   直角坐标（cartesian）
	temp_b_move['cartRotation'] = b_data["cartRotation"];						//直角坐标 - 直角坐标整体旋转
	temp_b_move['cartXSpeedType'] = b_data["cartXSpeedType"];					//直角坐标 - x - 类型
	temp_b_move['cartXSpeedBase'] = b_data["cartXSpeedBase"];					//直角坐标 - x - 初速度
	temp_b_move['cartXSpeedRandom'] = b_data["cartXSpeedRandom"];				//直角坐标 - x - 速度随机波动量
	temp_b_move['cartXSpeedInc'] = b_data["cartXSpeedInc"];						//直角坐标 - x - 加速度
	temp_b_move['cartXSpeedMax'] = b_data["cartXSpeedMax"];						//直角坐标 - x - 最大速度
	temp_b_move['cartXSpeedMin'] = b_data["cartXSpeedMin"];						//直角坐标 - x - 最小速度
	temp_b_move['cartXDistanceFormula'] = b_data["cartXDistanceFormula"];		//直角坐标 - x - 路程计算公式
	temp_b_move['cartYSpeedType'] = b_data["cartYSpeedType"];					//直角坐标 - y - 类型
	temp_b_move['cartYSpeedBase'] = b_data["cartYSpeedBase"];					//直角坐标 - y - 初速度
	temp_b_move['cartYSpeedRandom'] = b_data["cartYSpeedRandom"];				//直角坐标 - y - 速度随机波动量
	temp_b_move['cartYSpeedInc'] = b_data["cartYSpeedInc"];						//直角坐标 - y - 加速度
	temp_b_move['cartYSpeedMax'] = b_data["cartYSpeedMax"];						//直角坐标 - y - 最大速度
	temp_b_move['cartYSpeedMin'] = b_data["cartYSpeedMin"];						//直角坐标 - y - 最小速度
	temp_b_move['cartYDistanceFormula'] = b_data["cartYDistanceFormula"];		//直角坐标 - y - 路程计算公式
	
	// > 随机因子（RandomFactor）
	//		（每个碎片对应一个随机因子，掌握一条弹道。）
	//		（注意，独立参数项之间，随机因子不可共用。会造成强关联的错误关系。）
	temp_b_move['polarSpeedRandomFactor'] = this._drill_randomFactor_speed;		//极坐标 - 速度 - 随机因子
	temp_b_move['polarDirRandomFactor'] = this._drill_randomFactor_dir;			//极坐标 - 方向 - 随机因子
	temp_b_move['cartXSpeedRandomFactor'] = this._drill_randomFactor_xSpeed;	//直角坐标 - x - 随机因子
	temp_b_move['cartYSpeedRandomFactor'] = this._drill_randomFactor_ySpeed;	//直角坐标 - y - 随机因子
	// > 随机迭代次数（RandomIteration）
	//		（无）
	
	// > 生成参数数据
	this._drill_COSE_ballistics_move = $gameTemp.drill_COBa_setBallisticsMove( temp_b_move );
}
//==============================
// * 碎片群弹道 - 弹道初始化（透明度）
//
//			说明：	> 只存 弹道配置，不存 实际弹道。包括随机因子，但不含随机迭代次数。
//					> 实际弹道只在贴图中进行推演并使用。
//==============================
Drill_COSE_Controller.prototype.drill_initBallisticsOpacity = function( data, sustain, orderDelay ){
	
	// > 弹道初始化（透明度）
	var orgOpacity = 255;
	var temp_b_opacity = {};
	temp_b_opacity['opacityMode'] = "目标值模式";
	
	if( data['shatter_opacityType'] == "不消失" ){	
		// > 基础参数
		temp_b_opacity['opacityNum'] = this._drill_spriteNum;
		temp_b_opacity['opacityTime'] = sustain;
		temp_b_opacity['opacityDelay'] = 0;
		temp_b_opacity['opacityEndDelay'] = 0;				
		temp_b_opacity['opacityOrderDelay'] = orderDelay;	
		// > 模式参数		
		temp_b_opacity['targetType'] = "瞬间变化";			
		temp_b_opacity['targetDifference'] = 0;
	}
	else if( data['shatter_opacityType'] == "瞬间消失" ){		//（透明度这里直接固定配置内容）
		// > 基础参数
		temp_b_opacity['opacityNum'] = this._drill_spriteNum;
		temp_b_opacity['opacityTime'] = sustain;	
		temp_b_opacity['opacityDelay'] = 0;						
		temp_b_opacity['opacityEndDelay'] = 0;					
		temp_b_opacity['opacityOrderDelay'] = orderDelay;	
		// > 模式参数
		temp_b_opacity['targetType'] = "瞬间变化";				
		temp_b_opacity['targetDifference'] = 0 - orgOpacity;	
	}
	else if( data['shatter_opacityType'] == "线性消失" ){		
		// > 基础参数
		temp_b_opacity['opacityNum'] = this._drill_spriteNum;
		temp_b_opacity['opacityTime'] = sustain;
		temp_b_opacity['opacityDelay'] = 0;					
		temp_b_opacity['opacityEndDelay'] = 0;					
		temp_b_opacity['opacityOrderDelay'] = orderDelay;	
		// > 模式参数		
		temp_b_opacity['targetType'] = "匀速变化";				
		temp_b_opacity['targetDifference'] = 0 - orgOpacity;	
	}
	else if( data['shatter_opacityType'] == "等一半时间后线性消失" ){	
		// > 基础参数
		temp_b_opacity['opacityNum'] = this._drill_spriteNum;
		temp_b_opacity['opacityTime'] = sustain/2;
		temp_b_opacity['opacityDelay'] = sustain/2;
		temp_b_opacity['opacityEndDelay'] = 0;					
		temp_b_opacity['opacityOrderDelay'] = orderDelay;	
		// > 模式参数		
		temp_b_opacity['targetType'] = "匀速变化";			
		temp_b_opacity['targetDifference'] = 0 - orgOpacity;
	}
	else{
		alert( DrillUp.drill_COSE_getPluginTip_ErrorOpacityType( data['shatter_opacityType'] ) );
	}
	
	//   随机因子（RandomFactor）
	//		（每个碎片对应一个随机因子，掌握一条弹道。）
	//		（注意，独立参数项之间，随机因子不可共用。会造成强关联的错误关系。）
	temp_b_opacity['randomFactor'] = this._drill_randomFactor_opacity;
	//   随机迭代次数（RandomIteration）
	//		（无）
	
	// > 生成参数数据
	this._drill_COSE_ballistics_opacity = $gameTemp.drill_COBa_setBallisticsOpacity( temp_b_opacity );
}
//==============================
// * 控制器 - 重设数据（私有）
//
//			说明：	data对象中的参数【可以缺项】。
//==============================
Drill_COSE_Controller.prototype.drill_COSE_resetData_Private = function( data ){
	
	// > 不接受空名称的碎片
	if( data['src_mode'] == "指定资源名" && data['src_img'] == "" ){
		return;
	}
	
	// > 判断数据重复情况
	if( this._drill_data != undefined ){
		var keys = Object.keys( data );
		var is_same = true;
		for( var i=0; i < keys.length; i++ ){
			var key = keys[i];
			if( this._drill_data[key] != data[key] ){
				is_same = false;
			}
		}
		if( is_same == true ){ return; }
	}
	// > 补充未设置的数据
	var keys = Object.keys( this._drill_data );
	for( var i=0; i < keys.length; i++ ){
		var key = keys[i];
		if( data[key] == undefined ){
			data[key] = this._drill_data[key];
		}
	}
	
	// > 执行重置
	this._drill_data = JSON.parse(JSON.stringify( data ));					//深拷贝
	this._drill_controllerSerial = new Date().getTime() + Math.random();	//（生成一个不重复的序列号）
    this.drill_initData();													//初始化数据
    this.drill_initPrivateData();											//私有数据初始化
}
//==============================
// * 控制器 - 是否正在播放（私有）
//==============================
Drill_COSE_Controller.prototype.drill_COSE_isPlaying_Private = function(){
	if( this._drill_curTime <= 0 ){ return false; }
	if( this._drill_curTime >= this._drill_playingTime ){ return false; }
	return true;
}
//==============================
// * 控制器 - 播放粉碎过程（私有）
//==============================
Drill_COSE_Controller.prototype.drill_COSE_runShatter_Private = function(){
	this._drill_curTime = 0;
	this._drill_backrun = false;
	this._drill_pause = false;
}
//==============================
// * 控制器 - 倒放粉碎过程（私有）
//==============================
Drill_COSE_Controller.prototype.drill_COSE_backrunShatter_Private = function(){
	this._drill_curTime = this._drill_playingTime;
	this._drill_backrun = true;
	this._drill_pause = false;
}
//==============================
// * 控制器 - 立即复原（私有）
//
//			说明：	立即复原表示：父类显示，碎片隐藏，不播放动画的状态。
//==============================
Drill_COSE_Controller.prototype.drill_COSE_restoreShatter_Private = function(){
	this._drill_curTime = 0;
	this._drill_backrun = false;
	this._drill_pause = true;
}
//==============================
// * 控制器 - 暂停粉碎过程（私有）
//==============================
Drill_COSE_Controller.prototype.drill_COSE_pause_Private = function(){
	this._drill_pause = true;
}
//==============================
// * 控制器 - 继续粉碎过程（私有）
//==============================
Drill_COSE_Controller.prototype.drill_COSE_continue_Private = function(){
	this._drill_pause = false;
}
//==============================
// * 帧刷新 - 碎片可见
//==============================
Drill_COSE_Controller.prototype.drill_updateVisible = function(){
	var data = this._drill_data;
	
	// > 强制隐藏时
	if( data['visible'] == false ){
		this._drill_visible = false;
		return;
	}
	
	// > 强制不消失时
	if( data['shatter_opacityType'] == "不消失" ){	
		this._drill_visible = true;
		return;
	}
	
	// > 可见情况（有父贴图时，碎片只在动画播放时显示）
	if( data['shatter_hasParent'] == true ){
		this._drill_visible = this.drill_COSE_isPlaying_Private();
		return;
	}
	
	// > 可见情况（无父贴图时，碎片只要没过消失时间，都显示）
	if( this._drill_curTime < this._drill_playingTime ){
		this._drill_visible = true;
	}else{
		this._drill_visible = false;
	}
}
//==============================
// * 帧刷新 - 碎片时间流逝
//==============================
Drill_COSE_Controller.prototype.drill_updateTime = function(){
	var data = this._drill_data;
	
	// > 暂停情况
	if( this._drill_pause == true ){ return; }
	
	// > 时间播放（倒放）
	if( this._drill_backrun == true ){
		this._drill_curTime -= 1;
		
	// > 时间播放
	}else{
		this._drill_curTime += 1;
	}
}


//=============================================================================
// ** 方块粉碎贴图【Drill_COSE_LayerSprite】
// **			
// **		索引：	COSE（可从子插件搜索到函数、类用法）
// **		来源：	继承于Sprite
// **		实例：	> Drill_EventShatterEffect 插件中 Sprite_Character 对象的 ._drill_ESE_sprite 成员
// **		应用：	> Drill_EventShatterEffect 插件中 Sprite_Character 对象的 drill_COSE_setController 绑定
// **		
// **		作用域：	地图界面、战斗界面、菜单界面
// **		主功能：	> 定义一个粉碎贴图，能够播放碎片四散的动画。
// **					> 贴图不影响父贴图，更【不会隐藏】父贴图。
// **
// **		说明：	> 子插件需要根据情况，手动控制 父贴图 的显示。
// **				> 该类不能单独使用，必须结合 方块粉碎控制器 数据类。
// **
// **		代码：	> 范围 - 该类只对 粉碎配置 提供简易动画。
// **				> 结构 - [合并/ ●分离 /混乱] 贴图与数据分离。
// **				> 数量 - [单个/ ●多个 ] 父贴图与粉碎一对一。
// **				> 创建 - [ ●一次性 /自延迟/外部延迟] 直接根据数据划分切片来创建碎片。
// **				> 销毁 - [不考虑/ ●自销毁 /外部销毁 ] 碎片数据加上销毁标记，即可使得贴图自销毁。
// **				> 样式 - [不可修改/ ●自变化 /外部变化 ] 
// **
// **		调用方法：	// > 创建 - 不存储数据 写法
// **						$gameTemp._drill_XXX_controller = new Drill_COSE_Controller();		//（放$gameTemp中，不存）
// **						$gameTemp._drill_XXX_sprite = new Drill_COSE_LayerSprite();
// **						$gameTemp._drill_XXX_sprite.drill_COSE_setController( $gameTemp._drill_XXX_controller );
// **						this._drill_SenceTopArea.addChild( $gameTemp._drill_XXX_sprite );
// **					// > 创建 - 存储数据 写法
// **						$gameSystem._drill_XXX_controller = new Drill_COSE_Controller();	//（放$gameSystem中初始化，存储）
// **						$gameTemp._drill_XXX_sprite = new Drill_COSE_LayerSprite();
// **						$gameTemp._drill_XXX_sprite.drill_COSE_setController( $gameSystem._drill_XXX_controller );
// **						this._drill_SenceTopArea.addChild( $gameTemp._drill_XXX_sprite );
// **
// **					// > 帧刷新
// **						$gameTemp._drill_XXX_controller.drill_COSE_update();				//（不要忘了，数据必须手动帧刷新）
// **
// **					// > 修改数据
// **						var data = {
// **							"frameX": 0,	
// **							"frameY": 0,
// **							"frameW": Graphics.boxWidth,
// **							"frameH": Graphics.boxHeight,
// **							"src_mode": "指定资源名",
// **							"src_img": bitmap_name,
// **							"src_file": "img/Map__shatterBackground/",
// **							"shatter_id": Number(temp2)-1,									//粉碎样式
// **							"shatter_opacityType": $gameSystem._drill_XXX_opacityType,		//透明度变化方式
// **							"shatter_hasParent": false,										//父贴图标记
// **						};
// **						$gameTemp._drill_XXX_controller.drill_COSE_resetData( data );		//方块粉碎核心 - 初始化
// **						$gameTemp._drill_XXX_controller.drill_COSE_runShatter();			//正常播放
// **						//（直接对控制器进行操作即可，贴图对象会自变化）
//=============================================================================
//==============================
// * 粉碎贴图 - 定义
//==============================
function Drill_COSE_LayerSprite() {
	this.initialize.apply(this, arguments);
}
Drill_COSE_LayerSprite.prototype = Object.create(Sprite.prototype);
Drill_COSE_LayerSprite.prototype.constructor = Drill_COSE_LayerSprite;
//==============================
// * 粉碎贴图 - 初始化
//==============================
Drill_COSE_LayerSprite.prototype.initialize = function(){
	Sprite.prototype.initialize.call(this);
	this._drill_controller = null;		//控制器对象
	this._drill_curSerial = -1;			//控制器序列号
	this._drill_bitmap = null;			//资源对象
	this._drill_spriteTank = [];		//碎片容器
	//（初始状态，不创建任何对象）
}
//==============================
// * 粉碎贴图 - 帧刷新
//==============================
Drill_COSE_LayerSprite.prototype.update = function(){
	Sprite.prototype.update.call(this);
	this.drill_updateAutoDestroy();			//帧刷新 - 自动销毁
	if( this.drill_COSE_isReady() == false ){ return; }
	this.drill_updateRebuild();				//帧刷新 - 碎片重刷时机
	this.drill_updateShatterVisible();		//帧刷新 - 碎片可见
	this.drill_updateShatterMove();			//帧刷新 - 碎片移动
}
//##############################
// * 粉碎贴图 - 设置控制器【标准函数】
//			
//			参数：	> controller 控制器对象
//			返回：	> 无
//			
//			说明：	> 由于贴图与数据分离，贴图必须依赖一个数据对象。
//##############################
Drill_COSE_LayerSprite.prototype.drill_COSE_setController = function( controller ){
	this._drill_controller = controller;
};
//##############################
// * 粉碎贴图 - 是否就绪【标准函数】
//			
//			参数：	> 无
//			返回：	> 布尔（是否显示）
//			
//			说明：	> 这里完全 不考虑 延迟加载问题。
//##############################
Drill_COSE_LayerSprite.prototype.drill_COSE_isReady = function(){
	if( this._drill_controller == undefined ){ return false; }
    return true;
};
//##############################
// * 粉碎贴图 - 是否需要销毁【标准函数】
//			
//			参数：	> 无
//			返回：	> 布尔（是否需要销毁）
//			
//			说明：	> 此函数可用于监听 控制器数据 是否被销毁，数据销毁后，贴图可自动销毁。
//##############################
Drill_COSE_LayerSprite.prototype.drill_COSE_isNeedDestroy = function(){
	if( this._drill_controller == undefined ){ return false; }	//（未绑定时，不销毁）
	if( this._drill_controller._drill_needDestroy == true ){ return true; }
    return false;
};
//##############################
// * 粉碎贴图 - 销毁【标准函数】
//			
//			参数：	> 无
//			返回：	> 无
//			
//			说明：	> 销毁不是必要的，但最好随时留意给 旧贴图 执行销毁函数。
//##############################
Drill_COSE_LayerSprite.prototype.drill_COSE_destroy = function(){
	this.drill_COSE_destroy_Private();
};
//##############################
// * 粉碎贴图 - 父贴图是否显示【标准函数】
//
//			参数：	> 无
//			返回：	> 布尔（是否显示）
//			
//			说明：	> 播放时、碎片消失后，都返回false。
//                  > 复原时、倒放碎片恢复后，才返回true。
//					> 注意，如果碎片没有父贴图，那么需要在 data['shatter_hasParent'] 中设置false。
//					> 当然，如果碎片没有父贴图，此函数也用不上。
//##############################
Drill_COSE_LayerSprite.prototype.drill_COSE_canParentVisible = function(){
	return this.drill_COSE_canParentVisible_Private();
};
//##############################
// * 粉碎贴图 - 设置非控制的资源对象【标准函数】
//
//			参数：	> bitmap 对象（资源对象）
//			返回：	> 无
//			
//			说明：	> 如果 控制器 中设置了"关闭资源控制"，那么需要此函数赋值bitmap对象。
//					> 调用后，将强制给贴图赋值资源对象。（此对象无法保存）
//##############################
Drill_COSE_LayerSprite.prototype.drill_COSE_setUncontroledBitmap = function( bitmap ){
	this._drill_bitmap = bitmap;
	this.drill_rebuildBitmap();
};
//==============================
// * 粉碎贴图 - 销毁（私有）
//==============================
Drill_COSE_LayerSprite.prototype.drill_COSE_destroy_Private = function(){
	
	// > 清空碎片贴图
	this.drill_clearShatter();
	
	// > 断开连接
	this._drill_controller = null;
	this._drill_bitmap = null;
};
//==============================
// * 粉碎贴图 - 父贴图是否显示（私有）
//==============================
Drill_COSE_LayerSprite.prototype.drill_COSE_canParentVisible_Private = function(){
	
	// > 未准备完全时，父贴图保持显示
	if( this.drill_COSE_isReady() == false ){ return true; }
	
	// > 处于粉碎状态时，父贴图不显示
	if( this._drill_controller._drill_curTime > 0 ){ return false; }
	
	return true;
};

//==============================
// * 粉碎贴图 - 清空碎片贴图
//==============================
Drill_COSE_LayerSprite.prototype.drill_clearShatter = function(){
	for( var i=0; i < this._drill_spriteTank.length; i++){
		var temp_sprite = this._drill_spriteTank[i];
		this.removeChild(temp_sprite);
	}
	this._drill_spriteTank = [];
};
//==============================
// * 粉碎贴图 - 重建 - 碎片贴图
//==============================
Drill_COSE_LayerSprite.prototype.drill_rebuildShatter = function(){
    var c_data = this._drill_controller;
    var d_data = this._drill_controller._drill_data;
	
	// > 序列号标记
	this._drill_curSerial = this._drill_controller._drill_controllerSerial;
	
	// > 清空碎片贴图
	this.drill_clearShatter();
	
	// > 私有属性初始化
    this.anchor.x = 0;
    this.anchor.y = 0;
	if( d_data['shatter_hasParent'] == true ){
		this.x = -1 * this.parent.anchor.x * d_data['frameW'];
		this.y = -1 * this.parent.anchor.y * d_data['frameH'];
	}else{
		this.x = 0;
		this.y = 0;
	}
	
	// > 碎片参数
	var sw = c_data._drill_spriteWidth;
	var sh = c_data._drill_spriteHeight;
	var colNum = c_data._drill_spriteColCount;
	var rowNum = c_data._drill_spriteRowCount;
	
	for( var i=0; i < colNum; i++ ){
		for( var j=0; j < rowNum; j++ ){
			
			// > 碎片初始化
			var temp_sprite = new Sprite();
			temp_sprite._orgX = 0 + sw * i;
			temp_sprite._orgY = 0 + sh * j;
			temp_sprite._orgOpacity = 255;
			temp_sprite.x = temp_sprite._orgX;
			temp_sprite.y = temp_sprite._orgY;
			
			// > 添加碎片
			this.addChild( temp_sprite );
			this._drill_spriteTank.push( temp_sprite );
		}
	}	
}
//==============================
// * 粉碎贴图 - 重建 - 资源对象
//==============================
Drill_COSE_LayerSprite.prototype.drill_rebuildBitmap = function(){
    var c_data = this._drill_controller;
    var d_data = this._drill_controller._drill_data;
	
	// > 资源对象设置
	if( d_data['src_mode'] == "指定资源名" ){
		this._drill_bitmap = ImageManager.loadBitmap( d_data['src_file'], d_data['src_img'], 0, true);
	}
	if( d_data['src_mode'] == "关闭资源控制" ){
		//（不操作）
	}
	
	// > 碎片参数
	var sw = c_data._drill_spriteWidth;
	var sh = c_data._drill_spriteHeight;
	var colNum = c_data._drill_spriteColCount;
	var rowNum = c_data._drill_spriteRowCount;
	
	for( var i=0; i < colNum; i++ ){
		for( var j=0; j < rowNum; j++ ){
			var temp_sprite = this._drill_spriteTank[ i*rowNum+j ];
			if( temp_sprite == undefined ){ continue; }
			
			// > 设置资源对象
			temp_sprite.bitmap = this._drill_bitmap;
			
			// > 设置框架范围
			var xx = sw * i;
			var yy = sh * j;
			var ww = sw;
			var hh = sh;
			if( xx + sw > d_data['frameW'] ){	//（切割不能越界）
				ww = d_data['frameW'] - xx;
			}
			if( yy + sh > d_data['frameH'] ){
				hh = d_data['frameH'] - yy;
			}
			xx += d_data['frameX'];
			yy += d_data['frameY'];
			temp_sprite.drill_COSE_setFrame( xx, yy, ww, hh );
		}
	}
}
//==============================
// * 粉碎贴图 - 优化浮点数过滤
//
//			说明：	用floor防止 浮点数 比较时，造成frame的反复刷新。
//==============================
Sprite.prototype.drill_COSE_setFrame = function( x, y, width, height ){
	this.setFrame( Math.floor(x), Math.floor(y), Math.floor(width), Math.floor(height) );
}
//==============================
// * 碎片群弹道 - 重新推演弹道
//==============================
Drill_COSE_LayerSprite.prototype.drill_rebuildBallistics = function(){
    var c_data = this._drill_controller;
    var d_data = this._drill_controller._drill_data;
	var style_data = DrillUp.g_COSE_style_list[ d_data['shatter_id'] ];
	
	// > 碎片参数
	var colNum = c_data._drill_spriteColCount;
	var rowNum = c_data._drill_spriteRowCount;
	
	var max_per = Math.floor( Math.abs( (Math.max( colNum,rowNum )-1) /2 ) );
	Drill_COBa_Manager._drill_COBa_planimetryData = c_data._drill_COSE_ballistics_move;	//（存储的弹道数据，赋值后预推演）
	Drill_COBa_Manager._drill_COBa_commonData = c_data._drill_COSE_ballistics_opacity;
	//alert( JSON.stringify(  Drill_COBa_Manager._drill_COBa_planimetryData ) );
	//alert( JSON.stringify(  Drill_COBa_Manager._drill_COBa_commonData ) );
	
	for( var i=0; i < colNum; i++ ){
		for( var j=0; j < rowNum; j++ ){
			var temp_sprite = this._drill_spriteTank[ i*rowNum+j ];
			if( temp_sprite == undefined ){ continue; }
			
			// > 碎片群弹道 - 预推演（坐标）
			$gameTemp.drill_COBa_preBallisticsMove( temp_sprite, i*rowNum+j , temp_sprite._orgX, temp_sprite._orgY );
			
			// > 碎片群弹道 - 预推演（透明度）
			$gameTemp.drill_COBa_preBallisticsOpacity( temp_sprite, i*rowNum+j , temp_sprite._orgOpacity );
			
			// > 变速矩阵
			if( style_data['speedPer'] == true ){
				var a = Math.abs( i - (colNum-1)/2 );
				var b = Math.abs( j - (rowNum-1)/2 );
				a = Math.floor( Math.max( a,b ) );
				a = a/max_per;
				
				a = a *( 1-style_data['speedPerMin'] ) + style_data['speedPerMin'];
				for( var n = 0; n < temp_sprite["_drill_COBa_x"].length; n++ ){
					temp_sprite["_drill_COBa_x"][n] = temp_sprite["_drill_COBa_x"][n] * a + temp_sprite._orgX * (1-a);
				}
				for( var n = 0; n < temp_sprite["_drill_COBa_y"].length; n++ ){
					temp_sprite["_drill_COBa_y"][n] = temp_sprite["_drill_COBa_y"][n] * a + temp_sprite._orgY * (1-a);
				}
			}
			
			// > 速度倍率
			var speedFactor = style_data['speedFactor'] || 1.0;
			for( var n = 0; n < temp_sprite["_drill_COBa_x"].length; n++ ){
				temp_sprite["_drill_COBa_x"][n] = temp_sprite["_drill_COBa_x"][n] * speedFactor;
			}
			for( var n = 0; n < temp_sprite["_drill_COBa_y"].length; n++ ){
				temp_sprite["_drill_COBa_y"][n] = temp_sprite["_drill_COBa_y"][n] * speedFactor;
			}
		}
	}
	
	// > 变速矩阵（测试显示）
	//var ss = "";
	//var max_per = Math.floor( Math.abs( (Math.max( colNum,rowNum )-1) /2 ) );
	//for( var i=0; i < colNum; i++){
	//	var sss = "";
	//	for( var j=0; j < rowNum; j++){
	//		var a = Math.abs( i - (colNum-1)/2 );
	//		var b = Math.abs( j - (rowNum-1)/2 );
	//		a = Math.floor( Math.max( a,b ) );
	//		a = a/max_per;
	//		
	//		sss += String(a) + "  ";
	//		//sss += String(j + i * rowNum) + "  ";
	//	}
	//	ss += sss + "\n";
	//}
	//alert(ss);
}


//==============================
// * 帧刷新 - 自动销毁
//==============================
Drill_COSE_LayerSprite.prototype.drill_updateAutoDestroy = function(){
	if( this.drill_COSE_isNeedDestroy() != true ){ return };
	
	// > 需要销毁，却没销毁时
	if( this._drill_spriteTank.length > 0 ){
		
		// > 自行销毁
		this.drill_COSE_destroy();
	}
}
//==============================
// * 帧刷新 - 碎片重刷时机
//==============================
Drill_COSE_LayerSprite.prototype.drill_updateRebuild = function(){
	
	// > 有控制器时
	if( this._drill_spriteTank.length == 0 ){
		this.drill_rebuildShatter();	//（自行创建）
		this.drill_rebuildBitmap();
		this.drill_rebuildBallistics();
		
	// > 控制器变化时
	}else{
		if( this._drill_curSerial != this._drill_controller._drill_controllerSerial ){
			this._drill_curSerial = this._drill_controller._drill_controllerSerial;
			this.drill_rebuildShatter();	//（自行创建）
			this.drill_rebuildBitmap();
			this.drill_rebuildBallistics();
		}
	}
}
//==============================
// * 帧刷新 - 碎片可见
//==============================
Drill_COSE_LayerSprite.prototype.drill_updateShatterVisible = function(){
	this.visible = this._drill_controller._drill_visible;
	//this.visible = true;
}
//==============================
// * 帧刷新 - 碎片移动
//==============================
Drill_COSE_LayerSprite.prototype.drill_updateShatterMove = function(){
	
	// > 如果不可见，移动也不需要 帧刷新
	if( this.visible == false ){ return; }
	
    var c_data = this._drill_controller;
    var d_data = this._drill_controller._drill_data;
	
	// > 根据轨迹进行播放
	for( var i=0; i < this._drill_spriteTank.length; i++ ){
		var temp_sprite = this._drill_spriteTank[i];
		if( temp_sprite == undefined ){ continue; }
		if( temp_sprite['_drill_COBa_x'] == undefined ){ continue; }
		
		var time = c_data._drill_curTime;
		if( time < 0 ){ time = 0; }
		if( time > temp_sprite['_drill_COBa_x'].length-1 ){
			time = temp_sprite['_drill_COBa_x'].length-1;
		}
		
		// > 位置（碎片群弹道）
		temp_sprite.x = temp_sprite['_drill_COBa_x'][time];	
		temp_sprite.y = temp_sprite['_drill_COBa_y'][time];
		
		// > 透明度（碎片群弹道）
		temp_sprite.opacity = temp_sprite['_drill_COBa_opacity'][time];
		//temp_sprite.opacity = 255;
	}
}

//=============================================================================
// ** 核心漏洞修复
//=============================================================================
//==============================
// * 核心漏洞修复 - 屏蔽根据版本重刷地图
//
//			说明：	此功能会刷掉旧存档的存储数据，因为版本不一样会强制重进地图。
//					而这样做只是 刷新旧存档的当前地图而已，没任何好处。
//==============================
Scene_Load.prototype.reloadMapIfUpdated = function() {
	// （禁止重刷）
};


//=============================================================================
// * <<<<基于插件检测<<<<
//=============================================================================
}else{
		Imported.Drill_CoreOfShatterEffect = false;
		var pluginTip = DrillUp.drill_COSE_getPluginTip_NoBasePlugin();
		alert( pluginTip );
}



