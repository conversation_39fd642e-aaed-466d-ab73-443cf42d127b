/*:
 * @help
 * Allheart <対象:screen> ＠2021/8/7
 * above <対象:character> ＠2020/8/11
 * aoyuge <対象:screen> ＠2021/8/7
 * blizard_o <対象:weather> ＠2020/6/6
 * blizard_w <対象:weather> ＠2020/6/6
 * bubble_cp2 <対象:character> ＠2021/4/22
 * cat <対象:character> ＠2021/9/23
 * dark <対象:character> ＠2024/4/10
 * devil <対象:character> ＠2021/12/10
 * dust_walk <対象:walk> ＠2020/6/22
 * dust_walk2 <対象:walk> ＠2020/6/22
 * fire <対象:character> ＠2020/8/30
 * fire2 <対象:character> ＠2020/8/30
 * fog_h <対象:screen> ＠2020/6/6
 * grass_walk2 <対象:walk> ＠2020/6/22
 * heart01 <対象:character> ＠2021/4/8
 * hihou <対象:character> ＠2021/12/10
 * hikari <対象:character> ＠2021/9/23
 * hinoko <対象:screen> ＠2021/9/21
 * imasime <対象:character> ＠2021/11/13
 * issou <対象:character> ＠2021/9/23
 * item <対象:character> ＠2020/8/14
 * item-14 <対象:character> ＠2020/8/11
 * item2 <対象:character> ＠2020/8/11
 * itigeki <対象:character> ＠2021/9/23
 * kaji <対象:character> ＠2021/9/21
 * ketyu <対象:character> ＠2021/10/4
 * kira <対象:weather> ＠2020/9/15
 * kira2 <対象:weather> ＠2020/9/15
 * koex <対象:character> ＠2021/12/10
 * mahouen <対象:character> ＠2021/8/19
 * murasakiyuge <対象:screen> ＠2021/8/7
 * nest <対象:character> ＠2021/4/10
 * nest02 <対象:character> ＠2021/4/10
 * pinkyuge <対象:screen> ＠2021/8/7
 * pinkyuge2 <対象:screen> ＠2021/8/7
 * rain_cw <対象:weather> ＠2020/6/6
 * rain_end <対象:weather> ＠2021/12/11
 * ring_walk <対象:walk> ＠2020/7/8
 * ripple_walk <対象:walk> ＠2020/5/15
 * rune <対象:character> ＠2020/10/20
 * sina <対象:character> ＠2020/9/15
 * sina_b <対象:character> ＠2020/9/15
 * sina_e <対象:character> ＠2020/9/15
 * sina_g <対象:character> ＠2020/9/15
 * sina_l <対象:character> ＠2020/9/15
 * sina_t <対象:character> ＠2020/9/15
 * sinen <対象:character> ＠2021/12/9
 * siroyuge <対象:screen> ＠2021/8/7
 * splash_walk <対象:walk> ＠2020/5/17
 * splash_walk2 <対象:walk> ＠2020/5/30
 * test <対象:character> ＠2024/6/28
 * thunder_1 <対象:weather> ＠2020/6/8
 * title01 <対象:character> ＠2021/9/23
 *
 * @requiredAssets img/particles/heart4g
 * @requiredAssets img/particles/line_oval3
 * @requiredAssets img/particles/smoke2
 * @requiredAssets img/particles/cloud2s
 * @requiredAssets img/particles/snow_particle2
 * @requiredAssets img/particles/snow_particle1
 * @requiredAssets img/particles/bubble1
 * @requiredAssets img/particles/line2
 * @requiredAssets img/particles/line_oval2
 * @requiredAssets img/particles/smog1
 * @requiredAssets img/particles/cartoon_fuss2
 * @requiredAssets img/particles/flame1
 * @requiredAssets img/particles/cloud3
 * @requiredAssets img/particles/cloud2
 * @requiredAssets img/particles/cloud1
 * @requiredAssets img/particles/heart1
 * @requiredAssets img/particles/particle2
 * @requiredAssets img/particles/circle3g
 * @requiredAssets img/particles/flame1g
 * @requiredAssets img/particles/smog2
 * @requiredAssets img/particles/smoke1
 * @requiredAssets img/particles/circle3
 * @requiredAssets img/particles/shine_thin3
 * @requiredAssets img/particles/snow_particle2g
 * @requiredAssets img/particles/ripple1g
 * @requiredAssets img/particles/particle8
 * @requiredAssets img/particles/particle5
 * @requiredAssets img/particles/particle4
 * @requiredAssets img/particles/line_rain2
 * @requiredAssets img/particles/line_rain1
 * @requiredAssets img/particles/ripple2
 * @requiredAssets img/particles/hexagon_line3
 * @requiredAssets img/particles/hexagon1
 * @requiredAssets img/particles/thunder1
 * @requiredAssets img/particles/thunder2
 * @requiredAssets img/particles/_ANIM_288_23
 */
PluginManager._parameters.trp_particlelist = {
	animImages:["ANIM:288:23"]
};