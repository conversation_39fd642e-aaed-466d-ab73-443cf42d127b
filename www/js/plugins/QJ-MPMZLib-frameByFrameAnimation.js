//=============================================================================
 /*:
 * @plugindesc 动画脚本
 * <AUTHOR>
 */
//=============================================================================

// 妹妹眨眼
QJ.MPMZ.tl._imoutoUtilImoutoBlinking = function () {
	
	this._coolDown = this._coolDown || 0;	
	if (this._coolDown > 0) {
	   this._coolDown -= 1;
	   return;
	}

	this._frames = this._frames || 1;

	if ( !$gameScreen.picture(5) || !$gameScreen.picture(5).name().includes("dozingOff") ) {
		this.setDead({t:['Time',0]});
		return;		
	}
	if ( $gameScreen.picture(5)._opacity < 250 ) {
		this._coolDown = 60;	
		return;		
	}
	
	let IMG = "sis_room/sis_room_dozingOff" + this._frames;
    $gameScreen.changePictureName(5, IMG);
	
	if (this._frames >= 4) {
		this._coolDown = 6;
		this._frames -= 1;
		this._upend = true;
		return;
	}

	if (this._upend && this._frames <= 1) {
		this._coolDown = Math.randomInt(120) + 180;
		this._frames += 1;
		this._upend = false;
		return;
	}
	
	if (this._upend) {
		this._frames -= 1;
	} else {
		this._frames += 1;
	}
    this._coolDown = 2;	
	
};

// 妹妹一个人玩游戏手柄动画
QJ.MPMZ.tl._imoutoUtilImoutoSoloPlay = function () {
	
	this._coolDown = this._coolDown || 0;	
	if (this._coolDown > 0) {
	   this._coolDown -= 1;
	   return;
	}

	this._frames = this._frames || 0;
	
	let IMG = "alt_sister_normal_hand" + this._frames;
	$gameScreen.showPictureFromPath(8, "game_itazura", IMG, 0, 0, 0, 100, 100, 255, 0);	
	
	if (this._frames >= 6) {
		this._coolDown = Math.randomInt(4) + 2;
		this._frames -= 1;
		this._upend = true;
		return;
	}

	if (this._upend && this._frames <= 0) {
		this._coolDown = Math.randomInt(4) + 2;
		this._frames += 1;
		this._upend = false;
		return;
	}
	
	if (this._upend) {
		this._frames -= 1;
	} else {
		this._frames += 1;
	}
    this._coolDown = Math.randomInt(4) + 2;	
};

